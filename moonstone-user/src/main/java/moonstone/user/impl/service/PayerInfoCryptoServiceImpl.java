package moonstone.user.impl.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EncryptHelper;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.PayerInfoRecord;
import moonstone.user.service.PayerInfoCryptoService;
import moonstone.user.vo.PayerInfoDeCodeReq;
import moonstone.user.vo.PayerInfoDeCodeRes;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;

@Slf4j
@Service
public class PayerInfoCryptoServiceImpl implements PayerInfoCryptoService {

    @Override
    public PayerInfoDeCodeRes decode(PayerInfoDeCodeReq req) throws DecoderException {
        String passwordHex = req.getPasswordHex();
        String payerNoHex = req.getPayerNoHex();
        String payerNameHex = req.getPayerNameHex();

        String password = new String(Hex.decodeHex(passwordHex));
        String payerNo = new String(Hex.decodeHex(payerNoHex));
        String payerName = new String(Hex.decodeHex(payerNameHex));

        PayerInfoRecord payerInfoRecord = new PayerInfoRecord(
                null,
                null,
                payerName.getBytes(StandardCharsets.UTF_8),
                payerNo.getBytes(StandardCharsets.UTF_8),
                password.getBytes(StandardCharsets.UTF_8),
                "");
        PayerInfo info = payerInfoRecord.info();
        PayerInfo.Helper.Info out = PayerInfo.Helper.decode(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey),info);
        System.out.println(JSONObject.toJSONString(out));

        PayerInfoDeCodeRes res = new PayerInfoDeCodeRes();
        res.setPayerName(out.getName());
        res.setPayerNo(out.getNo());
        return res;
    }


    public static void main(String[] args) throws DecoderException, KeyStoreException, IOException, CertificateException, NoSuchAlgorithmException {

        String passwordHex = "4A3667376F617A62306B6A384D3763714C334C344663534E2B4458665A74756E327756514F66305A4C736A49692F6142776C673649766C48517177386A514459";
        String hashHex = "554CB536E7763ED04F61C4587649FC913611600560BCABF2D19D6C1D4A1EBC4B";
        String payerNoHex = "624639536A4438767874456C414D4457764E6A41576B6131582F4A6272374A47555430493764346F727A303D";
        String payerNameHex = "2B6E445959544551616E74382F6647694A4B463379673D3D";

        String password = new String(Hex.decodeHex(passwordHex));
        String hash = new String(Hex.decodeHex(hashHex));
        String payerNo = new String(Hex.decodeHex(payerNoHex));
        String payerName = new String(Hex.decodeHex(payerNameHex));

        PayerInfoRecord payerInfoRecord = new PayerInfoRecord(
                null,
                null,
                // payerName
                payerName.getBytes(StandardCharsets.UTF_8),
                // payerNo
                payerNo.getBytes(StandardCharsets.UTF_8),
                // password
//                null,
                password.getBytes(StandardCharsets.UTF_8),
                "");
        PayerInfo info = payerInfoRecord.info();
        KeyStore key = KeyStore.getInstance(KeyStore.getDefaultType());
        //key.load(new FileInputStream("/home/<USER>/keystore/online.keystore"), "online-secret".toCharArray());
        key.load(new FileInputStream("F:\\2025\\05\\mall7\\moonstone-web-core\\src\\main\\resources\\stag.keystore"), "online-secret".toCharArray());
        EncryptHelper.instance.keyStore(key);
        EncryptHelper.instance.secret("online-secret");
        var out = PayerInfo.Helper.decode(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey),info);
        System.out.println(JSONObject.toJSONString(out));

//        String hexString = "2B6E445959544551616E74382F6647694A4B463379673D3D";
//        // 十六进制转二进制
//        byte[] data = Hex.decodeHex(hexString);
//        System.out.println(new String(data));


//        String passwordHex = "4A3667376F617A62306B6A384D3763714C334C344663534E2B4458665A74756E327756514F66305A4C736A49692F6142776C673649766C48517177386A514459";
//        String hashHex = "554CB536E7763ED04F61C4587649FC913611600560BCABF2D19D6C1D4A1EBC4B";
//        String payerNo = "2B6E445959544551616E74382F6647694A4B463379673D3D";
//        PayerInfo payerInfo = new PayerInfo();
//        payerInfo.setPayerNo(new String(Hex.decodeHex(payerNo)));
//        payerInfo.setHash(hashHex);
//        payerInfo.setPassword(new String(Hex.decodeHex(passwordHex)));
////        PayerInfo.Helper.Info info = PayerInfo.Helper.decode(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey), payerInfo);
//
//        PayerInfo.Helper.Info out = PayerInfo.Helper.decode(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey), payerInfo);
//
//        System.out.println(JSONObject.toJSONString(out));
    }

}
