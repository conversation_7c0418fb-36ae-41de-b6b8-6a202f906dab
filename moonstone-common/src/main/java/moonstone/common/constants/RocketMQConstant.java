package moonstone.common.constants;

/**
 * author：书生
 */
public class RocketMQConstant {

    /**
     * 商家端主题
     */
    public static final String APP_MERCHANT_TOPIC = "app_merchant_topic";

    /**
     * 小二端主题
     */
    public static final String APP_ADMIN_TOPIC = "app_admin_topic";

    /**
     * 查询版本构建状态
     */
    public static final String ADMIN_BUILD_QUERY_TAG = "parana_admin_build_query_tag";

    /**
     * 查询版本构建状态消费者组
     */
    public static final String ADMIN_QUERY_BUILD_CONSUMER_GROUP = "parana_admin_query_build_consumer_group";

    /**
     * 体验版状态查询
     */
    public static final String ADMIN_QUERY_EXPERIENCE_TAG = "parana_admin_query_experience_tag";

    /**
     * 体验版状态查询消费者组
     */
    public static final String ADMIN_QUERY_EXPERIENCE_CONSUMER_GROUP = "parana_admin_query_experience_consumer_group";


    /**
     * 打包门店月度打卡照片
     */
    public static final String MERCHANT_PACKING_STORE_MONTHLY_PUNCH_PICTURE_TAG = "parana_merchant_packing_store_monthly_purchase_picture_tag";


    /**
     * 打包门店月度打卡照片消费者组
     */
    public static final String MERCHANT_PACKING_STORE_MONTHLY_PUNCH_PICTURE_CONSUMER_GROUP = "parana_merchant_packing_store_monthly_purchase_picture_consumer_group";


    /**
     * 生成订单真实利润
     */
    public static final String GENERATE_ORDER_FORESEE_PROFIT_TAG = "parana_generate_order_foresee_profit_tag";

    /**
     * 生成订单真实利润消费者组
     */
    public static final String GENERATE_ORDER_FORESEE_PROFIT_CONSUMER_GROUP = "parana_generate_order_foresee_profit_consumer_group";

    /**
     * 申请退款与三方系统调用标签
     */
    public static final String REFUND_APPLY_THIRD_PARTY_TAG = "parana_refund_apply_third_party_tag";

    /**
     * 申请退款与三方系统调用消费者组
     */
    public static final String REFUND_APPLY_THIRD_PARTY_CONSUMER_GROUP = "parana_refund_apply_third_party_consumer_group";

    /**
     * 拒绝退款与三方系统调用标签
     */
    public static final String REFUND_REJECT_THIRD_PARTY_TAG = "parana_refund_reject_third_party_tag";

    /**
     * 拒绝退款与三方系统调用消费者组
     */
    public static final String REFUND_REJECT_THIRD_PARTY_CONSUMER_GROUP = "parana_refund_reject_third_party_consumer_group";


    /**
     * 上传物流单号与三方系统调用标签
     */
    public static final String REFUND_AFTER_SALE_LOGISTICS_UPLOAD_THIRD_PARTY_TAG = "parana_refund_after_sale_logistics_upload_third_party_tag";

    /**
     * 上传物流单号与三方系统调用消费者组
     */
    public static final String REFUND_AFTER_SALE_LOGISTICS_UPLOAD_THIRD_PARTY_CONSUMER_GROUP = "parana_refund_after_sale_logistics_upload_third_party_consumer_group";

    /**
     * 退款回调重试标签
     */
    public static final String REFUND_CALLBACK_SUCCESS_TAG = "parana_refund_callback_success_tag";

    /**
     * 退款回调重试消费者组
     */
    public static final String REFUND_CALLBACK_SUCCESS_CONSUMER_GROUP = "parana_refund_success_retry_consumer_group";

    /**
     * 订单异常记录标签
     */
    public static final String ORDER_EXCEPTION_RECORD_TAG = "parana_order_exception_record_tag";

    /**
     * 订单异常记录消费者组
     */
    public static final String ORDER_EXCEPTION_RECORD_CONSUMER_GROUP = "parana_order_exception_record_consumer_group";

    /**
     * 无需推送标签
     */
    public static final String ORDER_NOT_NEED_PUSH_TAG = "parana_order_not_need_push_tag";


    /**
     * 无需推送消费组
     */
    public static final String ORDER_NOT_NEED_PUSH_CONSUMER_GROUP = "parana_order_not_need_push_consumer_group";


    /**
     * 商品同步第三方库存标签
     */
    public static final String PRODUCT_THIRD_PARTY_STOCK_SYNCHRONIZATION_TAG = "parana_product_third_party_stock_synchronization_tag";

    /**
     * 商品同步第三方库存消费者组
     */
    public static final String PRODUCT_THIRD_PARTY_STOCK_SYNCHRONIZATION_CONSUMER_GROUP = "parana_product_third_party_stock_synchronization_consumer_group";

    /**
     * 订单超时关闭标签
     */
    public static final String ORDER_TIMEOUT_CLOSE_TAG = "mall_order_timeout_close_tag";

    /**
     * 订单取消标签
     */
    public static final String ORDER_CANCEL_TAG = "mall_order_cancel_tag";

    /**
     * 订单退款成功标签
     */
    public static final String ORDER_REFUND_SUCCESS_TAG = "mall_order_refund_success_tag";

    /**
     * 订单异常记录标签
     */
    public static final String ORDER_SHIPPED_TAG = "parana_order_shipped_tag";

    public static final String ORDER_SEND_INVENTED_GIFT_COUPON_TAG_NEW = "mall_order_send_invented_gift_coupon_tag";

    /**
     * sku库存变化的TAG
     */
    public static final String SKU_STOCK_CHANGE_TAG = "mall_sku_stock_change_tag";

    /**
     * 订单确认收货消息
     */
    public static final String ORDER_CONFIRMED_TAG = "parana_order_confirmed_tag";
}
