package moonstone.common.constants;

import java.util.Optional;

/**
 * <AUTHOR>
 */

public enum ShopExtra {
    /**
     * 店铺额外字段 通常用于店铺额外功能开启或者关闭
     */
    AUTO_CONFIRM("ACF", "自动确定收货时间"),
    editLimit(null, "是否允许修改商品"), /// 修改限制
    SubProfitFee("SPF", "门店分销利润 已作废请勿使用"),                /// 门店分销利润
    SubProfitRate("SPR", "门店分销利润比 已作废请勿使用"),               /// 门店分销利润比
    GuiderProfitRate("GPR", "导购员分销利润比  已作废请勿使用"),            /// 导购员分销利润比
    GuiderProfitFee("GPF", "导购员分销利润  已作废请勿使用")              /// 导购员分销利润
    , shopUrl("shopUrl", "店铺在小程序的url")                /// 店铺在小程序的Url
    , openFans("openFans", "是否开启粉丝系统(1:0)")              /// 开启粉丝系统          (1:0)
    , allowSelfBuyProfit("allowSelfBuy", "是否允许自购利润(true:false)")/// 允许自购            (true:false)
    , AllowRefund("allowRefund", "多少分钟内允许退款(true[无限制]:number[分钟]{例如15})")    /// 一个小时内允许退款
    , SalesPattern("salesPattern", "分销模式")    /// 分销模式
    , commonShop("commonShop", "普通商场")          /// 普通商场
	, communityOperation("communityOperation","社群运营") // 社群运营
    , delayPaymentPush("delayPaymentPush", "支付单延迟推送时间(空{}:number[分钟]{})")  /// 延迟支付单推送
    , AllowNoSource("allowNoSource", "允许无来源订单") //  允许无来源(refererId)订单
    , WhCode("whCode", "Y800Storage配置的仓库编码") // Y800 仓库编码
    , Y800StorageAccessCode("accessCode", "Y800Storage配置的访问码") // Y800大贸接口对接
    , PushSystem("pushSystem", "可配置的推送第三方系统")   //  可以配置的第三方系统有那些
    , ItemTagType("itemTageType", "商品tag影响的范围") // 如果为1 则表示 全部有效,0 则为只对消费者 默认为1
    , NoGatherNeed("noGatherNeed", "不需要采购单")    // 如果存在true则表示不需要
	, LessAuth("lessAuth", "减少审核次数, 当代理改名的时候与注册的时候减少审核次数") // 1 为开启, 0为关闭, 默认为0
	, ManualBonded("manualBonded", "手动添加跨境商品类型 与导出非对接仓库的跨境订单") // 1 为开启, 0为关闭 默认关闭
	, ImportShipment("importShipment", "导入发货单") // 1 为开启 0为关闭 默认关闭
	, WithdrawRequireCert("withdrawRequireCert", "提现需要实名验证(开启:关闭 = 1:0), 默认关闭")
    , defaultWeShop("defaultWeShop", "默认的微店店铺Id")
    , homeDecoration("homeDecoration", "默认开启小程序分销")
	// 即将关闭
	, PreClose("preClose", "即将关闭")
    , GongXiaoShopId("GongXiaoShop", "供销的店铺Id")
	// 奥利派的合同图片
	, WithdrawImage("wImg", "奥利派提现签约图片地址")
    , ExpressReturnAddress("address", "默认退货地址")
    , IntegralMode("startIntegral", "1: close, 2: open, 3: open")
    , PromotionMode("marketingActivities", "open : true, close : false, decide if the promotion is activated")//
	, AreaQuery("A_Q", "open: true, close: false, default, close, open by SubStoreAreaController")//area query, @see IndexedItem.tags
    , subStoreWithdrawPeriod_1("subStoreWithdrawPeriod_1", "subStore模式下，导购每月可以发起账单提现的日期区间")
    , subStoreWithdrawPeriod_3("subStoreWithdrawPeriod_3", "subStore模式下，服务商每月可以发起账单提现的日期区间")
    , defaultFrontPageItemIds("defaultFrontPageItemIds", "未登录状态下，小程序首页展示的固定商品的ID")
    , ystUseScanWeiXin("ystUseScanWeiXin", "通联支付云商通，发起支付，是否使用 SCAN_WEIXIN 模式")
    , withdrawSingleLimit("withdrawSingleLimit", "门店提现申请单次限额，单位为分")
    , blockV2Item("blockV2Item", "是否屏蔽v2接口标准相关的商品数据，true屏蔽")
    , ystUseMiniProgram("ystUseMiniProgram", "通联支付云商通，发起支付，是否使用 WECHATPAY_MINIPROGRAM_CASHIER_VSP 模式")
	;
	String code;
	String desc;

	ShopExtra(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public String getDesc() {
        return Optional.of(desc).orElse("");
    }

    public String getCode() {
        return null == code ? name() : code;
    }

}
