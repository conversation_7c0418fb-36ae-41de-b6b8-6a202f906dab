package moonstone.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import moonstone.common.enums.UserRole;

@AllArgsConstructor
@Getter
public enum SalePattern {
    Common("commonShop", UserRole.SELLER),
    WeShop("we-distribution", UserRole.WE_DISTRIBUTOR),
    StoreProxy("ladderDistribution", UserRole.STORE_PROXY),
    SubStore("subStore", UserRole.SUB_STORE),
    CommunityOperation("communityOperation",UserRole.COMMUNITY_OPERATION);


    String code;
    UserRole role;

    static public SalePattern from(String code) {
        for (SalePattern value : SalePattern.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return Common;
    }

}
