package moonstone.common.enums;

/**
 * <AUTHOR>
 */
public enum UserRole {

    /**
     * admin 角色, 通常用户类型为: {@link UserType#ADMIN}
     */
    ADMIN,

    /**
     * 买家角色(个人), 属于用户类型 {@link UserType#NORMAL} 专有角色
     */
    BUYER,

    /**
     * 商家角色(个人), 属于用户类型 {@link UserType#NORMAL} 专有角色
     */
    SELLER,

    /**
     * 微分销店主角色(个人), 属于用户类型 {@link UserType#NORMAL} 专有角色
     */
    WE_DISTRIBUTOR,

    /**
     * 分销店主角色(个人), 属于用户类型 {@link UserType#NORMAL} 专有角色
     */
    STORE_PROXY,

    /**
     * 运营, 通常来说, 用户类型为: {@link UserType#OPERATOR}
     */
    OPERATOR,

    /**
     * 商家子账户, 属于用户类型 {@link UserType#SUB_ACCOUNT} 专有角色
     */
    SUB_SELLER,

    /**
     * sub store mode {@link UserType#NORMAL}
     */
    SUB_STORE,


    COMMUNITY_OPERATION
}
