package moonstone.common.enums;

import com.google.common.base.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/12/14.
 */
@AllArgsConstructor
@Getter
public enum OrderOutFrom {
    UNKNOW(null, null)               // 未知订单
    , WE_SHOP(1, "weShop")            //微分销商城
    , SUB_STORE(2, "subStore")      // 门店
    , EXCEL_IMPORT(3, "import")     // excel导入订单
    , LEVEL_Distribution(4, "levelDistribution")
    , COMMUNITY_OPERATION(5, "communityOperation")
    ;

    private final Integer orderOutFormId;

    private final String orderOutFormCode;


    public Integer Id() {
        return this.orderOutFormId;
    }

    public String Code() {
        return this.orderOutFormCode;
    }

    public static OrderOutFrom fromInt(Integer id) {
        for (OrderOutFrom orderFrom : OrderOutFrom.values()) {
            if (Objects.equal(orderFrom.orderOutFormId, id)) {
                return orderFrom;
            }
        }
        throw new IllegalArgumentException("unknown out form id: " + id);
    }

    public static OrderOutFrom fromCode(String code) {
        for (OrderOutFrom orderFrom : OrderOutFrom.values()) {
            if (Objects.equal(orderFrom.orderOutFormCode, code)) {
                return orderFrom;
            }
        }
        throw new IllegalArgumentException("unknown out form code: " + code);
    }
}
