package moonstone.common.enums;


import lombok.Getter;

@Getter
public enum OrderSourceEnum {
    DEFAULT(0,"原逻辑", false),
    GROUP_BUYING(1,"拼团购订单", true),
    SHARE_PURCHASE(2,"分享购订单", true),
    CUT_PRICE(3,"砍一刀订单",false),
    ;

    private Integer code;
    private String description;

    private boolean hasIndependentStock;

    OrderSourceEnum(Integer code, String description, Boolean hasIndependentStock) {
        this.code = code;
        this.description = description;
        this.hasIndependentStock = hasIndependentStock;
    }

    public static OrderSourceEnum findByCode(Integer orderSource) {
        for (OrderSourceEnum orderSourceEnum : OrderSourceEnum.values()){
            if(orderSourceEnum.code.equals(orderSource)){
                return orderSourceEnum;
            }
        }
        return null;
    }

    public static boolean isHasIndependentStock(Integer orderSource){
        OrderSourceEnum orderSourceEnum = findByCode(orderSource);
        if(orderSourceEnum == null){
            return false;
        }
        return orderSourceEnum.isHasIndependentStock();
    }
}
