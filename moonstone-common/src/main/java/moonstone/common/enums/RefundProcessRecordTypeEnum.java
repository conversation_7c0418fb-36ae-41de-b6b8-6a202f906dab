package moonstone.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RefundProcessRecordTypeEnum {

    /**
     * 买家发起申请
     */
    BUYER_APPLY("buyerApply", "买家发起申请退款,待卖家处理"),

    /**
     * 卖家发起申请
     */
    SELLER_APPLY("merchantApply", "卖家发起申请退款,待卖家处理"),

    /**
     * 系统发起申请
     */
    SYSTEM_APPLY("systemApply", "系统发起申请退款"),

    /**
     * 买家寄回商品，待商家收货
     */
    BUYER_SEND_EXPRESS("buyerSendExpress", "买家寄回商品，待商家收货"),

    /**
     * 京东云交易取消结果回执
     */
    JD_APPLY_REFUND_CALLBACK("jdApplyRefundCallback", "京东云交易取消结果回执"),

    /**
     * 代塔仓取消结果回执
     */
    DT_APPLY_REFUND_CALLBACK("dtApplyRefundCallback", "代塔仓取消结果回执"),

    /**
     * 京东云交易回执退款成功
     */
    JD_TRADE_REFUND_CALLBACK_SUCCESS("jdTradeRefundCallbackSuccess", "京东云交易回执退款成功"),

    /**
     * 卖家同意退款申请
     */
    SELLER_REFUND_APPLY_AGREE("sellerRefundApplyAgree", "商家同意退款申请"),

    /**
     * 卖家同意退货退款申请
     */
    SELLER_RETURN_APPLY_AGREE("sellerReturnApplyAgree", "商家同意退货退款申请，待买家寄回商品"),

    /**
     * 卖家拒绝退款申请
     */
    SELLER_REFUND_APPLY_REJECT("sellerRefundApplyReject", "商家拒绝退款申请"),

    /**
     * 卖家拒绝退货退款申请
     */
    SELLER_RETURN_APPLY_REJECT("sellerReturnApplyReject", "商家拒绝退货退款申请"),

    /**
     * 买家寄回商品
     */
    BUYER_RETURN_EXPRESS("buyerReturnExpress","买家寄回商品"),

    /**
     * 卖家上传物流信息
     */
    SELLER_UPLOAD_EXPRESS_INFO("sellerUploadExpressInfo","卖家上传物流信息"),

    /**
     * 系统退款成功
     */
    SYSTEM_REFUND_SUCCESS("systemRefundSuccess", "系统退款成功"),

    /**
     * 系统退款失败
     */
    SYSTEM_REFUND_FAIL("systemRefundFail", "系统退款失败"),

    /**
     * 售后关闭
     */
    SYSTEM_REFUND_CLOSE("systemRefundClose", "售后关闭"),
    ;
    private final String code;
    private final String desc;

    public static String getDesc(String code) {
        for (RefundProcessRecordTypeEnum processRecordTypeEnum : RefundProcessRecordTypeEnum.values()) {
            if (processRecordTypeEnum.getCode().equals(code)) {
                return processRecordTypeEnum.getDesc();
            }
        }
        return "";
    }
}

