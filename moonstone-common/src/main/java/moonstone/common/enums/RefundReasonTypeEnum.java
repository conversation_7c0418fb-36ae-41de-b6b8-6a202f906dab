package moonstone.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RefundReasonTypeEnum {

    DESCRIPTION_NOT_MATCH(8924, "商品与页面描述不符合"),
    OTHER_REASON(10878, "其他原因"),
    QUALITY_PROBLEM(10044, "质量问题"),
    ACTIVITY_FAIL(9999, "活动失败")
    ;


    private final Integer code;

    private final String desc;


    public static String getDesc(Integer code) {
        RefundReasonTypeEnum[] values = RefundReasonTypeEnum.values();
        for (RefundReasonTypeEnum refundReasonTypeEnum : values) {
            if (refundReasonTypeEnum.getCode().equals(code)) {
                return refundReasonTypeEnum.getDesc();
            }
        }
        return "";
    }
}
