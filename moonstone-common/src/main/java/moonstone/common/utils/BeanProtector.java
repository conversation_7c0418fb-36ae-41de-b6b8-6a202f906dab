package moonstone.common.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.aop.framework.ProxyFactory;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.CyclicBarrier;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * Bean保护器,并非完全线程安全,并没有提供完全的线程操作
 */
@Slf4j
public class BeanProtector<B> implements MethodInterceptor, AutoCloseable {
    final private B bean;
    public static final ThreadLocal<Map<String, Object>> threadLocal = ThreadLocal.withInitial(HashMap::new);
    protected List<String> protectWriteMethodName = Collections.singletonList("set");
    protected List<String> protectReadMethodName = Arrays.asList("get", "is");
    protected boolean allowMethodWarn = true;
    protected ProxyFactory proxyFactory;

    public BeanProtector(B bean, ProxyFactory proxyFactory) {
        this.proxyFactory = proxyFactory;
        this.bean = bean;
        proxyFactory.setTarget(bean);
        proxyFactory.addAdvice(this);
    }

    public BeanProtector(B bean) {
        this(bean, new ProxyFactory());
    }

    /**
     * 获取是否被锁定用于写入数据
     */
    public boolean isLockForWriteOnThisThread() {
        synchronized (this) {
            return threadLocal.get().getOrDefault("lockForWrite", "Nope").equals("true");
        }
    }

    /**
     * 锁住全体数据,注意需要写入数据时应该使用try-catch,不然需要手动调用close
     */
    public void lockForRealWriteOnThisThread() {
        synchronized (this) {
            threadLocal.get().put("lockForWrite", "true");
        }
    }

    public B getProtector() {
        return (B) proxyFactory.getProxy();
        //return (T) Proxy.newProxyInstance(bean.getClass().getClassLoader(), new Class[]{bean.getClass(), BeanProtector.class}, this);
    }

    /**
     * 线程安全的操作内部数据,注意性能不要进行重IO操作
     *
     * @param peekFunc 吸收器
     */
    public void peekAtomic(Consumer<B> peekFunc) {
        try {
            synchronized (this) {
                threadLocal.get().put("lockForWrite", "true");
                peekFunc.accept(bean);
            }
        } finally {
            threadLocal.get().remove("lockForWrite");
        }
    }

    /**
     * 原子性获取内部数据,注意不能进行重IO操作
     *
     * @param brFunction 数据转换函数
     */
    public <R> R getByAtomic(Function<B, R> brFunction) {
        try {
            synchronized (this) {
                threadLocal.get().put("lockForWrite", "true");
                return brFunction.apply(bean);
            }
        } finally {
            threadLocal.get().remove("lockForWrite");
        }
    }

    protected Object invoke(Object bean, Method method, Object[] args) throws Throwable {
        if (bean == null) bean = this.bean;
        boolean isGetterOrSetterMethod = false;
        if (method.getName().equals("toString") && method.getParameters().length == 0 && method.getReturnType().equals(String.class))
            return String.format("BeanProtectorShell#%s@%s", bean.getClass().getName(), this.getClass().getName());
        if (isLockForWriteOnThisThread()) {
            synchronized (this) {
                return method.invoke(bean, args);
            }
        } else {
            for (String prefix : protectReadMethodName) {
                if (method.getName().startsWith(prefix)) {
                    if (!isGetterOrSetterMethod)
                        isGetterOrSetterMethod = true;
                    if (method.getParameters().length == 0)
                        return threadLocal.get().getOrDefault(method.getName().substring(prefix.length()), method.invoke(bean, args));
                    else if (allowMethodWarn)
                        log.warn("{} should use the protect method but the define not suit for this method:[{}@{}],so we used the real method", LogUtil.getClassMethodName(), method.getName(), bean.getClass().getName());
                }
            }
            for (String namePrefix : protectWriteMethodName) {
                if (method.getName().startsWith(namePrefix)) {
                    if (!isGetterOrSetterMethod)
                        isGetterOrSetterMethod = true;
                    if (method.getParameters().length == 1) {
                        threadLocal.get().put(method.getName().substring(namePrefix.length()), args[0]);
                        return null;
                    } else if (allowMethodWarn)
                        log.warn("{} should use the protect method but the define not suit for this method:[{}@{}],so we used the real method", LogUtil.getClassMethodName(), method.getName(), bean.getClass().getName());
                }
            }
            if (!isLockForWriteOnThisThread() && isGetterOrSetterMethod) {
                log.error("{} fall back to use the real METHOD:{}@{}", LogUtil.getClassMethodName(), method.getName(), bean.getClass().getName());
            }
            return method.invoke(bean, args);
        }
    }

    public static void main(String[] args) throws Exception {
        @Data
        class AlipayToken {
            String account;
        }
        BeanProtector<AlipayToken> beanProtector = new BeanProtector(new AlipayToken());
        AlipayToken alipayToken = beanProtector.getProtector();
        alipayToken.setAccount("ash");
        System.out.println(alipayToken.getAccount().endsWith("ash"));
        System.out.println(alipayToken.getAccount());
        CyclicBarrier barrier = new CyclicBarrier(2);
        new Thread(() -> {
            System.out.println("[SIDE-THREAD] test: thread write and read");
            System.out.println(alipayToken.getAccount() == null);
            alipayToken.setAccount("Tuck");
            try {
                barrier.await();
            } catch (Exception ex) {
            }
        }).start();
        barrier.await();
        System.out.println("[MAIN-THREAD] test:verify the value");
        System.out.println(alipayToken.getAccount().endsWith("ash"));
        System.out.println(alipayToken.getAccount());
        barrier.reset();
        new Thread(() -> {
            System.out.println("[WRITE-THREAD] test:reRead and try-write");
            beanProtector.lockForRealWriteOnThisThread();
            System.out.println(alipayToken.getAccount() == null);
            alipayToken.setAccount("Tuck");
            System.out.println("Tuck".equals(alipayToken.getAccount()));
            try {
                barrier.await();
            } catch (Exception ex) {
            }
        }).start();
        barrier.await();
        System.out.println("[MAIN-THREAD] test:write");
        // 由于该线程本身就已经有数据了
        System.out.println(!"Tuck".endsWith(alipayToken.getAccount()));
        alipayToken.setAccount(null);
        System.out.println(String.format("after set null:%s", alipayToken.getAccount()));
        barrier.reset();
        new Thread(() -> {
            System.out.println("[SIDE-THREAD] test: thread write and read");
            System.out.println(!(alipayToken.getAccount() == null));
            System.out.println(String.format("real value:%s", alipayToken.getAccount()));
            try {
                barrier.await();
            } catch (Exception ex) {
            }
        }).start();
        barrier.await();
    }

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        return invoke(invocation.getThis(), invocation.getMethod(), invocation.getArguments());
    }

    @Override
    public void close() throws Exception {
        threadLocal.get().remove("lockForWrite");
    }
}
