package moonstone.common.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * author：书生
 */
@Document(collection = "baseAddress")
@Data
public class BaseAddress {
    /**
     * id
     */
    @Id
    private String id;
    /**
     * 区域编码
     */
    @Field("areaCode")
    private String areaCode;

    /**
     * 区域级别
     */
    @Field("level")
    private Integer level;

    /**
     * 区域名称
     */
    @Field("areaName")
    private String areaName;

    /**
     * 城市编码
     */
    @Field("cityCode")
    private String cityCode;

    /**
     * 父级id
     */
    @Field("parentId")
    private String parentId;

}
