package moonstone.common.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
@Document(collection = "sku_history")
public class SkuHistory {


	@Id
	private String id;

	/**
	 * 店铺id
	 */
	@Field(value = "shop_id")
	private Long shopId;

	/**
	 * skuId
	 */
	@Field("sku_id")
	private Long skuId;

	/**
	 * 操作类型
	 */
	@Field("operation_type")
	private String operationType;

	/**
	 * 变更前数据
	 */
	@Field("before_data")
	private SkuHistoryData beforeData = new SkuHistoryData();

	/**
	 * 当前数据
	 */
	@Field("current_data")
	private SkuHistoryData currentData = new SkuHistoryData();

	/**
	 * 版本号
	 */
	@Field("version")
	private Integer version;

	/**
	 * 创建时间
	 */
	@Field("created_time")
	private Long createdTime;

}
