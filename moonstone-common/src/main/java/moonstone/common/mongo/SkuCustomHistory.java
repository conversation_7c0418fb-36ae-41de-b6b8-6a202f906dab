package moonstone.common.mongo;

import lombok.Data;

/**
 * author：书生
 */
@Data
public class SkuCustomHistory {

	/**
	 * 关联的SKU的ID
	 */
	private Long skuId;

	/**
	 * 海关HS编码
	 */
	private String hsCode;

	/**
	 * 海关信息-原产地ID
	 */
	private Long customOriginId;

	/**
	 * 海关信息-原产地
	 */
	private String customOrigin;

	/**
	 * 税金承担方（1:买家承担，2:卖家承担）
	 */
	private Integer customTaxHolder;

	/**
	 * 所在地-省ID
	 */
	private Long provinceId;

	/**
	 * 所在地-省
	 */
	private String province;

	/**
	 * 所在地-市ID
	 */
	private Long cityId;

	/**
	 * 所在地-市
	 */
	private String city;

	/**
	 * 海关信息（镇/区）
	 */
	private String town;
}
