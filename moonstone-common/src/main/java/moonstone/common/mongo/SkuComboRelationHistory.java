package moonstone.common.mongo;

import lombok.Data;

/**
 * author：书生
 */
@Data
public class SkuComboRelationHistory {

	/**
	 * 店铺ID
	 */
	private Long shopId;

	/**
	 * SKU ID
	 */
	private Long skuId;

	/**
	 * 组合商品ID（组合内的商品ID）
	 */
	private Long comboItemId;

	/**
	 * 组合商品名称（组合内的商品名称）
	 */
	private String comboItemName;

	/**
	 * 组合SKU ID（组合内的SKU ID）
	 */
	private Long comboSkuId;

	/**
	 * 组合SKU数量（组合内该SKU的数量）
	 */
	private Integer comboSkuQuantity;

	/**
	 * 组合品下的单品海关信息
	 */
	private SkuCustomHistory comboSkuCustomHistory;


}
