package moonstone.common.mongo;

import lombok.Data;

import java.util.List;

/**
 * author：书生
 */
@Data
public class SkuHistoryData {

	/**
	 * SKU编码（标准库存单位编码）
	 */
	private String skuCode;

	/**
	 * 商品ID
	 */
	private Long itemId;

	/**
	 * 保税类型：1-保税，0-完税
	 */
	private Integer type;

	/**
	 * 商品型号/款式
	 */
	private String specification;

	/**
	 * 外部系统SKU编号
	 */
	private String outerSkuId;

	/**
	 * 商品图片URL
	 */
	private String image;

	/**
	 * SKU名称
	 */
	private String name;

	/**
	 * SKU其他价格的JSON格式数据
	 */
	private String extraPriceJson;

	/**
	 * 实际售卖最低价格（单位：分）
	 */
	private Integer price;

	/**
	 * SKU属性的JSON键值对
	 */
	private String attrsJson;

	/**
	 * 库存类型：0-不分仓，1-分仓（从商品表冗余）
	 */
	private Integer stockType;

	/**
	 * 初始库存数量
	 */
	private Integer initialStockQuantity;

	/**
	 * 当前库存数量
	 */
	private Integer stockQuantity;

	/**
	 * 销售数量
	 */
	private Integer saleQuantity;

	/**
	 * SKU扩展信息
	 */
	private String extra;

	/**
	 * 标签信息的JSON表示
	 */
	private String tagsJson;

	/**
	 * SKU缩略图URL
	 */
	private String thumbnail;


	/**
	 * sku海关信息
	 */
	private SkuCustomHistory skuCustomHistory;

	/**
	 * sku扩展信息
	 */
	private List<SkuIntermediateInfoHistory> skuIntermediateInfoHistories;

	/**
	 * sku规格信息
	 */
	private List<SkusSpecificationRelevanceHistory> skusSpecificationRelevanceHistories;

	/**
	 * 组合商品信息
	 */
	private List<SkuComboRelationHistory> skuComboRelationHistories;


}
