package moonstone.common.mongo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * author：书生
 */
@Data
public class SkuIntermediateInfoHistory {

	/**
	 * 第三方系统ID
	 */
	private Long thirdId;

	/**
	 * 一级分销佣金比例（单位取决于isThousandMode）
	 */
	private Long firstRate;

	/**
	 * 一级分销佣金金额（单位：分）
	 */
	private Long firstFee;

	/**
	 * 二级分销佣金比例（单位取决于isThousandMode）
	 */
	private Long secondRate;

	/**
	 * 二级分销佣金金额（单位：分）
	 */
	private Long secondFee;

	/**
	 * 拉新奖励佣金（单位：分）
	 */
	private Long commission;

	/**
	 * 首购奖励佣金（单位：分）
	 */
	private Long firstCommission;

	/**
	 * 单品佣金开关：0-关闭，1-开启
	 */
	private Integer isCommission;

	/**
	 * 配置类型
	 */
	private Integer type;

	/**
	 * 状态：0-禁用，1-启用
	 */
	private Integer status;

	/**
	 * 扩展信息（JSON格式）
	 */
	private String extraJson;

	/**
	 * 是否使用千分制：0-百分比，1-千分比
	 */
	private Integer isThousandMode;

	/**
	 * 服务商佣金金额（单位：分）
	 */
	private Long serviceProviderFee;

	/**
	 * 服务商佣金比例（单位取决于isThousandMode）
	 */
	private Long serviceProviderRate;

	/**
	 * 适用类型：1-通常配置，2-活动配置
	 */
	private Integer matchingType;

	/**
	 * 活动配置开始时间
	 */
	private Date matchingStartTime;

	/**
	 * 活动配置结束时间
	 */
	private Date matchingEndTime;

	/**
	 * 是否福豆奖励：0-否，1-是
	 */
	private Integer isCashGift;

	/**
	 * 福豆最大抵扣金额（保留2位小数）
	 */
	private BigDecimal maxDeduction;

	/**
	 * 佣金单位：1-百分比，2-固定金额（元）
	 */
	private Integer unit;

	/**
	 * 福豆使用开始时间（时间戳）
	 */
	private Long cashGiftUseTimeStart;

	/**
	 * 福豆使用结束时间（时间戳）
	 */
	private Long cashGiftUseTimeEnd;
}
