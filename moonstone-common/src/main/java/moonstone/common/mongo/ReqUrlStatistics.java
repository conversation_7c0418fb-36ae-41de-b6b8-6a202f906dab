package moonstone.common.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
@Document("url_statistics")
public class ReqUrlStatistics {

	/**
	 * id
	 */
	@Id
	private String id;
	/**
	 * 服务地址
	 */
	@Field("serviceName")
	private String serviceName;

	/**
	 * 调用地址
	 */
	@Field("url")
	private String url;

	/**
	 * 调用次数
	 */
	@Field("count")
	private Long count;


}
