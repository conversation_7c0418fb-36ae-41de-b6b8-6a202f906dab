package moonstone.common.mongo;

import lombok.Data;

/**
 * author：书生
 */
@Data
public class SkusSpecificationRelevanceHistory {

	/**
	 * 店铺ID
	 */
	private Long shopId;

	/**
	 * SKU ID
	 */
	private Long skuId;

	/**
	 * 规格详情ID
	 */
	private Long specDetailId;

	/**
	 * 规格名称
	 */
	private String specName;

	/**
	 * 规格详情前端生成的ID
	 */
	private String specDetailFrontId;

	/**
	 * 规格的顺序
	 */
	private Integer sequence;

}