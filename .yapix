# é¡¹ç®å°å
yapiProjectId=758
#yapiProjectId=759

# ç±»çº§å«è·¯å¾
apiClassAnnotationPath = org.springframework.web.bind.annotation.RequestMapping@value
# æ¥å£åç»
apiClassAnnotationGroup = io.swagger.annotations.ApiModel@value
# å·ä½apiçæ³¨è§£
apiMethodAnnotation = org.springframework.web.bind.annotation.PostMapping
# æ¥å£å­è·¯å¾
apiMethodAnnotationPath = org.springframework.web.bind.annotation.PostMapping@value
# æ¥å£æè¿°
apiMethodAnnotationDesc = io.swagger.annotations.ApiOperation@value
# æ¥å£è¯·æ±æ¹æ³ãä¸ºç©ºé»è®¤post
apiMethodAnnotationHttpMethod =
# æ¥å£è¯·æ±ç±»å
defaultRequestBodyType = application/json