package moonstone.web.admin.customs;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.danding.common.net.Response;
import com.danding.mercury.pay.sdk.MercuryPayClient;
import com.danding.mercury.pay.sdk.MercuryPayResult;
import com.danding.mercury.pay.sdk.customs.notify.MercuryPayCustomsDeclareNotifyResult;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.model.Either;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.OutSystemIdProvider;
import moonstone.common.utils.Translate;
import moonstone.order.dto.fsm.OrderExceptionEnum;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.enu.PaymentExtraIndexEnum;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.enu.ShopOrderIdentityErrorEnum;
import moonstone.order.model.OrderBase;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.*;
import moonstone.user.model.UserCertification;
import moonstone.user.service.UserCertificationReadService;
import moonstone.user.service.UserCertificationWriteService;
import moonstone.web.core.config.FunctionSwitch;
import moonstone.web.core.events.customs.CustomsDeclareFailEvent;
import moonstone.web.core.events.customs.CustomsDeclareSuccessEvent;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Predicate;

/**
 * 海关相关控制层
 */
@SuppressWarnings("ALL")
@RestController
@RequestMapping("/api/customs")
@Slf4j
public class Customs {

    @Value("${mercury.pay.host}")
    private String mercuryPayHost;

    @Value("${mercury.pay.appCode}")
    private String mercuryPayAppCode;

    @Value("${mercury.pay.merchantCode}")
    private String mercuryPayMerchantCode;

    @Autowired
    private UserCertificationWriteService userCertificationWriteService;

    @Autowired
    private UserCertificationReadService userCertificationReadService;

    @Autowired
    private FunctionSwitch functionSwitch;

    @Autowired
    private SkuOrderReadService skuOrderReadService;

    @Autowired
    private ShopOrderReadService shopOrderReadService;

    @Autowired
    private OrderWriteService orderWriteService;

    @Autowired
    private PaymentReadService paymentReadService;

    @Autowired
    private PaymentWriteService paymentWriteService;

    @Autowired
    private OutSystemIdProvider outSystemIdProvider;

    @Autowired
    private RocketMQProducer rocketMQProducer;

    // 恶心...
    private static final List<String> identityErrorHint = List.of("证件号码校验异常", "身份证", "支付人姓名和证件号不匹配", "身份验证返回异常");

    /*
     * 支付单推送异步通知接口
     */
    @RequestMapping("/payment/declare/notify")
    public String paymentDeclareNotify(HttpServletRequest request) {
        try {
            log.debug("{} request:{}", LogUtil.getClassMethodName(), request.getParameterMap());
            MercuryPayClient client = new MercuryPayClient(mercuryPayHost + "/api",
                    mercuryPayAppCode, mercuryPayMerchantCode);
            Response<? extends MercuryPayResult> response = client.receive(request, MercuryPayCustomsDeclareNotifyResult.class);
            log.info("[op: paymentDeclareNotify] response={}", JSON.toJSONString(response));
            if (!response.isSuccess()) {
                log.error("错误码={},错误描述={}", response.getError(), response.getErrorMessage());
                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("订单申报回调", Translate.of("订单回调解析失败, 请检查日志 [%s]", LocalDateTime.now()), EmailReceiverGroup.DEVELOPER));
                return "SUCCESS";
            }
            MercuryPayCustomsDeclareNotifyResult result = (MercuryPayCustomsDeclareNotifyResult) response.getResult();
            Payment payment = Optional.ofNullable(paymentReadService.findByOutId(result.getOrderNo()).getResult())
                    .orElseThrow(() -> new RuntimeException(Translate.of("[op: 未找到对应申报支付订单] outId=%s", result.getOrderNo())));
            payment.setRecpCode(result.getRecpCode());
            payment.setRecpName(result.getRecpName());
            // 设置验核机构
            payment.setVerDept(result.getVerDept());
            payment.setPayTransactionId(result.getPayTransactionId());
            payment.setExtra(Optional.ofNullable(payment.getExtra()).orElseGet(HashMap::new));

            Long shopOrderId = decodeOrderId(result).orElseGet(() -> Long.valueOf(result.getDeclareOrderNo()));
            ShopOrder shopOrder = shopOrderReadService.findById(shopOrderId).getResult();
            log.debug("{} shopOrderId:{} shopOrder-status:{}", LogUtil.getClassMethodName(), shopOrderId, shopOrder.getStatus());
            // 测试环境下对测试订单处理
            if (result.getDeclareOrderNo().startsWith("DT") && System.currentTimeMillis() % 2 == 0) {
                log.debug("{} declaredNo:{} set to identity-failed", LogUtil.getClassMethodName(), result.getDeclareOrderNo());
                EventSender.sendApplicationEvent(new CustomsDeclareFailEvent(payment.getId(), shopOrder.getId(), true, "[测试环境]身份证不匹配"));
            }

            Map<String, String> shopOrderExtra = CollectionUtils.isEmpty(shopOrder.getExtra()) ? new HashMap<>() : shopOrder.getExtra();
            if (result.getSuccess() && result.getIdentityCheck()) {
                syncBuyerInformation(shopOrder.getBuyerId(), shopOrderExtra);
                declareSuccess(shopOrderId, shopOrderExtra, payment, result);
                return "SUCCESS";
            }
            declareFail(shopOrderId, shopOrderExtra, payment, result);
            return "SUCCESS";
        } catch (Exception ex) {
            log.error("{} react for declare call back fail", LogUtil.getClassMethodName(), ex);
            return "FAIL";
        }
    }

    private Either<Long> decodeOrderId(MercuryPayCustomsDeclareNotifyResult result) {
        try {
            return Either.ok(outSystemIdProvider.decode(result.getDeclareOrderNo()).getId());
        } catch (Exception ex) {
            log.error("{} declareOrderNo:{} result:{}", LogUtil.getClassMethodName(), result.getDeclareOrderNo(), JSON.toJSONString(result), ex);
            return Either.error(ex);
        }
    }

    /**
     * 同步支付人信息
     *
     * @param buyerId        买家id
     * @param shopOrderExtra 订单额外信息
     */
    private void syncBuyerInformation(Long buyerId, Map<String, String> shopOrderExtra) {
        String payerName = shopOrderExtra.get(ShopOrderExtra.payerName.name());
        String payerNo = shopOrderExtra.get(ShopOrderExtra.payerNo.name());
        if (StringUtils.isBlank(payerName) || StringUtils.isBlank(payerNo)) {
            return;
        }

        Optional<UserCertification> userCertificationOptional = userCertificationReadService.findDefaultByUserId(buyerId).getResult();
        if (userCertificationOptional == null || !userCertificationOptional.isPresent()) {
            return;
        }
        UserCertification userCertification = userCertificationOptional.get();
        if (userCertification.getPaperName().equals(payerName)
                && userCertification.getPaperNo().equals(payerNo)) {
            return;
        }
        userCertification.setPaperName(payerName);
        userCertification.setPaperNo(payerNo);
        userCertificationWriteService.update(userCertification);
    }

    /**
     * 申报成功, 只有当所有申报订单全部申报通过时, 才会进入下一个阶段
     *
     * @param shopOrderId    订单单号
     * @param shopOrderExtra 订单额外信息
     * @param payment        支付单
     * @param result         回调结果
     */
    private void declareSuccess(Long shopOrderId, Map<String, String> shopOrderExtra, Payment payment, MercuryPayCustomsDeclareNotifyResult result) {
        shopOrderExtra.put(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.FALSE.getCode());
        // 记录子订单流水号, 如果我们系统内有了 那就拒绝使用支付系统提供的 因为可能是为了重复推单而进行的魔改
        shopOrderExtra.put(ShopOrderExtra.subBankNo.name(), result.getSubBankNo());
        shopOrderExtra.put(ShopOrderExtra.declareOk.name(), "t");
        log.debug("{} save the extra-data [{}] for orderId[{}], action [{}]", LogUtil.getClassMethodName(), shopOrderExtra, shopOrderId
                , JSON.toJSONString(orderWriteService.updateOrderExtra(shopOrderId, OrderLevel.SHOP, shopOrderExtra)));
        if (payment.getPushStatus() == PaymentPushStatus.DECLARE_SUCCESS.getValue() || payment.getPushStatus() == PaymentPushStatus.PUSH_SUCCESS.getValue()) {
            return;
        }
        // 判断 订单是否全部完成推送
        List<? extends OrderBase> orderBaseList = Optional.ofNullable(paymentReadService.findOrdersByPaymentId(payment.getId()).getResult())
                .orElseThrow(() -> new RuntimeException(Translate.of("支付单[%s] 未能查找到对应的订单信息", payment.getId())));
        // 申报时只使用ShopOrder级别订单
        boolean declareOk = orderBaseList.stream().filter(orderBase -> orderBase instanceof ShopOrder).filter(orderBase -> ((ShopOrder) orderBase).getStatus().equals(OrderStatus.PAID.getValue()))
                .map(OrderBase::getExtra).map(extra -> Optional.ofNullable(extra).orElseGet(HashMap::new).getOrDefault(ShopOrderExtra.declareOk.name(), "f"))
                .allMatch(Predicate.isEqual("t"));
        if (!declareOk) {
            Long orderId = orderBaseList.stream().filter(orderBase -> orderBase instanceof ShopOrder)
                    .filter(order -> Optional.ofNullable(order.getExtra()).orElseGet(HashMap::new).getOrDefault(ShopOrderExtra.declareOk.name(), "f").equals("f"))
                    .map(OrderBase::getId).findFirst().orElseThrow(() -> new RuntimeException(Translate.of("支付单[%s]异常状态, 订单没有申报完成, 但是查找未完成订单Id失败", payment.getId())));
            log.info("{} payment[{}] is waiting for order[{}] declare", LogUtil.getClassMethodName(), payment.getId(), orderId);
            return;
        }
        // 目前并没有依赖ShopOrderId进行处理订单信息
        EventSender.publish(new CustomsDeclareSuccessEvent(payment.getId(), shopOrderId, false, result.toString()));
        payment.setPushStatus(PaymentPushStatus.DECLARE_SUCCESS.getValue());
        boolean paymentUpdateResult = Objects.equals(true, paymentWriteService.update(payment).getResult());
        orderWriteService.updateOrderPushException(shopOrderId, OrderExceptionEnum.CLEAR_ERROR.getCode(), OrderExceptionEnum.CLEAR_ERROR.getMessage());
        log.info("{} payment[{}] declare pass, update result [{}]", LogUtil.getClassMethodName(), payment.getId(), paymentUpdateResult);
    }

    /**
     * 处理申报失败
     *
     * @param shopOrderId    订单Id
     * @param shopOrderExtra 订单额外信息
     * @param payment        支付单
     * @param result         申报结果
     */
    private void declareFail(Long shopOrderId, Map<String, String> shopOrderExtra, Payment payment, MercuryPayCustomsDeclareNotifyResult result) {
        String errorMessage = StringUtils.isNotBlank(result.getErrorMsg()) ? result.getErrorMsg() : StringUtils.EMPTY;
        JSONObject msg = new JSONObject();
        msg.set("orderId", shopOrderId);
        msg.set("exceptionMessage", "支付单回调异常：" + errorMessage);
        if (identityErrorHint.stream().anyMatch(hint -> errorMessage.contains(hint))) {
            shopOrderExtra.put(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.TRUE.getCode());
            orderWriteService.updateSkuOrderPushStatusByOrderId(shopOrderId, SkuOrderPushStatus.IDENTITY_CHECK.value(), SkuOrderPushStatus.WAIT_PAYMENT_PUSH_PASS.value());
            msg.set("exceptionType", OrderExceptionEnum.PAYEE_INFO_ERROR.getCode());
            EventSender.sendApplicationEvent(new CustomsDeclareFailEvent(payment.getId(), shopOrderId, true, "身份证校验异常"));
        } else {
            msg.set("exceptionType", OrderExceptionEnum.SYSTEM_ERROR.getCode());
            EventSender.sendApplicationEvent(new CustomsDeclareFailEvent(payment.getId(), shopOrderId, true, errorMessage));
        }
        rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_EXCEPTION_RECORD_TAG, msg.toString());
        log.info("发送支付单回调失败，订单异常消息 {}", msg);
        shopOrderExtra.remove(ShopOrderExtra.declareOk.name());
        payment.getExtra().put(PaymentExtraIndexEnum.customsDeclareErrorMsg.getCode(), errorMessage);
        payment.setExtra(payment.getExtra());
        payment.setPushStatus(PaymentPushStatus.DECLARE_FAIL.getValue());
        log.error("支付单申报失败 店铺订单编号={},错误描述={}", result.getDeclareOrderNo(), errorMessage);
        boolean paymentUpdateResult = Objects.equals(true, paymentWriteService.update(payment).getResult());
        boolean shopOrderUpdateResult = Objects.equals(true, orderWriteService.updateOrderExtra(shopOrderId, OrderLevel.SHOP, shopOrderExtra).getResult());

        log.debug("{} save the extra-data [{}] for orderId[{}], success [paymentUpdate => {}, shopOrderUpdate => {}]", LogUtil.getClassMethodName(), shopOrderExtra, shopOrderId
                , paymentUpdateResult, shopOrderUpdateResult);
    }
}
