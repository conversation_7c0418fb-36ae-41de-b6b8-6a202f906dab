package moonstone.web.admin.express;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.express.model.ExpressCompany;
import moonstone.express.service.ExpressCompanyReadService;
import moonstone.order.api.ExpressTrackInfo;
import moonstone.order.model.SkuOrder;
import moonstone.web.core.express.service.OrderExpressDetailCenter;
import moonstone.web.core.express.service.impl.OrderExpress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Date: 7/8/16
 * Time: 7:38 PM
 * Author: 2016年 <a href="mailto:<EMAIL>">张成栋</a>
 */
@Slf4j
@RestController
public class Expresses {

    @Autowired
    ExpressCompanyReadService expressCompanyReadService;
    @Autowired
    OrderExpressDetailCenter orderExpressDetailCenter;

    @Autowired
    OrderExpress orderExpress;

    @RequestMapping(value = "/api/express", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<Paging<ExpressCompany>> findBy(@RequestParam(value = "pageNo",required = false) Integer pageNo,
                                                   @RequestParam(value = "size",required = false) Integer size,
                                                   @RequestParam(value = "name",required = false) String name,
                                                   @RequestParam(value = "code",required = false) String code,
                                                   @RequestParam(value = "status",required = false) Integer status) {
        return expressCompanyReadService.paging(pageNo, size, name, code, status);
    }

    /**
     * 查询订单物流信息
     *
     * @param orderId   订单id
     * @param orderType 订单类型
     * @return 订单物流信息
     */
    @RequestMapping(value = "/api/order/express", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<? extends ExpressTrackInfo> orderExpress(@RequestParam("orderId") Long orderId,
                                                         @RequestParam("orderType") Integer orderType) {

        return orderExpressDetailCenter.queryOrderExpressDetail(orderId, orderType);
    }

    @RequestMapping(value = "/api/shipment/{id}/sku-orders", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SkuOrder> findSkuOrdersForSeller(@PathVariable("id") Long shipmentId) {
        return orderExpress.findSkuOrders(shipmentId);
    }
}
