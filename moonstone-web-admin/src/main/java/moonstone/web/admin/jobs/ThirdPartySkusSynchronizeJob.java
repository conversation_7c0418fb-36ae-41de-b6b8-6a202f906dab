package moonstone.web.admin.jobs;

import com.google.common.base.Stopwatch;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.utils.LogUtil;
import moonstone.thirdParty.model.ThirdSystemAID;
import moonstone.thirdParty.service.ThirdPartyJobService;
import moonstone.web.admin.config.JobSwitch;
import moonstone.web.core.component.cache.ThirdPartyUserShopCache;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@ConditionalOnProperty(value = "enable.thirdParty.synchronize", havingValue = "true", matchIfMissing = true)
@RestController
@Slf4j
public class ThirdPartySkusSynchronizeJob {
    @RpcConsumer
    public ThirdPartyJobService thirdPartyJobService;
    @Autowired
    private ThirdPartyUserShopCache thirdPartyUserShopCache;

    @Autowired
    private JobSwitch jobSwitch;

    private static final DateTimeFormatter DFT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(value = "/api/item/thirdParty/synchronizeShop", produces = MediaType.APPLICATION_JSON_VALUE)
    public void synchronizeShop(Long shopId) {
        List<Throwable> throwableList = new ArrayList<>();
        for (ThirdPartySystem system : ThirdPartySystem.values()) {
            if (system == ThirdPartySystem.ALL || system == ThirdPartySystem.Unknown) {
                continue;
            }
            thirdPartyUserShopCache.findBy(new ThirdSystemAID(system.Id(), shopId)).ifPresent(thirdPartyUserShop -> {
                try {
                    thirdPartyJobService.synchronize(system, thirdPartyUserShop);
                } catch (Exception e) {
                    log.error("{} fail to update", LogUtil.getClassMethodName(), e);
                    throwableList.add(e);
                }
            });
        }
        for (Throwable throwable : throwableList) {
            throw new RuntimeException(throwable);
        }
    }

    @RequestMapping(value = "/api/item/thirdParty/synchronizeJob", produces = MediaType.APPLICATION_JSON_VALUE)
//    @Scheduled(cron = "0 0 2 * * ?")
    public void synchronizeJob() {  //每天凌晨两点执行一次
        if (jobSwitch.isOpenPayInfoPush()) {
            log.info("[CRON-JOB] synchronize thirdPartySku begin {}", DFT.print(DateTime.now()));
            Stopwatch stopwatch = Stopwatch.createStarted();

            thirdPartyJobService.synchronizeJob();

            stopwatch.stop();
            log.info("[CRON-JOB] synchronize thirdPartySku done at {} cost {} ms", DFT.print(DateTime.now()), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        } else {
            log.info("synchronize thirdPartySku switch is close");
        }
    }
}
