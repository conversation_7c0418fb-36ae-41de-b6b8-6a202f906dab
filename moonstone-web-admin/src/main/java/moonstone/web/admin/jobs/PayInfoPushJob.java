package moonstone.web.admin.jobs;

import cn.hutool.json.JSONUtil;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.UUID;
import moonstone.event.*;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.enu.PaymentDeclareErrorMessageEnum;
import moonstone.order.enu.PaymentExtraIndexEnum;
import moonstone.order.model.Payment;
import moonstone.order.service.PaymentReadService;
import moonstone.web.admin.config.JobSwitch;
import moonstone.web.core.order.service.OrderDeclareService;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.MDC;
import org.slf4j.helpers.LoggerConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Stream;

@Component
@RestController
@Slf4j
public class PayInfoPushJob {
    private static final DateTimeFormatter DFT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
    // 主等待队列(小心队列爆炸)
    private final BlockingDeque<Payment> paidPaymentCacheQueue = new LinkedBlockingDeque<>();
    @Autowired
    private PaymentReadService paymentReadService;
    @Autowired
    private OrderDeclareService orderDeclareService;
    @Autowired
    private JobSwitch jobSwitch;
    // 支付单队列抛弃大小
    @Value("${payInfo.queue.limit:5000}")
    private Integer queueMax;
    // 次要队列
    private final Map<String, BlockingDeque<Long>> paymentOtherChannelQueue = new ConcurrentHashMap<>();

    AtomicInteger pushInfoThreadPoolCount = new AtomicInteger(0);
    ThreadFactory pushInfoThreadFactory = runAble -> {
        Thread thread = new Thread(runAble);
        thread.setName("PayInfo-" + pushInfoThreadPoolCount.addAndGet(1));
        return thread;
    };
    private ExecutorService executorPool = Executors.newCachedThreadPool(pushInfoThreadFactory);

    private boolean suspend = false;

    @PostConstruct
    void init() {
        paymentOtherChannelQueue.put("_ERROR", new LinkedBlockingDeque<>());
        paymentOtherChannelQueue.put("_DELAY", new LinkedBlockingDeque<>());
    }

    @GetMapping("/api/payment/restartJob")
    @EventListener(ContextRefreshedEvent.class)
    public void contextRefreshedOver(ContextRefreshedEvent contextRefreshedEvent) {
        executorPool.shutdown();
        executorPool.shutdownNow();
        pushInfoThreadPoolCount.set(0);
        executorPool = Executors.newCachedThreadPool(pushInfoThreadFactory);
        executorPool.execute(() -> pressure(executorPool, paidPaymentCacheQueue, this::flow));
        executorPool.execute(() -> pressure(executorPool, paidPaymentCacheQueue, this::flow));
        executorPool.execute(() -> pressure(executorPool, paidPaymentCacheQueue, this::flow));
        executorPool.execute(() -> pressure(executorPool, paymentOtherChannelQueue.get("_DELAY"), this::reflux));
        executorPool.execute(() -> pressure(executorPool, paymentOtherChannelQueue.get("_ERROR"), this::reflux));
    }

    @PreDestroy
    void shutdown() {
        if (executorPool.isTerminated())
            return;
        executorPool.shutdown();
        executorPool.shutdownNow();
    }

    @EventListener(PaymentDeclareInlineEvent.class)
    public void paymentDeclareInLine(PaymentDeclareInlineEvent paymentDeclareInlineEvent) {
        paidPaymentCacheQueue.add(paymentReadService.findById(paymentDeclareInlineEvent.getPaymentId()).getResult());
    }

    @EventListener(PaymentPaidEvent.class)
    public void addPaymentIntoQueue(PaymentPaidEvent paymentPaidEvent) {
        if (paidPaymentCacheQueue.size() > queueMax) {
            return;
        }
        Payment payment = paymentReadService.findById(paymentPaidEvent.getPaymentId()).getResult();
        if (payment == null) {
            log.error("{} failed to find the payment(id:{}) orderId:{}", LogUtil.getClassMethodName(), paymentPaidEvent.getPaymentId(), paymentPaidEvent.getOrderId());
            return;
        }
        if (paymentPaidEvent.getPaymentNowStatus() != null && !paymentPaidEvent.getPaymentNowStatus().equals(payment.getStatus())) {
            log.error("{} not expected status so ignore it,paymentId:{} orderId:{} expect status:{} but got:{}", LogUtil.getClassMethodName(), payment.getId(), paymentPaidEvent.getOrderId(), OrderStatus.fromInt(paymentPaidEvent.getPaymentNowStatus()), OrderStatus.fromInt(payment.getStatus()));
            return;
        }
        // 一秒1w单都不可能 所以不需要管这个sync性能问题
        paidPaymentCacheQueue.add(payment);
    }

    @EventListener(PaymentDeclaredDelayNotify.class)
    public void paymentDelayed(PaymentDeclaredDelayNotify paymentDeclaredDelayNotify) {
        if (paymentOtherChannelQueue.get("_DELAY").size() > queueMax) {
            log.info("{} already cache enough job so skip DELAY JOB", LogUtil.getClassMethodName());
            return;
        }
        if (paymentDeclaredDelayNotify.getIsNew())
            paymentOtherChannelQueue.get("_DELAY").add(paymentDeclaredDelayNotify.getPaymentId());
        else
            paymentOtherChannelQueue.get("_DELAY").addFirst(paymentDeclaredDelayNotify.getPaymentId());
    }


    @EventListener(PaymentDeclareFailNotify.class)
    public void paymentFailed(PaymentDeclareFailNotify declareFail) {
        if (paymentOtherChannelQueue.get("_ERROR").size() < queueMax && System.currentTimeMillis() % 2 == 1)
            paymentOtherChannelQueue.get("_ERROR").add(declareFail.getPaymentId());
    }

    @EventListener(PaymentPushFailedNotify.class)
    public void paymentFailed(PaymentPushFailedNotify paymentPushFailedNotify) {
        if (paymentOtherChannelQueue.get("_ERROR").size() < queueMax && System.currentTimeMillis() % 2 == 1)
            paymentOtherChannelQueue.get("_ERROR").add(paymentPushFailedNotify.getPaymentId());
    }

    /**
     * 管道压力
     *
     * @param executorService 线程池
     * @param objectQueue     对象队列
     * @param action          动作
     */
    protected <T> void pressure(ExecutorService executorService, BlockingDeque<T> objectQueue, Consumer<T> action) {
        try {
            while (!executorService.isShutdown() && !suspend) {
                T object = objectQueue.take();
                executorService.execute(() -> action.accept(object));
            }
        } catch (InterruptedException iex) {
            log.warn("{} is System closing? or queue got an error", LogUtil.getClassMethodName());
        } catch (Exception ex) {
            log.error("{} something wrong with the circle", LogUtil.getClassMethodName(), ex);
        }
    }

    protected void reflux(Long paymentId) {
        if (paymentId == null) {
            log.error("{} no null paymentId is acceptable", LogUtil.getClassMethodName());
            return;
        }
        Payment payment = paymentReadService.findById(paymentId).getResult();
        if (orderDeclareService.needDelay(payment)) {
            try {
                Thread.sleep(1000);
            } catch (Exception ex) {
                log.error("{} failed to sleep", LogUtil.getClassMethodName(), ex);
            }
            EventSender.publish(new PaymentDeclaredDelayNotify(paymentId, false));
            return;
        }
        if (payment.getPushStatus() == PaymentPushStatus.PUSH_FAIL.getValue()) {
            try {
                Thread.sleep(1000 * 5L);
                payment.setPushStatus(PaymentPushStatus.DECLARE_SUCCESS.getValue());
                orderDeclareService.payInfoPushV1(payment);
            } catch (Exception ex) {
                log.error("{} fail to delay", LogUtil.getClassMethodName(), ex);
            }
            return;
        }
        if (payment.getPushStatus() == PaymentPushStatus.DECLARED_FAIL_WAIT_PUSH_RETRY.getValue() ||
                payment.getPushStatus() == PaymentPushStatus.WAIT_PUSH.getValue()) {
            try {
                MDC.put(LoggerConstant.MDC_KEY_TRACE_ID, UUID.generate().toString());

                Thread.sleep(1000 * 5L);
                payment.setPushStatus(PaymentPushStatus.WAIT_PUSH.getValue());
                orderDeclareService.paymentDeclare(payment);
            } catch (Exception ex) {
                log.error("{} fail to delay", LogUtil.getClassMethodName(), ex);
            } finally {
                MDC.clear();
            }
        }
    }

    /**
     * 支付单在队列的流动
     *
     * @param targetPayment 支付单
     */
    protected void flow(Payment targetPayment) {
        if (targetPayment == null) {
            log.error("{} no null Payment is acceptable", LogUtil.getClassMethodName());
            return;
        }
        final Payment payment = orderDeclareService.syncPaymentPushStatus(targetPayment).orElse(targetPayment);
        Optional<PaymentPushStatus> paymentNowPushStatusOpt = Stream.of(PaymentPushStatus.values()).filter(status -> status.getValue() == payment.getPushStatus()).findFirst();
        Consumer<Payment> action;
        switch (paymentNowPushStatusOpt.orElse(PaymentPushStatus.NULL)) {
            case DECLARED_FAIL_WAIT_PUSH_RETRY:
                log.info("{} restart push payment[{}] because it used be DECLARE_ERROR [{}]", LogUtil.getClassMethodName(), payment.getId(),
                        Optional.ofNullable(payment.getExtra()).orElseGet(HashMap::new)
                                .getOrDefault(PaymentExtraIndexEnum.customsDeclareErrorMsg.getCode(), "UNKNOWN_ERROR"));
            case WAIT_PUSH: {
                action = orderDeclareService::paymentDeclare;
                break;
            }
            case DECLARE_FAIL: {
                if (!canRetry(payment)) {
                    log.info("PayInfoPushJob.flow, paymentId={}, status=DECLARE_FAIL, 不自动重试申报", payment.getId());
                    return;
                }

                log.info("PayInfoPushJob.flow, paymentId={}, status=DECLARE_FAIL, 将自动重试申报", payment.getId());
                action = orderDeclareService::paymentDeclare;
                break;
            }
            case PUSH_FAIL:
                log.info("{} restart push FAIL_PUSH payment[{}]", LogUtil.getClassMethodName(), payment.getId());
            case DECLARE_SUCCESS: {
                action = orderDeclareService::payInfoPushV1;
                break;
            }
            default: {
                log.debug("{} no need push for payment(id:{}) that status:{}", LogUtil.getClassMethodName(), payment.getId(), paymentNowPushStatusOpt.orElse(PaymentPushStatus.NULL));
                return;
            }
        }
        try {
            MDC.put(LoggerConstant.MDC_KEY_TRACE_ID, UUID.generate().toString());

            action.accept(payment);
        } catch (Exception ex) {
            log.error("{} something wrong with payment(Id:{}) status:{} push", LogUtil.getClassMethodName(), payment.getId(), paymentNowPushStatusOpt.orElse(PaymentPushStatus.NULL), ex);
        } finally {
            MDC.clear();
        }
    }

    /**
     * 判断：支付单申报是否可以自动自试
     *
     * @param payment
     * @return
     */
    private boolean canRetry(Payment payment) {
        var extra = payment.getExtra();
        if (extra == null) {
            extra = Collections.emptyMap();
        }

        var errorMessage = extra.get(PaymentExtraIndexEnum.customsDeclareErrorMsg.getCode());
        var messageEnum = PaymentDeclareErrorMessageEnum.parse(errorMessage);
        log.info("PayInfoPushJob.canRetry, paymentId={}, customsDeclareErrorMsg={}", payment.getId(), errorMessage);

        if (messageEnum == null) {
            return false;
        }
        return messageEnum.isCanRetry();
    }

    /**
     * 额外线程进行完成推送任务
     */
    @PostMapping("/api/payment/push/circle")
    public void scanPaymentInThreadPool() {
        executorPool.submit(this::scanPayment);
    }

    @Scheduled(cron = "0 0/5 * * * ?")
    public void scanPayment() {
        List<Payment> paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.WAIT_PUSH).getResult();
        paymentList.addAll(paymentReadService.listByPushStatus(PaymentPushStatus.DECLARED_FAIL_WAIT_PUSH_RETRY).getResult());

        // Declare the payment
        for (Payment payment : paymentList) {
            paidPaymentCacheQueue.offer(payment);
        }
        paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.DECLARE_SUCCESS).getResult();
        // push the payment
        for (Payment payment : paymentList) {
            paidPaymentCacheQueue.offer(payment);
        }
    }

    // 旧的推送系统
    @PostMapping("/api/payment/push/normal")
    public void oldNormalPush() {
        executorPool.submit(this::pushWaitPayment);
    }

    @Scheduled(cron = "0 0/3 * * * ?")
    public void pushWaitPayment() {  //
        if (jobSwitch.isOpenPayInfoPush()) {
            DateTime startAt = DateTime.now();
            Stopwatch stopwatch = Stopwatch.createStarted();
            List<Payment> paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.WAIT_PUSH).getResult();
            log.info("待推送的支付单单号id {}", JSONUtil.toJsonStr(paymentList.stream().map(Payment::getId).toList()));
            final int waitPaymentSize = paymentList.size();
            for (Payment payment : paymentList) {
                try {
                    orderDeclareService.paymentDeclare(payment);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    log.error("{} paymentId:{}", LogUtil.getClassMethodName("DECLARED"), payment.getId());
                }
            }
            paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.DECLARE_SUCCESS).getResult();
            final int declaredPaymentSize = paymentList.size();
            for (Payment payment : paymentList) {
                try {
                    orderDeclareService.payInfoPushV1(payment);
                    log.debug("{} paymentId:{}", LogUtil.getClassMethodName("ready-push-declare"), payment == null ? "null" : payment.getId());
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("{} paymentId:{}", LogUtil.getClassMethodName("PUSH-PAY-INFO"), Optional.ofNullable(payment).map(Payment::getId).orElse(null));
                }
            }
            stopwatch.stop();
            log.info("[CRON-JOB] push payInfo(WAIT_PUSH) begin at {} done at {} tried-count (wait:[{}]) (declared:[{}]) cost {} ms", startAt, DFT.print(DateTime.now()), waitPaymentSize, declaredPaymentSize, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        } else {
            log.info("payInfo push switch is close");
        }
    }

    @PostMapping("/api/payment/suspend")
    @ResponseBody
    public boolean suspend() {
        suspend = !suspend;
        return suspend;
    }

    // 已经推送失败的订单，降低其再次推送的时间间隔
    @PostMapping("/api/payment/push/fail")
    public void pushFailPaymentAtTP() {
        executorPool.submit(this::pushFail);
    }

    @Scheduled(cron = "0 0/15 * * * ? ")
    public void pushFail() {  //每隔30分钟执行一次
        if (jobSwitch.isOpenPayInfoPush()) {
            log.info("[CRON-JOB] push payInfo(PUSH_FAIL) begin {}", DFT.print(DateTime.now()));
            Stopwatch stopwatch = Stopwatch.createStarted();

            List<Payment> paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.DECLARE_FAIL).getResult();
            for (Payment payment : paymentList) {
                paidPaymentCacheQueue.offer(payment);
            }
            paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.DECLARE_SUCCESS).getResult();
            paymentList.addAll(Optional.ofNullable(paymentReadService.listByPushStatus(PaymentPushStatus.PUSH_FAIL).getResult()).orElse(new ArrayList<>()));
            for (Payment payment : paymentList) {
                paidPaymentCacheQueue.offer(payment);
            }
            stopwatch.stop();
            log.info("[CRON-JOB] push payInfo(PUSH_FAIL) done at {} cost {} ms", DFT.print(DateTime.now()), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        } else {
            log.warn("payInfo push switch is close");
        }
    }
}
