package moonstone.web.admin.jobs.settle.config;

import moonstone.web.admin.jobs.settle.component.*;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * DATE: 16/8/11 下午4:22 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Configuration
public class SettleJobConfig {

    @Configuration
    @ComponentScan("moonstone.web.admin.jobs.settle.trans")
    public static class TransConfig{
    }

    @ConditionalOnProperty(name = "settle.job.summary.enable", havingValue = "true", matchIfMissing = true)
    @Configuration
    @ComponentScan("moonstone.web.admin.jobs.settle.component")
    public static class DailySummaryConfig{

        @Bean
        @Profile({"test", "dev", "stag", "integration"})
        public TestSummaryJob testSummaryJob(SummaryPayChannelDailyJob summaryPayChannelDailyJob,
                                             SummaryPlatformTradeDailyJob summaryPlatformTradeDailyJob,
                                             SummarySellerTradeDailyJob summarySellerTradeDailyJob) {
            return new TestSummaryJob(summaryPayChannelDailyJob, summaryPlatformTradeDailyJob, summarySellerTradeDailyJob);
        }

        @Bean
        public ProductSummaryJob productSummaryJob(SummaryPayChannelDailyJob summaryPayChannelDailyJob,
                                                   SummaryPlatformTradeDailyJob summaryPlatformTradeDailyJob,
                                                   SummarySellerTradeDailyJob summarySellerTradeDailyJob){
            return new ProductSummaryJob(summaryPayChannelDailyJob,summaryPlatformTradeDailyJob,summarySellerTradeDailyJob);
        }
    }

    @ConditionalOnProperty(name="settle.job.detail.enable", havingValue = "true", matchIfMissing = true)
    @Configuration
    @ComponentScan("moonstone.web.admin.jobs.settle.component")
    public static class SettleTradeConfig{
        @Bean
        @Profile({"test", "dev", "stag", "integration"})
        public TestSettleTradeJob testSettleTradeJob(SettlePaymentJob settlePaymentJob,
                                                     SettleRefundJob settleRefundJob){
            return new TestSettleTradeJob(settlePaymentJob,settleRefundJob);
        }

        @Bean
        public ProductSettleTradeJob productSettleTradeJob(SettlePaymentJob settlePaymentJob,
                                                           SettleRefundJob settleRefundJob){
            return new ProductSettleTradeJob(settlePaymentJob,settleRefundJob);
        }
    }

}
