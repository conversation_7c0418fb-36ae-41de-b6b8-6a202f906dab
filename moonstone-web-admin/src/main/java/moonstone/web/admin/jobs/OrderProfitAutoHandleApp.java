package moonstone.web.admin.jobs;

import io.vertx.core.Vertx;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.events.trade.app.OrderProfitAutoHandle;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class OrderProfitAutoHandleApp implements OrderProfitAutoHandle {
    Vertx vertx;

    @Profile({"dev", "test", "stag", "integration"})
    @Component
    public class testConfig {
        @Scheduled(cron = "0 0/1 * * * ?")
        public void publishEvent() {
            OrderProfitAutoHandleApp.this.publishEvent();
        }
    }

    @Profile("online")
    @Component
    public class onlineConfig {
        @Scheduled(cron = "0 0/5 * * * ?")
        public void publishEvent() {
            OrderProfitAutoHandleApp.this.publishEvent();
        }
    }

    void publishEvent() {
        vertx.eventBus().request(CHANNEL, "");
    }
}
