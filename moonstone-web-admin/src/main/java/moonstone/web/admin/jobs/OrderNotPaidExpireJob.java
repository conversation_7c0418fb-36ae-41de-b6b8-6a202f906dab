package moonstone.web.admin.jobs;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.ShopExtra;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.event.OrderCancelEvent;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.ShopOrderFeeDetailDTO;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.web.core.component.WxSubscribeMsgSender;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.component.wx.WxSubscribeMsg;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Consumer;

@Slf4j
public class OrderNotPaidExpireJob {

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private OrderWriteService orderWriteService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Value("${order.auto.cancel.in.minutes}")
    private Integer expireMinutes;

    @Autowired
    private WxSubscribeMsgSender wxSubscribeMsgSender;
    @Autowired
    private ShopWxaCacheHolder shopWxaCacheHolder;
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RocketMQProducer rocketMQProducer;

    static final Integer BATCH_SIZE = 100;     // 批处理数量
    private static final DateTimeFormatter DFT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    @Profile("test")
    @Scheduled(cron = "${order.auto.cancel.job.cron: 0 0/1 * * * ?}")
    public void test() {
        remindOrderMayBeExpired();
    }

    @Scheduled(cron = "${order.auto.cancel.job.cron: 0 0/30 * * * ?}")
    public void remindOrderMayBeExpired() {
        OrderCriteria criteria = new OrderCriteria();
        LocalDateTime endAt = LocalDateTime.now().minusMinutes(30);
        criteria.setEndAt(Date.from(endAt.atZone(ZoneId.systemDefault()).toInstant()));
        criteria.setStartAt(Date.from(LocalDateTime.now().minusDays(7L).atZone(ZoneId.systemDefault()).toInstant()));
        criteria.setStatus(Collections.singletonList(OrderStatus.NOT_PAID.getValue()));
        log.debug("{} criteria:{}", LogUtil.getClassMethodName("remind-user-pay-order"), criteria);
        Optional.ofNullable(shopOrderReadService.findBy(1, Integer.MAX_VALUE, criteria).getResult())
                .map(Paging::getData)
                .orElse(new ArrayList<>())
                .forEach(order ->
                {
                    if (order.getStatus() != OrderStatus.NOT_PAID.getValue()) {
                        return;
                    }
                    log.debug("{} found not paid order[{}] for shop[{}]", LogUtil.getClassMethodName(), order.getId(), order.getShopId());
                    if (shopWxaCacheHolder.findReleaseOneForShopId(order.getShopId()) == null) {
                        return;
                    }
                    if (!wxSubscribeMsgSender.getSupportedAppId().contains(shopWxaCacheHolder.findReleaseOneForShopId(order.getShopId()).getAppId())) {
                        return;
                    }
                    List<SkuOrder> skuOrders = skuOrderReadService.findByShopOrderId(order.getId()).getResult();
                    if (skuOrders == null || skuOrders.isEmpty()) {
                        return;
                    }
                    String name = skuOrders.get(0).getItemName() + (skuOrders.size() > 1 ? "..." : "");

                    DateTime shouldPaidAt = new DateTime(order.getCreatedAt()).plusMinutes(expireMinutes);
                    Consumer<WxSubscribeMsg> constructor = (msg) -> {
                        Map<String, Object> data = new HashMap<>();
                        data.put("thing1", limitString(name));
                        data.put("amount2", new BigDecimal(order.getFee()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN).toString());
                        data.put("thing4", limitString(new Translate("请在%s分钟内完成支付", (shouldPaidAt.getMillis() - DateTime.now().getMillis()) / 60000).toString()));
                        data.put("date3", new DateTime().toString("YYYY年MM月dd日 HH:mm"));
                        msg.setData(data);
                    };

                    WxSubscribeMsg subscribeMsg = new WxSubscribeMsg();
                    subscribeMsg.setShopId(skuOrders.get(0).getShopId());
                    subscribeMsg.setUserId(skuOrders.get(0).getBuyerId());
                    subscribeMsg.setTemplate_id("0KMOd9hWTAkv7vt0ZBXExnhmSo9IMKzjX2JDr32M3jQ");
                    subscribeMsg.setPage(String.format("pages/order_detail?id=%s", skuOrders.get(0).getOrderId()));
                    constructor.accept(subscribeMsg);
                    wxSubscribeMsgSender.send(subscribeMsg);
                });

    }

    private String limitString(String str) {
        if (str.length() > 20) {
            return str.substring(0, 18) + "..";
        }
        return str;
    }

    @Scheduled(cron = "${order.auto.cancel.job.cron: 0 0/5 * * * ?}")
    public void handOrderPaidExpire() {
        log.info("[CRON-JOB] [HANDLE-ORDER-PAID-EXPIRE] begin {}", DFT.print(DateTime.now()));
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<ShopOrder> notPaidOrders = Lists.newArrayList();
        Lock iLock = redissonClient.getLock(getClass().getName() + "#handOrderPaidExpire");
        if (!iLock.tryLock()) {
            return;
        }
        try {
            var startAt = Date.from(LocalDateTime.now().minusDays(7L).atZone(ZoneId.systemDefault()).toInstant());
            int pageNo = 1;
            boolean next = batchHandle(pageNo, BATCH_SIZE, startAt, notPaidOrders);
            while (next) {
                pageNo++;
                next = batchHandle(pageNo, BATCH_SIZE, startAt, notPaidOrders);
            }

            for (ShopOrder shopOrder : notPaidOrders) {
                shopOrder = shopOrderReadService.findById(shopOrder.getId()).getResult();
                if (shopOrder.getStatus() != OrderStatus.NOT_PAID.getValue()) {
                    log.warn("{} shopOrder[Id => {} Status => {}] unknown status", LogUtil.getClassMethodName()
                            , shopOrder.getId(), shopOrder.getStatus());
                    continue;
                }
                // warn the payment should close at same time, mostly use outer PaymentSystem to make sure this.
                if (hasOrderExpire(shopOrder.getCreatedAt(), expireMinutes)) {
                    Response<Boolean> handleRes = orderWriteService.shopOrderStatusChanged(shopOrder.getId(),
                            shopOrder.getStatus(),
                            OrderStatus.TIMEOUT_CANCEL.getValue());
                    if (!handleRes.isSuccess()) {
                        log.error("hand expire not paid shop order:(id => {}) fail,error:{}", shopOrder.getId(), handleRes.getError());
                        continue;
                    }
                    // send Event of Order Cancel to roll back the stock
                    EventSender.sendApplicationEvent(new OrderCancelEvent(shopOrder.getId(), OrderLevel.SHOP.getValue(), null));
                    log.info("发送订单超时关闭 无需推送消息 订单号 {}", shopOrder.getId());
                    JSONObject msg = new JSONObject();
                    msg.put("orderId", shopOrder.getId());
                    rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_NOT_NEED_PUSH_TAG, msg.toString());
                    if (shopOrder.getOutFrom().equals(ShopExtra.communityOperation.getCode())) {
                        // 社群营销模式
                        log.info("社群运营模式 当前订单信息 {}",shopOrder);
                        msg.put("shopId", shopOrder.getShopId());
                        msg.put("userId", shopOrder.getBuyerId());
                        String feeDetailJson = shopOrder.getFeeDetailJson();
                        ShopOrderFeeDetailDTO shopOrderFeeDetail = JSONUtil.toBean(feeDetailJson, ShopOrderFeeDetailDTO.class);
                        msg.put("whetherUsedCoupon", !shopOrderFeeDetail.getCouponPrice().equals(BigDecimal.ZERO));
                        msg.put("whetherUsedGiftMoney", !shopOrderFeeDetail.getGiftPrice().equals(BigDecimal.ZERO));
                        msg.put("whetherUsedCash", !shopOrderFeeDetail.getCashPrice().equals(BigDecimal.ZERO));
                        log.info("发送订单超时消息给新系统 {}",msg);
                        rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_TIMEOUT_CLOSE_TAG, msg.toString());
                        log.info("已发送订单超时消息");
                    }
                }
            }
        } finally {
            iLock.unlock();
        }

        stopwatch.stop();
        log.info("[CRON-JOB] [HANDLE-ORDER-PAID-EXPIRE] done at {} cost {} ms", DFT.print(DateTime.now()), stopwatch.elapsed(TimeUnit.MILLISECONDS));


    }

    @SuppressWarnings("unchecked")
    private boolean batchHandle(int pageNo, int size, Date startAt, List<ShopOrder> notPaidOrders) {

        List<Integer> status = Lists.newArrayList(OrderStatus.NOT_PAID.getValue());
        OrderCriteria criteria = new OrderCriteria();
        criteria.setStatus(status);
        criteria.setStartAt(startAt);
        Response<Paging<ShopOrder>> pagingRes = shopOrderReadService.findBy(pageNo, size, criteria);
        if (!pagingRes.isSuccess()) {
            log.error("paging shop order fail,criteria:{},error:{}", criteria, pagingRes.getError());
            return Boolean.FALSE;
        }

        Paging<ShopOrder> paging = pagingRes.getResult();
        List<ShopOrder> shopOrders = paging.getData();

        if (paging.getTotal().equals(0L) || CollectionUtils.isEmpty(shopOrders)) {
            return Boolean.FALSE;
        }
        notPaidOrders.addAll(shopOrders);

        int current = shopOrders.size();
        return current == size;  // 判断是否存在下一个要处理的批次
    }


    private boolean hasOrderExpire(Date createdAt, Integer expireMinutes) {

        return new DateTime(createdAt).isBefore(DateTime.now().minusMinutes(expireMinutes));
    }

}
