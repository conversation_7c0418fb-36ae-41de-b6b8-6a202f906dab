package moonstone.web.admin.inner;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.utils.EventSender;
import moonstone.event.OrderShipmentEvent;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * 内部接口
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("api/inner/orderShipment")
public class OrderShipmentInnerController {


	/**
	 * 手动发送订单已发货事件
	 * @return 是否发送成功
	 */
	@GetMapping("send/event")
	public Result<Boolean> sendEvent(@RequestParam("shipmentId") Long shipmentId,
									 @RequestParam("shipmentAt") String shipmentAtStr) {
		log.info("手动发送订单已发货事件 请求参数 {} {}", shipmentId,shipmentAtStr);
		DateTime shipmentAtDate = DateUtil.parse(shipmentAtStr);
		LocalDateTime shipmentAt = DateUtil.toLocalDateTime(shipmentAtDate);
		EventSender.sendApplicationEvent(new OrderShipmentEvent(shipmentId,shipmentAt));
		return Result.data(true);
	}
}
