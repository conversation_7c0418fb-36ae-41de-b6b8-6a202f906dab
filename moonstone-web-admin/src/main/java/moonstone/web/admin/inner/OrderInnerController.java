package moonstone.web.admin.inner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.constants.ShopExtra;
import moonstone.common.utils.R;
import moonstone.common.utils.UserUtil;
import moonstone.order.api.RichOrderMaker;
import moonstone.order.dto.CommunitySubmittedOrder;
import moonstone.order.dto.ShopOrderFeeDetailDTO;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.ShopOrder;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.logic.ShopOrderLogic;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.order.OrderWriteLogic;
import moonstone.web.core.util.ParanaUserMaker;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 订单内部接口
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("api/inner/order")
public class OrderInnerController {


    @Resource
    private OrderWriteLogic orderWriteLogic;

    @Resource
    private ShopOrderLogic shopOrderLogic;

    @Resource
    private RocketMQProducer rocketMQProducer;

    @Resource
    private UserReadService userReadService;

    @Resource
    RichOrderMaker richOrderMaker;

    /**
     * 手动关闭订单
     *
     * @param orderId 订单id
     * @return 是否关闭成功
     */
    @GetMapping("manual/close")
    public Result<Boolean> manualCloseOrder(@RequestParam("orderId") Long orderId) {
        log.info("手动关闭订单 订单id {}", orderId);
        Map<String, Object> query = new HashMap<>();
        query.put("id", orderId);
        ShopOrder shopOrder = shopOrderLogic.getOne(query);
        if (shopOrder == null) {
            return Result.data(true);
        }
        JSONObject msg = new JSONObject();
        msg.set("orderId", shopOrder.getId());
        rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_NOT_NEED_PUSH_TAG, msg.toString());
        if (Objects.equals(shopOrder.getOutFrom(), ShopExtra.communityOperation.getCode())) {
            // 社群运营模式下
            log.info("社群运营模式 当前订单信息 {}", JSONUtil.toJsonStr(shopOrder));
            msg.set("shopId", shopOrder.getShopId());
            msg.set("userId", shopOrder.getBuyerId());
            String feeDetailJson = shopOrder.getFeeDetailJson();
            ShopOrderFeeDetailDTO shopOrderFeeDetail = JSONUtil.toBean(feeDetailJson, ShopOrderFeeDetailDTO.class);
            msg.set("whetherUsedCoupon", !shopOrderFeeDetail.getCouponPrice().equals(BigDecimal.ZERO));
            msg.set("whetherUsedGiftMoney", !shopOrderFeeDetail.getGiftPrice().equals(BigDecimal.ZERO));
            msg.set("whetherUsedCash", !shopOrderFeeDetail.getCashPrice().equals(BigDecimal.ZERO));
            log.info("发送取消订单消息 {}", msg);
            rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_CANCEL_TAG, msg.toString());
            log.info("已发送取消订单消息");
        }
        boolean flag = orderWriteLogic.updateOrderStatusByOrderEvent(shopOrder, OrderLevel.SHOP, OrderEvent.SELLER_CANCEL);
        return Result.data(flag);
    }

    /**
     * 小程序下单（社群运营）
     * @param submittedOrder 提交订单参数
     * @return 订单号
     */
    @RequestMapping(value = "/community/submit", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public R createCommunity(@RequestBody CommunitySubmittedOrder submittedOrder) {
        log.info("内部提交社群运营模式下的订单 请求参数 {}", JSONUtil.toJsonStr(submittedOrder));
        if (submittedOrder == null) {
            log.error("submittedOrder:{}", submittedOrder);
            return R.error(-1, "下单失败");
        }
        if (ObjectUtils.isEmpty(submittedOrder.getReceiverInfoId())) {
            return R.error(-1, "收货地址为空");
        }
        User user = (User) userReadService.findById(submittedOrder.getBuyerId()).getResult();
        if (user == null) {
            return R.error(-1, "订购人信息不存在");
        }
        UserUtil.putCurrentUser(ParanaUserMaker.from(user));
        try {
            List<Long> list = orderWriteLogic.createCommunity(submittedOrder, UserUtil.getCurrentUser());
            log.info("内部提交社群运营模式下的订单 返回结果 {}", JSONUtil.toJsonStr(list));
            if (CollUtil.isEmpty(list)) {
                log.error("list:{}", list);
                return R.error(-1, "下单失败");
            }
            Map<String, Object> mapId = new HashMap<>();
            mapId.put("idList", list);
            return R.ok().add("data", mapId);
        } catch (Exception e) {
            return R.error(-1, e.getMessage());
        }
    }

}
