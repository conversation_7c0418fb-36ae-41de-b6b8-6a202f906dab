package moonstone.web.admin.inner;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.enums.UserRole;
import moonstone.user.model.UserRoleMenu;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 内部接口
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/api/inner/user/role/menu")
public class UserRoleMenuInnerController {


	@Resource
	private MongoTemplate mongoTemplate;


	/**
	 * 复制 SELLER 下的用户角色菜单
	 * @param req 请求参数
	 * @return 生成后的菜单
	 */
	@PostMapping("copySeller")
	public Result<UserRoleMenu> copyByRoleName(@RequestBody UserRoleMenu req) {
		log.info("手动生成 {} 允许的菜单", JSONUtil.toJsonStr(req));
		UserRoleMenu userRoleMenu = mongoTemplate.findOne(Query.query(Criteria.where("roleName").is(UserRole.SELLER.name()).and("defaultMenu").is(true)), UserRoleMenu.class);
		log.info("查询到SELLER默认允许的路由信息 {}", JSONUtil.toJsonStr(userRoleMenu));
		if (userRoleMenu != null) {
			userRoleMenu.setRoleName(req.getRoleName());
			mongoTemplate.save(userRoleMenu);
		}
		return Result.data(userRoleMenu);
	}


	/**
	 * 删除允许访问的菜单路由
	 * @param req 请求参数
	 * @return 删除后的菜单路由
	 */
	@PostMapping("deleteAllowMenu")
	public Result<UserRoleMenu> deleteAllowMenu(@RequestBody UserRoleMenu req) {
		log.info("手动删除不允许的菜单路由 {}",JSONUtil.toJsonStr(req));
		UserRoleMenu userRoleMenu = mongoTemplate.findOne(Query.query(Criteria.where("roleName").is(req.getRoleName())), UserRoleMenu.class);
		if (userRoleMenu != null) {
			List<String> allowMenu = userRoleMenu.getAllowMenu();
			allowMenu.removeAll(req.getAllowMenu());
			mongoTemplate.upsert(Query.query(Criteria.where("roleName").is(req.getRoleName())), Update.update("allowMenu", allowMenu), UserRoleMenu.class);
		}
		return Result.data(userRoleMenu);
	}

	/**
	 * 手动添加允许的菜单路由
	 * @param req 请求参数
	 * @return 添加后的菜单路由
	 */
	@PostMapping("addAllowMenu")
	public Result<UserRoleMenu> addAllowMenu(@RequestBody UserRoleMenu req) {
		log.info("手动添加允许的菜单路由 {}",JSONUtil.toJsonStr(req));
		UserRoleMenu userRoleMenu = mongoTemplate.findOne(Query.query(Criteria.where("roleName").is(req.getRoleName())), UserRoleMenu.class);
		if (userRoleMenu != null) {
			List<String> allowMenu = userRoleMenu.getAllowMenu();
			Set<String> distinctAllowMenu = new HashSet<>(allowMenu);
			distinctAllowMenu.addAll(req.getAllowMenu());
			mongoTemplate.upsert(Query.query(Criteria.where("roleName").is(req.getRoleName())), Update.update("allowMenu", distinctAllowMenu), UserRoleMenu.class);
		}
		return Result.data(userRoleMenu);
	}

	/**
	 * 手动根据角色名称查询对应的路由信息
	 * @param roleName 角色名称
	 * @return 路由信息
	 */
	@GetMapping("/by/roleName")
	public Result<UserRoleMenu> getByRoleName(@RequestParam("roleName") String roleName) {
		log.info("手动根据角色名称查询对应的路由信息 {}", roleName);
		UserRoleMenu userRoleMenu = mongoTemplate.findOne(Query.query(Criteria.where("roleName").is(roleName)), UserRoleMenu.class);
		log.info("查询结果 {}", JSONUtil.toJsonStr(userRoleMenu));
		return Result.data(userRoleMenu);
	}

}
