package moonstone.web.admin.inner;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableBiMap;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.UserStatus;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.event.OrderCreatedEvent;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.RichOrderMaker;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;
import moonstone.order.dto.SubmittedOrder;
import moonstone.order.enu.OrderCreateChannelEnum;
import moonstone.order.rule.OrderRuleEngine;
import moonstone.order.service.OrderWriteService;
import moonstone.promotion.api.CouponCheck;
import moonstone.promotion.component.OrderCharger;
import moonstone.promotion.component.PromotionOngoingValidator;
import moonstone.shop.model.Shop;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.UserCertification;
import moonstone.user.service.UserCertificationReadService;
import moonstone.web.core.constants.RedisConstants;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.user.RealNameCertificationManager;
import moonstone.web.core.user.StoreProxyManager;
import moonstone.web.core.user.UserRelationManager;
import moonstone.web.core.util.LockKeyUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * 订单
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class Orders {

    private static final String PAYER_ID_PATTERN = "(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{4}\\*{9}\\d{4}$)";
    private static final int CANCEL_DELAY = 5;

    @Resource
    RichOrderMaker richOrderMaker;
    @Resource
    OrderWriteService orderWriteService;
    @Resource
    PromotionOngoingValidator promotionOngoingValidator;
    @Resource
    OrderRuleEngine orderRuleEngine;
    @Resource
    OrderCharger charger;
    @Autowired
    List<CouponCheck> couponChecks;
    @Resource
    SkuReadService skuReadService;
    @Resource
    UserCertificationReadService userCertificationReadService;
    @Resource
    SubStoreReadService subStoreReadService;
    @Resource
    SubStoreTStoreGuiderReadService guiderReadService;
    @Autowired
    UserRelationManager userRelationManager;
    @Autowired
    StoreProxyManager storeProxyManager;
    @Resource
    ShopCacheHolder shopCacheHolder;
    @Autowired
    Orders self;
    @Autowired(required = false)
    JedisPool jedisPool;

    @Resource
    private RedissonClient redissonClient;
    @Autowired
    private RealNameCertificationManager realNameCertificationManager;
    @Resource
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private UserCacheHolder userCacheHolder;


    private void addMemberShip(long userId, RichOrder richOrder) {
        if (richOrder != null && !richOrder.getRichSkusByShops().isEmpty()) {
            RichSkusByShop richSkusByShop = richOrder.getRichSkusByShops().get(0);
            Long shopId = richSkusByShop.getShop().getId();
            if (shopId != null) {
                try {
                    userRelationManager.addSubStoreMember(shopId, userId);
                } catch (Exception ex) {
                    log.warn("{} userId:{} shopId:{}", LogUtil.getClassMethodName("fail-add-memberShip"), userId, shopId);
                }
            }
        }
    }

    public List<Long> create(SubmittedOrder submittedOrder, CommonUser user) {
        var lock = redissonClient.getLock(LockKeyUtils.orderCreate(user.getId()));
        if (!lock.tryLock()) {
            log.error("Orders.create error, userId={}, submittedOrder={}, 获取锁失败", user.getId(), JSON.toJSONString(submittedOrder));
            throw new RuntimeException("正在处理下单流程，请稍后重试");
        }

        try {
            var shopOrderIds = realCreate(submittedOrder, user);
            log.info("Orders.create, userId={}, submittedOrder={}, result shopOrderIds={}",
                    user.getId(), JSON.toJSONString(submittedOrder), JSON.toJSONString(shopOrderIds));

            return shopOrderIds;
        } catch (Exception ex) {
            log.error("Orders.create error, userId={}, submittedOrder={}", user.getId(), JSON.toJSONString(submittedOrder), ex);
            throw ex;
        } finally {
            lock.unlock();
        }
    }

    public List<Long> realCreate(@RequestBody SubmittedOrder submittedOrder, CommonUser user) {
        // 手动控制事务保证eventBus触发前事务写入完毕
        try {
            log.info("[order-realCreate] userId={}, submittedOrder={}", user.getId(), JSON.toJSONString(submittedOrder));
            RichOrder richOrder = richOrderMaker.full(submittedOrder, user);


            //检查用户是否具有购买资格
            orderRuleEngine.canBuy(richOrder);
            //提前增加注册用户
            addMemberShip(user.getId(), richOrder);

            Predicate<RichSkusByShop> isBondedCheck = richSkusByShop -> richSkusByShop.getRichSkus().stream()
                    .map(RichSku::getSku).map(Sku::getType).anyMatch(Predicate.isEqual(1));
            boolean hasBonded = richOrder.getRichSkusByShops().stream().anyMatch(isBondedCheck);
            //含有保税商品，检查订购人实名认证信息
            checkPayerInfo(submittedOrder.getChannel(), richOrder, user, hasBonded);

            //检查商家营销活动的有效性
            promotionOngoingValidator.validate(richOrder);

            //检查优惠券是否符合使用规则
            for (CouponCheck couponCheck : couponChecks) {
                couponCheck.check(richOrder);
            }

            //检查用户是否可以享受对应的营销, 如果可以, 则计算营销, 需要实付的金额, 以及运费等
            charger.charge(richOrder, null);

            validTheGuider(richOrder, user, submittedOrder);

            // 通过AOP层进行独立事务处理，以使eventBus的线程在persist事务后进行，避免幻读
            Response<List<Long>> rOrder = self.persistRichOrder(richOrder);

            if (!rOrder.isSuccess()) {
                log.error("failed to create {}, error code:{}", submittedOrder, rOrder.getError());
                throw new JsonResponseException(rOrder.getError());
            }

            final List<Long> shopOrderIds = rOrder.getResult();

            orderCreatedEventSend(shopOrderIds);
            return shopOrderIds;
        } catch (Exception e) {
            log.error("Orders.realCreate, failed to create, userId={}, submittedOrder={}, cause:",
                    user.getId(), JSON.toJSONString(submittedOrder), e);
            throw e;
        }
    }


    /**
     * 判断库存是否可用，然后持久化订单
     *
     * @param richOrder 订单内容
     * @return 是否持久化成功
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Response<List<Long>> persistRichOrder(RichOrder richOrder) {
        // 如果没有配置redis 那么就直接走吧
        if (jedisPool == null) {
            log.debug("{} redis not enable so may cause over sell", LogUtil.getClassMethodName());
            return orderWriteService.create(richOrder);
        }
        // 配置了redis 那么就从redis里拉缓存
        try (Jedis jedis = jedisPool.getResource()) {
            HashMap<String, Long> skuStockQuantityModifyHistory = new HashMap<>(8);
            //modify redis and record its action
            BiFunction<Long, Long, Long> modifySkuQuantity = (skuId, modQuantity) -> {
                String skuStockQuantityCacheInRedis = RedisConstants.SKU_STOCK_PREFIX + skuId;
                Lock lock = redissonClient.getLock(RedisConstants.SKU_STOCK_LOCK_PREFIX + skuId);
                lock.lock();
                try {
                    if (!jedis.exists(skuStockQuantityCacheInRedis)) {
                        Integer stockQuantity = skuReadService.findSkuById(skuId).getResult().getStockQuantity();
                        log.debug("{} update skuStock[{}] at redis", LogUtil.getClassMethodName(), stockQuantity);
                        jedis.setnx(skuStockQuantityCacheInRedis, stockQuantity.toString());
                    }
                    log.debug("{} " + RedisConstants.SKU_STOCK_PREFIX + " sku[{}] exists[{}]", LogUtil.getClassMethodName(), skuId, jedis.exists(skuStockQuantityCacheInRedis));
                    skuStockQuantityModifyHistory.put(skuStockQuantityCacheInRedis, modQuantity);
                    jedis.expire(skuStockQuantityCacheInRedis, 2 * 60);
                    return jedis.decrBy(skuStockQuantityCacheInRedis, modQuantity);
                } finally {
                    lock.unlock();
                }
            };
            //revoke all redis action
            Consumer<Map<String, Long>> revokeRedisAction = (map) -> map.forEach((skuStockQuantityCacheInRedis, modQuantity) -> {
                log.debug("{} redis stock[{}] add {} result [{}]", LogUtil.getClassMethodName(), skuStockQuantityCacheInRedis, modQuantity,
                        jedis.decrBy(skuStockQuantityCacheInRedis, -modQuantity));
                jedis.expire(skuStockQuantityCacheInRedis, 2 * 60);
            });
            boolean failed = false;
            // 遍历处理所有的单品去处理库存
            StockLoop:
            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                for (RichSku richSkus : richSkusByShop.getRichSkus()) {
                    Long afterQuantity = modifySkuQuantity.apply(richSkus.getSku().getId(), richSkus.getQuantity().longValue());
                    log.debug("{} skuId:{} after-stock-quantity:{} in redis", LogUtil.getClassMethodName(), richSkus.getSku().getId(), afterQuantity);
                    if (afterQuantity < 0) {
                        log.error("{} buyer:{} skuId:{} buy-quantity:{} quantity:{}", LogUtil.getClassMethodName()
                                , richOrder.getBuyer()
                                , richSkus.getSku()
                                , richSkus.getQuantity()
                                , afterQuantity);
                        failed = true;
                        break StockLoop;
                    }
                }
            }
            // 出现库存不足的情况了 回退所有吃掉的库存
            if (failed) {
                revokeRedisAction.accept(skuStockQuantityModifyHistory);
                return Response.fail(new Translate("扣减库存失败，请重新下单").toString());
            }
            Response<List<Long>> rShopOrderIds = orderWriteService.create(richOrder);
            if (!rShopOrderIds.isSuccess()) {
                revokeRedisAction.accept(skuStockQuantityModifyHistory);
            }
            return rShopOrderIds;
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} sku-stock-quantity modify error:{}", LogUtil.getClassMethodName(), ex.getMessage());
            return Response.fail(new Translate("扣减库存失败，请重新下单").toString());
        }
    }

    /**
     * check the `outFrom` guider data,fix it if we can
     *
     * @param richOrder      order
     * @param user
     * @param submittedOrder
     */
    private void validTheGuider(RichOrder richOrder, CommonUser user, SubmittedOrder submittedOrder) {
        for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {

            if (ObjectUtils.isEmpty(richSkusByShop.getOutFrom())) {
                continue;
            }
            OrderOutFrom outFrom = OrderOutFrom.fromCode(richSkusByShop.getOutFrom());
            switch (outFrom) {
                case SUB_STORE -> {
                    // never trust a frontend given data
                    // not from subStore mean skip this test
                    if (ObjectUtils.isEmpty(richSkusByShop.getOutShopId())) {
                        throw new RuntimeException("门店id丢失，请重新扫描二维码");
                    }
                    Long subStoreId = Long.valueOf(richSkusByShop.getOutShopId());
                    var rSubStore = subStoreReadService.findById(subStoreId);

                    if (rSubStore.isSuccess()) {
                        // wrong subStore?
                        if (rSubStore.getResult() == null) {
                            log.error("{} outShopId:{} outFrom:{} richOrder:{} subShopOrder:{}", LogUtil.getClassMethodName("wong-subStore"), richSkusByShop.getOutShopId(), richSkusByShop.getOutFrom(), richOrder, richSkusByShop);
                            richSkusByShop.setRefererId(null);
                            throw new RuntimeException("门店信息查询失败，请重新扫描二维码");
                        }

                        //门店对应的用户的状态校验
                        checkSubStoreUser(rSubStore.getResult());
                        // no  guider skip too
                        if (richSkusByShop.getRefererId() == null) {
                            continue;
                        }

                        //导购对应的用户状态校验
                        checkGuiderUser(richSkusByShop.getRefererId());

                        @SuppressWarnings("deprecation") var rGuiderList = guiderReadService.findByStoreGuiderId(richSkusByShop.getRefererId());
                        // not a guider?
                        if (!rGuiderList.isSuccess() || CollectionUtils.isEmpty(rGuiderList.getResult())) {
                            log.error("{} refererId:{} richOrder:{} subOrder:{}", LogUtil.getClassMethodName("not-guider"), richSkusByShop.getRefererId(), richOrder, richSkusByShop);
                            richSkusByShop.setRefererId(null);
                            continue;
                        }
                        // not guider from this subStore?
                        if (rGuiderList.getResult().stream().noneMatch(guider -> guider.getSubStoreId().equals(subStoreId))) {
                            log.error("{} refererId:{} richOrder:{} subOrder:{}", LogUtil.getClassMethodName("wrong-guider"), richSkusByShop.getRefererId(), richOrder, richSkusByShop);
                            richSkusByShop.setRefererId(null);
                            continue;
                        }
                    } else {
                        throw new RuntimeException("门店信息查询失败，请重新扫描二维码");
                    }
                }
                case LEVEL_Distribution -> {
                    if (richSkusByShop.getRefererId() == null) {
                        break;
                    }
                    if (richSkusByShop.getExtra() == null) {
                        richSkusByShop.setExtra(new HashMap<>(8));
                    }
                    Optional<StoreProxy> storeProxyOpt = storeProxyManager.findValidStoreProxyFromRefererIdAndShopId(richSkusByShop.getShop().getId(), richSkusByShop.getRefererId());
                    Optional<Long> realProxyId = storeProxyOpt.map(StoreProxy::getUserId);
                    if (realProxyId.isPresent() && !Objects.equals(realProxyId.get().toString(), richSkusByShop.getExtra().get("refererId"))) {
                        richSkusByShop.getExtra().put("from", richSkusByShop.getRefererId().toString());
                    }
                    realProxyId.ifPresent(richSkusByShop::setRefererId);
                    realProxyId.map(Object::toString).ifPresent(idStr -> richSkusByShop.getExtra().put("refererId", idStr));
                }
                default ->
                        log.debug("{} outFrom:{} userId:{}", LogUtil.getClassMethodName("no-check-for"), outFrom, richOrder.getBuyer());
            }

        }
    }

    /**
     * 门店用户状态不符合条件的，不允许下单
     *
     * @param subStore
     */
    private void checkSubStoreUser(SubStore subStore) {
        var subStoreUser = userCacheHolder.findByUserId(subStore.getUserId()).orElse(null);
        if (subStoreUser == null) {
            throw new RuntimeException("门店对应的用户不存在，请更换门店进行下单");
        }

        if (subStoreUser.getStatus() == null) {
            throw new RuntimeException("门店对应的用户状态无效，请更换门店进行下单");
        }
        if (UserStatus.FROZEN.value() == subStoreUser.getStatus()) {
            throw new RuntimeException("门店对应的用户已被冻结，请更换门店进行下单");
        }

        var serviceProvider = serviceProviderCache.findBySubStoreUserIdAndShopId(
                subStore.getUserId(), subStore.getShopId());
        if (serviceProvider == null) {
            throw new RuntimeException("门店对应的服务商不存在，请更换门店进行下单");
        }

        var serviceProviderUser = userCacheHolder.findByUserId(serviceProvider.getUserId()).orElse(null);
        if (serviceProviderUser == null) {
            throw new RuntimeException("门店对应的服务商用户不存在，请更换门店进行下单");
        }
        if (serviceProviderUser.getStatus() == null) {
            throw new RuntimeException("门店对应的服务商用户状态无效，请更换门店进行下单");
        }
        if (UserStatus.FROZEN.value() == serviceProviderUser.getStatus()) {
            throw new RuntimeException("门店对应的服务商用户已被冻结，请更换门店进行下单");
        }
    }

    private void checkGuiderUser(Long guiderUserId) {
        var user = userCacheHolder.findByUserId(guiderUserId).orElse(null);
        if (user == null) {
            throw new RuntimeException("导购对应的用户不存在，请更换导购进行下单");
        }

        if (user.getStatus() == null) {
            throw new RuntimeException("导购对应的用户状态无效，请更换导购进行下单");
        }
        if (UserStatus.FROZEN.value() == user.getStatus()) {
            throw new RuntimeException("导购对应的用户已被冻结，请更换导购进行下单");
        }
    }

    /**
     * 用户下单后的事件发送
     *
     * @param shopOrderIds 订单创建结果（订单主体id)
     */
    private void orderCreatedEventSend(List<Long> shopOrderIds) {
        for (Long shopOrderId : shopOrderIds.stream().filter(Objects::nonNull).toList()) {
            EventSender.sendApplicationEvent(new OrderCreatedEvent(shopOrderId));
        }
    }

    /**
     * 根据传入的UserId作为查询会员价的依据 创建订单
     *
     * @param submittedOrder 订单信息
     * @param pid            查询会员价依据的用户Id
     * @return 订单号列表
     */
    @RequestMapping(value = "/api/membershipByPid/order", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Long> createMembershipByPid(@RequestBody SubmittedOrder
                                                    submittedOrder, @RequestParam(required = false) Long pid) {
        try {
            CommonUser user = UserUtil.requireLoginUser();
            RichOrder richOrder = richOrderMaker.full(submittedOrder, UserUtil.getCurrentUser());
            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                for (RichSku richSku : richSkusByShop.getRichSkus()) {
                    richSku.setPid(pid);
                }
            }

            //检查用户是否具有购买资格
            orderRuleEngine.canBuy(richOrder);

            //检查用户是否享有会员价并计算税费
            Predicate<RichSkusByShop> isBondedCheck = richSkusByShop -> richSkusByShop.getRichSkus().stream()
                    .map(RichSku::getSku).map(Sku::getType).anyMatch(Predicate.isEqual(1));
            boolean hasBonded = richOrder.getRichSkusByShops().stream().anyMatch(isBondedCheck);
            //含有保税商品，检查订购人实名认证信息
            checkPayerInfo(submittedOrder.getChannel(), richOrder, user, hasBonded);

            //检查商家营销活动的有效性
            promotionOngoingValidator.validate(richOrder);

            //检查优惠券是否符合使用规则
            for (CouponCheck couponCheck : couponChecks) {
                couponCheck.check(richOrder);
            }

            //检查用户是否可以享受对应的营销, 如果可以, 则计算营销, 需要实付的金额, 以及运费等
            charger.charge(richOrder, null);

            Response<List<Long>> rOrder = self.persistRichOrder(richOrder);

            if (!rOrder.isSuccess()) {
                log.error("failed to create {}, error code:{}", submittedOrder, rOrder.getError());
                throw new JsonResponseException(rOrder.getError());
            }

            final List<Long> shopOrderIds = rOrder.getResult();
            orderCreatedEventSend(shopOrderIds);
            return shopOrderIds;
        } catch (Exception e) {
            log.error("failed to create {}, cause:{}", submittedOrder, e);
            throw new JsonResponseException("order.create.fail");
        }
    }





    private void judgePaperNo(String paperNo) {
        if (!paperNo.matches(PAYER_ID_PATTERN)) {
            throw new JsonResponseException(400, "user.certification.paperNo.invalid");
        }
    }

    private void checkPayerInfo(Integer channel, RichOrder richOrder, CommonUser user, boolean hasBonded) {
        var channelEnum = OrderCreateChannelEnum.parse(channel);
        if (channelEnum == null) {
            return;
        }

        switch (channelEnum) {
            case PC_WEB -> {
                if (ObjectUtils.isEmpty(richOrder.getPayerName()) || ObjectUtils.isEmpty(richOrder.getPayerId())) {
                    log.warn("submitOrder has no payer info[{}], when submit order with bonded item", user.getId());
                    // 尝试使用默认的验证信息
                    if (ObjectUtils.isEmpty(richOrder.getReceiverInfo())) {
                        if (hasBonded) {
                            throw new JsonResponseException("请填写收货人的身份证信息, 因为是跨境商品");
                        } else {
                            return;
                        }
                    }
                    richOrder.setPayerId(richOrder.getReceiverInfo().getPaperNo());
                    richOrder.setPayerName(richOrder.getReceiverInfo().getReceiveUserName());
                    if (ObjectUtils.isEmpty(richOrder.getPayerId())) {
                        if (hasBonded) {
                            throw new JsonResponseException("请填写收货人的身份证信息, 因为是跨境商品");
                        } else {
                            return;
                        }
                    }
                }
                judgePaperNo(richOrder.getPayerId());

                //当前只针对pc网页端提交的订单做实名验证并保存
                realNameCertificationManager.saveUserCertification(richOrder.getBuyer().getId(), richOrder.getPayerName(), richOrder.getPayerId());
            }
            case WX_APP -> {
                Response<Optional<UserCertification>> userCertificationResponse = userCertificationReadService.findDefaultByUserId(user.getId());
                if (!userCertificationResponse.isSuccess()) {
                    log.error("failed to find default user certification by userId={}, error code: {}", user.getId(), userCertificationResponse.getError());
                    if (hasBonded) {
                        throw new JsonResponseException(userCertificationResponse.getError());
                    } else {
                        return;
                    }
                }
                Optional<UserCertification> userCertificationOptional = userCertificationResponse.getResult();
                if (userCertificationOptional.isPresent()) {
                    UserCertification userCertification = userCertificationOptional.get();
                    richOrder.setPayerName(userCertification.getPaperName());
                    richOrder.setPayerId(userCertification.getPaperNo());
                } else {
                    log.warn("user(id={}) has no default certification, when submit order with bonded item on wxa", user.getId());
                    if (hasBonded) {
                        throw new JsonResponseException("default.user.certification.not.exist");
                    }
                }
            }
        }
    }


    @GetMapping("/api/order/outFromList")
    public List<Map<String, String>> orderOutFromList() {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            throw new JsonResponseException("user.not.login");
        }
        Shop shop = shopCacheHolder.findShopById(commonUser.getShopId());
        Map<String, String> shopExtra = Optional.ofNullable(shop.getExtra()).orElseGet(HashMap::new);

        List<Map<String, String>> result = new ArrayList<>(8);

        BiFunction<String, String, Map<String, String>> getEntity = (name, value) -> ImmutableBiMap.of("name", name, "value", value);

        if (shop.getUserId().equals(commonUser.getId())) {
            result.add(getEntity.apply(new Translate("全部订单").toString(), ""));
            if (shop.getType() != 1) {
                result.add(getEntity.apply(new Translate("导入订单").toString(), OrderOutFrom.EXCEL_IMPORT.Code()));
            }
        }

        switch (shopExtra.getOrDefault(ShopExtra.SalesPattern.getCode(), "commonShop")) {
            case "subStore" ->
                    result.add(getEntity.apply(new Translate("门店订单").toString(), OrderOutFrom.SUB_STORE.Code()));
            case "ladderDistribution" ->
                    result.add(getEntity.apply(new Translate("分销订单").toString(), OrderOutFrom.LEVEL_Distribution.Code()));
            case "weShop" -> result.add(getEntity.apply(Translate.of("供销订单"), OrderOutFrom.WE_SHOP.Code()));
        }

        return result;
    }
}
