package moonstone.web.admin.inner;

import cn.hutool.core.collection.CollUtil;
import io.terminus.common.model.Paging;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.constants.CommonSqlConstant;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.order.service.ReceiverInfoWriteService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author：书生
 */
@Slf4j
@RestController
@RequestMapping("/api/inner/receiver-infos")
public class ReceiverInfoInnerController {


	@Resource
	private ReceiverInfoReadService receiverInfoReadService;

	@Resource
	private ReceiverInfoWriteService receiverInfoWriteService;


	/**
	 * 回填收货信息历史数据
	 *
	 * @return 是否成功
	 */
	@GetMapping("history/fill")
	public Result<String> fillHistory() {
		log.info("回填收货信息历史数据");
		int pageNum = 10000;
		int current = 1;
		for (; ; ) {
			Map<String, Object> query = new HashMap<>();
			int offset = (current - 1) * pageNum;
			query.put(CommonSqlConstant.OFFSET, offset);
			query.put(CommonSqlConstant.LIMIT, pageNum);
			Paging<ReceiverInfo> page = receiverInfoReadService.paging(query);
			if (current == 1) {
				log.info("当前需要查询的数据次数 {}", page.getTotal() % pageNum == 0 ? page.getTotal() / pageNum : page.getTotal() / pageNum + 1);
			}
			List<ReceiverInfo> receiverInfoList = page.getData();
			if (CollUtil.isEmpty(receiverInfoList)) {
				break;
			}
			for (ReceiverInfo receiverInfo : receiverInfoList) {
				receiverInfoWriteService.appendAreaId(receiverInfo);
			}
			receiverInfoWriteService.batchUpdateReceiverInfos(receiverInfoList);
			current++;
		}

		return Result.data("success");
	}


}
