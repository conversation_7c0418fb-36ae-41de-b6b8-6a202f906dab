package moonstone.web.admin.inner;


import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.mongo.ReqUrlStatistics;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/api/inner")
@Slf4j
@RestController
public class ReqUrlStatisticsController {

	@Resource
	private MongoTemplate mongoTemplate;


	/**
	 * 获取当前正在使用的url
	 * @return 当前正在使用的url
	 */
	@GetMapping("/used/url")
	public Result<List<ReqUrlStatistics>> getUsedUrl() {
		log.info("获取当前正在使用的url");
		List<ReqUrlStatistics> statistics = mongoTemplate.findAll(ReqUrlStatistics.class);
		return Result.data(statistics);
	}



}
