package moonstone.web.admin.inner;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.web.core.fileNew.dto.RefundAutoReqDto;
import moonstone.web.core.fileNew.logic.RefundLogic;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 退款内部接口
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("api/inner/refund")
public class RefundInnerController {


	@Resource
	private RefundLogic refundLogic;


	/**
	 * 自动发起退款
	 */
	@PostMapping("auto")
	public Result<Boolean> autoRefund(@RequestBody RefundAutoReqDto req) {
		log.info("社群模式下 内部调用自动发起退款 请求参数 {}", JSONUtil.toJsonStr(req));
		return Result.data(refundLogic.autoRefund(req));
	}

}
