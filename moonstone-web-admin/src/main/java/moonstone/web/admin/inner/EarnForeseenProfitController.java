package moonstone.web.admin.inner;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.enums.OrderOutFrom;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.ShopOrder;
import moonstone.web.core.events.trade.app.ForeseenProfitForOrderTakerApp;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.logic.BalanceDetailLogic;
import moonstone.web.core.fileNew.logic.ShopOrderLogic;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 内部接口
 */
@RestController
@Slf4j
@RequestMapping("api/inner")
public class EarnForeseenProfitController {

	@Resource
	private ForeseenProfitForOrderTakerApp foreseenProfitForOrderTakerApp;

	@Resource
	private ShopOrderLogic shopOrderLogic;

	@Resource
	private BalanceDetailLogic balanceDetailLogic;

	@Resource
	private RocketMQProducer rocketMQProducer;

	/**
	 * 手动生成订单早期收益
	 *
	 * @param req 请求参数
	 */
	@PostMapping("create/earn/foreseen/profit")
	public Result<Boolean> createEarnForeseenProfit(@RequestBody ForeseenProfitDto req) {
		log.info("手动生成订单早期收益 请求参数{}", JSONUtil.toJsonStr(req));
		Map<String, Object> query = BeanUtil.beanToMap(req);
		List<ShopOrder> shopOrderList = shopOrderLogic.list(query);
		log.info("查询到的订单数据 {} 条", shopOrderList.size());
		if (shopOrderList.size() < 50) {
			log.info("查询到的订单信息 {}", JSONUtil.toJsonStr(shopOrderList));
		}
		for (ShopOrder shopOrder : shopOrderList) {
			query = new HashMap<>();
			query.put("relatedId", shopOrder.getId());
			List<BalanceDetail> balanceDetailList = balanceDetailLogic.list(query);
			if (balanceDetailList.size() != 3) {
				if (balanceDetailList.size() != 9) {
					balanceDetailLogic.deleteByRelatedId(shopOrder.getId());
					foreseenProfitForOrderTakerApp.earnForeseenProfitForOrderPayment(shopOrder, OrderOutFrom.fromCode(shopOrder.getOutFrom()));
				}
			}
		}
		return Result.data(true);
	}

	/**
	 * 手动生成订单实际收益
	 * @param req 请求参数
	 */
	@PostMapping("create/actual/foreseen/profit")
	public Result<Boolean> createActualForeseenProfit(@RequestBody ForeseenProfitDto req) {
		log.info("手动生成订单实际收益 请求参数{}", JSONUtil.toJsonStr(req));
		Map<String, Object> query = BeanUtil.beanToMap(req);
		List<ShopOrder> shopOrderList = shopOrderLogic.list(query);
		log.info("查询到的订单数据 {} 条", shopOrderList.size());
		if (shopOrderList.size() < 50) {
			log.info("查询到的订单信息 {}", JSONUtil.toJsonStr(shopOrderList));
		}
		for (ShopOrder shopOrder : shopOrderList) {
			query = new HashMap<>();
			query.put("relatedId", shopOrder.getId());
			List<BalanceDetail> balanceDetailList = balanceDetailLogic.list(query);
			if (balanceDetailList.size() != 9) {
				balanceDetailLogic.deleteByRelatedId(shopOrder.getId());
				foreseenProfitForOrderTakerApp.earnForeseenProfitForOrderPayment(shopOrder, OrderOutFrom.fromCode(shopOrder.getOutFrom()));
				rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.GENERATE_ORDER_FORESEE_PROFIT_TAG, String.valueOf(shopOrder.getId()));
			}
		}
		return Result.data(true);
	}

}
@Data
class ForeseenProfitDto {
	/**
	 * 店铺id
	 */
	private Long shopId;

	/**
	 * 开始时间
	 */
	private Date orderTimeStart;

	/**
	 * 结束时间
	 */
	private Date orderTimeEnd;

	private List<Integer> status;

}