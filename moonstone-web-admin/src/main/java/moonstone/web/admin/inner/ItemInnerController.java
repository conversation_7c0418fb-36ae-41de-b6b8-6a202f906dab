package moonstone.web.admin.inner;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.component.dto.item.EditItem;
import moonstone.component.item.component.ItemReader;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 商品内部接口
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/inner/item")
public class ItemInnerController {
    
    @Resource
    private ItemReader itemReader;


    /**
     * 社群模式下 内部调用获取商品属性
     * @param itemId 商品id
     * @return 商品属性
     */
    @GetMapping("attr")
    public Result<EditItem> getItemAttr(@RequestParam("itemId") Long itemId) {
        log.info("社群模式下 内部调用获取商品属性 请求参数 {}", itemId);
        return Result.data(itemReader.getItemAttr(itemId));
    }

}
