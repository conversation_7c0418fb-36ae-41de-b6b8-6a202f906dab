/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.admin.item;

import com.google.common.base.Objects;
import com.google.common.base.Splitter;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.category.dto.ShopCategoryWithChildren;
import moonstone.category.service.ShopCategoryReadService;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.enums.BondedType;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.CopyUtil;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.component.dto.item.EditItem;
import moonstone.component.item.component.ItemReader;
import moonstone.component.item.component.ItemWriter;
import moonstone.component.item.component.TaxChecker;
import moonstone.delivery.model.DeliveryFeeTemplate;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.delivery.service.DeliveryFeeReadService;
import moonstone.item.api.InitialItemInfoFiller;
import moonstone.item.dto.*;
import moonstone.item.dto.paging.ItemCriteria;
import moonstone.item.emu.ThirdPartyItemType;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.*;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import moonstone.user.ext.UserTypeBean;
import moonstone.web.admin.item.component.ItemReadLogic;
import moonstone.web.core.config.FunctionSwitch;
import moonstone.web.core.constants.ParanaConfig;
import moonstone.web.core.events.item.ItemCreatedEvent;
import moonstone.web.core.events.item.ItemUpdateEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 运营后台商品管理接口
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-16
 */
@SuppressWarnings("UnstableApiUsage")
@RestController
@RequestMapping("/api/item")
@Slf4j
public class AdminItemApp {
    /// ## 添加平台商品创建,可以创建平台商品,包括自建与第三方商品
    ///
    /// 其中平台商品中,** status==-1 **下架状态,则可以上架,-2** 冻结 ** 状态则不能被商家上架

    @RpcConsumer
    ItemReadService itemReadService;
    @Autowired
    ItemReadLogic itemReadLogic;
    @RpcConsumer
    AdminItemReadService adminItemReadService;
    @RpcConsumer
    AdminItemWriteService itemWriteService;
    @Autowired
    FunctionSwitch functionSwitch;
    @Resource
    ItemWriter itemWriter;
    @Resource
    InitialItemInfoFiller initialItemInfoFiller;
    @RpcConsumer
    ThirdPartySkuStockReadService thirdPartySkuStockReadService;
    @RpcConsumer
    DeliveryFeeReadService deliveryFeeReadService;
    @RpcConsumer
    SkuReadService skuReadService;
    @Autowired
    ParanaConfig paranaConfig;
    @RpcConsumer
    ShopCategoryReadService shopCategoryReadService;
    @Resource
    ItemReader itemReader;
    @Autowired
    AdminItemApp This;
    @Resource
    TaxChecker taxChecker;
    @Resource
    private ItemCacheHolder itemCacheHolder;
    @Resource
    private UserTypeBean userTypeBean;
    @Resource
    private RecommendItemManager recommendItemManager;

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Item> findById(@RequestParam(name = "id") Long id) {
        Response<Item> rItem = itemReadService.findById(id);
        if (!rItem.isSuccess()) {
            log.error("failed to find item(id={}),error code:{}", id, rItem.getError());
        }
        return rItem;
    }

    @RequestMapping(value = "/status", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> updateItemStatus(@RequestParam(name = "id") Long id, @RequestParam(name = "status") Integer status) {
        Response<Boolean> r = This.updateStatus(id, status);
        if (!r.isSuccess()) {
            log.error("failed to update status to {} for item(id={}), error code:{} ",
                    status, id, r.getError());
        } else {
            EventSender.publish(new ItemUpdateEvent(id));
        }
        return r;
    }

    @RequestMapping(value = "/status/batch", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> batchUpdateStatus(@RequestParam(name = "ids") String ids, @RequestParam(name = "status") Integer status) {
        List<String> parts = Splitters.COMMA.splitToList(ids);
        if (CollectionUtils.isEmpty(parts)) {
            log.warn("no item ids specified, skip");
            return Response.ok(Boolean.TRUE);
        }
        List<Long> itemIds = Lists.newArrayListWithCapacity(parts.size());
        for (String part : parts) {
            itemIds.add(Long.parseLong(part));
        }
        Response<Boolean> r = This.batchUpdateStatus(itemIds, status);
        if (!r.isSuccess()) {
            log.error("failed to update status to {} for items(ids={}), error code:{} ",
                    status, ids, r.getError());
        } else {
            for (Long itemId : itemIds) {
                EventSender.publish(new ItemUpdateEvent(itemId));
            }
        }
        return r;
    }

    @RequestMapping(value = "/tags", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> updateTags(@RequestParam(name = "id") Long id, @RequestParam("tags") String tags) {
        Map<String, String> realTags = Splitter.on(',').withKeyValueSeparator(':').split(tags);
        Response<Boolean> r = This.tags(id, realTags);
        if (!r.isSuccess()) {
            log.error("failed to update tags to {} for item(id={}), error code:{} ",
                    tags, id, r.getError());
            throw new JsonResponseException(r.getError());
        } else {
            EventSender.publish(new ItemUpdateEvent(id));
        }
        return r;
    }

    @RequestMapping(value = "/paging-by", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Paging<Item>> pagingBy(@RequestParam(required = false) Long itemId,
                                           @RequestParam(required = false) Long userId,
                                           @RequestParam(required = false) Long shopId,
                                           @RequestParam(required = false) String itemName,
                                           @RequestParam(required = false) Integer status,
                                           @RequestParam(required = false) Integer type,
                                           @RequestParam(required = false) Integer pageNo,
                                           @RequestParam(required = false) Integer pageSize) {
        return adminItemReadService.findBy(itemId, userId, shopId, Stream.of(0L).collect(Collectors.toList()), itemName, status, type, pageNo, pageSize);
    }

    @RequestMapping(value = "/admin/setStatus")
    public Response<Boolean> setStatus(@RequestParam("ids[]") Long[] ids, @RequestParam Integer status) {
        List<Long> _ids = Arrays.asList(ids);
        //List<Long> _ids = Stream.of(ids.substring(1).substring(0,ids.length()-2).split(",")).map(Long::parseLong).collect(Collectors.toList());
        Response<List<Item>> rItems = itemReadService.findByIds(_ids);
        if (status == null) {
            throw new JsonResponseException("no.status.set");
        }
        if (!rItems.isSuccess() || _ids.size() == 0) {
            throw new JsonResponseException("item.find.fail");
        }
        if (rItems.getResult().parallelStream().anyMatch(item -> !item.getShopId().equals(0L))) {
            throw new JsonResponseException("item.not.all.admin");
        }
        Response<Boolean> ok = Response.ok();
        for (Long id : _ids) {
            ok = This.updateStatus(id, status);
            if (!ok.isSuccess()) {
                return ok;
            }
        }
        return ok;
    }

    @RequestMapping(value = "/seller/paging-delivery-fee-template", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<DeliveryFeeTemplate> pagingDeliveryFeeTemplate(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                                 @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        commonUser.setShopId(0L);
        Response<Paging<DeliveryFeeTemplate>> pagingResp = deliveryFeeReadService.pagingDeliveryFeeTemplate(commonUser, pageNo, pageSize, null);
        if (!pagingResp.isSuccess()) {
            log.error("seller(shop id={}) paging delivery fee template failed with pageNo:{},pageSize:{} error:{}",
                    commonUser.getShopId(), pageNo, pageSize, pagingResp.getError());
            throw new JsonResponseException(pagingResp.getError());
        }
        return pagingResp.getResult();
    }

    @RequestMapping(value = "/admin/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Paging<ItemForList>> pagingItems(@RequestParam(required = false) String itemCode,
                                                     @RequestParam(required = false) Long itemId,
                                                     @RequestParam(required = false) String itemName,
                                                     @RequestParam(required = false) Integer status,
                                                     @RequestParam(required = false) String statuses,
                                                     @RequestParam(required = false) Integer type,
                                                     @RequestParam(required = false) String typees,
                                                     @RequestParam(required = false) Integer pageNo,
                                                     @RequestParam(required = false) Integer pageSize) {
        Long shopId = 0L;
        CommonUser commonUser = new CommonUser();
        commonUser.setShopId(0L);
        // TODO: 暂时用黑魔法, 等 node 完善后重构
        Response<Paging<Item>> rpItem = itemReadService.findBy(commonUser, null, itemCode, itemId, itemName, statuses, status, type, typees,
                null, null, null, pageNo, pageSize);
        if (!rpItem.isSuccess()) {
            log.error("failed to paging items by shopId={}, itemCode={}, itemId={}, itemName={}, status={}, statuses={}, type={}, pageNo={}, pageSize={}, error code:{}",
                    shopId, itemCode, itemId, itemName, status, statuses, type, pageNo, pageSize, rpItem.getError());
            throw new JsonResponseException(rpItem.getError());
        }
        Paging<Item> pItem = rpItem.getResult();
        List<ItemForList> itemForLists = new ArrayList<>();
        for (Item item : pItem.getData()) {
            ItemForList itemForList = CopyUtil.copy(item, ItemForList.class);
            itemForList.setWxaUrl(paranaConfig.getParanaWxaUrl() + "/goods/detail?storeId=" + shopId + "&skuSn=" + item.getId());
            itemForLists.add(itemForList);
        }
        return Response.ok(new Paging<>(pItem.getTotal(), itemForLists));
    }

    @RequestMapping(value = "/seller/items", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long createItem(@RequestBody FullItem fullItem) {
        Item item = fullItem.getItem();

        if (item.getIsThirdPartyItem() == null || (item.getIsThirdPartyItem() != 0 && item.getIsThirdPartyItem() != 1)) {
            throw new JsonResponseException("item.isThirdPartyItem.illegal");
        }

        if (item.getIsThirdPartyItem() == 1) {
            //判断第三方平台商品是否传了推单平台
            for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                Sku sku = skuWithCustom.getSku();
                if (sku.getTags() == null || sku.getTags().get("pushSystem") == null) {
                    log.error("bonded sku[{}] has no pushSystem", sku.getId());
                    throw new JsonResponseException("sku.bonded.no.pushSystem");
                }
            }
            //检查该第三方商品库存是否充足
            if (functionSwitch.getThirdPartyStockLimit()) {
                checkThirdPartyStock(item.getShopId(), fullItem.getSkuWithCustoms());
            }
        }

        checkDeliveryFeeTemplate(fullItem.getItemDeliveryFee());
        //关键信息防止被伪造

        item.setShopId(0L);
        item.setShopName(new Translate("system.create.item.shop.name").toString());
        item.setTags(null);

        //完善商品的默认信息
        initialItemInfoFiller.fill(item);

        item.setExtra(item.getExtra());


        //计算商品价格区间
        try {
            extractInfoFromSkus(item, fullItem.getSkuWithCustoms());
        } catch (Exception e) {
            log.error("bad sku info", e);
            //throw new JsonResponseException("illegal.sku.info");
            throw new JsonResponseException(e.getMessage());
        }

        Response<Long> rItemId = This.create(fullItem);
        if (!rItemId.isSuccess()) {
            log.error("failed to create {}, error code:{}", fullItem, rItemId.getError());
            throw new JsonResponseException(rItemId.getError());
        }
        //发出商品创建事件
        EventSender.sendApplicationEvent(new ItemCreatedEvent(rItemId.getResult()));

        return rItemId.getResult();
    }

    private void extractInfoFromSkus(Item item, List<SkuWithCustom> skuWithCustoms) {

        //计算商品库存, 只考虑不分仓存储
        if (Objects.equal(item.getStockType(), 0)) {
            int stockQuantity = 0;
            for (SkuWithCustom skuWithCustom : skuWithCustoms) {
                Sku sku = skuWithCustom.getSku();
                if (sku.getStockQuantity() == null) {
                    throw new JsonResponseException("stock.empty");
                }
                if (sku.getStockQuantity() < 0) {
                    throw new IllegalArgumentException("sku.stock.negative");
                }
                stockQuantity += sku.getStockQuantity();
            }
            item.setStockQuantity(stockQuantity);
        }

        int highPrice = -1;
        int lowPrice = -1;

        for (SkuWithCustom skuWithCustom : skuWithCustoms) {
            Sku sku = skuWithCustom.getSku();
            if (sku.getPrice() != null) {
                if (sku.getPrice() <= 0) {
                    throw new IllegalArgumentException("sku.price.need.positive");
                }
                if (sku.getPrice() > highPrice) {
                    highPrice = sku.getPrice();
                }
                if (sku.getPrice() < lowPrice || lowPrice < 0) {
                    lowPrice = sku.getPrice();
                }
            }
            //判断税费是否为null
            if (!shapeMap(sku.getTags()).getOrDefault("pushSystem", "").isEmpty()) {
                if (taxChecker.getRate(Integer.parseInt(sku.getTags().get("pushSystem")), sku, skuWithCustom.getSkuCustom()) == null) {
                    log.error("tax can't be null of Sku(id:{})", sku.getId());
                    throw new JsonResponseException("tax.find.fail");
                }
            }
        }
        if (highPrice > 0) {
            item.setHighPrice(highPrice);
        }
        if (lowPrice > 0) {
            item.setLowPrice(lowPrice);
        }
    }

    private Map<String, String> shapeMap(Map<String, String> map) {
        return map == null ? new TreeMap<>() : map;
    }

    private void checkThirdPartyStock(Long shopId, List<SkuWithCustom> skuWithCustoms) {
        for (SkuWithCustom skuWithCustom : skuWithCustoms) {
            boolean flag = false;
            String pushSystemStr = skuWithCustom.getSku().getTags().get("pushSystem");
            //拆成列表
            if (pushSystemStr.endsWith(",")) {
                pushSystemStr = pushSystemStr.substring(0, pushSystemStr.length() - 1);
            }
            List<String> stringList = Splitter.on(",").splitToList(pushSystemStr);
            List<Integer> pushSystems = stringList.stream().map(Integer::parseInt).collect(Collectors.toList());

            Integer totalAuthenticStock = 0;
            for (Integer thirdPartyId : pushSystems) {
                Response<List<ThirdPartySkuStock>> response =
                        thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(shopId, thirdPartyId, skuWithCustom.getSku().getOuterSkuId());
                if (!response.isSuccess()) {
                    log.error("fail to find thirdPartySkuStock by thirdPartyId={}, outerSkuId={}",
                            thirdPartyId, skuWithCustom.getSku().getOuterSkuId());
                    throw new JsonResponseException("thirdParty.sku.stock.find.fail");
                }
                for (ThirdPartySkuStock thirdPartySkuStock : response.getResult()) {
                    totalAuthenticStock += thirdPartySkuStock.getAuthenticStock();
                }
                if (skuWithCustom.getSku().getStockQuantity() <= totalAuthenticStock) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                log.error("outSku(outerSkuId={}) inventory shortage", skuWithCustom.getSku().getOuterSkuId());
                throw new JsonResponseException(513, "thirdParty.sku.stock.shortage");
            }
        }
    }

    private void checkDeliveryFeeTemplate(ItemDeliveryFee itemDeliveryFee) {
        if (itemDeliveryFee == null) {
            return;
        }
        final Long deliveryFeeTemplateId = itemDeliveryFee.getDeliveryFeeTemplateId();
        if (deliveryFeeTemplateId == null) {
            return;
        }

        Response<DeliveryFeeTemplate> findResp = deliveryFeeReadService.findDeliveryFeeTemplateById(deliveryFeeTemplateId);
        if (!findResp.isSuccess()) {
            log.error("fail to find delivery fee template by id:{},cause:{}", deliveryFeeTemplateId, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        DeliveryFeeTemplate deliveryFeeTemplate = findResp.getResult();

        CommonUser commonUser = UserUtil.getCurrentUser();
        if (!Objects.equal(deliveryFeeTemplate.getShopId(), commonUser.getShopId())) {
            log.error("the delivery fee template(id={}) not belong to seller(shop id={})",
                    deliveryFeeTemplateId, commonUser.getShopId());
            throw new JsonResponseException("delivery.fee.template.not.belong.to.seller");
        }
    }

    private void checkIfNotBelong(Item item) {
        if (java.util.Objects.equals(0L, item.getShopId())) {
            throw new JsonResponseException("item.not.belong.to.user");
        }
    }

    @RequestMapping(value = "/seller/items", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean updateItem(@RequestBody FullItem fullItem) {
        Long shopId = 0L;

        Item item = fullItem.getItem();

        checkIfNotBelong(item);

        if (java.util.Objects.nonNull(item.getIsThirdPartyItem())) {
            Stream.of(ThirdPartyItemType.values()).filter(type -> item.getIsThirdPartyItem() == type.getType())
                    .findFirst().orElseThrow(() -> new JsonResponseException("item.isThirdPartyItem.illegal"));
        }

        if (BondedType.fromInt(item.getIsBonded()).isBonded()) {
            //判断保税商品是否传了报关平台
            for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                Sku sku = skuWithCustom.getSku();
                if (sku.getTags() == null || sku.getTags().get("pushSystem") == null) {
                    log.error("bonded sku[{}] has no pushSystem", sku.getId());
                    throw new JsonResponseException("sku.bonded.no.pushSystem");
                }
            }
            //检查该第三方商品库存是否充足
            if (functionSwitch.getThirdPartyStockLimit()) {
                checkThirdPartyStock(item.getShopId(), fullItem.getSkuWithCustoms());
            }
        }

        checkDeliveryFeeTemplate(fullItem.getItemDeliveryFee());

        //设置为平台商品
        item.setShopId(0L);
        item.setShopName(new Translate("system.create.item.shop.name").toString());
        //关键信息防止被伪造
        item.setTags(null);

        final Long itemId = item.getId();
        Response<Item> rItem = itemReadService.findById(itemId);
        if (!rItem.isSuccess()) {
            log.error("failed to find item(id={}), error code:{}", itemId, rItem.getError());
            throw new JsonResponseException(rItem.getError());
        }

        if (!Objects.equal(rItem.getResult().getShopId(), shopId)) {
            log.error("the item(id={}) is not belong to seller(shop id={})", itemId, shopId);
            throw new JsonResponseException("item.not.belong.to.seller");
        }

        //澳新商品、微分销商品标志
        try {
            Map<String, String> existExtra = rItem.getResult().getExtra();
            Map<String, String> toUpdateExtra = item.getExtra();
            String sellInWeShop = existExtra.get(DistributionConstants.SELL_IN_WE_SHOP);

            if (!ObjectUtils.isEmpty(sellInWeShop)) {
                toUpdateExtra.put(DistributionConstants.SELL_IN_WE_SHOP, sellInWeShop);
                for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                    Sku sku = skuWithCustom.getSku();
                    Response<List<Sku>> rExistSkus = skuReadService.findSkusByItemId(itemId);
                    if (!rExistSkus.isSuccess()) {
                        log.error("failed to find skus by itemId={}, error code: {}", itemId, rExistSkus.getError());
                        throw new JsonResponseException(rExistSkus.getError());
                    }
                    Map<Long, Sku> skuById = Maps.uniqueIndex(rExistSkus.getResult(), Sku::getId);
                    Map<String, String> extra = sku.getExtraMap();
                    Map<String, Integer> extraPrice = sku.getExtraPrice();
                    Sku existSku = skuById.get(sku.getId());
                    String skuSellInWeShop = existSku.getExtraMap().get(DistributionConstants.SELL_IN_WE_SHOP);
                    Integer skuDistributionPrice = existSku.getExtraPrice().get(DistributionConstants.SKU_DISTRIBUTION_PRICE);
                    Integer skuProfit = existSku.getExtraPrice().get(DistributionConstants.SKU_PROFIT);
                    if (!ObjectUtils.isEmpty(skuSellInWeShop)) {
                        extra.put(DistributionConstants.SELL_IN_WE_SHOP, skuSellInWeShop);
                    }
                    if (!ObjectUtils.isEmpty(skuDistributionPrice)) {
                        extraPrice.put(DistributionConstants.SKU_DISTRIBUTION_PRICE, skuDistributionPrice);
                    }
                    if (!ObjectUtils.isEmpty(skuProfit)) {
                        extraPrice.put(DistributionConstants.SKU_PROFIT, skuProfit);
                    }
                    sku.setExtraMap(extra);
                    sku.setExtraPrice(extraPrice);
                }
            }
            item.setExtra(toUpdateExtra);
        } catch (Exception e) {
            log.error("fail to handle exist extra or extraPrice when update item(fullItem: {}), cause: {}", fullItem, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("item.update.handle.exist.fail");
        }

        item.setStockType(rItem.getResult().getStockType());
        //避免重复计算默认值啥的
        extractInfoFromSkus(item, fullItem.getSkuWithCustoms());

        Response<Boolean> rUpdate = This.update(fullItem);
        if (!rUpdate.isSuccess()) {
            log.error("failed to update {}, error code:{}", fullItem, rUpdate.getError());
            throw new JsonResponseException(rUpdate.getError());
        }

        EventSender.publish(new ItemUpdateEvent(itemId));
        return rUpdate.getResult();
    }

    @RequestMapping(value = "/{itemId}/for-edit", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<EditItem> findForEdit(@PathVariable Long itemId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null) {
            log.error("user not login when find item for edit.");
            throw new JsonResponseException(401, "user.not.login");
        }
        commonUser.setShopId(0L);
        return itemReader.findForEdit(commonUser, itemId);
    }

    @RequestMapping(value = "/{itemId}/detail-info", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<ViewedItemDetailInfo> findItemDetailInfoByItemId(@PathVariable Long itemId) {
        return Optional.ofNullable(itemCacheHolder.findViewDetail(itemId))
                .map(Response::ok)
                .orElseGet(() -> Response.fail(Translate.of("商品查询失败")));
    }

    @RequestMapping(value = "/seller/items/item-export", method = RequestMethod.GET)
    public void exportItems(ItemCriteria criteria, HttpServletRequest request, HttpServletResponse response) {
        criteria.setShopId(0L);
        criteria.setParanaWxaUrl(paranaConfig.getParanaWxaUrl());
        itemReadLogic.exportExcel(criteria, request, response);
    }

    @RequestMapping(value = "/fullTree")
    public Response<List<ShopCategoryWithChildren>> findEntireTreeByShopId(@RequestParam("shopId") Long shopId) {
        return shopCategoryReadService.findEntireTreeByShopId(shopId);
    }

    /**
     * 平台配置推荐商品
     *
     * @param itemIds 推荐商品ID，多个以逗号隔开；必填
     */
    @RequestMapping(value = "/admin/recommend", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> recommendItems(@RequestParam String itemIds) {
        // 小二权限校验
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || (!userTypeBean.isAdmin(commonUser) && !userTypeBean.isOperator(commonUser))) {
            log.error("can not recommend items(id={})", itemIds);
            throw new JsonResponseException("item.recommend.not.authorized");
        }
        List<Long> itemIdList = Splitters.splitToLong(itemIds, Splitters.COMMA);
        // 限制最大推荐商品数
        int maxItemNum = 5;
        if (itemIdList.size() > maxItemNum) {
            log.error("item recommend num({}) too big", itemIdList.size());
            throw new JsonResponseException("item.recommend.num.too.big");
        }
        // 获取对应有效商品信息列表
        int itemStatus = 1; // 商品状态：上架
        List<Item> itemInfoList = new ArrayList<>(itemIdList.size());
        itemIdList.forEach(itemId -> {
            //由于参数有顺序，需要依次单独查询获得
            Response<List<Item>> itemsResponse = itemReadService.findByIdsAndStatus(new ArrayList<Long>(1) {{
                add(itemId);
            }}, itemStatus);
            if (!itemsResponse.isSuccess()) {
                log.warn("failed to find item(id={}), error code:{}", itemId, itemsResponse.getError());
            }
            if (!CollectionUtils.isEmpty(itemsResponse.getResult())) {
                itemInfoList.add(itemsResponse.getResult().get(0));
            }
        });
        // 只对无有效商品时抛错，对有效商品数小于预期配置的商品数时，不做抛错处理
        if (CollectionUtils.isEmpty(itemInfoList)) {
            log.error("failed to find valid items(id={})", itemIds);
            throw new JsonResponseException(HttpStatus.BAD_REQUEST.value(), "please check your items");
        }
        // 推荐商品列表存入mongo
        List<RecommendItem> recommendItemList = new ArrayList<>(itemInfoList.size());
        itemInfoList.forEach(item -> recommendItemList.add(new RecommendItem(item.getId(), item.getCategoryId(), item.getShopId(),
                item.getName(), item.getMainImage_(), item.getLowPrice())));
        Either<Boolean> createResult = recommendItemManager.createRecommendItems(new RecommendItems(recommendItemList, Boolean.TRUE, null));

        return Response.ok(createResult.take());
    }

    /**
     * 删除平台配置的推荐商品
     */
    @RequestMapping(value = "/admin/recommend", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> removeRecommendItems() {
        // 小二权限校验
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || (!userTypeBean.isAdmin(commonUser) && !userTypeBean.isOperator(commonUser))) {
            log.error("can not delete recommend because user not authorized");
            throw new JsonResponseException("item.recommend.not.authorized");
        }
        Either<Boolean> removeResult = recommendItemManager.removeRecommendItems(Boolean.TRUE, null);
        return Response.ok(removeResult.take());
    }

    // 以下为事务独立处理方法，避免eventBus触发事件幻读
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Boolean> batchUpdateStatus(List<Long> ids, Integer status) {
        return itemWriteService.batchUpdateStatus(ids, status);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Boolean> updateStatus(Long id, Integer status) {
        return itemWriteService.updateStatus(id, status);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Boolean> tags(Long itemId, Map<String, String> tags) {
        return itemWriteService.tags(itemId, tags);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Long> create(FullItem fullItem) {
        return itemWriter.create(fullItem);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Boolean> update(FullItem fullItem) {
        return itemWriter.update(fullItem);
    }
}
