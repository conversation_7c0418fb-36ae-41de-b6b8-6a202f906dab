package moonstone.web.admin.item.component;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.common.collect.Lists;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.enums.BondedType;
import moonstone.common.utils.CopyUtil;
import moonstone.common.utils.Json;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.item.dto.SkuWithItemForList;
import moonstone.item.dto.paging.ItemCriteria;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.web.core.exports.common.DefaultExporter;
import moonstone.web.core.exports.common.Exporter;
import moonstone.web.core.exports.common.SheetMergeUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.*;

/**
 * Author:  CaiZhy
 * Date:    2019/2/20
 */
@Slf4j
@Component
@Deprecated
public class ItemReadLogic {
    @Autowired
    ItemReadService itemReadService;
    @Autowired
    SkuReadService skuReadService;
    @Autowired
    Exporter exporter;
    @Autowired
    ShopReadService shopReadService;
    @Autowired
    ShopCacheHolder shopCacheHolder;

    public void exportExcel(ItemCriteria criteria, HttpServletRequest request, HttpServletResponse response) {
        try {
            if (ObjectUtils.isEmpty(criteria.getSortBy())) {
                criteria.setSortBy("id");
            }
            if (criteria.getSortType() == null) {
                criteria.setSortType(2);
            }
            Response<List<Item>> rItemList = itemReadService.list(criteria);
            if (!rItemList.isSuccess()) {
                log.error("failed to list items, param={}, error code: {}", criteria, rItemList.getError());
                throw new JsonResponseException(rItemList.getError());
            }
            List<Item> items = rItemList.getResult();
            List<Long> itemIds = Lists.transform(items, Item::getId);
            Response<List<Sku>> rSkus = skuReadService.findSkusByItemIds(itemIds);
            if (!rSkus.isSuccess()) {
                log.error("failed to find skus by itemIds={}, error code: {}", itemIds, rSkus.getError());
                throw new JsonResponseException(rSkus.getError());
            }
            List<Sku> skus = rSkus.getResult();
            Map<Long, List<Sku>> skusByItemId = new HashMap<>();
            for (Sku sku : skus) {
                List<Sku> skuList = skusByItemId.get(sku.getItemId());
                if (skuList == null) {
                    skuList = new ArrayList<>();
                }
                skuList.add(sku);
                skusByItemId.put(sku.getItemId(), skuList);
            }
            List<SkuWithItemForList> data = new ArrayList<>();
            for (Item item : items) {
                List<Sku> skuList = skusByItemId.get(item.getId());
                if (!CollectionUtils.isEmpty(skuList)) {
                    for (Sku sku : skuList) {
                        SkuWithItemForList skuWithItemForList = CopyUtil.copy(sku, SkuWithItemForList.class);
                        Map<String, Integer> extraPrice = sku.getExtraPrice() == null ? new HashMap<>() : sku.getExtraPrice();
                        Map<String, String> skuExtra = sku.getExtraMap() == null ? new HashMap<>() : sku.getExtraMap();
                        Map<String, String> itemExtra = item.getExtra() == null ? new HashMap<>() : item.getExtra();
                        skuWithItemForList.setDistributionPrice(extraPrice.get(DistributionConstants.SKU_DISTRIBUTION_PRICE));
                        skuWithItemForList.setProfit(extraPrice.get(DistributionConstants.SKU_PROFIT));
                        String attrsStr = "";
                        if (sku.getAttrs() != null) {
                            for (SkuAttribute skuAttribute : sku.getAttrs()) {
                                if (!"".equals(attrsStr)) {
                                    attrsStr += ", ";
                                }
                                attrsStr += skuAttribute.getAttrKey() + ": " + skuAttribute.getAttrVal();
                            }
                        }
                        skuWithItemForList.setAttrsStr(attrsStr);
                        skuWithItemForList.setUnitQuantity(ObjectUtils.isEmpty(skuExtra.get("unitQuantity")) ? null : Integer.parseInt(skuExtra.get("unitQuantity")));
                        skuWithItemForList.setSkuStatus(statusToString(sku.getStatus()));
                        skuWithItemForList.setItemCode(item.getItemCode());
                        skuWithItemForList.setIsBonded(BondedType.fromInt(item.getIsBonded()).isBonded() ? "保税" : "完税");
                        skuWithItemForList.setIsThirdPartyItem(item.getIsThirdPartyItem() == 1 ? "第三方商品" : "自建商品");
                        skuWithItemForList.setShopName(item.getShopName());
                        skuWithItemForList.setName(item.getName());
                        skuWithItemForList.setLowPrice(item.getLowPrice());
                        skuWithItemForList.setHighPrice(item.getHighPrice());
                        skuWithItemForList.setItemStockQuantity(item.getStockQuantity());
                        skuWithItemForList.setSaleQuantity(item.getSaleQuantity());
                        String sellInWeShop = itemExtra.get(DistributionConstants.SELL_IN_WE_SHOP);
                        skuWithItemForList.setSellInWeShop("true".equals(sellInWeShop) ? "是" : "否");
                        skuWithItemForList.setItemStatus(statusToString(item.getStatus()));
                        skuWithItemForList.setWxaUrl(criteria.getParanaWxaUrl() + "/goods/detail?storeId=" + criteria.getShopId() + "&skuSn=" + item.getId());
                        data.add(skuWithItemForList);
                    }
                }
            }
            DefaultExporter.setHttpServletResponse(request, response, "商家商品列表" + new DateTime(new Date()).toString("yyyyMMddHHmmss"));
            Workbook workbook = exporter.exportWithDecorate(data, SkuWithItemForList.class, this::transformUrlIntoQrCode);
            SheetMergeUtil.mergeWorkbook(workbook).write(response.getOutputStream());
        } catch (Exception e) {
            log.error("export item excel fail, param={}", criteria, e);
            throw Translate.exceptionOf("导出错误");
        }
    }

    public void transformUrlIntoQrCode(Row row, SkuWithItemForList skuWithItemForList) {
        val draw = row.getSheet().createDrawingPatriarch();
        try {
            if (skuWithItemForList == null) {
                return;
            }
            Long shopId = skuWithItemForList.getShopId();
            String url = skuWithItemForList.getWxaUrl();
            Shop shop = shopCacheHolder.findShopById(shopId);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BufferedImage logoImage = ImageIO.read(new URL(shop.getImageUrl()));
            BufferedImage image = QrCodeUtil.generate(url, QrConfig.create().setImg(logoImage));
            ImageIO.write(image, "png", outputStream);
            draw.createPicture(draw.createAnchor(0, 0, 0, 0, 8, row.getRowNum(), 8, row.getRowNum()), row.getSheet().getWorkbook().addPicture(outputStream.toByteArray(), HSSFWorkbook.PICTURE_TYPE_PNG));
        } catch (Exception ex) {
            log.error("{} fail to generate qrCode[Item => {}]", LogUtil.getClassMethodName(), Json.toJson(skuWithItemForList), ex);
        }
    }

    /**
     * 合并sheet, 目前已抛弃
     * 由于出现流式表单 所以目前不允许对内存中的sheet进行上下文标记处理
     *
     * @param sheet 表单
     * @return sheet
     */
    @Deprecated
    private Sheet mergeSheet(Sheet sheet) {
        Row titleRow = sheet.getRow(sheet.getFirstRowNum());
        int scanStartRowNum = sheet.getFirstRowNum() + 1;
        int scanEndRowNum = sheet.getLastRowNum();
        int scanStartCellNum = titleRow.getFirstCellNum();
        int scanEndCellNum = titleRow.getLastCellNum() - 1;

        int sameFirstR = scanStartRowNum;
        int r;
        for (r = scanStartRowNum + 1; r <= scanEndRowNum; r++) {
            String lastValue = sheet.getRow(r - 1).getCell(0).getStringCellValue();
            String value = sheet.getRow(r).getCell(0).getStringCellValue();
            if (!lastValue.equals(value)) {
                for (int c = scanStartCellNum; c <= scanEndCellNum; c++) {
                    if (!titleRow.getCell(c).getStringCellValue().contains("sku")) {
                        sheet.addMergedRegion(new CellRangeAddress(sameFirstR, r - 1, c, c));
                    }
                }
                sameFirstR = r;
            }
        }
        if (r != sameFirstR) {
            for (int c = scanStartCellNum; c <= scanEndCellNum; c++) {
                if (!titleRow.getCell(c).getStringCellValue().contains("sku")) {
                    sheet.addMergedRegion(new CellRangeAddress(sameFirstR, r - 1, c, c));
                }
            }
        }

        return sheet;
    }

    private String statusToString(Integer status) {
        String skuStatus;
        switch (status) {
            case 1:
                skuStatus = "上架";
                break;
            case -1:
                skuStatus = "下架";
                break;
            case -2:
                skuStatus = "冻结";
                break;
            case -3:
                skuStatus = "删除";
                break;
            default:
                skuStatus = "非法状态";
        }
        return skuStatus;
    }
}
