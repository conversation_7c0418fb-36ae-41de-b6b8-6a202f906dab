<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>moonstone</groupId>
    <artifactId>moonstone-mall</artifactId>
    <version>1.0.0.RELEASE</version>
    <modules>
        <module>moonstone-common</module>
        <module>moonstone-item-api</module>
        <module>moonstone-user-api</module>
        <module>moonstone-trade-api</module>
        <module>moonstone-decoration-api</module>
        <module>moonstone-distribution-api</module>
        <module>moonstone-category</module>
        <module>moonstone-item</module>
        <module>moonstone-user</module>
        <module>moonstone-trade</module>
        <module>moonstone-decoration</module>
        <module>moonstone-distribution</module>
        <module>moonstone-web-core</module>
        <module>moonstone-web-front</module>
        <module>moonstone-web-admin</module>
        <module>moonstone-web-distribution</module>
        <module>showcase</module>
        <module>showcase-admin</module>
        <module>showcase-we</module>
        <module>moonstone-op</module>
        <module>moonstone-project</module>
    </modules>
    <packaging>pom</packaging>

    <name>moonstone-mall</name>
    <description>moonstone mall project</description>

    <properties>
        <soul.mvc.version>2.1.3-20200729.070106-17</soul.mvc.version>
        <scala.compatible.version>3.6.RELEASE</scala.compatible.version>
        <moonstone.version>0.0.5.RELEASE</moonstone.version>
        <vertx.version>4.2.5</vertx.version>
        <hazelcast.version>4.0.2</hazelcast.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <terminus-search.version>3.1.RELEASE</terminus-search.version>
        <terminus-boot.version>1.5.RELEASE</terminus-boot.version>
        <logback.version>1.2.3</logback.version>
        <hikari.version>2.4.6</hikari.version>
        <poi.version>4.1.2</poi.version>
        <terminus-pay.version>********.RELEASE</terminus-pay.version>
        <bulid.version>3.8.1</bulid.version>
        <scala.version>2.13.1</scala.version>
        <fastjson.version>1.2.79</fastjson.version>
        <msgpack.version>0.8.22</msgpack.version>
        <java.version>17</java.version>
        <lombok.version>1.18.22</lombok.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.msgpack</groupId>
            <artifactId>msgpack-core</artifactId>
            <version>${msgpack.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.2.5</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.hazelcast</groupId>
            <artifactId>hazelcast</artifactId>
            <version>${hazelcast.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.22</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>29.0-jre</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.1</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.22</version>
            </dependency>
            <dependency>
                <groupId>com.graphql-java</groupId>
                <artifactId>graphql-java</artifactId>
                <version>14.1</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.8.4</version>
            </dependency>
            <dependency>
                <groupId>blue.sea.moonstone</groupId>
                <artifactId>moonstone</artifactId>
                <version>${moonstone.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vertx</groupId>
                <artifactId>vertx-core</artifactId>
                <version>${vertx.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vertx</groupId>
                <artifactId>vertx-mongo-client</artifactId>
                <version>${vertx.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vertx</groupId>
                <artifactId>vertx-config</artifactId>
                <version>${vertx.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vertx</groupId>
                <artifactId>vertx-mail-client</artifactId>
                <version>${vertx.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vertx</groupId>
                <artifactId>vertx-hazelcast</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.hazelcast</groupId>
                        <artifactId>hazelcast</artifactId>
                    </exclusion>
                </exclusions>
                <version>${vertx.version}</version>
            </dependency>
            <dependency>
                <groupId>io.vertx</groupId>
                <artifactId>vertx-mysql-client</artifactId>
                <version>${vertx.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.6.4</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>2.6.4</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>io.spring.platform</groupId>-->
<!--                <artifactId>platform-bom</artifactId>-->
<!--                <version>2.0.7.RELEASE</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikari.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus</groupId>
                <artifactId>terminus-common</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>

            <!-- terminus-boot -->
            <dependency>
                <groupId>io.terminus.boot</groupId>
                <artifactId>spring-boot-starter-mybatis</artifactId>
                <version>1.5.1.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>1.3.0</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>io.terminus.boot</groupId>-->
            <!--                <artifactId>terminus-spring-boot-starter-session</artifactId>-->
            <!--                <version>${terminus-boot.version}</version>-->
            <!--            </dependency>-->
            <!-- terminus-search -->
            <dependency>
                <groupId>io.terminus.search</groupId>
                <artifactId>search-api</artifactId>
                <version>${terminus-search.version}</version>
            </dependency>
            <!-- current (moonstone-mall) -->
            <dependency>
                <groupId>moonstone</groupId>
                <artifactId>moonstone-item-api</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>moonstone</groupId>
                <artifactId>moonstone-trade-api</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>moonstone</groupId>
                <artifactId>moonstone-user-api</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>moonstone</groupId>
                <artifactId>moonstone-decoration-api</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>moonstone</groupId>
                <artifactId>moonstone-distribution-api</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>moonstone</groupId>
                <artifactId>moonstone-web-distribution</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>moonstone</groupId>
                <artifactId>moonstone-web-front</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <!-- xls related -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-excelant</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>io.terminus.pay</groupId>
                <artifactId>terminus-pay-api</artifactId>
                <version>${terminus-pay.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>javax.servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-test</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>net.sf.dozer</groupId>
                        <artifactId>dozer</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.scala-lang/scala-library -->
            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>3.31.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-data-30</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-27</artifactId>
                <version>3.31.0</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>4.39.70.ALL</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>imm20200930</artifactId>
                <version>4.5.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>aliyun</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>
        <repository>
            <id>danding</id>
            <name>danding private repository</name>
            <url>http://mvn.yang800.cn/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>danding</id>
            <name>danding release resp</name>
            <url>http://mvn.yang800.cn/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>danding</id>
            <name>danding snapshot resp</name>
            <url>http://mvn.yang800.cn/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>perm</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${bulid.version}</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <skipSource>true</skipSource>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
