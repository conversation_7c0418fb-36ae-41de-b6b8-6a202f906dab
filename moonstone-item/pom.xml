<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>moonstone-mall</artifactId>
        <groupId>moonstone</groupId>
        <version>1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>moonstone-item</artifactId>
    <version>1.0.0.RELEASE</version>

    <dependencies>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>tax-utils</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-item-api</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-category</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.search</groupId>
            <artifactId>search-api</artifactId>
            <version>${terminus-search.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>

        </dependency>

        <!-- https://mvnrepository.com/artifact/org.scala-lang/scala-library -->
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <version>${scala.version}</version>
        </dependency>
        <dependency>
            <groupId>trade.herald</groupId>
            <artifactId>scala.compatible.common</artifactId>
            <version>${scala.compatible.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.vertx</groupId>
                    <artifactId>vertx-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.moonstone</groupId>
                    <artifactId>moonstone-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.moonstone</groupId>
                    <artifactId>moonstone-trade-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.moonstone</groupId>
                    <artifactId>moonstone-item-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.moonstone</groupId>
                    <artifactId>moonstone-distribution-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>moonstone-trade-api</artifactId>
                    <groupId>moonstone</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>moonstone-web-core</artifactId>
                    <groupId>moonstone</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-op-allinpay</artifactId>
            <version>1.0.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>