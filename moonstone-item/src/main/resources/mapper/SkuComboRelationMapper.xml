<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="SkuComboRelation">
    <!-- 通用查询映射结果 -->
    <resultMap id="skuComboRelationMap" type="SkuComboRelation">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="sku_id" property="skuId" />
        <result column="combo_item_id" property="comboItemId" />
        <result column="combo_item_name" property="comboItemName" />
        <result column="combo_sku_id" property="comboSkuId" />
        <result column="combo_sku_quantity" property="comboSkuQuantity" />
        <result column="version" property="version" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->

    <sql id="tb">
        dt_sku_combo_relation
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        shop_id
        , sku_id, combo_item_id, combo_item_name, combo_sku_id, combo_sku_quantity, version, created_by, created_time, updated_by, updated_time
    </sql>

    <sql id="vals">
        #{shopId}
        ,
        #{skuId},
        #{comboItemId},
        #{comboItemName},
        #{comboSkuId},
        #{comboSkuQuantity},
        #{version},
        #{createdBy},
        #{createdTime},
        #{updatedBy},
        #{updatedTime}
    </sql>

    <sql id="criteria">
        <if test="id !=null"> AND id= #{id}</if>
        <if test="ids != null"> AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="shopId != null"> AND shop_id = #{shopId}</if>
        <if test="skuId != null"> AND sku_id = #{skuId}</if>
        <if test="skuIdList != null"> AND id IN
            <foreach collection="skuIdList" open="(" separator="," close=")" item="skuId">#{skuId}</foreach>
        </if>
        <if test="comboItemId != null"> AND combo_item_id = #{comboItemId}</if>
        <if test="comboItemName != null"> AND combo_item_name = #{comboItemName}</if>
        <if test="comboSkuId !=null"> AND combo_sku_id = #{comboSkuId}</if>
        <if test="comboSkuQuantity !=null"> AND combo_sku_quantity = #{comboSkuQuantity}</if>
        <if test="version !=null"> AND version = #{version}</if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <insert id="create" parameterType="SkuComboRelation" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="SkuComboRelation">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="id !=null">id= #{id},</if>
            <if test="shopId != null">  shop_id = #{shopId},</if>
            <if test="skuId != null">  shop_name = #{skuId},</if>
            <if test="comboItemId != null">  combo_item_id = #{comboItemId},</if>
            <if test="comboItemName != null">  combo_item_name = #{comboItemName},</if>
            <if test="comboSkuId !=null">  combo_sku_id = #{comboSkuId},</if>
            <if test="comboSkuQuantity !=null">  combo_sku_quantity = #{comboSkuQuantity},</if>
            <if test="version !=null">  version = #{version},</if>
            <if test="updatedBy !=null">  updated_by = #{updatedBy},</if>
            updatedTime = #{updateTime}
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <delete id="deletes" parameterType="list">
        DELETE FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="findById" parameterType="long" resultMap="skuComboRelationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="selectOne" parameterType="map" resultMap="skuComboRelationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="selectList" parameterType="map" resultMap="skuComboRelationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="skuComboRelationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>


</mapper>