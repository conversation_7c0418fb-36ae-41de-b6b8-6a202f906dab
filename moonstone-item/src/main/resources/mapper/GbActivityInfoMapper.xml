<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="GbActivityInfo">

    <resultMap id="GbActivityInfoMap" type="moonstone.item.model.GbActivityInfo">
        <id column="id" property="id"/>
        <result column="marketing_tool_id" property="marketingToolId"/>
        <result column="activity_name" property="activityName"/>
        <result column="activity_des" property="activityDes"/>
        <result column="activity_pics" property="activityPics"/>
        <result column="activity_rules" property="activityRules"/>
        <result column="activity_num" property="activityNum"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="activity_status" property="activityStatus"/>
        <result column="reward_flag" property="rewardFlag"/>
        <result column="reward_object" property="rewardObject"/>
        <result column="shop_id" property="shopId"/>
        <result column="version" property="version"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="remarks" property="remarks"/>
        <result column="deleted" property="deleted"/>
        <result column="auto_success" property="autoSuccess"/>
        <result column="payment_deadline" property="paymentDeadline"/>
    </resultMap>

    <select id="findById" resultMap="GbActivityInfoMap">
        select * from dt_gb_activity_info where id = #{id} and deleted = 0
    </select>

    <select id="selectListOnUse" resultMap="GbActivityInfoMap">
        select * from dt_gb_activity_info where activity_status in (1,2) and deleted = 0
    </select>

</mapper>
