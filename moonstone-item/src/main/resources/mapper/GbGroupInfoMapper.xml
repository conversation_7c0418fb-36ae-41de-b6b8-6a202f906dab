<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="GbGroupInfo">

    <resultMap id="GbGroupInfoMap" type="moonstone.item.model.GbGroupInfo">
        <id column="id" property="id"/>
        <result column="activity_id" property="activityId"/>
        <result column="marketing_tool_id" property="marketingToolId"/>
        <result column="group_time" property="groupTime"/>
        <result column="group_status" property="groupStatus"/>
        <result column="fail_reason" property="failReason"/>
        <result column="group_des" property="groupDes"/>
        <result column="shop_id" property="shopId"/>
        <result column="version" property="version"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="remarks" property="remarks"/>
        <result column="deleted" property="deleted"/>
        <result column="success_time" property="successTime"/>
    </resultMap>

    <select id="findById" resultMap="GbGroupInfoMap">
        select * from dt_gb_group_info where id = #{id} and deleted = 0
    </select>

</mapper>
