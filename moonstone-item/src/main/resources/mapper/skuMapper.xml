<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="Sku">

    <resultMap id="SkuMap" type="Sku">
        <id property="id" column="id"/>
        <result column="sku_code" property="skuCode"/>
        <result column="item_id" property="itemId"/>
        <result column="shop_id" property="shopId"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="specification" property="specification"/>
        <result column="outer_sku_id" property="outerSkuId"/>
        <result column="outer_shop_id" property="outerShopId"/>
        <result column="depot_code" property="depotCode"/>
        <result column="activity_sales_price_switch" property="activitySalesPriceSwitch"/>
        <result column="extra_price_json" property="extraPriceJson"/>
        <result column="price" property="price"/>
        <result column="image" property="image"/>
        <result column="thumbnail" property="thumbnail"/>
        <result column="name" property="name"/>
        <result column="attrs_json" property="attrsJson"/>
        <result column="stock_type" property="stockType"/>
        <result column="stock_quantity" property="stockQuantity"/>
        <result column="sale_quantity" property="saleQuantity"/>
        <result column="version" property="version"/>
        <result column="extra" property="extraJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="guider_profit_fee" property="guiderProfitFee"/>
        <result column="guider_profit_rate" property="guiderProfitRate"/>
        <result column="sub_store_profit_fee" property="subStoreProfitFee"/>
        <result column="sub_store_profit_rate" property="subStoreProfitRate"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <resultMap id="LongMap" type="Long"/>

    <sql id="tb">
        parana_skus
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        sku_code, item_id, shop_id, `type`, status, specification, outer_sku_id, outer_shop_id, depot_code,
        activity_sales_price_switch,extra_price_json, price, image,
        thumbnail, `name`, attrs_json, stock_type, stock_quantity, sale_quantity, version, extra,
        tags_json,guider_profit_rate,guider_profit_fee,sub_store_profit_fee,sub_store_profit_rate, created_at,
        updated_at
    </sql>

    <sql id="vals">
        #{skuCode}, #{itemId}, #{shopId}, #{type}, #{status}, #{specification}, #{outerSkuId}, #{outerShopId},
        #{depotCode},#{activitySalesPriceSwitch},#{extraPriceJson}, #{price},
        #{image},
        #{thumbnail},
        #{name},
        #{attrsJson},
        #{stockType},
        #{stockQuantity},
        #{saleQuantity},
        #{version},
        #{extraJson},
        #{tagsJson},#{guiderProfitRate},#{guiderProfitFee},#{subStoreProfitFee},#{subStoreProfitRate}, now(), now()
    </sql>

    <sql id="criteria">
        <if test="ids  !=  null">AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="name  !=  null">AND name LIKE CONCAT(#{name} ,'%')</if>
        <if test="skuCode != null">AND sku_code = #{skuCode}</if>
        <if test="activitySalesPriceSwitch != null">AND activity_sales_price_Switch = #{activitySalesPriceSwitch}</if>
        <if test="itemId  !=  null">AND item_id = #{itemId}</if>
        <if test="shopId  !=  null">AND shop_id = #{shopId}</if>
        <if test="updatedFrom  !=  null">AND <![CDATA[updated_at >= #{updatedFrom}]]> </if>
        <if test="updatedTo  !=  null">AND <![CDATA[updated_at < #{updatedTo}]]> </if>
        <if test="statuses == null and status == null">
            AND `status` != -3
        </if>
        <if test="status  !=  null">AND status = #{status}</if>
        <if test="version  !=  null">AND status = #{version}</if>
        <if test="profitRate  !=  null">and profit_rate=#{profitRate}</if>
        <if test="profitFee  !=  null">and profit_fee=#{profitFee}</if>
        <if test="saleQuantity  !=  null">and sale_quantity=#{saleQuantity}</if>
        <if test="statuses  !=  null">
            AND status in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
        <if test="profitSet  !=  null">and #{profitSet} = (`guider_profit_fee` >0 or `guider_profit_rate`>0 or
            `sub_store_profit_fee`>0 or `sub_store_profit_rate`>0)
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType  !=  null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy  !=  null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'itemId'">ORDER BY item_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'price'">ORDER BY price
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'updatedAt'">ORDER BY updated_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <insert id="create" parameterType="Sku" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.skuCode}, #{i.itemId}, #{i.shopId}, #{i.status}, #{i.specification}, #{i.outerSkuId}, #{i.outerShopId},
            #{i.depotCode},#{i.activitySalesPriceSwitch},
            #{i.extraPriceJson}, #{i.price}, #{i.image}, #{i.thumbnail}, #{i.name},#{i.attrsJson}, #{i.stockType},
            #{i.stockQuantity}, #{i.saleQuantity}, #{i.extraJson}, #{i.tagsJson}, now(), now()
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="SkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="SkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findIdsByNameAndShopId" parameterType="map" resultMap="LongMap">
        SELECT
        id
        FROM
        <include refid="tb"/>
        WHERE shop_id=#{shopId} AND `name` LIKE CONCAT(#{name} ,'%') AND status != -3
    </select>


    <select id="findByShopIdAndSkuCode" parameterType="map" resultMap="SkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id=#{shopId} AND sku_code=#{skuCode} AND status != -3
    </select>

    <select id="findByShopIdAndOuterSkuId" parameterType="map" resultMap="SkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            shop_id=#{shopId} AND outer_sku_id=#{outerSkuId} AND status != -3
        </where>
    </select>

    <select id="findByItemId" parameterType="long" resultMap="SkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE item_id = #{itemId} AND status != -3
    </select>

    <select id="findByItemIds" parameterType="list" resultMap="SkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE item_id IN
        <foreach item="itemId" collection="list" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        AND status != -3
    </select>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>


    <update id="update" parameterType="Sku">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="skuCode  !=  null">sku_code = #{skuCode},</if>
            <if test="specification  !=  null">specification = #{specification},</if>
            <if test="outerSkuId  !=  null">outer_sku_id = #{outerSkuId},</if>
            <if test="outerShopId  !=  null">outer_shop_id = #{outerShopId},</if>
            <if test="depotCode != null">depot_code = #{depotCode},</if>
            <if test="activitySalesPriceSwitch != null">activity_sales_price_Switch = #{activitySalesPriceSwitch},</if>
            <if test="extraPriceJson  !=  null">extra_price_json = #{extraPriceJson},</if>
            <if test="price  !=  null">price = #{price},</if>
            <if test="image  !=  null">image = #{image},</if>
            <if test="type  != null">`type` = #{type},</if>
            <if test="status != null">status=#{status},</if>
            <if test="thumbnail  !=  null">thumbnail = #{thumbnail},</if>
            <if test="name  !=  null">`name` = #{name},</if>
            <if test="attrsJson  !=  null">attrs_json = #{attrsJson},</if>
            <if test="stockType  !=  null">stock_type = #{stockType},</if>
            <if test="stockQuantity  !=  null">stock_quantity = #{stockQuantity},</if>
            <if test="saleQuantity  !=  null">sale_quantity = #{saleQuantity},</if>
            <if test="extraJson  !=  null">extra = #{extraJson},</if>
            <if test="tagsJson  !=  null">tags_json = #{tagsJson},</if>
            <if test="guiderProfitRate  !=  null">guider_profit_rate=#{guiderProfitRate},</if>
            <if test="guiderProfitFee  !=  null">guider_profit_fee=#{guiderProfitFee},</if>
            <if test="subStoreProfitRate  !=  null">sub_store_profit_rate=#{subStoreProfitRate},</if>
            <if test="subStoreProfitFee  !=  null">sub_store_profit_fee=#{subStoreProfitFee},</if>
            updated_at = now()
        </set>
        WHERE id = #{id} and status != -2
    </update>

    <update id="updateByItemIdAndSkuCode" parameterType="Sku">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="specification  !=  null">specification = #{specification},</if>
            <if test="outerSkuId  !=  null">outer_sku_id = #{outerSkuId},</if>
            <if test="outerShopId  !=  null">outer_shop_id = #{outerShopId},</if>
            <if test="depotCode != null">depot_code = #{depotCode},</if>
            <if test="activitySalesPriceSwitch != null">activity_sales_price_Switch = #{activitySalesPriceSwitch},</if>
            <if test="extraPriceJson  !=  null">extra_price_json = #{extraPriceJson},</if>
            <if test="price  !=  null">price = #{price},</if>
            <if test="image  !=  null">image = #{image},</if>
            <if test="type  != null">`type` = #{type},</if>
            <if test="status != null">status=#{status},</if>
            <if test="thumbnail  !=  null">thumbnail = #{thumbnail},</if>
            <if test="name  !=  null">`name` = #{name},</if>
            <if test="attrsJson  !=  null">attrs_json = #{attrsJson},</if>
            <if test="stockType  !=  null">stock_type = #{stockType},</if>
            <if test="stockQuantity  !=  null">stock_quantity = #{stockQuantity},</if>
            <if test="saleQuantity  !=  null">sale_quantity = #{saleQuantity},</if>
            <if test="extraJson  !=  null">extra = #{extraJson},</if>
            <if test="tagsJson  !=  null">tags_json = #{tagsJson},</if>
            <if test="guiderProfitRate  !=  null">guider_profit_rate=#{guiderProfitRate},</if>
            <if test="guiderProfitFee  !=  null">guider_profit_fee=#{guiderProfitFee},</if>
            <if test="subStoreProfitRate  !=  null">sub_store_profit_rate=#{subStoreProfitRate},</if>
            <if test="subStoreProfitFee  !=  null">sub_store_profit_fee=#{subStoreProfitFee},</if>
            updated_at = now()
        </set>
        WHERE item_id=#{itemId} and sku_code=#{skuCode} and status != -2
    </update>

    <update id="updateStockQuantity" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET stock_quantity = stock_quantity - #{quantity}
        WHERE id = #{skuId}
    </update>

    <!-- 设置 SKU 状态 -->
    <update id="updateStatusByItemIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE item_id IN
        <foreach item="itemId" collection="itemIds"
                 open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and `status` != -3
    </update>

    <update id="updateStatusByItemId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE item_id = #{itemId}
        and `status` != -3
    </update>

    <update id="deletes" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET status = -3
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateStatusBySkuId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id = #{skuId}
    </update>

    <select id="countByItemId" parameterType="long" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            item_id = #{itemId} AND status != -3
        </where>
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="SkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countSellInWeShop" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <if test="shopId  !=  null">AND `shop_id` = #{shopId}</if>
            <if test="name  !=  null">AND `name` LIKE CONCAT(#{name} ,'%')</if>
            <if test="itemId  !=  null">AND `item_id` = #{itemId}</if>
            AND `extra` LIKE '%"sellInWeShop":"true"%'
            AND `status` != -3
        </where>
    </select>

    <select id="pagingSellInWeShop" parameterType="map" resultMap="SkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="shopId  !=  null">AND `shop_id` = #{shopId}</if>
            <if test="name  !=  null">AND `name` LIKE CONCAT(#{name} ,'%')</if>
            <if test="itemId  !=  null">AND `item_id` = #{itemId}</if>
            AND `extra` LIKE '%"sellInWeShop":"true"%'
            AND `status` != -3
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>
    <select id="findByShopIdAndProfitSet" parameterType="map" resultMap="SkuMap">
        select
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="shopId  !=  null">and `shop_id` = #{shopId}</if>
            <if test="profitSet = true">and(`sub_store_profit_fee`>0 or `sub_store_profit_rate`>0 or
                `guider_profit_rate`>0 or `guider_profit_fee`)
            </if>
        </where>
    </select>

    <select id="selectList" parameterType="map" resultMap="SkuMap">
        select
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

</mapper>
