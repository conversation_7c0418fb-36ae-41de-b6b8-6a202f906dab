<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="IntermediateInfo">
    <resultMap id="IntermediateInfoMap" type="IntermediateInfo">
        <id column="id" property="id"/>
        <result column="third_id" property="thirdId"/>
        <result column="first_rate" property="firstRate"/>
        <result column="first_fee" property="firstFee"/>
        <result column="second_rate" property="secondRate"/>
        <result column="second_fee" property="secondFee"/>
        <result column="service_provider_fee" property="serviceProviderFee"/>
        <result column="service_provider_rate" property="serviceProviderRate"/>
        <result column="commission" property="commission"/>
        <result column="first_commission" property="firstCommission"/>
        <result column="is_commission" property="isCommission"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="extra_json" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>

        <result column="matching_type" property="matchingType"/>
        <result column="matching_start_time" property="matchingStartTime"/>
        <result column="matching_end_time" property="matchingEndTime"/>
        <result column="unit" property="unit" />
        <result column="is_cash_gift" property="isCashGift"/>
        <result column="max_deduction" property="maxDeduction"/>
        <result column="cash_gift_use_time_start" property="cashGiftUseTimeStart"/>
        <result column="cash_gift_use_time_end" property="cashGiftUseTimeEnd"/>
    </resultMap>

    <sql id="tb">
        parana_intermediate_info
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        third_id, first_rate,first_fee,second_rate,second_fee, commission,first_commission, is_commission,`type`,
        `status`, extra_json, service_provider_fee, service_provider_rate,
        created_at, updated_at, matching_type, matching_start_time, matching_end_time,is_cash_gift,max_deduction,unit,cash_gift_use_time_start,cash_gift_use_time_end
    </sql>

    <sql id="vals">
        #{thirdId}, #{firstRate},#{firstFee},#{secondRate},#{secondFee},
        #{commission},#{firstCommission},#{isCommission}, #{type},
        #{status}, #{extraStr}, #{serviceProviderFee}, #{serviceProviderRate},
        now(), now(), #{matchingType}, #{matchingStartTime}, #{matchingEndTime},
        #{isCashGift},#{maxDeduction},#{unit},#{cashGiftUseTimeStart},#{cashGiftUseTimeEnd}
    </sql>

    <sql id="criteria">
        <if test="status != null">AND `status` = #{status}</if>
        <if test="status == null">AND `status` != -1</if>
    </sql>


    <insert id="create" parameterType="IntermediateInfo" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="IntermediateInfo">
        UPDATE
        <include refid="tb"/>
        SET
        <if test="thirdId != null">third_id = #{thirdId},</if>
        <if test="firstRate != null">first_rate = #{firstRate},</if>
        <if test="firstFee != null">first_fee = #{firstFee},</if>
        <if test="secondRate != null">second_rate = #{secondRate},</if>
        <if test="secondFee != null">second_fee = #{secondFee},</if>
        <if test="commission != null">commission= #{commission},</if>
        <if test="firstCommission != null">first_commission= #{firstCommission},</if>
        <if test="isCommission != null">is_commission= #{isCommission},</if>
        <if test="type != null">`type`= #{type},</if>
        <if test="status != null">`status` = #{status},</if>
        <if test="extraStr != null">extra_json = #{extraStr},</if>
        <if test="serviceProviderFee != null">service_provider_fee = #{serviceProviderFee},</if>
        <if test="serviceProviderRate != null">service_provider_rate = #{serviceProviderRate},</if>
        <if test="matchingType != null">matching_type = #{matchingType},</if>
        <if test="matchingStartTime != null">matching_start_time = #{matchingStartTime},</if>
        <if test="matchingEndTime != null">matching_end_time = #{matchingEndTime},</if>
        <if test="isCashGift != null">is_cash_gift = #{isCashGift},</if>
        <if test="maxDeduction != null">max_deduction = #{maxDeduction},</if>
        <if test="unit != null">unit = #{unit},</if>

        <choose>
            <!-- 值 > 0 时更新字段 -->
            <when test="cashGiftUseTimeStart != null ">
                cash_gift_use_time_start = #{cashGiftUseTimeStart},
            </when>
            <!-- 值 == -1 时设为 null -->
            <when test="cashGiftUseTimeStart == null ">
                cash_gift_use_time_start = null,
            </when>
        </choose>
        <!-- cashGiftUseTimeEnd -->
        <choose>
            <when test="cashGiftUseTimeEnd != null ">
                cash_gift_use_time_end = #{cashGiftUseTimeEnd},
            </when>
            <when test="cashGiftUseTimeEnd == null ">
                cash_gift_use_time_end = null,
            </when>
        </choose>
        updated_at = now()
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="long" resultMap="IntermediateInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByThirdAndType" parameterType="map" resultMap="IntermediateInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        third_id=#{thirdId}
        and `type`=#{type}
        <if test="matchingType == null">
            and (matching_type is null or matching_type = 1)
        </if>
        <if test="matchingType != null">
            and matching_type = #{matchingType}
        </if>
    </select>

    <select id="findAllByThirdAndType" parameterType="map" resultMap="IntermediateInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        third_id=#{thirdId}
        and `type`=#{type}
    </select>

    <!-- 如果存在符合条件的活动配置，则取活动配置，否则取一般的通常配置 -->
    <select id="findWithActivityByThirdAndType" parameterType="map" resultMap="IntermediateInfoMap">
        SELECT <include refid="cols_all"/>
          FROM <include refid="tb"/>
         WHERE id in( IF( EXISTS(SELECT id
                                   FROM parana_intermediate_info
                                  WHERE third_id = #{thirdId}
                                    AND `type` = #{type}
                                    AND matching_type = 2
                                    AND matching_start_time <![CDATA[ <= ]]> NOW()
                                    AND matching_end_time >= NOW() ),

                            ( SELECT id
                                FROM parana_intermediate_info
                               WHERE third_id = #{thirdId}
                                 AND `type` = #{type}
                                 AND matching_type = 2
                                 AND matching_start_time <![CDATA[ <= ]]> NOW()
                                 AND matching_end_time >= NOW() ),

                            ( SELECT id
                                FROM parana_intermediate_info
                               WHERE third_id = #{thirdId}
                                 AND `type` = #{type}
                                 AND (matching_type is null or matching_type = 1) )
                        )
                    )
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>
    <select id="exists" parameterType="map" resultType="long">
        select exists(select 1 from
        <include refid="tb"/>
        <where>
            <if test="type!=null">`type`=#{type}</if>
            <if test="thirdId!=null">and `third_id`=#{thirdId}</if>
            <if test="status!=null">and `status`=#{status}</if>
            <if test="status==null">and `status` != -1</if>
            <if test="matchingType==null">and (matching_type is null or matching_type = 1)</if>
            <if test="matchingType!=null">and matching_type = #{matchingType}</if>
        </where>
        )
    </select>

    <select id="paging" parameterType="map" resultMap="IntermediateInfoMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <delete id="deleteByThirdAndType" parameterType="map">
        delete from <include refid="tb"/>
        where `third_id` = #{thirdId}
          and `type` = #{type}
          and `matching_type` = #{matchingType}
    </delete>

    <select id="findByThirdIdsAndType" resultMap="IntermediateInfoMap" parameterType="map">
        select <include refid="cols_all"/>
        from <include refid="tb"/>
        where `type` = #{type}
        and third_id in
        <foreach collection="thirdIdList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>