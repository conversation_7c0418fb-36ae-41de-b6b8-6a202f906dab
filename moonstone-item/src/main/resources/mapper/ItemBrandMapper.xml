<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="ItemBrand">
    <resultMap id="BrandMap" type="moonstone.itemBrand.model.ItemBrand">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="shop_id" property="shopId"/>
        <result column="created_at" property="createdAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_item_brand
    </sql>

    <sql id="cols_all">
        id, <include refid="cols_exclude_id" />
    </sql>

    <sql id="cols_exclude_id">
        name, shop_id, created_at, created_by, updated_at
    </sql>

    <sql id="vals">
        #{name}, #{shopId}, now(), #{createdBy}, now()
    </sql>


    <insert id="create" parameterType="moonstone.itemBrand.model.ItemBrand" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb" />
        (<include refid="cols_exclude_id" />)
        VALUES
        (<include refid="vals" />)
    </insert>


    <select id="findById" parameterType="long" resultMap="BrandMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="BrandMap">
        SELECT id,
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach collection="list" separator="," open="("
                 close=")" item="id">
            #{id}
        </foreach>
    </select>


    <select id="findByOuterId" parameterType="string" resultMap="BrandMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE outer_id = #{outerId}
    </select>

    <select id="findByName" parameterType="map" resultMap="BrandMap" >
        SELECT <include refid="cols_all" />
        FROM <include refid="tb" />
        WHERE name = #{name} and shop_id = #{shopId}
    </select>

    <select id="findByNamePrefix" parameterType="map" resultMap="BrandMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
             `name` LIKE CONCAT(#{name}, '%')
              AND status=1
        LIMIT #{limit}
    </select>

    <select id="paging" parameterType="map" resultMap="BrandMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            shop_id = #{shopId}
            <if test="name!=null">and `name` LIKE CONCAT('%',#{name}, '%')</if>
        </where>
        order by `id` desc
        LIMIT #{offset},#{size}
    </select>

    <select id="count"  parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            shop_id = #{shopId}
            <if test="name!=null"> and `name` LIKE CONCAT('%',#{name}, '%')</if>
        </where>
    </select>

    <select id="list" parameterType="map" resultMap="BrandMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <if test="shopId!=null">shop_id=#{shopId}</if>
            <if test="name!=null">and `name` LIKE CONCAT('%',#{name}, '%')</if>
        </where>
    </select>

    <update id="update" parameterType="moonstone.itemBrand.model.ItemBrand">
        UPDATE <include refid="tb"/>
        <set>
            <if test="name != null"> name = #{name}, </if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM <include refid="tb"/>
        WHERE id=#{id}
    </delete>
</mapper>
