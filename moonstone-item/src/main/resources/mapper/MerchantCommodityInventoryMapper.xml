<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="MerchantCommodityInventory">

    <resultMap id="MerchantCommodityInventoryMap" type="moonstone.item.model.MerchantCommodityInventory">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="item_id" property="itemId" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="activity_id" property="activityId" jdbcType="BIGINT"/>
        <result column="stock_type" property="stockType" jdbcType="INTEGER"/>
        <result column="init_qty" property="initQty" jdbcType="INTEGER"/>
        <result column="available_qty" property="availableQty" jdbcType="INTEGER"/>
        <result column="allocated_qty" property="allocatedQty" jdbcType="INTEGER"/>
        <result column="locked_qty" property="lockedQty" jdbcType="INTEGER"/>
        <result column="in_transit_qty" property="inTransitQty" jdbcType="INTEGER"/>
        <result column="safety_stock" property="safetyStock" jdbcType="INTEGER"/>
        <result column="reorder_point" property="reorderPoint" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询给定sku 启用的库存记录总和-->
    <select id="sumAvailableQty" resultType="java.lang.Long">
        select sum(available_qty) from dt_merchant_commodity_inventory
              where sku_id = #{skuId} and status = 1 and deleted = 0
    </select>

    <select id="findByActivityIdAndSkuId" resultMap="MerchantCommodityInventoryMap">
        select * from dt_merchant_commodity_inventory
        where sku_id = #{skuId} and status = 1 and activity_id = #{activityId} and deleted = 0
    </select>

</mapper>