<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="GbGroupMember">

    <resultMap id="GbGroupMemberMap" type="moonstone.item.model.GbGroupMember">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="activity_id" property="activityId" jdbcType="BIGINT"/>
        <result column="group_id" property="groupId" jdbcType="BIGINT"/>
        <result column="group_user_id" property="groupUserId" jdbcType="BIGINT"/>
        <result column="member_role" property="memberRole" jdbcType="TINYINT"/>
        <result column="join_time" property="joinTime" jdbcType="BIGINT"/>
        <result column="member_status" property="memberStatus" jdbcType="TINYINT"/>
        <result column="out_time" property="outTime" jdbcType="BIGINT"/>
        <result column="out_reason" property="outReason" jdbcType="VARCHAR"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_time" property="updatedTime" jdbcType="BIGINT"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
    </resultMap>
    <select id="findByShopOrderId" resultMap="GbGroupMemberMap">
        select * from dt_gb_group_members where order_id = #{shopOrderId} and deleted = 0
    </select>

    <select id="findLeaderByActivityId" resultMap="GbGroupMemberMap">
        select * from dt_gb_group_members
        where activity_id = #{activityId}
          and deleted = 0
          and group_user_id = #{userId}
          and member_role = 1
          and member_status in (2,4,5,6)
    </select>
    <select id="findGroupMemberList" resultMap="GbGroupMemberMap">
        select * from dt_gb_group_members
        where activity_id = #{activityId}
          and deleted = 0
          and group_id = #{groupId}
          and member_status in (2,4,5,6)
    </select>

    <select id="finByShopOrderId" resultMap="GbGroupMemberMap">
        select * from dt_gb_group_members
        where order_id = #{shopOrderId}
          and deleted = 0
    </select>

</mapper>
