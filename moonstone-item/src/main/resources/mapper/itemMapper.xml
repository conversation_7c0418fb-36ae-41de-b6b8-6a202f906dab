<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Item">
    <resultMap id="ItemMap" type="Item">
        <id column="id" property="id"/>
        <result column="item_code" property="itemCode"/>
        <result column="category_id" property="categoryId"/>
        <result column="spu_id" property="spuId"/>
        <result column="shop_id" property="shopId"/>
        <result column="is_bonded" property="isBonded"/>
        <result column="is_third_party_item" property="isThirdPartyItem"/>
        <result column="shop_name" property="shopName"/>
        <result column="brand_id" property="brandId"/>
        <result column="brand_name" property="brandName"/>
        <result column="name" property="name"/>
        <result column="intro" property="intro"/>
        <result column="main_image" property="mainImage"/>
        <result column="low_price" property="lowPrice"/>
        <result column="high_price" property="highPrice"/>
        <result column="stock_type" property="stockType"/>
        <result column="stock_quantity" property="stockQuantity"/>
        <result column="sale_quantity" property="saleQuantity"/>
        <result column="status" property="status"/>
        <result column="on_shelf_at" property="onShelfAt"/>
        <result column="advertise" property="advertise"/>
        <result column="specification" property="specification"/>
        <result column="type" property="type"/>
        <result column="tips" property="tips"/>
        <result column="activity_image_switch" property="activityImageSwitch"/>
        <result column="reduce_stock_type" property="reduceStockType"/>
        <result column="extra_json" property="extraJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="item_info_md5" property="itemInfoMd5"/>
        <result column="index" property="index"/>
        <result column="bar_code" property="barCode"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="sell_out_status" property="sellOutStatus"/>
        <result column="restricted_sales_area_template_id" property="restrictedSalesAreaTemplateId"/>
        <result column="source_type" property="sourceType"/>
    </resultMap>

    <sql id="tb">
        parana_items
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        item_code
        , category_id, spu_id, shop_id, is_bonded, is_third_party_item, shop_name, brand_id,
        brand_name, `name`, intro, main_image, low_price, high_price, stock_type,stock_quantity, sale_quantity,
        status, on_shelf_at, advertise, specification, `type`,`tips`, activity_image_switch,reduce_stock_type, extra_json,tags_json,
        item_info_md5, `index`, bar_code, created_at, updated_at
        ,sell_out_status
        ,restricted_sales_area_template_id,source_type
    </sql>

    <sql id="cols_all_with_table_alias">
        t
        .
        id
        , t.item_code, t.category_id, t.spu_id, t.shop_id, t.is_bonded, t.is_third_party_item, t.shop_name, t.brand_id,
        t.brand_name, t.`name`, t.intro, t.main_image, t.low_price, t.high_price, t.stock_type, t.stock_quantity, t.sale_quantity,
        t.status, t.on_shelf_at, t.advertise, t.specification, t.`type`, t.`tips`,t.activity_image_switch, t.reduce_stock_type, t.extra_json, t.tags_json,
        t.item_info_md5, t.`index`, t.bar_code, t.created_at, t.updated_at
        ,t.sell_out_status
        ,t.restricted_sales_area_template_id
    </sql>

    <sql id="vals">
        #{itemCode}
        ,
        #{categoryId},
        #{spuId},
        #{shopId},
        #{isBonded},
        #{isThirdPartyItem},
        #{shopName},
        #{brandId},
        #{brandName},
        #{name},
        #{intro},
        #{mainImage},
        #{lowPrice},
        #{highPrice},
        #{stockType},
        #{stockQuantity},
        #{saleQuantity},
        #{status},
        #{onShelfAt},
        #{advertise},
        #{specification},
        #{type},
        #{tips},
        #{activityImageSwitch},
        #{reduceStockType},
        #{extraJson},
        #{tagsJson},
        #{itemInfoMd5},
        #{index},
        #{barCode},
        now(),
        now(),
        #{sellOutStatus},
        #{restrictedSalesAreaTemplateId},
        #{sourceType}
    </sql>

    <select id="findExportViewById" parameterType="map" resultMap="ItemMap">
        SELECT id, shop_id, name, bar_code, item_code
        FROM
        <include refid="tb"/>
        <where>
            id = #{id}
        </where>
    </select>

    <sql id="criteria">
        <if test="ids != null">AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="itemIdList != null">AND id IN
            <foreach collection="itemIdList" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="name != null">AND `name` LIKE CONCAT('%',#{name} ,'%')</if>
        <if test="itemCode!=null">AND item_code = #{itemCode}</if>
        <if test="categoryId != null">AND category_id = #{categoryId}</if>
        <if test="spuId != null">AND spu_id = #{spuId}</if>
        <if test="shopId != null">AND shop_id = #{shopId}</if>
        <if test="exShopIds != null">AND shop_id not in
            <foreach collection="exShopIds" open="(" close=")" separator="," item="exShopId">
                #{exShopId}
            </foreach>
        </if>
        <if test="isBonded != null">AND is_bonded = #{isBonded}</if>
        <if test="isThirdPartyItem != null">AND is_third_party_item = #{isThirdPartyItem}</if>
        <if test="brandId != null">AND brand_id = #{brandId}</if>
        <if test="type != null">AND `type` = #{type}</if>

        <if test="typees != null">
            and type in
            <foreach collection="typees" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>

        <if test="updatedFrom != null">AND <![CDATA[updated_at >= #{updatedFrom}]]> </if>
        <if test="updatedTo != null">AND <![CDATA[updated_at < #{updatedTo}]]> </if>

        <if test="sellOnSubStore != null">AND `extra_json` like '%SOSS%'</if>


        <if test="statuses == null and status == null">
            AND `status` != -3
        </if>
        <if test="status != null">AND status = #{status}</if>
        <if test="statuses != null">
            and status in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
        <if test="index != null">
            and `index` = #{index}
        </if>
        <if test="sellOutStatus != null">
            and sell_out_status = #{sellOutStatus}
        </if>
        <if test="shopCategoryId != null">
            AND EXISTS(
            SELECT sci.id
            FROM parana_shop_category_items sci
            WHERE sci.`shop_id` = t.`shop_id`
            AND sci.`item_id` = t.`id`
            AND sci.`shop_category_id` = #{shopCategoryId}
            )
        </if>
        <if test="shopCategoryIds != null">
            AND EXISTS(
            SELECT sci.id
            FROM parana_shop_category_items sci
            WHERE sci.`shop_id` = t.`shop_id`
            AND sci.`item_id` = t.`id`
            AND sci.`shop_category_id` IN
            <foreach collection="shopCategoryIds" item="scId" open="(" separator="," close=")">
                #{scId}
            </foreach>
            )
        </if>
        <if test="restrictedSalesAreaTemplateId != null">
            and restricted_sales_area_template_id = #{restrictedSalesAreaTemplateId}
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
        <if test="sortType == null">
            DESC
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY `index` desc, id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'price'">ORDER BY price
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'saleQuantity'">ORDER BY sale_quantity
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'onShelfAt'">ORDER BY on_shelf_at
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'updatedAt'">ORDER BY updated_at
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'index'">ORDER BY `index`
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <select id="queryQuantity" resultMap="ItemMap" parameterType="map">
        SELECT sale_quantity, stock_quantity
        FROM
        <include refid="tb"/>
        <where>`id` = #{id}</where>
    </select>

    <insert id="create" parameterType="Item" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>
    <select id="findByShopIdAndExtraJsonLike" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        where shop_id = #{shopId} and extra_json like concat('%',concat(#{extraJson},'%')) and status != -3
    </select>
    <select id="findById" parameterType="long" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIdsInWeShop" parameterType="list" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        and `extra_json` like '%sellInWeShop":"true%'
    </select>
    <select id="findByIds" parameterType="list" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectIndexByShopId" parameterType="map" resultMap="ItemMap">
        SELECT `id`, `index`
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId}
        <if test="status == null">
            and `status` > 0
        </if>
        <if test="status != null">
            and `status` = #{status}
        </if>
        order by `index`,`id` desc
    </select>
    <select id="findByIdsAndStatusInWeShop" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="itemIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        and `extra_json` like '%sellInWeShop":"true%'
        and `status` = #{status}
    </select>

    <select id="findByIdsAndStatus" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="itemIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        and `status` = #{status}
    </select>

    <select id="queryHotSellItem" parameterType="map" resultType="long">
        SELECT id
        FROM
        <include refid="tb"/>
        WHERE status > 0
        and `sale_quantity` > 0
        order by `updated_at`, `sale_quantity` desc
        limit #{num}
    </select>

    <select id="countByIdsAndStatus" parameterType="map" resultType="long">
        SELECT
        count(id)
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="itemIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        and `status` = #{status}
    </select>

    <select id="countByIds" parameterType="list" resultType="long">
        SELECT
        count(id)
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByIdsAndName" parameterType="list" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="itemIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        and name like ${name}
    </select>
    <select id="findByShopIdsOrderByTimeAndStatusUp" parameterType="list" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id IN
        <foreach item="shopId" collection="shopIds" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and status=1
        ORDER BY updated_at desc
    </select>


    <select id="pageFindByAll" parameterType="list" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId} and (status = 1 or status = -1)
        <if test="exShopIds != null ">
            and shop_id IN
            <foreach item="sId" collection="exShopIds" open="(" separator="," close=")">
                #{sId}
            </foreach>
        </if>
        <if test="statuses != null">
            and status IN
            <foreach item="st" collection="statuses" open="(" separator="," close=")">
                #{st}
            </foreach>
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="typees != null">
            and type IN
            <foreach item="typ" collection="typees" open="(" separator="," close=")">
                #{typ}
            </foreach>
        </if>
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
        <if test="itemCode != null and itemCode != ''">
            and item_code = #{itemCode}
        </if>
        <if test="ids != null">
            and id IN
            <foreach item="itemId" collection="ids" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        <if test="name != null and name != ''">
            and name like "%"#{name}"%"
        </if>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>


    <select id="countFindByAll" parameterType="list" resultType="long">
        SELECT
        count(*)
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId} and (status = 1 or status = -1)
        <if test="exShopIds != null ">
            and shop_id IN
            <foreach item="sId" collection="exShopIds" open="(" separator="," close=")">
                #{sId}
            </foreach>
        </if>
        <if test="statuses != null">
            and status IN
            <foreach item="st" collection="statuses" open="(" separator="," close=")">
                #{st}
            </foreach>
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="typees != null">
            and type IN
            <foreach item="typ" collection="typees" open="(" separator="," close=")">
                #{typ}
            </foreach>
        </if>
        <if test="type != null and type != ''">
            and type = #{type}
        </if>
        <if test="itemCode != null and itemCode != ''">
            and item_code = #{itemCode}
        </if>
        <if test="ids != null">
            and id IN
            <foreach item="itemId" collection="ids" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        <if test="name != null and name != ''">
            and name like "%"#{name}"%"
        </if>
    </select>

    <update id="update" parameterType="Item">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="intro != null">intro = #{intro},</if>
            <if test="isBonded != null">is_bonded = #{isBonded},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="isThirdPartyItem != null">is_third_party_item = #{isThirdPartyItem},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="brandName != null">brand_name = #{brandName},</if>
            <if test="mainImage != null">main_image = #{mainImage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="stockQuantity != null">stock_quantity = #{stockQuantity},</if>
            <if test="highPrice != null">high_price = #{highPrice},</if>
            <if test="lowPrice != null">low_price = #{lowPrice},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="advertise != null">advertise = #{advertise},</if>
            <if test="reduceStockType !=null">reduce_stock_type=#{reduceStockType},</if>
            <if test="stockType !=null">stock_type=#{stockType},</if>
            <if test="type!=null">`type`=#{type},</if>
            <if test="tips!=null">`tips`=#{tips},</if>
            <if test="activityImageSwitch!=null">`activity_image_switch`=#{activityImageSwitch},</if>
            <if test="extraJson != null">extra_json = #{extraJson},</if>
            <if test="tagsJson != null">tags_json = #{tagsJson},</if>
            <if test="itemInfoMd5!=null">item_info_md5=#{itemInfoMd5},</if>
            <if test="index != null">`index` = #{index},</if>
            <if test="barCode != null">bar_code = #{barCode},</if>
            <if test="sellOutStatus != null">sell_out_status = #{sellOutStatus},</if>
            <if test="restrictedSalesAreaTemplateId != null">restricted_sales_area_template_id =
                #{restrictedSalesAreaTemplateId},
            </if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>


    <update id="batchUpdateStatusByShopIdAndIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now(),`status`=#{status}
        </set>
        WHERE
        shop_id=#{shopId} AND
        id IN
        <foreach item="id" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
        AND status != -2     <!-- prevent frozen item to be set by seller -->
    </update>


    <update id="batchUpdateStatusByShopId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now(),`status`=#{status}
        </set>
        WHERE
        shop_id=#{shopId}
    </update>


    <update id="updateSaleQuantityAndStockQuantity" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        sale_quantity = sale_quantity + #{quantity},
        stock_quantity = stock_quantity - #{quantity}
        WHERE id = #{itemId}
    </update>

    <update id="updateSaleQuantity" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        sale_quantity = sale_quantity + #{quantity}
        WHERE id = #{itemId}
    </update>

    <update id="setSaleQuantity" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        sale_quantity = #{saleQuantity}
        WHERE id = #{itemId}
    </update>

    <update id="updateTags" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        tags_json = #{tagsJson}, item_info_md5 = #{itemInfoMd5}, updated_at = now()
        WHERE id = #{itemId}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id = #{id}
    </update>

    <update id="updateStatusByShopIdAndItemId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id = #{id}
        and shop_id=#{shopId}
        and status != -2  <!-- prevent frozen item to be set by seller -->
    </update>

    <update id="updateItemInfoMd5" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET item_info_md5 = #{itemInfoMd5}, updated_at = now()
        WHERE id = #{id}
    </update>

    <update id="batchUpdateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id IN
        <foreach item="id" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateSellOutStatusByIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET sell_out_status = #{sellOutStatus}, updated_at = now()
        WHERE id IN
        <foreach item="id" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        t
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        t
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 将所有商品(排除逻辑删除的)变为冻结状态 -->
    <update id="frozenByShopIds" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET status = -2, updated_at = now()
        WHERE shop_id IN
        <foreach item="shopId" collection="list"
                 open="(" separator="," close=")">
            #{shopId}
        </foreach>
        AND status != -3
    </update>

    <!-- 解冻商品, 将冻结的商品变为下架 -->
    <update id="unFrozenByShopIds" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET status = -1, updated_at = now()
        WHERE shop_id IN
        <foreach item="shopId" collection="list"
                 open="(" separator="," close=")">
            #{shopId}
        </foreach>
        AND status = -2
    </update>

    <!-- for search -->
    <select id="maxId" resultType="long">
        SELECT MAX(id) FROM
        <include refid="tb"/>
    </select>

    <select id="maxIdByShopId" resultType="long">
        SELECT MAX(id) FROM
        <include refid="tb"/>
        where shop_id = #{shopId}
    </select>

    <select id="listSince" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE  <![CDATA[
          id > #{lastId} AND updated_at > #{since} and shop_id!=0 and `status` >= -1
        ]]>
        ORDER BY id DESC LIMIT #{limit}
    </select>

    <select id="findByItemIdsAndLikeName" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(concat('%',#{name}),'%')
        AND
        id in
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>
    <select id="pageByItemIdsAndLikeNameAndStatusInWeShop" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(concat('%',#{name}),'%')
        AND
        id in
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and `status`=#{status}
        and `extra_json` like '%sellInWeShop":"true%'
        ORDER BY updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="pageByItemIdsAndLikeNameAndStatus" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(concat('%',#{name}),'%')
        AND
        id in
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and `status`=#{status}
        ORDER BY updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="findByItemIdsAndLikeNameAndStatus" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(concat('%',#{name}),'%')
        AND
        id in
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and `status`=#{status}
    </select>
    <select id="pageByItemIdsAndLikeNameInWeShop" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(concat('%',#{name}),'%')
        AND
        id in
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and `extra_json` like '%sellInWeShop":"true%'
        ORDER BY `index`, updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="pageByItemIdsAndLikeName" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(concat('%',#{name}),'%')
        AND
        id in
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        ORDER BY `index`, updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="pageByItemIdsAndNameLike" parameterType="map" resultMap="ItemMap">
        Select
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        where `name` like concat(concat('%',#{name}),'%')
        and id in
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        ORDER BY `index`, updated_at desc limit #{offset},#{limit}
    </select>

    <select id="pageByShopIdsAndLikeNameAndStatusInWeShop" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(#{name},'%')
        AND
        shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and `extra_json` like '%sellInWeShop":"true%'
        and `status`=#{status}
        ORDER BY `index`, updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="pageByShopIdsAndLikeNameAndStatus" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(#{name},'%')
        AND
        shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and `status`=#{status}
        ORDER BY `index`, updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="findByShopIdsAndLikeNameAndStatus" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(#{name},'%')
        AND
        shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and `status`=#{status}
    </select>
    <select id="pageByShopIdsAndLikeNameInWeShop" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(#{name},'%')
        AND
        shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and `extra_json` like '%sellInWeShop":"true%'
        ORDER BY `index`, updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="pageByShopIdsAndLikeName" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(concat('%',#{name}),'%')
        AND
        shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        ORDER BY `index`, updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="findByShopIdsAndLikeName" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        `name` like concat(concat('%',#{name}),'%')
        AND
        shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
    </select>
    <select id="findByShopIds" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
    </select>
    <select id="pageByShopIds" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        ORDER BY `index`, updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="pageByShopIdsInWeShop" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and `extra_json` like '%sellInWeShop":"true%'
        ORDER BY `index`, updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="findByShopIdsAndStatus" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and `status` = #{status}
    </select>
    <select id="pageByShopIdsAndStatusInWeShop" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and `status` = #{status}
        and `extra_json` like '%sellInWeShop":"true%'
        ORDER BY `index`, updated_at DESC LIMIT #{offset},#{limit}
    </select>
    <select id="pageByShopIdsAndStatus" parameterType="map" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and `status` = #{status}
        ORDER BY `index`, updated_at DESC LIMIT #{offset},#{limit}
    </select>

    <select id="listByShopId" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId} and  <![CDATA[
          id < #{lastId}
        ]]>
        ORDER BY `index`, id DESC LIMIT #{limit}
    </select>

    <select id="list" resultMap="ItemMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
    </select>

    <select id="findByCode" parameterType="map" resultMap="ItemMap">
        SELECT * FROM
        <include refid="tb"/>
        WHERE item_code=#{itemCode} and `status` != -3
        <if test="shopId!=null">and shop_id=#{shopId}</if>
    </select>

    <select id="findByShopIdAndCategoryIdLimit" parameterType="map" resultMap="ItemMap">
        SELECT id,shop_id,category_id,`name`,main_image,low_price FROM
        <include refid="tb"/>
        WHERE category_id=#{categoryId} and `status` = 1
        <if test="shopId != null">and shop_id=#{shopId}</if>
        ORDER BY `index`, sale_quantity DESC LIMIT #{limitNum}
    </select>

    <select id="countOnShelfByShopId" parameterType="long" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        where shop_id=#{shopId} and `status`=1
    </select>

    <update id="updateCategoryId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET category_id = #{categoryId}, updated_at = now()
        WHERE id = #{id}
    </update>
    <update id="updateIndex" parameterType="map">
        UPDATE
        <include refid="tb"/>
        set `index` = #{newIndex}
        <where>
            id = #{id}
        </where>
    </update>
    <select id="findIdByIndex" parameterType="map" resultType="long">
        SELECT id FROM
        <include refid="tb"/>
        <where>
            shop_id = #{shopId}
            and `index` = #{index}
            <if test="ynOnShelf != null and ynOnShelf == 'yes'.toString()">
                and `status` > 0
            </if>
            <if test="ynOnShelf != null and ynOnShelf == 'no'.toString()">
                and <![CDATA[ `status` < 0 ]]>
            </if>
            limit 1
        </where>
    </select>

    <update id="batchUpdateItemIndex" parameterType="map">
        update
        <include refid="tb"/>
        <trim prefix="set" suffixOverrides=",">
            <trim prefix=" `index` = ( case " suffix=" ELSE `index` end ),">
                <foreach collection="list" item="item" index="index">
                    when id = #{item.id} then #{item.index}
                </foreach>
            </trim>
        </trim>

        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="findSmallestIndex" parameterType="map" resultType="int">
        SELECT MIN(`index`)
        FROM parana_items
        WHERE `shop_id` = #{shopId}
          and `status` in (1, -1)
    </select>

    <select id="findByShopIdAndStatus" parameterType="map" resultMap="ItemMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where `shop_id` = #{shopId}
        and `status` in
        <foreach collection="statusList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findAllByShopId" parameterType="map" resultMap="ItemMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where `shop_id` = #{shopId}
    </select>

    <update id="batchUpdateSellOutStatus">
        update
        <include refid="tb"/>
        set sell_out_status =
        <foreach collection="updateList" item="item" separator=" " open="case id" close="end">
            when #{item.id} then #{item.sellOutStatus}
        </foreach>
        where id in
        <foreach collection="updateList" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateRestrictedSalesAreaTemplate" parameterType="map">
        update
        <include refid="tb"/>
        set restricted_sales_area_template_id = #{templateId}
        where shop_id = #{shopId}
        and `id` in
        <foreach collection="itemIds" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="initOldItemDefaultTemplateId" parameterType="map">
        update
        <include refid="tb"/>
        set restricted_sales_area_template_id = #{restrictedSalesAreaTemplateId}
        where shop_id = #{shopId}
        and restricted_sales_area_template_id is null
    </update>

    <select id="findAllByThirdParty" parameterType="map" resultMap="ItemMap">
        SELECT DISTINCT
        <include refid="cols_all_with_table_alias"/>
        FROM parana_items t
        JOIN parana_skus t_s ON t.id = t_s.item_id
        AND t.`is_third_party_item` = 1
        AND t.`status` NOT IN ( -3, -2 )
        <if test="thirdParty == 1">
            AND LOCATE('"pushSystem":"1"', t_s.tags_json) > 0
        </if>
        <if test="thirdParty == 3">
            AND LOCATE('"pushSystem":"3"', t_s.tags_json) > 0
        </if>

        WHERE t.shop_id = #{shopId}
    </select>

    <select id="countByCondition" parameterType="map" resultType="long">
        SELECT count(DISTINCT a.id) FROM parana_items a
        <if test="isCategory != null">
            LEFT JOIN parana_shop_category_items b ON a.id = b.item_id
        </if>
        <if test="shopCategoryIds != null">
            LEFT JOIN parana_shop_category_items c ON a.id = c.item_id
        </if>
        <if test="isUseCashGift != null">
            LEFT JOIN parana_intermediate_info d ON a.id = d.third_id
        </if>
        <if test="outerSkuIds != null">
            LEFT JOIN parana_skus e ON a.id = e.item_id
        </if>
        <where>
            a.shop_id = #{shopId}
            <if test="status == null">
                and a.`status` != -3
            </if>
            <if test="status != null">
                and a.`status` = #{status}
            </if>
            <if test="sellOutStatus != null">
                and a.sell_out_status = #{sellOutStatus}
            </if>
            <if test="restrictedSalesAreaTemplateId != null">
                and a.restricted_sales_area_template_id = #{restrictedSalesAreaTemplateId}
            </if>
            <if test="isCategory != null and isCategory == true">
                and b.id is not null
            </if>
            <if test="isCategory != null and isCategory == false ">
                and b.id is null
            </if>
            <if test="itemName != null">
                and a.`name` Like concat('%', #{itemName},'%')
            </if>
            <if test="brandName != null">
                and a.brand_name Like concat('%', #{brandName},'%')
            </if>
            <if test="brandId != null">
                and a.brand_id = #{brandId}
            </if>
            <if test="outerSkuIds != null">
                and e.outer_sku_id IN
                <foreach collection="outerSkuIds" item="outerSkuId" open="(" separator="," close=")">
                    #{outerSkuId}
                </foreach>
            </if>
            <if test="itemIdList != null">
                and a.id IN
                <foreach collection="itemIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="brandId != null">
                and a.brand_id = #{brandId}
            </if>
            <if test="shopCategoryIds != null">
                and c.shop_category_id IN
                <foreach collection="shopCategoryIds" item="shopCategoryId" open="(" separator="," close=")">
                    #{shopCategoryId}
                </foreach>
            </if>
            <if test="sourceType != null">
                and a.source_type = #{sourceType}
            </if>
            <if test="isUseCashGift != null ">
                and d.is_cash_gift = #{isUseCashGift} and d.type = 2
            </if>
        </where>
    </select>

    <select id="pagesByCondition" parameterType="map" resultMap="ItemMap">
        SELECT DISTINCT a.* FROM parana_items a
        <if test="isCategory != null">
            LEFT JOIN parana_shop_category_items b ON a.id = b.item_id
        </if>
        <if test="shopCategoryIds != null">
            LEFT JOIN parana_shop_category_items c ON a.id = c.item_id
        </if>
        <if test="isUseCashGift != null">
            LEFT JOIN parana_intermediate_info d ON a.id = d.third_id
        </if>
        <if test="outerSkuIds != null">
            LEFT JOIN parana_skus e ON a.id = e.item_id
        </if>
        <where>
            a.shop_id = #{shopId}
            <if test="status == null">
                and a.`status` != -3
            </if>
            <if test="status != null">
                and a.`status` = #{status}
            </if>
            <if test="sellOutStatus != null">
                and a.sell_out_status = #{sellOutStatus}
            </if>
            <if test="restrictedSalesAreaTemplateId != null">
                and a.restricted_sales_area_template_id = #{restrictedSalesAreaTemplateId}
            </if>
            <if test="isCategory != null and isCategory == true">
                and b.id is not null
            </if>
            <if test="isCategory != null and isCategory == false ">
                and b.id is null
            </if>
            <if test="itemName != null">
                and a.`name` Like concat('%', #{itemName},'%')
            </if>
            <if test="brandName != null">
                and a.brand_name Like concat('%', #{brandName},'%')
            </if>
            <if test="brandId != null">
                and a.brand_id = #{brandId}
            </if>
            <if test="outerSkuIds != null">
                and e.outer_sku_id IN
                <foreach collection="outerSkuIds" item="outerSkuId" open="(" separator="," close=")">
                    #{outerSkuId}
                </foreach>
            </if>
            <if test="itemIdList != null">
                and a.id IN
                <foreach collection="itemIdList" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="shopCategoryIds != null">
                and c.shop_category_id IN
                <foreach collection="shopCategoryIds" item="shopCategoryId" open="(" separator="," close=")">
                    #{shopCategoryId}
                </foreach>
            </if>
            <if test="sourceType != null">
                and a.source_type = #{sourceType}
            </if>
            <if test="isUseCashGift != null ">
                and d.is_cash_gift = #{isUseCashGift} and d.type = 2
            </if>
        </where>
        <if test="sortBy != null">
            <if test="sortBy == 'id'">
                order by a.id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'createdAt'">
                order by a.created_at
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'updatedAt'">
                order by a.updated_at
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'index'">
                order by a.index
                <include refid="custom_sort_type"/>
            </if>
        </if>
        LIMIT #{offset},#{limit}
    </select>


</mapper>
