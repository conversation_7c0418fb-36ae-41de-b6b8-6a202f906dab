<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="GbActivityConfigSku">


    <resultMap id="GbActivityConfigSkuMap" type="moonstone.item.model.GbActivityConfigSku">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="activity_id" property="activityId" jdbcType="BIGINT"/>
        <result column="marketing_tool_id" property="marketingToolId" jdbcType="BIGINT"/>
        <result column="item_id" property="itemId" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="gb_price" property="gbPrice" jdbcType="DOUBLE"/>
        <result column="sku_limit" property="skuLimit" jdbcType="INTEGER"/>
        <result column="is_fudou" property="isFudou" jdbcType="TINYINT"/>
        <result column="is_lijing" property="isLijing" jdbcType="TINYINT"/>
        <result column="is_youhui" property="isYouhui" jdbcType="TINYINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_time" property="updatedTime" jdbcType="BIGINT"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="TINYINT"/>
        <result column="join_flag" property="joinFlag" jdbcType="TINYINT"/>
        <result column="sku_stock" property="skuStock" jdbcType="INTEGER"/>
    </resultMap>

    <select id="findByItemId" resultMap="GbActivityConfigSkuMap">
        select * from dt_gb_activity_config_sku where item_id = #{itemId}  and deleted = 0
    </select>

    <select id="findByActivityIdAndSkuId" resultMap="GbActivityConfigSkuMap">
        select * from dt_gb_activity_config_sku where sku_id = #{skuId} and activity_id = #{activityId} and deleted = 0 and marketing_tool_id = #{marketingToolId}
    </select>

</mapper>
