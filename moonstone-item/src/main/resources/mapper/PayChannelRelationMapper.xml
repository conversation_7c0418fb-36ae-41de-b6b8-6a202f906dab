<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="PayChannelRelation">
    <resultMap id="PayChannelRelationMap" type="PayChannelRelation">
        <id column="id" property="id" />
        <result column="pro_file_id" property="proFileId" />
        <result column="status" property="status" />
        <result column="relation_type" property="relationType" />
        <result column="trade_type" property="tradeType" />
        <result column="pay_channel" property="payChannel" />
        <result column="shop_id" property="shopId" />
        <result column="version" property="version" />
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <!--表-->
    <sql id="tb">dt_pay_channel_relation</sql>
    <!--除ID外的所有字段-->
    <sql id="cols_exclude_id">
        pro_file_id,
        status,
        relation_type,
        trade_type,
        pay_channel,
        shop_id,
        version,
        created_by,
        created_time,
        updated_by,
        updated_time,
        deleted
    </sql>
    <!--所有字段-->
    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>
    <!--值-->
    <sql id="values">
        #{proFileId},
        #{status},
        #{relationType},
        #{tradeType},
        #{payChannel},
        #{shopId},
        #{version},
        #{createdBy},
        UNIX_TIMESTAMP(NOW()),
        #{updatedBy},
        UNIX_TIMESTAMP(NOW()),
        #{deleted}
    </sql>
    <!--更新条件-->
    <sql id="updateCondition">
        <set>
            updated_time = UNIX_TIMESTAMP(NOW()),
            version = version +1,
            <if test="proFileId!=null">`pro_file_id` = #{proFileId},</if>
            <if test="status!=null">`status` = #{status},</if>
            <if test="relationType!=null">`relation_type` = #{relationType},</if>
            <if test="tradeType!=null">`trade_type` = #{tradeType},</if>
            <if test="payChannel!=null">`pay_channel` = #{payChannel},</if>
            <if test="shopId!=null">`shop_id` = #{shopId},</if>
            <if test="updatedBy!=null">`updated_by` = #{updatedBy},</if>
            <if test="deleted!=null">`deleted` = #{deleted},</if>
        </set>
    </sql>
    <!--查询条件-->
    <sql id="queryCondition">
        <where>
            `deleted` = 0
            <if test="proFileId!=null">AND `pro_file_id` = #{proFileId}</if>
            <if test="status!=null">AND `status` = #{status}</if>
            <if test="relationType!=null">AND `relation_type` = #{relationType}</if>
            <if test="tradeType!=null">AND `trade_type` = #{tradeType}</if>
            <if test="payChannel!=null">AND `pay_channel` = #{payChannel}</if>
            <if test="shopId!=null">AND `shop_id` = #{shopId}</if>
            <if test="shopIdList != null">
                AND `shop_id` in
                <foreach collection="shopIdList" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>
    <!--新增一条记录-->
    <insert id="create"
            parameterType="PayChannelRelation"
            keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="values"/>)
    </insert>
    <!--新增多条记录-->
    <insert id="creates" parameterType="PayChannelRelation" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
                #{i.proFileId},
                #{i.status},
                #{i.relationType},
                #{i.tradeType},
                #{i.payChannel},
                #{i.shopId},
                #{i.version},
                #{i.createdBy},
                UNIX_TIMESTAMP(NOW()),
                #{i.updatedBy},
                UNIX_TIMESTAMP(NOW()),
                #{i.deleted}
            )
        </foreach>
    </insert>
    <!--删除一条记录-->
    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id} and `deleted` = 0
    </delete>
    <!--更新一条记录-->
    <update id="update" parameterType="PayChannelRelation">
        UPDATE <include refid="tb"/>
        <include refid="updateCondition"/>
        WHERE id = #{id} and `deleted` = 0
        <if test="version != null">
            and version = #{version}
        </if>
    </update>
    <!--根据信息查找多条记录-->
    <select id="findByQuery" parameterType="map" resultMap="PayChannelRelationMap">
        SELECT <include refid="cols_all"/>
        FROM <include refid="tb"/>
        <include refid="queryCondition"/>
    </select>
    <!--根据ID查找一条记录-->
    <select id="findById" parameterType="long" resultMap="PayChannelRelationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} and `deleted` = 0
    </select>
    <!--根据信息查找多条记录-->
    <select id="findByShopId" parameterType="long" resultMap="PayChannelRelationMap">
        SELECT <include refid="cols_all"/>
        FROM <include refid="tb"/>
        where shop_id = #{shopId} and `deleted` = 0
    </select>
    <!--根据信息查找多条记录-->
    <select id="findByProFileId" parameterType="map" resultMap="PayChannelRelationMap">
        SELECT <include refid="cols_all"/>
        FROM <include refid="tb"/>
        where shop_id = #{shopId} and pro_file_id = #{proFileId} and `deleted` = 0
    </select>

    <select id="list" resultMap="PayChannelRelationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `status` = 1 and `deleted` = 0;
    </select>
    <!--根据信息查找多条记录-->
    <select id="findByType" parameterType="map" resultMap="PayChannelRelationMap">
        SELECT <include refid="cols_all"/>
        FROM <include refid="tb"/>
        where shop_id = #{shopId} and trade_type = #{tradeType} and relation_type = #{relationType} and `deleted` = 0
    </select>

</mapper>