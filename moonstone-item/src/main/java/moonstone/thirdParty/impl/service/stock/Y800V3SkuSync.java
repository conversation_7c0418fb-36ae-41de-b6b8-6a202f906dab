package moonstone.thirdParty.impl.service.stock;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.ThirdPartySkuSourceEnum;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.enums.Y800GoodsAndRecordTypeEnum;
import moonstone.common.model.Either;
import moonstone.common.model.rpcAPI.enums.Y800StorageSkuTradeTypeEnum;
import moonstone.common.model.rpcAPI.enums.Y800V3SkuStockInventoryTypeEnum;
import moonstone.common.model.rpcAPI.y800Storage.*;
import moonstone.common.utils.Json;
import moonstone.common.utils.LogUtil;
import moonstone.item.emu.SkuTypeEnum;
import moonstone.showcase.mq.Topic;
import moonstone.showcase.mq.message.SyncImageToOSSMessage;
import moonstone.thirdParty.enums.ThirdPartySkuStatusEnum;
import moonstone.thirdParty.impl.dao.*;
import moonstone.thirdParty.impl.service.Y800V3ItemApi;
import moonstone.thirdParty.model.*;
import moonstone.thirdParty.service.stock.StockSyncTrait;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class Y800V3SkuSync implements StockSyncTrait {
    @Autowired
    private ThirdPartySkuShopDao thirdPartySkuShopDao;
    @Autowired
    private ThirdPartySkuDao thirdPartySkuDao;
    @Autowired
    private ThirdPartySkuStockDao thirdPartySkuStockDao;
    @Autowired
    private ThirdPartySkuDetailDao thirdPartySkuDetailDao;

    @Autowired
    private ThirdPartySkuDetailOssDao thirdPartySkuDetailOssDao;

    @RemoteAPI
    private Y800V3ItemApi y800StorageRemoteAPI;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;


    @Resource
    private Environment  environment;

    @Override
    public ThirdPartySystem syncSystem() {
        return ThirdPartySystem.Y800_V3;
    }

    @Override
    public void synchronize(ThirdPartySystem thirdPartySystem, ThirdPartyUserShop thirdPartyUserShop) {

        String[] activeProfiles = environment.getActiveProfiles();
        String profiles = "";
        if (activeProfiles.length > 0) {
            profiles = activeProfiles[0];
        }

        log.info("y800v3版本 接收到同步请求 {} {}", thirdPartySystem.toString(), JSONUtil.toJsonStr(thirdPartyUserShop));
        // 1. 清空数据
        thirdPartySkuShopDao.updateStatusByThirdPartyIdAndShopId(thirdPartySystem.Id(), thirdPartyUserShop.getShopId(), -1);

        // 3. 获取访问基础数据
        String whCode = thirdPartyUserShop.getExtra().get(ShopExtra.WhCode.getCode());
        String accessCode = thirdPartyUserShop.getExtra().get(ShopExtra.Y800StorageAccessCode.getCode());
        if (ObjectUtils.isEmpty(accessCode)) {
            log.warn("{} whCode [{}] accessCode [{}] are both required for shop [{}] at [{}]", LogUtil.getClassMethodName(), whCode, accessCode, thirdPartyUserShop.getShopId(), thirdPartySystem);
            return;
        }

        List<Long> jdShopIds = new ArrayList<>();
        switch (profiles) {
            case "test"-> {
                jdShopIds.add(198L);
			}
            case "pre"-> jdShopIds.add(198L);
            case "online", "dev" -> jdShopIds.add(312L);
		}
        for (int page = 1; ; page++) {
            if (jdShopIds.contains(thirdPartyUserShop.getShopId())) {
                // 4. 开始同步
                Y800SkuQuery y800SkuQuery = new Y800SkuQuery();
                y800SkuQuery.setSize("100");
                y800SkuQuery.setAccessCode(accessCode);
                if (StrUtil.isNotBlank(whCode)) {
                    y800SkuQuery.setWhCode(whCode);
                }
                y800SkuQuery.setStatus("4");
                // 京东云的 走原来的商品同步接口
                try (Y800V3ItemApi api = y800StorageRemoteAPI) {
                    y800SkuQuery.setPage(page + "");
                    api.setSecret(thirdPartyUserShop.getThirdPartyKey());
                    api.setAppId(thirdPartyUserShop.getThirdPartyCode());
                    log.info("y800V3 商品档案查询 查询参数 {}", JSONUtil.toJsonStr(y800SkuQuery));
                    // 对应表: parana_third_party_sku_shop parana_third_party_sku_stock parana_third_party_skus parana_third_party_sku_detail
                    Either<YPaging<Y800StorageSku>> queryResult = api.goodsQuery(y800SkuQuery);
                    log.info("y800V3 商品档案查询 查询结果 {}", JSONUtil.toJsonStr(queryResult.take()));
                    YPaging.Pagination pagination = queryResult.getResult().getPagination();
                    if (pagination.getPage() > pagination.getTotalPage()) {
                        break;
                    }
                    for (var y800StorageSku : queryResult.take().getResult()) {
                        createThirdPartySkuAndLogicalDeleteStockForStorageSku(thirdPartySystem, thirdPartyUserShop, y800StorageSku);
                    }
                    var y800StorageSkuList = queryResult.take().getResult();

                    //打印日志
                    logGoodsQueryResult(thirdPartyUserShop, y800StorageSkuList);

                    Map<String, String> outerSkuIdMapOuterSkuName = new HashMap<>(y800StorageSkuList.size());
                    y800StorageSkuList.forEach(sku -> outerSkuIdMapOuterSkuName.put(sku.getSkuNo(), sku.getSkuName()));

                    Map<String, Integer> skuSourceMap = y800StorageSkuList.stream().collect(Collectors.toMap(Y800StorageSku::getSkuNo, Y800StorageSku::getSource, (k1, k2) -> k1));

                    buildStockSyncTask(thirdPartySystem, thirdPartyUserShop, api, whCode,
                            y800StorageSkuList.stream().map(Y800StorageSku::getSkuNo).distinct().collect(Collectors.toList()),
                            outerSkuIdMapOuterSkuName, skuSourceMap);
                } catch (Exception ex) {
                    log.error("Y800V3SkuSync.synchronize error, thirdPartyUserShop={}", JSON.toJSONString(thirdPartyUserShop), ex);
                    break;
                }
            }else{
                // 其余使用货品同步接口
                Y800GoodsAndRecordQueryRequest goodsAndRecordQueryRequest = new Y800GoodsAndRecordQueryRequest();
                goodsAndRecordQueryRequest.setAccessCode(accessCode);
                goodsAndRecordQueryRequest.setSize(100);
                y800StorageRemoteAPI.setSecret(thirdPartyUserShop.getThirdPartyKey());
                y800StorageRemoteAPI.setAppId(thirdPartyUserShop.getThirdPartyCode());
                goodsAndRecordQueryRequest.setPage(page);
                log.info("y800v3 货品档案查询 goodsAndRecord.query 查询参数 {}", JSONUtil.toJsonStr(goodsAndRecordQueryRequest));
                Either<YPaging<Y800GoodsAndRecord>> queryResult = y800StorageRemoteAPI.goodsAndRecordQuery(goodsAndRecordQueryRequest);
                log.info("y800v3版本 货品档案查询 goodsAndRecord.query 查询结果 {}", JSONUtil.toJsonStr(queryResult.take()));
                YPaging.Pagination pagination = queryResult.getResult().getPagination();
                if (pagination.getPage() > pagination.getTotalPage()) {
                    break;
                }
                List<Y800GoodsAndRecord> y800GoodsAndRecordList = queryResult.getResult().getResult();
                Map<String, List<Y800GoodsAndRecord>> goodsRecordMap = y800GoodsAndRecordList.stream().collect(Collectors.groupingBy(Y800GoodsAndRecord::getSkuNo));
                // 动态同步第三方货品相关记录
                dynamicSyncThirdPartySkuRecords(y800GoodsAndRecordList, thirdPartyUserShop, thirdPartySystem);
                Y800StockQuery y800StockQuery = new Y800StockQuery();
                y800StockQuery.setAccessCode(accessCode);
                if (StrUtil.isNotBlank(whCode)) {
                    y800StockQuery.setWhCode(whCode);
                }
                List<String> skuNoList = y800GoodsAndRecordList.stream().map(Y800GoodsAndRecord::getSkuNo).toList();
                log.info("查询货品库存 {}", JSONUtil.toJsonStr(skuNoList));
                y800StockQuery.setInventoryType("0");
                for (String skuNo : skuNoList) {
                    y800StockQuery.setSkus(skuNo);
                    log.info("y800v3 erp货品库存查询 erp.inventory.query 查询参数 {}", JSONUtil.toJsonStr(y800StockQuery));
                    Either<Y800StorageSkuStockList> inventoryQueryResult = y800StorageRemoteAPI.erpInventoryQuery(y800StockQuery);
                    log.info("y800v3 erp货品库存查询 erp.inventory.query 查询结果 {}", JSONUtil.toJsonStr(inventoryQueryResult.take()));
                    Y800StorageSkuStockList goodsInventoryList = inventoryQueryResult.getResult();
                    Map<String, List<Y800StorageSkuStock>> goodsInventoryMap = Arrays.stream(goodsInventoryList.getSkuList()).toList().stream().collect(Collectors.groupingBy(Y800StorageSkuStock::getSkuNo));
                    ThirdPartySkuStock thirdPartySkuStock = convertGoodsInventroyToThirdPartySkuStock(goodsInventoryMap);
                    thirdPartySkuStock.setShopId(thirdPartyUserShop.getShopId());
                    thirdPartySkuStock.setOuterSkuId(skuNo);
                    List<Y800GoodsAndRecord> y800GoodsAndRecords = goodsRecordMap.get(skuNo);
                    if (CollUtil.isNotEmpty(y800GoodsAndRecords)) {
                        Y800GoodsAndRecord y800GoodsAndRecord = y800GoodsAndRecords.get(0);
                        thirdPartySkuStock.setOuterSkuName( y800GoodsAndRecord.getSkuName());
                        thirdPartySkuStock.setVolume(y800GoodsAndRecord.getVolume());
                        thirdPartySkuStock.setWeight(y800GoodsAndRecord.getWeight());
                    }
                    List<ThirdPartySkuStock> thirdPartySkuStockList = thirdPartySkuStockDao.findByThirdPartyIdAndOuterSkuId(thirdPartyUserShop.getShopId(), ThirdPartySystem.Y800_V3.Id(), thirdPartySkuStock.getOuterSkuId());
                    if (CollUtil.isEmpty(thirdPartySkuStockList)) {
                        thirdPartySkuStockDao.create(thirdPartySkuStock);
                    }
                }
            }
        }
    }

    private ThirdPartySkuStock convertGoodsInventroyToThirdPartySkuStock(Map<String, List<Y800StorageSkuStock>> goodsInventoryMap) {
        ThirdPartySkuStock thirdPartySkuStock = new ThirdPartySkuStock();
        thirdPartySkuStock.setThirdPartyId(ThirdPartySystem.Y800_V3.Id());
        thirdPartySkuStock.setSourceType(ThirdPartySkuSourceEnum.DAI_TA.getCode());
        int authenticStock = 0;
        int defectiveStock = 0;
        String depotCode = "";
        String depotName = "";
        String batch = "";
        if (CollUtil.isNotEmpty(goodsInventoryMap)) {
            for (Map.Entry<String, List<Y800StorageSkuStock>> entry : goodsInventoryMap.entrySet()) {
                for (Y800StorageSkuStock y800StorageSkuStock : entry.getValue()) {
                    switch (y800StorageSkuStock.getInventoryType()) {
                        case "ZP":
                            authenticStock = y800StorageSkuStock.getNum() - y800StorageSkuStock.getLockNum();
                        case "CP":
                            defectiveStock = y800StorageSkuStock.getNum() - y800StorageSkuStock.getLockNum();
                    }
                    depotCode = y800StorageSkuStock.getWarehouseSn();
                    depotName = y800StorageSkuStock.getWarehouseName();
                    batch = y800StorageSkuStock.getBatchCode();
                }
            }
        }
        thirdPartySkuStock.setAuthenticStock(authenticStock);
        thirdPartySkuStock.setDefectiveStock(defectiveStock);
        thirdPartySkuStock.setDepotCode(depotCode);
        thirdPartySkuStock.setDepotName(depotName);
        thirdPartySkuStock.setBatch(batch);
        thirdPartySkuStock.setStatus(ThirdPartySkuStatusEnum.valid.getCode());
        return thirdPartySkuStock;
    }


    private void dynamicSyncThirdPartySkuRecords(List<Y800GoodsAndRecord> y800GoodsAndRecordList, ThirdPartyUserShop thirdPartyUserShop, ThirdPartySystem thirdPartySystem) {
        log.info("查询到的货品档案 {}", JSONUtil.toJsonStr(y800GoodsAndRecordList));
        for (Y800GoodsAndRecord y800GoodsAndRecord : y800GoodsAndRecordList) {
            ThirdPartySku thirdPartySku = new ThirdPartySku();
            thirdPartySku.setOuterSkuName(y800GoodsAndRecord.getSkuName());
            // 状态
            int status = ThirdPartySkuStatusEnum.valid.getCode();
            int type = SkuTypeEnum.fromY800GoodsAndRecordType(Y800GoodsAndRecordTypeEnum.valueOf(y800GoodsAndRecord.getType())).getCode();
            thirdPartySku.setType(type);
            thirdPartySku.setStatus(status);
            thirdPartySku.setOuterSkuId(y800GoodsAndRecord.getSkuNo());
            thirdPartySku.setPackVolume(y800GoodsAndRecord.getVolume());
            thirdPartySku.setThirdPartyId(ThirdPartySystem.Y800_V3.Id());
            thirdPartySku.setSourceType(ThirdPartySkuSourceEnum.DAI_TA.getCode());
            ThirdPartySku exists = thirdPartySkuDao.findByThirdPartyAndSkuId(thirdPartySystem.Id(), y800GoodsAndRecord.getSkuNo());
            if (exists == null) {
                thirdPartySkuDao.create(thirdPartySku);
            } else {
                thirdPartySku.setId(exists.getId());
                if (!thirdPartySkuDao.update(thirdPartySku)) {
                    log.error("同步货品档案信息失败 商家编码 {} ", y800GoodsAndRecord.getSkuNo());
                }
                // 删除重建
                thirdPartySkuDetailDao.deleteByOuterSkuId(exists.getOuterSkuId());
            }
            ThirdPartySkuShop thirdPartySkuShop = thirdPartySkuShopDao.findByThreeId(thirdPartySystem.Id(), thirdPartyUserShop.getShopId(), thirdPartySku.getOuterSkuId());
            log.info("查询 商家编码 {} 店铺 {} 货品表信息 {}", y800GoodsAndRecord.getSkuNo(), thirdPartyUserShop.getShopId(), JSONUtil.toJsonStr(thirdPartySkuShop));
            if (ObjUtil.isEmpty(thirdPartySkuShop)) {
                ThirdPartySkuShop skuShop = new ThirdPartySkuShop();
                skuShop.setOuterSkuId(thirdPartySku.getOuterSkuId());
                skuShop.setShopId(thirdPartyUserShop.getShopId());
                skuShop.setThirdPartyId(thirdPartySystem.Id());
                skuShop.setStatus(status);
                skuShop.setOuterSkuName(thirdPartySku.getOuterSkuName());
                skuShop.setType(type);
                skuShop.setSourceType(ThirdPartySkuSourceEnum.DAI_TA.getCode());
                thirdPartySkuShopDao.create(skuShop);
            } else {
                ThirdPartySkuShop update = new ThirdPartySkuShop();
                update.setId(thirdPartySkuShop.getId());
                update.setOuterSkuName(y800GoodsAndRecord.getSkuName());
                update.setStatus(status);
                update.setType(type);
                update.setSourceType(ThirdPartySkuSourceEnum.DAI_TA.getCode());
                thirdPartySkuShopDao.update(update);
            }
            thirdPartySkuStockDao.updateStatusByThirdPartyAndSkuId(thirdPartyUserShop.getShopId(), thirdPartySystem.Id(), thirdPartySku.getOuterSkuId(), -1);
        }
    }

    private void logGoodsQueryResult(ThirdPartyUserShop thirdPartyUserShop, List<Y800StorageSku> y800StorageSkuList) {
        var sb = new StringBuilder();
        if (!CollectionUtils.isEmpty(y800StorageSkuList)) {
            var groupByStatus = y800StorageSkuList.stream().collect(
                    Collectors.groupingBy(x -> Optional.ofNullable(x.getStatus()).orElse(-9999)));

            sb.append("statusSet=")
                    .append(JSON.toJSONString(groupByStatus.keySet()))
                    .append(",");

            for (Object status : groupByStatus.keySet()) {
                sb.append("count[status=" + status.toString() + "]")
                        .append("=" + (groupByStatus.get(status) != null ? groupByStatus.get(status).size() : 0))
                        .append(", ")
                        .append("skuNo[status=" + status.toString() + "]")
                        .append("=" + JSON.toJSONString(groupByStatus.get(status).stream().map(Y800StorageSku::getSkuNo).collect(Collectors.toList())))
                        .append(", ");
            }
        }

        log.debug("Y800V3SkuSync.goodsQuery_debug thirdPartyId={}, shopId={}, totalCount={}," + sb.toString(),
                thirdPartyUserShop.getThirdPartyId(), thirdPartyUserShop.getShopId(), y800StorageSkuList.size());
    }

    /**
     * 更新第三方系统Y800Storage里的商品
     *
     * @param thirdPartySystem   Y800Storage
     * @param thirdPartyUserShop 用户
     * @param y800StorageSku     单品
     */
    private void createThirdPartySkuAndLogicalDeleteStockForStorageSku(ThirdPartySystem thirdPartySystem, ThirdPartyUserShop thirdPartyUserShop,
                                                                       Y800StorageSku y800StorageSku) {
        if (y800StorageSku.getStatus() == null) {
            log.warn("{} Y800_SKU_STATUS [{}] is null", LogUtil.getClassMethodName(), y800StorageSku.getSkuNo());
        }
        ThirdPartySku thirdPartySku = new ThirdPartySku();
        thirdPartySku.setOuterSkuName(y800StorageSku.getSkuName());

        // 状态
        int status = ThirdPartySkuStatusEnum.valid.getCode();
        int type = SkuTypeEnum.from(Y800StorageSkuTradeTypeEnum.parse(y800StorageSku.getTradeType())).getCode();

        thirdPartySku.setType(type);
        thirdPartySku.setStatus(status);
        thirdPartySku.setOuterSkuId(y800StorageSku.getSkuNo());
        thirdPartySku.setPackVolume(y800StorageSku.getVolume());
        thirdPartySku.setThirdPartyId(ThirdPartySystem.Y800_V3.Id());
        thirdPartySku.setSourceType(y800StorageSku.getSource());
        thirdPartySku.setDescription(y800StorageSku.getWReadMe());
        var skuBaseInfo = y800StorageSku.getSkuBaseInfo();
        if (skuBaseInfo != null) {
            thirdPartySku.setUnit(skuBaseInfo.getUnit());
            thirdPartySku.setTax(skuBaseInfo.getTax());
        }


        ThirdPartySku exists = thirdPartySkuDao.findByThirdPartyAndSkuId(thirdPartySystem.Id(), y800StorageSku.getSkuNo());
        if (exists == null) {
            thirdPartySkuDao.create(thirdPartySku);
            this.createSkuDetail(y800StorageSku);
        } else {
            thirdPartySku.setId(exists.getId());
            thirdPartySku.setStatus(status);
            thirdPartySku.setOuterSkuName(y800StorageSku.getSkuName());
            if (!thirdPartySkuDao.update(thirdPartySku)) {
                log.error("{} thirdPartySku [{}] for shop [{}] fail to update", LogUtil.getClassMethodName(), thirdPartySystem, thirdPartyUserShop.getShopId());
            }
            // 删除重建
            thirdPartySkuDetailDao.deleteByOuterSkuId(exists.getOuterSkuId());
            this.createSkuDetail(y800StorageSku);
        }

        Optional<Long> shopSkuId = Optional.ofNullable(thirdPartySkuShopDao.findByThreeId(thirdPartySystem.Id(), thirdPartyUserShop.getShopId(), thirdPartySku.getOuterSkuId()))
                .map(ThirdPartySkuShop::getId);

        log.debug("{} find stock[{}] of sku [{}] for shop [{}]", LogUtil.getClassMethodName(), shopSkuId, thirdPartySku.getOuterSkuId(), thirdPartyUserShop.getShopId());
        shopSkuId.ifPresent(id -> {
            ThirdPartySkuShop update = new ThirdPartySkuShop();
            update.setId(id);
            update.setOuterSkuName(y800StorageSku.getSkuName());
            update.setStatus(status);
            update.setType(type);
            update.setSourceType(y800StorageSku.getSource());
            log.debug("{} update [{}] for shop [{}] system [{}]", LogUtil.getClassMethodName(), update, thirdPartyUserShop.getShopId(), thirdPartySystem);
            if (!thirdPartySkuShopDao.update(update)) {
                log.error("{} thirdPartySku [{}] for shop [{}] fail to update", LogUtil.getClassMethodName(), thirdPartySystem, thirdPartyUserShop.getShopId());
            }
        });
        if (shopSkuId.isEmpty()) {
            ThirdPartySkuShop skuShop = new ThirdPartySkuShop();
            skuShop.setOuterSkuId(thirdPartySku.getOuterSkuId());
            skuShop.setShopId(thirdPartyUserShop.getShopId());
            skuShop.setThirdPartyId(thirdPartySystem.Id());
            skuShop.setStatus(status);
            skuShop.setOuterSkuName(thirdPartySku.getOuterSkuName());
            skuShop.setType(type);
            skuShop.setSourceType(y800StorageSku.getSource());
            thirdPartySkuShopDao.create(skuShop);
        }
        thirdPartySkuStockDao.updateStatusByThirdPartyAndSkuId(thirdPartyUserShop.getShopId(), thirdPartySystem.Id(), thirdPartySku.getOuterSkuId(), -1);
    }
    private void createSkuDetail(Y800StorageSku y800StorageSku) {
        List<Y800StorageSku.ImageInfo> imageInfos = y800StorageSku.getImageInfos();
        if (CollectionUtils.isEmpty(imageInfos)) {
            return;
        }
        List<ThirdPartySkuDetail> ts = new ArrayList<>();

        // 主图与其他
        for (var imageInfo : imageInfos) {
            ThirdPartySkuDetail detail = new ThirdPartySkuDetail();
            detail.setOuterSkuId(y800StorageSku.getSkuNo());
            detail.setPath(imageInfo.getPath());
            detail.setOrderSort(imageInfo.getOrderSort());
            detail.setType(1);
            detail.setIsPrimary(Objects.equals(1, imageInfo.getIsPrimary()));
            ts.add(detail);
        }

        // 详情图
        Y800StorageSku.SkuBigFieldInfo skuBigFieldInfo = y800StorageSku.getSkuBigFieldInfo();
        if(skuBigFieldInfo != null){
            String pcCssContent = skuBigFieldInfo.getPcCssContent();
            if(StringUtils.isNotBlank(pcCssContent)){
                List<String> images = PcCssContentUtils.parser(pcCssContent);
                // 注意 这些地址是没有带协议前缀的
                log.info("识别的图片地址集合 {}", JSONObject.toJSONString(images));
                for (int i = 0; i < images.size(); i++) {
                    ThirdPartySkuDetail detail = new ThirdPartySkuDetail();
                    detail.setOuterSkuId(y800StorageSku.getSkuNo());
                    detail.setPath(images.get(i));
                    detail.setOrderSort(0);
                    detail.setType(2);
                    detail.setIsPrimary(false);
                    ts.add(detail);
                }
            }
        }
        thirdPartySkuDetailDao.creates(ts);


        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            // 如果当前存在事务，注册事务提交后的回调
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    sendMessagesToMQ(ts);
                }
            });
        } else {
            // 如果当前没有事务，直接发送 MQ 消息
            sendMessagesToMQ(ts);
        }

    }

    /**
     * 发送 MQ 消息的方法
     * @param ts List of ThirdPartySkuDetail
     */
    private void sendMessagesToMQ(List<ThirdPartySkuDetail> ts) {
        for (ThirdPartySkuDetail detail : ts) {
            SyncImageToOSSMessage message = new SyncImageToOSSMessage();
            message.setOuterSkuId(detail.getOuterSkuId());
            message.setPath(detail.getPath());

            try {
                SendResult sendResult = rocketMQTemplate.syncSend(Topic.SYNC_IMAGE_TO_OSS_TOPIC, message);
                if (SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                    log.info("图片同步消息发送成功 {} {}", sendResult.getMsgId(), JSONObject.toJSONString(message));
                } else {
                    log.error("图片同步消息发送失败 {}", JSONObject.toJSONString(message));
                }
            } catch (Exception e) {
                log.error("图片同步消息发送异常 {}", JSONObject.toJSONString(message), e);
            }
        }
    }

    private void buildStockSyncTask(ThirdPartySystem thirdPartySystem, ThirdPartyUserShop thirdPartyUserShop,
                                    Y800V3ItemApi y800StorageRemoteAPI, String whCode, List<String> subList,
                                    Map<String, String> skuIdMapName, Map<String, Integer> skuSourceMap) {
        if (subList == null || subList.isEmpty()) {
            return;
        }

        try (Y800V3ItemApi api = y800StorageRemoteAPI) {
            api.setSecret(thirdPartyUserShop.getThirdPartyKey());
            api.setAppId(thirdPartyUserShop.getThirdPartyCode());

            StringBuilder skuNoBuilder = new StringBuilder();
            for (String skuNo : subList.stream().sorted().toList()) {
                skuNoBuilder.append(skuNo);
                skuNoBuilder.append(",");
            }
            skuNoBuilder.deleteCharAt(skuNoBuilder.length() - 1);

            Y800StockQuery y800StockQuery = new Y800StockQuery();
            if (StrUtil.isNotBlank(whCode)) {
                y800StockQuery.setWhCode(whCode);
            }
            y800StockQuery.setAccessCode(thirdPartyUserShop.getExtra().get(ShopExtra.Y800StorageAccessCode.getCode()));
            y800StockQuery.setInventoryType("0");
            y800StockQuery.setSkus(skuNoBuilder.toString());

            Map<String, List<Y800StorageSkuStock>> stockGroupBySkuNo = new HashMap<>();

            var sourceStockList = api.inventoryQuery(y800StockQuery).take().getSkuList();
            log.debug("inventoryQuery_debug, shopId={}, y800StockQuery={}, sourceStockList={}",
                    thirdPartyUserShop.getShopId(), JSON.toJSONString(y800StockQuery), JSON.toJSONString(sourceStockList));

            for (Y800StorageSkuStock y800StorageSkuStock : sourceStockList) {
                stockGroupBySkuNo.computeIfAbsent(y800StorageSkuStock.getSkuNo(), k -> new ArrayList<>());
                stockGroupBySkuNo.get(y800StorageSkuStock.getSkuNo()).add(y800StorageSkuStock);
            }

            for (Map.Entry<String, List<Y800StorageSkuStock>> entry : stockGroupBySkuNo.entrySet()) {
                log.info("{} query the sku[SkuNo => {}] stock[{}]", LogUtil.getClassMethodName(),
                        entry.getKey(), Json.toJson(entry.getValue()));
                updateY800StorageSkuStock(thirdPartyUserShop.getShopId(), thirdPartySystem, entry.getValue(), whCode, skuIdMapName.get(entry.getKey()), skuSourceMap.get(entry.getKey()));
            }

        } catch (Exception ex) {
            log.error("Y800V3SkuSync.buildStockSyncTask error, subList={}, ", JSON.toJSONString(subList), ex);
        }
    }

    /**
     * 更新系统内部对应的数据
     * 如果不存在则创建,存在则刷新
     *
     * @param thirdPartySystem        第三方系统
     * @param y800StorageSkuStockList Y800StorageSku
     */
    private void updateY800StorageSkuStock(Long shopId, ThirdPartySystem thirdPartySystem, List<Y800StorageSkuStock> y800StorageSkuStockList,
                                           String whCode, String skuName, Integer sourceType) {
        if (y800StorageSkuStockList.isEmpty()) {
            log.warn("{} no stock sync for {}", LogUtil.getClassMethodName(), skuName);
            return;
        }
        List<ThirdPartySkuStock> thirdPartySkuStocks = thirdPartySkuStockDao.findByThirdPartyIdAndOuterSkuId(shopId, thirdPartySystem.Id(), y800StorageSkuStockList.get(0).getSkuNo());
        // mark the other as not used
        thirdPartySkuStockDao.updateStatusByThirdPartyAndSkuId(shopId, thirdPartySystem.Id(), y800StorageSkuStockList.get(0).getSkuNo(), -1);
        for (Y800StorageSkuStock y800StorageSkuStock : y800StorageSkuStockList) {
            log.debug("{} stock[OuterSkuId => {}, Name => {}, BatchCode => {}, Stock => {}", LogUtil.getClassMethodName(),
                    y800StorageSkuStock.getSkuNo(), skuName, y800StorageSkuStock.getBatchCode(), y800StorageSkuStock.getNum());
            String skuStockName = Optional.ofNullable(skuName)
                    .orElseGet(() -> Optional.ofNullable(thirdPartySkuDao.findByThirdPartyAndSkuId(thirdPartySystem.Id(), y800StorageSkuStock.getSkuNo())).map(ThirdPartySku::getOuterSkuName).orElse("NO_NAME"));
            Optional<ThirdPartySkuStock> exists = thirdPartySkuStocks.stream()
                    .filter(stock -> Objects.equals(stock.getBatch(), y800StorageSkuStock.getBatchCode())).findFirst();
            // try to reuse the old one
            if (exists.isEmpty()) {
                // findByParams could choose one that's marked as not used
                // let reuse the old data
                exists = Optional.ofNullable(thirdPartySkuStockDao.findByParams(shopId, thirdPartySystem.Id(), y800StorageSkuStock.getSkuNo(), null, Optional.ofNullable(y800StorageSkuStock.getBatchCode()).orElse("")));
            }
            log.debug("{} shopId={}, stock[OuterSkuId => {}, Name => {}, BatchCode => {}, Stock => {}]", LogUtil.getClassMethodName(), shopId,
                    y800StorageSkuStock.getSkuNo(), skuName, y800StorageSkuStock.getBatchCode(), y800StorageSkuStock.getNum());
            ThirdPartySkuStock skuStock = new ThirdPartySkuStock();
            skuStock.setShopId(shopId);
            skuStock.setDepotName(y800StorageSkuStock.getWarehouseName());
            skuStock.setDepotCode(StringUtils.isNotBlank(y800StorageSkuStock.getWarehouseSn()) ? y800StorageSkuStock.getWarehouseSn() : whCode);
            if (exists.isEmpty()) {
                skuStock.setOuterSkuName(skuStockName);
                skuStock.setThirdPartyId(thirdPartySystem.Id());
                skuStock.setOuterSkuId(y800StorageSkuStock.getSkuNo());
                skuStock.setBatch(Optional.ofNullable(y800StorageSkuStock.getBatchCode()).map(str -> str.substring(0, str.length() > 32 ? 31 : str.length())).orElse("noBatch_Code_Default"));

                appendStockNum(skuStock, y800StorageSkuStock, true);

                skuStock.setStatus(1);
                skuStock.setVolume(thirdPartySkuDao.findByThirdPartyAndSkuId(thirdPartySystem.Id(), y800StorageSkuStock.getSkuNo()).getPackVolume());
                skuStock.setPackVolume(skuStock.getVolume());
                skuStock.setSourceType(sourceType);
                thirdPartySkuStockDao.create(skuStock);
            } else {
                exists.map(ThirdPartySkuStock::getId).ifPresent(skuStock::setId);
                skuStock.setDefectiveStock(0);
                skuStock.setOuterSkuName(skuStockName);
                skuStock.setBatch(Optional.ofNullable(y800StorageSkuStock.getBatchCode()).map(str -> str.substring(0, str.length() > 32 ? 31 : str.length())).orElse("noBatch_Code_Default"));

                appendStockNum(skuStock, y800StorageSkuStock, false);

                skuStock.setVolume(thirdPartySkuDao.findByThirdPartyAndSkuId(thirdPartySystem.Id(), y800StorageSkuStock.getSkuNo()).getPackVolume());
                skuStock.setPackVolume(skuStock.getVolume());
                skuStock.setStatus(1);
                skuStock.setSourceType(sourceType);
                if (!thirdPartySkuStockDao.update(skuStock)) {
                    log.error("{} thirdPartySku [{}] fail to update", LogUtil.getClassMethodName(), thirdPartySystem);
                }
            }
        }
    }

    private void appendStockNum(ThirdPartySkuStock skuStock, Y800StorageSkuStock y800StorageSkuStock, boolean create) {
        if (create) {
            if (y800StorageSkuStock.getAvailableNum() != null || y800StorageSkuStock.getDefectiveAvailableNum() != null) {
                skuStock.setAuthenticStock(y800StorageSkuStock.getAvailableNum() != null ? y800StorageSkuStock.getAvailableNum() : 0);
                skuStock.setDefectiveStock(y800StorageSkuStock.getDefectiveAvailableNum() != null ? y800StorageSkuStock.getDefectiveAvailableNum() : 0);

            } else if (Y800V3SkuStockInventoryTypeEnum.isAuthentic(y800StorageSkuStock.getInventoryType())) {
                skuStock.setAuthenticStock(y800StorageSkuStock.getNum() - y800StorageSkuStock.getLockNum());
                skuStock.setDefectiveStock(0);

            } else {
                skuStock.setAuthenticStock(0);
                skuStock.setDefectiveStock(y800StorageSkuStock.getNum() - y800StorageSkuStock.getLockNum());
            }
        } else {
            if (y800StorageSkuStock.getAvailableNum() != null || y800StorageSkuStock.getDefectiveAvailableNum() != null) {
                if (y800StorageSkuStock.getAvailableNum() != null) {
                    skuStock.setAuthenticStock(y800StorageSkuStock.getAvailableNum());
                }
                if (y800StorageSkuStock.getDefectiveAvailableNum() != null) {
                    skuStock.setDefectiveStock(y800StorageSkuStock.getDefectiveAvailableNum());
                }

            } else if (Y800V3SkuStockInventoryTypeEnum.isAuthentic(y800StorageSkuStock.getInventoryType())) {
                skuStock.setAuthenticStock(y800StorageSkuStock.getNum() - y800StorageSkuStock.getLockNum());
            } else {
                skuStock.setDefectiveStock(y800StorageSkuStock.getNum() - y800StorageSkuStock.getLockNum());
            }
        }
    }
}
