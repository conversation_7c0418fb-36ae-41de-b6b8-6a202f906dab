package moonstone.itemBrand.impl.service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.hutool.core.bean.BeanUtil;
import io.terminus.common.model.Paging;
import moonstone.common.model.vo.ComboBoxVo;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.common.model.vo.PageVo;
import moonstone.itemBrand.dto.ItemBrandDto;
import moonstone.itemBrand.dto.ItemBrandPageDto;
import moonstone.itemBrand.impl.dao.ItemBrandDao;
import moonstone.itemBrand.model.ItemBrand;
import moonstone.itemBrand.service.ItemBrandService;

/**
 * @Author: yousx
 * @Date: 2025/02/21
 * @Description:
 */
@Service
public class ItemBrandServiceImpl implements ItemBrandService {

    @Resource
    private ItemBrandDao itemBrandDao;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ItemBrand itemBrand) {
        itemBrandDao.create(itemBrand);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        itemBrandDao.delete(id);
    }

    @Override
    public PageVo<ItemBrandDto> pages(ItemBrandPageDto req) {
        Map<String, Object> map = req.toMap();
        map.put("offset", (req.getCurrent() - 1) * req.getSize());
        Paging<ItemBrand> paging = itemBrandDao.paging(map);
        Long total = paging.getTotal();
        long pages = total / req.getSize() + (total % req.getSize() > 0 ? 1 : 0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<ItemBrandDto> result = new ArrayList<>();
        for (ItemBrand itemBrand : paging.getData()) {
            ItemBrandDto itemBrandDto = new ItemBrandDto();
            itemBrandDto.setId(itemBrand.getId());
            itemBrandDto.setName(itemBrand.getName());
            itemBrandDto.setCreateTime(sdf.format(itemBrand.getCreatedAt()));
            itemBrandDto.setCreateBy(itemBrand.getCreatedBy());
            itemBrandDto.setCreatedAt(itemBrand.getCreatedAt());
            itemBrandDto.setUpdatedAt(itemBrand.getUpdatedAt());
            result.add(itemBrandDto);
        }
        return PageVo.build(total, req.getSize(), req.getCurrent(), pages, result);
    }

    @Override
    public List<ComboBoxVo> getDropDownBox(Long shopId) {
        List<ComboBoxVo> res = new ArrayList<>();
        ItemBrand param = new ItemBrand();
        param.setShopId(shopId);
        List<ItemBrand> itemBrands = itemBrandDao.list(param);
        for (ItemBrand itemBrand : itemBrands) {
            ComboBoxVo dropDownBoxVo = new ComboBoxVo();
            dropDownBoxVo.setId(itemBrand.getId());
            dropDownBoxVo.setName(itemBrand.getName());
            res.add(dropDownBoxVo);
        }
        return res;
    }

    @Override
    public ItemBrandDto findByName(String name, Long shopId) {
        ItemBrand itemBrand = itemBrandDao.findByName(name, shopId);
        return BeanUtil.copyProperties(itemBrand, ItemBrandDto.class);
    }

    @Override
    public ItemBrandDto findById(Long id) {
        ItemBrand brand = itemBrandDao.findById(id);
        return BeanUtil.copyProperties(brand, ItemBrandDto.class);
    }

    @Override
    public void update(ItemBrand itemBrand) {
        itemBrandDao.update(itemBrand);
    }
}
