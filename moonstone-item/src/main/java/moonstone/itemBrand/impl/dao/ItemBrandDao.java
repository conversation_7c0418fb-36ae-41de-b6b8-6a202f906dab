package moonstone.itemBrand.impl.dao;

import org.springframework.stereotype.Repository;

import com.google.common.collect.ImmutableMap;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.itemBrand.model.ItemBrand;

/**
 * @Author: yousx
 * @Date: 2025/02/21
 * @Description:
 */
@Repository
public class ItemBrandDao extends MyBatisDao<ItemBrand> {


    public ItemBrand findByName(String name, Long shopId) {
        return getSqlSession().selectOne(sqlId("findByName"), ImmutableMap.of("name", name, "shopId", shopId));
    }
}
