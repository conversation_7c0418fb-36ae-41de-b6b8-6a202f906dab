/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.storage.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.LockKeyConstant;
import moonstone.item.emu.ItemTypeEnum;
import moonstone.item.model.SkuComboRelation;
import moonstone.item.service.SkuComboRelationService;
import moonstone.storage.impl.manager.DefaultStorageManager;
import moonstone.storage.service.StorageService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 默认的库存实现, 不分仓, 支持sku级别, 及item级别的库存 , 其中 productType 为1代表sku, 为2代表item
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-24
 */
@Slf4j
@RpcProvider
public class DefaultStorageServiceImpl implements StorageService {

    private final DefaultStorageManager defaultStorageManager;

    private final RedissonClient redissonClient;

    private final SkuComboRelationService skuComboRelationService;


    public DefaultStorageServiceImpl(DefaultStorageManager defaultStorageManager,RedissonClient redissonClient,SkuComboRelationService skuComboRelationService) {
        this.defaultStorageManager = defaultStorageManager;
        this.redissonClient = redissonClient;
        this.skuComboRelationService = skuComboRelationService;
    }

    /**
     * 根据产品id(可以是skuId, 也可以是itemId, 甚至也可以是spuId等), 以及对应的仓库id, 查找对应的库存
     *
     * @param productId   产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等), 由productType决定
     * @param productType 产品类型, 决定productId指sku, item还是spu
     * @param warehouseCode 仓库编码
     * @return 产品在对应仓库的库存
     */
    @Override
    public Response<Integer> findBy(Long productId, Integer productType, String warehouseCode) {
        throw new UnsupportedOperationException();
    }

    /**
     * 减少对应产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)及仓库id的库存
     *
     * @param productId     产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)
     * @param productType   产品类型, 决定productId指sku, item还是spu
     * @param warehouseCode 仓库编码
     * @param delta         库存变更数量
     * @return 是否变更成功
     */
    @Override
    public Response<Boolean> decreaseBy(Long productId, Integer productType, String warehouseCode, Integer delta, Integer itemType) {
        if (itemType.equals(ItemTypeEnum.COMBINATION.getCode())) {
            log.info("增加组合品的销量 skuId {} 数量 {} 扣减库存 {}", productId, delta, delta);
            List<SkuComboRelation> skuComboRelationList = skuComboRelationService.getSkuComboRelationListBySkuId(productId);
            List<RLock> locks = skuComboRelationList.stream().map(SkuComboRelation::getComboSkuId).map(comboSkuId -> redissonClient.getLock(inventoryLockKey(comboSkuId))).toList();
            RLock multiLock = redissonClient.getMultiLock(locks.toArray(new RLock[0]));
            try {
                // 尝试获取锁
                boolean locked = multiLock.tryLock(30, 60, TimeUnit.SECONDS);
                if (locked) {
                    for (SkuComboRelation skuComboRelation : skuComboRelationList) {
                        log.info("需要扣减的skuId {} 扣减数量 {}", skuComboRelation.getComboSkuId(), delta * skuComboRelation.getComboSkuQuantity());
                        defaultStorageManager.decreaseBy(skuComboRelation.getComboSkuId(), productType, warehouseCode, delta * skuComboRelation.getComboSkuQuantity());
                    }
                }
            } catch (Exception e) {
                log.error("扣减库存失败 失败原因 {}", e.getMessage(), e);
            } finally {
                multiLock.unlock();
            }
        } else {
            log.info("增加单品的销量 skuId {} 数量 {} 扣减库存 {}", productId, delta, delta);
            RLock lock = redissonClient.getLock(inventoryLockKey(productId));
            try {
                boolean locked = lock.tryLock(5, 20, TimeUnit.SECONDS);
                if (locked) {
                    defaultStorageManager.decreaseBy(productId, productType, warehouseCode, delta);
                }
                return Response.ok(Boolean.TRUE);
            } catch (ServiceException e) {
                log.error("fail to decrease by productId:{},productType:{},warehouseId:{},delta:{},cause:{}",
                        productId, productType, warehouseCode, delta, Throwables.getStackTraceAsString(e));
                return Response.fail(e.getMessage());
            } catch (Exception e) {
                log.error("fail to decrease by productId:{},productType:{},warehouseId:{},delta:{},cause:{}",
                        productId, productType, warehouseCode, delta, Throwables.getStackTraceAsString(e));
                return Response.fail("storage.decrease.fail");
            } finally {
                if (lock.isLocked()) {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }
        }
        return Response.ok(Boolean.TRUE);

    }

    /**
     * 增加对应产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)及仓库id的库存
     *
     * @param productId     产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)
     * @param productType   产品类型, 决定productId指sku, item还是spu
     * @param warehouseCode 仓库编码
     * @param delta         库存变更数量
     * @return 是否变更成功
     */
    @Override
    public Response<Boolean> increaseBy(Long productId, Integer productType, String warehouseCode, Integer delta, Integer itemType) {
        if (itemType.equals(ItemTypeEnum.COMBINATION.getCode())) {
            log.info("增加组合品的库存 skuId {} 数量 {} 扣减销量 {}", productId, delta, delta);
            List<SkuComboRelation> skuComboRelationList = skuComboRelationService.getSkuComboRelationListBySkuId(productId);
            List<RLock> locks = skuComboRelationList.stream().map(SkuComboRelation::getComboSkuId).map(comboSkuId -> redissonClient.getLock(inventoryLockKey(comboSkuId))).toList();
            RLock multiLock = redissonClient.getMultiLock(locks.toArray(new RLock[0]));
            try {
                // 尝试获取锁
                boolean locked = multiLock.tryLock(60, 60, TimeUnit.SECONDS);
                if (locked) {
                    for (SkuComboRelation skuComboRelation : skuComboRelationList) {
                        log.info("需要增加库存的skuId {} 增加数量 {}", skuComboRelation.getComboSkuId(), delta * skuComboRelation.getComboSkuQuantity());
                        defaultStorageManager.increaseBy(skuComboRelation.getComboSkuId(), productType, warehouseCode, delta * skuComboRelation.getComboSkuQuantity());
                    }
                }
            } catch (Exception e) {
                log.error("新加库存失败 失败原因 {}", e.getMessage(), e);
            } finally {
                multiLock.unlock();
            }
        } else {
            log.info("增加单品的库存 skuId {} 数量 {} 扣减销量 {}", productId, delta, delta);
            RLock lock = redissonClient.getLock(inventoryLockKey(productId));
            try {
                boolean locked = lock.tryLock(60, 60, TimeUnit.SECONDS);
                if (locked) {
                    defaultStorageManager.increaseBy(productId, productType, warehouseCode, delta);
                }
            } catch (ServiceException e) {
                log.error("fail to increase by productId:{},productType:{},warehouseId:{},delta:{},cause:{}",
                        productId, productType, warehouseCode, delta, Throwables.getStackTraceAsString(e));
                return Response.fail(e.getMessage());
            } catch (Exception e) {
                log.error("fail to increase by productId:{},productType:{},warehouseId:{},delta:{},cause:{}",
                        productId, productType, warehouseCode, delta, Throwables.getStackTraceAsString(e));
                return Response.fail("storage.decrease.fail");
            } finally {
                if (lock.isLocked()) {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }
        }
        return Response.ok(Boolean.TRUE);
    }

    /**
     * 设置对应产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)及仓库id的库存
     *
     * @param productId   产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)
     * @param productType 产品类型, 决定productId指sku, item还是spu
     * @param warehouseCode 仓库编码
     * @param quantity    库存目标数量
     * @return 是否设置成功
     */
    @Override
    public Response<Boolean> set(Long productId, Integer productType, String warehouseCode, Integer quantity) {
        throw new UnsupportedOperationException();
    }

    /**
     * 库存操作锁
     * @param skuId
     * @return
     */
    static String inventoryLockKey(Long skuId) {
        return LockKeyConstant.MALL_APP_SKU_STOCK_LOCK_PREFIX + skuId;
    }

}
