/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.storage.impl.manager;

import io.terminus.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.impl.dao.ItemDao;
import moonstone.item.impl.dao.SkuDao;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import org.springframework.transaction.annotation.Transactional;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-24
 */
@Slf4j
public class DefaultStorageManager {

    private final ItemDao itemDao;

    private final SkuDao skuDao;

    public DefaultStorageManager(ItemDao itemDao, SkuDao skuDao) {
        this.itemDao = itemDao;
        this.skuDao = skuDao;
    }

    /**
     * 减少对应产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)及仓库id的库存
     *
     * @param productId   产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)
     * @param productType 产品类型, 决定productId指sku, item还是spu
     * @param warehouseCode 仓库编码
     * @param delta       库存变更数量
     */
    @Transactional
    public void decreaseBy(Long productId, Integer productType, String warehouseCode, Integer delta) {
        Sku sku = skuDao.findById(productId);


        sku.setSaleQuantity(sku.getSaleQuantity() + delta);
        sku.setStockQuantity(sku.getStockQuantity() - delta);
        skuDao.update(sku);
//        Item item = itemDao.findById(itemId);

        //检查是否有库存
//        checkStockIfEnough(item, sku, delta);

//        skuDao.updateStockQuantity(skuId, delta);
//        itemDao.updateSaleQuantityAndStockQuantity(itemId, delta);
    }

    /**
     * 增加对应产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)及仓库id的库存
     *
     * @param productId   产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)
     * @param productType 产品类型, 决定productId指sku, item还是spu
     * @param warehouseCode 仓库编码
     * @param delta       库存变更数量
     */
    @Transactional
    public void increaseBy(Long productId, Integer productType, String warehouseCode, Integer delta) {
        //TODO 考虑productType和warehouseId
        Sku sku = skuDao.findById(productId);
        Integer newSaleQuantity  = sku.getSaleQuantity() - delta;
        Integer newStockQuantity = sku.getStockQuantity() + delta;
        log.info("库存返还 销量:{}-{}={} 库存:{}+{}={}",
                sku.getSaleQuantity(), delta, newSaleQuantity,
                sku.getStockQuantity(), delta, newStockQuantity);
        sku.setSaleQuantity(newSaleQuantity);
        sku.setStockQuantity(newStockQuantity);

//        sku.setSaleQuantity(sku.getSaleQuantity() - delta);
//        sku.setStockQuantity(sku.getStockQuantity() + delta);
        skuDao.update(sku);
//        if (sku == null) {
//            log.error("sku(id={}) not found", productId);
//            throw new ServiceException("sku.not.found");
//        }
//        final Long skuId = sku.getId();
//        final Long itemId = sku.getItemId();
//
//        if (delta >= 0) {
//            delta = -delta;
//        }
//
//        skuDao.updateStockQuantity(skuId, delta);
//        itemDao.updateSaleQuantityAndStockQuantity(itemId, delta);
    }

    private void checkStockIfEnough(Item item, Sku sku, Integer delta) {
        if (item.getStockQuantity() - delta < 0) {
            log.error("stock quantity not enough where item id={},expect quantity:{},but actual quantity:{}",
                    item.getId(), delta, item.getStockQuantity());
            throw new ServiceException("item.stock.quantity.not.enough");
        }
        if (sku.getStockQuantity() - delta < 0) {
            log.error("stock quantity not enough where sku id={},expect quantity:{},but actual quantity:{}",
                    sku.getId(), delta, sku.getStockQuantity());
            throw new ServiceException("sku.stock.quantity.not.enough");
        }
    }


}
