package moonstone.shop.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.shop.model.ShopPayInfo;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ShopPayInfoDao extends MyBatisDao<ShopPayInfo> {
    /**
     * 根据shopId查找该店铺下的所有支付信息记录
     *
     * @param shopId shopId
     * @return 支付信息记录
     */
    public List<ShopPayInfo> findByShopId(Long shopId) {
        return getSqlSession().selectList(sqlId("findByShopId"), shopId);
    }

    /**
     * 根据shopId及payChannel查找一条唯一支付信息记录
     *
     * @param shopId
     * @param payChannel
     * @return
     */
    public ShopPayInfo findByShopIdAndPayChannel(Long shopId, String payChannel) {
        return getSqlSession().selectOne(sqlId("findByShopIdAndPayChannel"), ImmutableMap.of(
                "shopId", shopId, "payChannel", payChannel
        ));
    }

    public List<ShopPayInfo> findAllByShopIdAndPayChannel(Long shopId, String payChannel, List<Integer> statusList) {
        return getSqlSession().selectList(sqlId("findAllByShopIdAndPayChannel"), ImmutableMap.of(
                "shopId", shopId,
                "payChannel", payChannel,
                "statusList", statusList
        ));
    }

    public List<ShopPayInfo> findAll() {
        return getSqlSession().selectList(sqlId("findAll"));
    }

    public ShopPayInfo getById(Long id) {
        return getSqlSession().selectOne(sqlId("getById"), Map.of("id", id));
    }

    public boolean revokeActive(Long shopId, Integer usageChannel) {
        return getSqlSession().update(sqlId("revokeActive"), Map.of("shopId", shopId,
                "usageChannel", usageChannel)) > 0;
    }

    public List<ShopPayInfo> find(Long shopId, Integer usageChannel, List<Integer> statusList) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopId", shopId);
        map.put("statusList", statusList);
        if (usageChannel != null) {
            map.put("usageChannel", usageChannel);
        }

        return getSqlSession().selectList(sqlId("find"), map);
    }

    public boolean updateStatus(Long shopPayInfoId, Integer newStatus, Integer oldStatus) {
        return getSqlSession().update(sqlId("updateStatus"), Map.of("id", shopPayInfoId,
                "newStatus", newStatus,
                "oldStatus", oldStatus)) > 0;
    }

    public List<ShopPayInfo> findByStatus(Long shopId, Integer status) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopId", shopId);
        map.put("status", status);
        return getSqlSession().selectList(sqlId("findByStatus"), map);
    }

}
