package moonstone.shop.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.shop.model.PayChannelRelation;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class PayChannelRelationDao extends MyBatisDao<PayChannelRelation> {

    public List<PayChannelRelation> findByQuery(PayChannelRelation relation) {
        return getSqlSession().selectList(sqlId("findByQuery"), ImmutableMap.of("relationType", relation.getRelationType()
                , "tradeType", relation.getTradeType()
                , "payChannel", relation.getPayChannel()
               , "shopId", relation.getShopId()));
    }


    public List<PayChannelRelation> findByTypeQuery(Long shopId, Integer relationType, Integer tradeType) {
        return getSqlSession().selectList(sqlId("findByQuery"), ImmutableMap.of("relationType", relationType
                , "tradeType", tradeType, "shopId", shopId));
    }

    public PayChannelRelation findById(Long id) {
        return getSqlSession().selectOne(sqlId("findById"), id);
    }

    public List<PayChannelRelation> findByShopId(Long shopId) {
        return getSqlSession().selectList(sqlId("findByShopId"), shopId);
    }

    public List<PayChannelRelation> findByProFileId(Long shopId, Long proFileId) {
        return getSqlSession().selectList(sqlId("findByProFileId"), ImmutableMap.of("shopId", shopId
                , "proFileId", proFileId));
    }

    public List<PayChannelRelation> findByType(Long shopId, Integer tradeType, Integer relationType) {
        return getSqlSession().selectList(sqlId("findByType"), ImmutableMap.of("shopId", shopId
                , "tradeType", tradeType, "relationType", relationType));
    }

}
