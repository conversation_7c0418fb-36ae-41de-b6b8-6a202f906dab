package moonstone.shop.impl.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.dto.shoppayinfo.PayChannelRelationDTO;
import moonstone.shop.enums.ShopPayInfoExtraIndexEnum;
import moonstone.shop.enums.ShopPayInfoStatusEnum;
import moonstone.shop.impl.dao.PayChannelRelationDao;
import moonstone.shop.impl.dao.ShopPayInfoDao;
import moonstone.shop.model.PayChannelRelation;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.PayChannelRelationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RpcProvider
public class PayChannelRelationServiceImpl implements PayChannelRelationService {

    @Autowired
    private PayChannelRelationDao payChannelRelationDao;
    @Autowired
    private ShopPayInfoDao shopPayInfoDao;

    @Override
    public Response<List<PayChannelRelation>> findAll() {
        return Response.ok(payChannelRelationDao.listAll());
    }

    @Override
    public Response<Long> saveRelation(PayChannelRelation relation) {
        try {
            payChannelRelationDao.create(relation);
            return Response.ok(relation.getId());
        } catch (Exception e) {
            log.error("新增支付渠道关系失败 relation={}, cause:{}", JSON.toJSONString(relation), Throwables.getStackTraceAsString(e));
            return Response.fail("relation add fail");
        }
    }

    @Override
    public Response<Boolean> modifyRelation(PayChannelRelation relation) {
        try{
            return Response.ok(payChannelRelationDao.update(relation));
        } catch (Exception e) {
            log.error("修改支付渠道关系失败 relation={}, cause:{}", JSON.toJSONString(relation), Throwables.getStackTraceAsString(e));
            return Response.fail("relation update fail");
        }
    }

    @Override
    public Response<Boolean> deleteRelation(Long id) {
        try{
            if (Objects.isNull(id)) {
                return Response.fail("id不能为空");
            }
            return Response.ok(payChannelRelationDao.delete(id));
        } catch (Exception e) {
            log.error("删除支付渠道关系失败 id={}, cause:{}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("relation delete fail");
        }
    }

    @Override
    public Response<PayChannelRelation> findById(Long id) {
        try{
            if (Objects.isNull(id)) {
                return Response.fail("id不能为空");
            }
            return Response.ok(payChannelRelationDao.findById(id));
        } catch (Exception e) {
            log.error("查询支付渠道关系失败 id={}, cause:{}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("relation delete fail");
        }
    }

    @Override
    public Response<List<PayChannelRelation>> findByShopId(Long shopId) {
        try{
            if (Objects.isNull(shopId)) {
                return Response.fail("shopId不能为空");
            }
            return Response.ok(payChannelRelationDao.findByShopId(shopId));
        } catch (Exception e) {
            log.error("查询支付渠道关系失败 shopId={}, cause:{}", shopId, Throwables.getStackTraceAsString(e));
            return Response.fail("relation delete fail");
        }
    }

    @Override
    public Response<List<PayChannelRelation>> findByProFileId(Long shopId, Long proFileId) {
        try{
            if (Objects.isNull(shopId)) {
                return Response.fail("shopId不能为空");
            }
            if (Objects.isNull(proFileId)) {
                return Response.fail("支付配置Id不能为空");
            }
            return Response.ok(payChannelRelationDao.findByProFileId(shopId, proFileId));
        } catch (Exception e) {
            log.error("查询支付渠道关系失败 shopId={}, cause:{}", shopId, Throwables.getStackTraceAsString(e));
            return Response.fail("relation delete fail");
        }
    }

    @Override
    public Response<List<PayChannelRelation>> findByType(Long shopId, Integer tradeType, Integer relationType) {
        try{
            if (Objects.isNull(shopId)) {
                return Response.fail("shopId不能为空");
            }
            if (Objects.isNull(tradeType)) {
                return Response.fail("贸易类型不能为空");
            }
            if (Objects.isNull(relationType)) {
                return Response.fail("发布类型不能为空");
            }
            return Response.ok(payChannelRelationDao.findByType(shopId, tradeType, relationType));
        } catch (Exception e) {
            log.error("查询支付渠道关系失败 shopId={}, cause:{}", shopId, Throwables.getStackTraceAsString(e));
            return Response.fail("relation delete fail");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<Boolean> shopPayChannelRelease(PayChannelRelationDTO relationDTO) {
        log.info("灰度或全量发布入参={}", JSON.toJSONString(relationDTO));
        if (Objects.isNull(relationDTO.getProFileId())) {
            throw new RuntimeException("配置ID不能为空");
        }
        ShopPayInfo shopPayInfo = shopPayInfoDao.getById(relationDTO.getProFileId());
        if (Objects.isNull(shopPayInfo)) {
            throw new RuntimeException("配置不存在");
        }
        if (!Objects.equals(ShopPayInfoStatusEnum.ACTIVE.getCode(), shopPayInfo.getStatus())) {
            throw new RuntimeException("配置已禁用");
        }
        if (Objects.equals(1, relationDTO.getRelationType())) {
            PayChannelRelation relation = new PayChannelRelation();
            relation.setTradeType(relationDTO.getTradeType());
            relation.setPayChannel(shopPayInfo.getPayChannel());
            relation.setRelationType(2);
            relation.setShopId(shopPayInfo.getShopId());
            List<PayChannelRelation> relationList = payChannelRelationDao.findByQuery(relation);
            if (!CollectionUtils.isEmpty(relationList)) {
                throw new RuntimeException("灰度发布与全量发布的支付方式不能一致");
            }
            //灰度需要更新成员
            Map<String, String> extra = shopPayInfo.getExtra();
            if (CollectionUtils.isEmpty(extra)) {
                extra = new HashMap<>();
            }
            extra.put(ShopPayInfoExtraIndexEnum.GREY_RELEASE_MEMBERS.getCode() + relationDTO.getTradeType(), JSON.toJSONString(relationDTO.getMemberList()));
            shopPayInfo.setExtra(extra);
            Boolean update = shopPayInfoDao.update(shopPayInfo);
            if (!update) {
                throw new RuntimeException("灰度更新成员失败");
            }
        } else {
            //全量发布,校验是否已存在灰度发布
            List<PayChannelRelation> relationList = payChannelRelationDao.findByTypeQuery(shopPayInfo.getShopId(), 1, relationDTO.getTradeType());
            if (!CollectionUtils.isEmpty(relationList)) {
                //说明灰度存在,直接覆盖
                for(PayChannelRelation channelRelation : relationList) {
                    Boolean delete = payChannelRelationDao.delete(channelRelation.getId());
                    if (!delete) {
                        throw new RuntimeException("覆盖灰度失败");
                    }
                    //灰度需要更新成员
                    ShopPayInfo payInfo = shopPayInfoDao.getById(channelRelation.getProFileId());
                    if (Objects.isNull(payInfo)) {
                        throw new RuntimeException("更新灰度成员,配置不存在");
                    }
                    String key = ShopPayInfoExtraIndexEnum.GREY_RELEASE_MEMBERS.getCode() + relationDTO.getTradeType();
                    Map<String, String> extra = payInfo.getExtra();
                    if (!CollectionUtils.isEmpty(extra) && StringUtils.isNotBlank(extra.get(key))) {
                        extra.remove(key);
                        payInfo.setExtra(extra);
                        Boolean update = shopPayInfoDao.update(payInfo);
                        if (!update) {
                            throw new RuntimeException("覆盖灰度成员失败");
                        }
                    }
                }
            }
        }

        //说明已经存在,需要编辑
        if (Objects.nonNull(relationDTO.getId())) {
            PayChannelRelation relation = payChannelRelationDao.findById(relationDTO.getId());
            if (Objects.equals(relationDTO.getProFileId(), relation.getProFileId())) {
                if (Objects.equals(1, relationDTO.getRelationType())) { //如果是灰度,前面有更新成员操作,直接返回
                    return Response.ok(Boolean.FALSE);
                }
                throw new RuntimeException("配置未发生变更,无需重复编辑");
            }
            relation.setProFileId(relationDTO.getProFileId());
            relation.setPayChannel(shopPayInfo.getPayChannel());
            relation.setUpdatedBy(relationDTO.getUpdatedBy());
            Boolean update = payChannelRelationDao.update(relation);
            if (!update) {
                throw new RuntimeException("修改配置失败");
            }
        } else {
            PayChannelRelation relation = new PayChannelRelation();
            relation.setProFileId(relationDTO.getProFileId());
            relation.setRelationType(relationDTO.getRelationType());
            relation.setTradeType(relationDTO.getTradeType());
            relation.setPayChannel(shopPayInfo.getPayChannel());
            relation.setShopId(shopPayInfo.getShopId());
            relation.setCreatedBy(relationDTO.getCreatedBy());
            relation.setUpdatedBy(relationDTO.getUpdatedBy());
            relation.setStatus(1);
            relation.setVersion(1L);
            Boolean insert = payChannelRelationDao.create(relation);
            if (!insert) {
                throw new RuntimeException("新增配置失败");
            }
        }
        return Response.ok(Boolean.TRUE);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<Boolean> shopPayChannelCancel(PayChannelRelation relation) {
        if (Objects.isNull(relation.getId())) {
            return Response.fail("取消失败,id不存在");
        }
        Boolean delete = payChannelRelationDao.delete(relation.getId());
        if (!delete) {
            throw new RuntimeException("取消失败");
        }
        ShopPayInfo shopPayInfo = shopPayInfoDao.getById(relation.getProFileId());
        if (Objects.isNull(shopPayInfo)) {
            throw new RuntimeException("取消失败,配置不存在");
        }
        if (Objects.equals(1, relation.getRelationType())) {
            // 灰度,删除灰度成员
            String key = ShopPayInfoExtraIndexEnum.GREY_RELEASE_MEMBERS.getCode() + relation.getTradeType();
            Map<String, String> extra = shopPayInfo.getExtra();
            if (!CollectionUtils.isEmpty(extra) && StringUtils.isNotBlank(extra.get(key))) {
                extra.remove(key);
                shopPayInfo.setExtra(extra);
                Boolean update = shopPayInfoDao.update(shopPayInfo);
                if (!update) {
                    throw new RuntimeException("取消失败");
                }
            }
        }
        return Response.ok(Boolean.TRUE);
    }


}
