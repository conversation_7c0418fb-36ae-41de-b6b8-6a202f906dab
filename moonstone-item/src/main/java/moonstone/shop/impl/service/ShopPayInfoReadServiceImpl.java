package moonstone.shop.impl.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.dto.ShopPayInfoCriteria;
import moonstone.shop.enums.ShopPayInfoPayChannelEnum;
import moonstone.shop.enums.ShopPayInfoStatusEnum;
import moonstone.shop.enums.ShopPayInfoUsageChannelEnum;
import moonstone.shop.impl.dao.ShopPayInfoDao;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.ShopPayInfoReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
@RpcProvider
public class ShopPayInfoReadServiceImpl implements ShopPayInfoReadService {
    private final ShopPayInfoDao shopPayInfoDao;

    @Autowired
    public ShopPayInfoReadServiceImpl(ShopPayInfoDao shopPayInfoDao) {
        this.shopPayInfoDao = shopPayInfoDao;
    }

    /**
     * 根据ID查找支付信息
     *
     * @param id ID
     * @return 支付信息
     */
    @Override
    public Response<ShopPayInfo> findById(Long id) {
        try {
            ShopPayInfo shopPayInfo = shopPayInfoDao.findById(id);
            if (shopPayInfo == null) {
                log.error("failed to find shopPayInfo by id={}", id);
                return Response.fail("shop.pay.info.not.find");
            }
            return Response.ok(shopPayInfo);
        } catch (Exception e) {
            log.error("failed to find shopPayInfo by id={}, cause:{}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.pay.info.not.find");
        }
    }

    /**
     * 根据shopId查找该店铺下的所有支付信息记录
     *
     * @param shopId shopId
     * @return 支付信息记录
     */
    @Override
    public Response<List<ShopPayInfo>> findByShopId(Long shopId) {
        try {
            List<ShopPayInfo> shopPayInfoList = shopPayInfoDao.findByShopId(shopId);
            if (CollectionUtils.isEmpty(shopPayInfoList)) {
                log.warn("not find shopPayInfo by id={}", shopId);
            }
            return Response.ok(shopPayInfoList);
        } catch (Exception e) {
            log.error("failed to find shopPayInfo by shopId={}, cause:{}", shopId, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.pay.info.not.find");
        }
    }

    /**
     * 根据shopId及payChannel查找一条唯一支付信息记录
     *
     * @param shopId
     * @param payChannel
     * @return
     */
    @Override
    public Response<ShopPayInfo> findByShopIdAndPayChannel(Long shopId, String payChannel) {
        try {
            ShopPayInfo shopPayInfo = shopPayInfoDao.findByShopIdAndPayChannel(shopId, payChannel);
            return Response.ok(shopPayInfo);
        } catch (Exception e) {
            log.error("fail to find shopPayInfo by shopId={}, payChannel={}, cause:{}", shopId, payChannel, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.pay.info.not.find");
        }
    }

    @Override
    public Response<List<ShopPayInfo>> findAllByShopIdAndPayChannel(Long shopId, ShopPayInfoPayChannelEnum payChannel,
                                                              List<ShopPayInfoStatusEnum> statusList) {
        try{
            if(shopId==null || payChannel==null || CollectionUtils.isEmpty(statusList)){
                return Response.fail("入参缺失");
            }

            return Response.ok(shopPayInfoDao.findAllByShopIdAndPayChannel(shopId, payChannel.getCode(),
                    statusList.stream().map(ShopPayInfoStatusEnum::getCode).toList()));
        }catch (Exception ex){
            log.error("ShopPayInfoReadServiceImp.findAllByShopIdAndPayChannel error, shopId={}, payChannel={}, statusList={}",
                    shopId, payChannel, JSON.toJSONString(statusList), ex);
            return Response.fail(ex.getMessage());
        }
    }

    /**
     * 搜索全平台支付信息
     *
     * @return
     */
    @Override
    public Response<List<ShopPayInfo>> findAll() {
        try {
            List<ShopPayInfo> shopPayInfoList = shopPayInfoDao.findAll();
            if (CollectionUtils.isEmpty(shopPayInfoList)) {
                log.warn("not find shopPayInfo");
            }
            return Response.ok(shopPayInfoList);
        } catch (Exception e) {
            log.error("failed to find shopPayInfo, cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("shop.pay.info.not.find");
        }
    }

    @Override
    public Response<Paging<ShopPayInfo>> paging(ShopPayInfoCriteria criteria) {
        try {
            int offset = criteria.getOffset();
            int limit = criteria.getLimit();

            return Response.ok(shopPayInfoDao.paging(offset, limit, criteria.toMap()));
        } catch (Exception ex) {
            log.error("shopPayInfoReadServiceImpl.paging error, criteria={}", JSON.toJSONString(criteria), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<ShopPayInfo> getById(Long shopPayInfoId) {
        try {
            if (shopPayInfoId == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(shopPayInfoDao.getById(shopPayInfoId));
        } catch (Exception ex) {
            log.error("shopPayInfoReadServiceImpl.getById error, shopPayInfoId={}", shopPayInfoId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<ShopPayInfo>> find(Long shopId, ShopPayInfoUsageChannelEnum usageChannel, List<ShopPayInfoStatusEnum> statusList) {
        try {
            if (shopId == null || usageChannel == null || CollectionUtils.isEmpty(statusList)) {
                return Response.fail("入参缺失");
            }

            return Response.ok(shopPayInfoDao.find(shopId, usageChannel.getCode(),
                    statusList.stream().map(ShopPayInfoStatusEnum::getCode).toList()));
        } catch (Exception ex) {
            log.error("shopPayInfoReadServiceImpl.find error, shopId={}, usageChannel={}, statusList={}", shopId,
                    usageChannel.getCode(), JSON.toJSONString(statusList), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<ShopPayInfo>> find(Long shopId, ShopPayInfoStatusEnum status) {
        try {
            if (shopId == null || status == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(shopPayInfoDao.find(shopId, null, List.of(status.getCode())));
        } catch (Exception ex) {
            log.error("shopPayInfoReadServiceImpl.find error, shopId={}, status={}", shopId, status.getCode(), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<ShopPayInfo>> findByStatus(Long shopId, ShopPayInfoStatusEnum status) {
        try {
            if (shopId == null || status == null) {
                return Response.fail("入参缺失");
            }
            return Response.ok(shopPayInfoDao.findByStatus(shopId, status.getCode()));
        } catch (Exception ex) {
            log.error("shopPayInfoReadServiceImpl.find error, shopId={}, status={}", shopId, status, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<ShopPayInfo>> findByIds(List<Long> idList) {
        try {
            if (CollectionUtils.isEmpty(idList)) {
                return Response.fail("入参缺失");
            }
            return Response.ok(shopPayInfoDao.findByIds(idList));
        } catch (Exception ex) {
            log.error("shopPayInfoReadServiceImpl.findByIds error, idList={}", idList, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
