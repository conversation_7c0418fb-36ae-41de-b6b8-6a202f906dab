package moonstone.shop.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.enums.ShopPayInfoStatusEnum;
import moonstone.shop.impl.dao.ShopPayInfoDao;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.ShopPayInfoWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@RpcProvider
public class ShopPayInfoWriteServiceImpl implements ShopPayInfoWriteService {
    private final ShopPayInfoDao shopPayInfoDao;

    @Autowired
    public ShopPayInfoWriteServiceImpl(ShopPayInfoDao shopPayInfoDao) {
        this.shopPayInfoDao = shopPayInfoDao;
    }

    /**
     * 添加店铺支付信息
     *
     * @param shopPayInfo 支付信息
     * @return 新增记录的ID
     */
    @Override
    public Response<Long> create(ShopPayInfo shopPayInfo) {
        try {
            shopPayInfoDao.create(shopPayInfo);
            if (shopPayInfo.getId() == null) {
                log.error("can not create shopPayInfo {}", shopPayInfo);
                return Response.fail("店铺支付信息添加失败");
            }
            return Response.ok(shopPayInfo.getId());
        } catch (DuplicateKeyException e) {
            throw e;
        } catch (Exception e) {
            log.error("failed to create shopPayInfo {}", shopPayInfo, e);
            return Response.fail(e.getMessage());
        }
    }

    /**
     * 更新店铺支付信息
     *
     * @param shopPayInfo 新的支付信息
     * @return 成功返回true
     */
    @Override
    public Response<Boolean> update(ShopPayInfo shopPayInfo) {
        try {
            return Response.ok(shopPayInfoDao.update(shopPayInfo));
        } catch (DuplicateKeyException e) {
            throw e;
        } catch (Exception e) {
            log.error("shopPayInfo update fail, cause = {}", e.getMessage());
            return Response.fail(e.getMessage());
        }
    }

    @Override
    public Response<Boolean> revokeActive(Long shopId, Integer usageChannel) {
        try {
            if (shopId == null || usageChannel == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(shopPayInfoDao.revokeActive(shopId, usageChannel));
        } catch (Exception ex) {
            log.error("ShopPayInfoWriteServiceImpl.revokeActive error, shopId={}", shopId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> updateStatus(Long shopPayInfoId, ShopPayInfoStatusEnum newStatus, ShopPayInfoStatusEnum oldStatus) {
        try {
            if (shopPayInfoId == null || newStatus == null || oldStatus == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(shopPayInfoDao.updateStatus(shopPayInfoId, newStatus.getCode(), oldStatus.getCode()));
        } catch (Exception ex) {
            log.error("ShopPayInfoWriteServiceImpl.updateStatus error, shopPayInfoId={}, newStatus={}, oldStatus={}",
                    shopPayInfoId, newStatus.getCode(), oldStatus.getCode(), ex);
            return Response.fail(ex.getMessage());
        }
    }

}
