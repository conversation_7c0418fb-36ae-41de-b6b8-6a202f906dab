/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.impl.service;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.dto.paging.SkuCriteria;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.impl.dao.SkuDao;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-03
 */
@Service
@Slf4j
@RpcProvider
public class SkuReadServiceImpl implements SkuReadService {

    private final SkuDao skuDao;

    @Autowired
    public SkuReadServiceImpl(SkuDao skuDao) {
        this.skuDao = skuDao;
    }

    /**
     * 根据skuId查找sku
     *
     * @param id sku id
     * @return SKU信息
     */
    @Override
    public Response<Sku> findSkuById(Long id) {
        try {
            Sku sku = skuDao.findById(id);
            if (sku == null) {
                log.error("sku(id={}) not found", id);
                return Response.fail("sku.not.found");
            }
            return Response.ok(sku);
        } catch (Exception e) {
            log.error("failed to find sku(id={}), cause:{}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.find.fail");
        }
    }

    /**
     * 根据商品名称和店铺id模糊查找skuId
     *
     * @param name
     * @param shopId
     * @return
     */
    @Override
    public Response<List<Long>> findIdsByNameAndShopId(String name, Long shopId) {
        try {
            return Response.ok(skuDao.findIdsByNameAndShopId(name, shopId));
        } catch (Exception e) {
            log.error("fail to find skuIds by name={}, shopId={}, cause:{}", name, shopId, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.ids.find.fail");
        }
    }

    /**
     * 根据sku id 列表获取sku列表
     *
     * @param skuIds skuid列表
     * @return sku列表
     */
    @Override
    public Response<List<Sku>> findSkusByIds(List<Long> skuIds) {

        if (CollectionUtils.isEmpty(skuIds)) {
            return Response.ok(Collections.emptyList());
        }
        try {
            List<Sku> skus = skuDao.findByIds(skuIds);
            return Response.ok(skus);
        } catch (Exception e) {
            log.error("failed to find skus(ids={}), cause:{}", skuIds, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.find.fail");
        }
    }

    /**
     * 根据外部数据信息查询sku数据
     *
     * @param shopId  店铺id
     * @param skuCode 外部sku编号
     * @return List
     * 返回sku列表
     */
    @Override
    public Response<List<Sku>> findSkuByCode(Long shopId, String skuCode) {
        try {
            List<Sku> skus = skuDao.findByShopIdAndSkuCode(shopId, skuCode);
            return Response.ok(skus);
        } catch (Exception e) {
            log.error("failed to find skus by (shopId={}, skuCode={}), cause:{}",
                    shopId, skuCode, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.find.fail");
        }
    }

    /**
     * 根据第三方skuId查询sku数据
     *
     * @param shopId
     * @param outerSkuId
     * @return
     */
    @Override
    public Response<List<Sku>> findSkuByOuterSkuId(Long shopId, String outerSkuId) {
        try {
            List<Sku> skus = skuDao.findByShopIdAndOuterSkuId(shopId, outerSkuId);
            return Response.ok(skus);
        } catch (Exception e) {
            log.error("failed to find skus by (shopId={}, outerSkuId={}), cause:{}",
                    shopId, outerSkuId, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.find.fail");
        }
    }

    /**
     * 根据店铺id及商品id找sku
     *
     * @param itemId 商品id, 对于分销系统也有可能是spuId , 这里统称为商品id
     * @return 商品的SKU
     */
    @Override
    public Response<List<Sku>> findSkusByItemId(Long itemId) {
        try {
            List<Sku> skus = skuDao.findByItemId(itemId);
            return Response.ok(skus);
        } catch (Exception e) {
            log.error("failed to find skus by (itemId={}), cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            e.printStackTrace();
            return Response.fail("sku.find.fail");
        }
    }

    @Override
    public Response<List<Sku>> findSkusByItemIds(List<Long> itemIds) {
        try {
            List<Sku> skus = skuDao.findByItemIds(itemIds);
            return Response.ok(skus);
        } catch (Exception e) {
            log.error("failed to find skus by (itemIds={}), cause:{}",
                    itemIds, Throwables.getStackTraceAsString(e));
            e.printStackTrace();
            return Response.fail("sku.find.fail");
        }
    }

    @Override
    public Response<Long> countByItemId(Long itemId) {
        try {
            Long count = skuDao.countByItemId(itemId);
            return Response.ok(count);
        } catch (Exception e) {
            log.error("fail to count skus by (itemId={}), cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            e.printStackTrace();
            return Response.fail("sku.count.fail");
        }
    }

    @Override
    public Response<Paging<Sku>> findBy(Long shopId,
                                        Long skuId,
                                        String skuCode,
                                        Long itemId,
                                        String skuName,
                                        String statuses,
                                        Integer status,
                                        Integer pageNo,
                                        Integer pageSize) {
        try {
            Map<String, Object> criteria = Maps.newHashMap();
            criteria.put("sortBy", "itemId");
            criteria.put("sortType", 2);

            criteria.put("shopId", shopId);
            if (StringUtils.hasText(statuses)) {
                criteria.put("statuses", Splitters.COMMA.split(statuses));
            } else if (status != null) {
                criteria.put("status", status);
            }
            if (StringUtils.hasText(skuCode)) {
                criteria.put("skuCode", skuCode);
            }
            if (itemId != null) {
                criteria.put("itemId", itemId);
            }
            if (skuId != null) {
                criteria.put("ids", ImmutableList.of(skuId));
            }
            if (StringUtils.hasText(skuName)) {
                criteria.put("name", skuName);
            }

            PageInfo page = PageInfo.of(pageNo, pageSize);
            criteria.put("offset", page.getOffset());
            criteria.put("limit", page.getLimit());
            return Response.ok(skuDao.paging(criteria));
        } catch (Exception e) {
            log.error("failed to find skus for seller(shopId={}),skuCode={},itemId={},skuName={}," +
                            "statues={},status={} pageNo={}, pageSize={}, cause: {}",
                    shopId, skuCode, itemId, skuName, statuses, status,
                    pageNo, pageSize, Throwables.getStackTraceAsString(e));
            e.printStackTrace();
            return Response.fail("sku.find.fail");
        }
    }

    @Override
    public Response<Paging<Sku>> pagingSellInWeShop(Long shopId,
                                                    Long itemId,
                                                    String skuName,
                                                    Integer pageNo,
                                                    Integer pageSize) {
        try {
            Map<String, Object> criteria = Maps.newHashMap();
            criteria.put("sortBy", "itemId");
            criteria.put("sortType", 2);

            criteria.put("shopId", shopId);
            if (itemId != null) {
                criteria.put("itemId", itemId);
            }
            if (StringUtils.hasText(skuName)) {
                criteria.put("name", skuName);
            }

            PageInfo page = PageInfo.of(pageNo, pageSize);
            criteria.put("offset", page.getOffset());
            criteria.put("limit", page.getLimit());
            return Response.ok(skuDao.pagingSellInWeShop(criteria));
        } catch (Exception e) {
            log.error("failed to paging skus sell in weShop for seller(shopId={}), itemId={}, skuName={}, pageNo={}, pageSize={}, cause: {}",
                    shopId, itemId, skuName, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            e.printStackTrace();
            return Response.fail("sku.paging.fail");
        }
    }

    @Override
    public Response<List<Sku>> findByShopIdAndProfitSet(long shopId, boolean profitSet) {
        try {
            return Response.ok(skuDao.findByShopIdAndProfitSet(shopId, profitSet));
        } catch (Exception ex) {
            log.error("[SkuReadService](findByShopIdAndProfitSet) fail to find by shopId:{} and profitSet:{}", shopId, profitSet);
            ex.printStackTrace();
            return Response.fail("sku.find.fail");
        }
    }

    public Response<Paging<Sku>> paing(SkuCriteria criteria) {
        try {
            if (criteria == null) {
                log.warn("[SkuReadService](paging) criteria is null so i init it");
                criteria = new SkuCriteria();
            }
            return Response.ok(skuDao.paging(criteria.toMap()));
        } catch (Exception ex) {
            log.error("[SkuReadService](paging) paging by criteria:{} fail", criteria.toString());
            ex.printStackTrace();
            return Response.fail("sku.paging.fail");
        }
    }

    @Override
    public List<Sku> selectList(Map<String, Object> query) {
        return skuDao.selectList(query);
    }

    @Override
    public void processSkuForActivity(List<Sku> skuList) {
        if (CollUtil.isEmpty(skuList)) {
            return;
        }
        for (Sku sku : skuList) {
            //更改售价
            if (!sku.isExtraActivityValid()) {
                continue;
            }
            String activitySalesPrice = sku.getExtraMap().get(SkuExtraIndex.activitySalesPrice.name());
            if (StringUtils.hasText(activitySalesPrice)) {
                sku.setPrice(Integer.parseInt(activitySalesPrice));
                sku.getExtraMap().put(SkuExtraIndex.isUseExtraActivity.name(), Boolean.TRUE.toString());
            }
        }


    }
}
