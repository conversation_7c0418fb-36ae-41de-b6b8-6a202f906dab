package moonstone.item.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.impl.dao.MerchantCommodityInventoryDao;
import moonstone.item.model.MerchantCommodityInventory;
import moonstone.item.service.MerchantCommodityInventoryReadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class MerchantCommodityInventoryReadImpl implements MerchantCommodityInventoryReadService {

    @Resource
    private MerchantCommodityInventoryDao merchantCommodityInventoryDao;


    @Override
    public Long isIndependentStockAvailable(Long skuId) {
        return merchantCommodityInventoryDao.sumAvailableQty(skuId);
    }

    @Override
    public MerchantCommodityInventory findByActivityIdAndSkuId(Integer activityId, Long skuId) {
        return merchantCommodityInventoryDao.findByActivityIdAndSkuId(activityId, skuId);
    }

    @Override
    public void validateSkuInventory(Integer activityId, Long skuId, Integer quantity) {
        // 校验独立库存
        MerchantCommodityInventory merchantCommodityInventory =  findByActivityIdAndSkuId(activityId, skuId);
        if(merchantCommodityInventory != null){
            if(merchantCommodityInventory.getAvailableQty() < quantity){
                log.info("订单支付 库存不足 {} {} < {}", skuId, merchantCommodityInventory.getAvailableQty(), quantity);
                throw new RuntimeException("库存不足");
            }
        }
    }
}
