package moonstone.item.impl.service;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.ApiException;
import moonstone.item.impl.dao.GbActivityConfigSkuDao;
import moonstone.item.impl.dao.GbActivityInfoDao;
import moonstone.item.impl.dao.MerchantCommodityInventoryDao;
import moonstone.item.model.GbActivityConfigSku;
import moonstone.item.model.GbActivityInfo;
import moonstone.item.model.MerchantCommodityInventory;
import moonstone.item.service.GbActivityConfigSkuReadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GbActivityConfigSkuReadServiceImpl implements GbActivityConfigSkuReadService {

    @Resource
    private GbActivityConfigSkuDao gbActivityConfigSkuDao;

    @Resource
    private GbActivityInfoDao gbActivityInfoDao;

    @Resource
    private MerchantCommodityInventoryDao merchantCommodityInventoryDao;

    @Override
    public List<GbActivityConfigSku> findByItemId(Long itemId) {
        List<GbActivityConfigSku> list = gbActivityConfigSkuDao.findByItemId(itemId);

        // 获取所有进行中和未开始的活动
        List<GbActivityInfo> gbActivityInfos = gbActivityInfoDao.selectListOnUse();
        if(CollUtil.isEmpty(gbActivityInfos)){
            return new ArrayList<>();
        }
        List<Long> activityIds = gbActivityInfos
                .stream()
                .map(GbActivityInfo::getId)
                .collect(Collectors.toList());

        return list.stream()
                .filter(dto -> activityIds.contains(dto.getActivityId()))
                .collect(Collectors.toList());
    }

    @Override
    public void validateSkuQuantity(Long skuId, Integer marketingToolId, Integer activityId, Integer quantity) {
        if(activityId == null){
            throw new ApiException("参数异常");
        }
        if(marketingToolId == null){
            throw new ApiException("参数异常");
        }
        GbActivityConfigSku gbActivityConfigSku = gbActivityConfigSkuDao.findByActivityIdAndSkuId(activityId, skuId, marketingToolId);
        if(gbActivityConfigSku == null){
            throw new ApiException("sku不在活动范围内");
        }
        if(gbActivityConfigSku.getSkuLimit() != null){
            if(quantity > gbActivityConfigSku.getSkuLimit()){
                throw new ApiException("超出限购数");
            }
        }

        MerchantCommodityInventory merchantCommodityInventory = merchantCommodityInventoryDao.findByActivityIdAndSkuId(activityId, skuId);
        if(merchantCommodityInventory == null){
            log.error("找不到活动库存 {} {}", activityId, skuId);
            throw new ApiException("活动库存不足");
        }
        if(quantity > merchantCommodityInventory.getAvailableQty()){
            throw new ApiException("活动库存不足");
        }
    }
}
