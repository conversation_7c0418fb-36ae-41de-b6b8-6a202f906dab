/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.*;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.GroupedSkuAttribute;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.cache.BackCategoryCacher;
import moonstone.cache.TaxCacher;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.CopyUtil;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.countryImage.impl.dao.CountryImageDao;
import moonstone.countryImage.model.CountryImage;
import moonstone.delivery.impl.dao.ItemDeliveryFeeDao;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.item.api.IndexedItemProcessor;
import moonstone.item.api.ProfitGain;
import moonstone.item.dto.*;
import moonstone.item.dto.paging.ItemCriteria;
import moonstone.item.emu.ItemExtraIndex;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.impl.dao.*;
import moonstone.item.model.*;
import moonstone.item.service.ItemReadService;
import moonstone.search.dto.IndexedItem;
import moonstone.shop.impl.dao.ShopDao;
import moonstone.shop.model.Shop;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 商品读服务
 *
 * <AUTHOR>
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-15
 */
@Slf4j
@Service
public class ItemReadServiceImpl implements ItemReadService {


    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    ItemDao itemDao;
    @Autowired
    ItemAttributeDao itemAttributeDao;

    @Autowired
    ItemDetailDao itemDetailDao;

    @Autowired
    SkuDao skuDao;

    @Autowired
    ItemDeliveryFeeDao itemDeliveryFeeDao;

    @Autowired
    SkuCustomDao skuCustomDao;

    @Autowired
    CountryImageDao countryImageDao;

    @Autowired
    TaxCacher taxCacher;

    @Autowired
    ShopDao shopDao;

    @Autowired
    ThirdPartySkuStockReadService thirdPartySkuStockReadService;

    @Autowired
    List<IndexedItemProcessor> indexedItemProcessorList;

    @Autowired
    BackCategoryCacher backCategoryCacher;


    @Autowired
    @SuppressWarnings("deprecation")
    ProfitGain profitGain;

    @Override
    public Response<List<Item>> findByShopIds(List<Long> shopIds) {
        try {
            return Response.ok(itemDao.findByShopIds(shopIds));
        } catch (Exception ex) {
            log.error("failed to page count item (shopId in {}), cause:{}", shopIds, ex);
            return Response.fail("category.find.fail");
        }
    }

    @Override
    public Response<List<Item>> findByShopIdAndExtraJsonLike(Long shopId, String extraJson) {
        try {
            return Response.ok(itemDao.findByShopIdAndExtraJsonLike(shopId, extraJson));
        } catch (Exception ex) {
            log.error("failed to find By ShopId And ExtraJson Like(shopId in {},extraJson), cause:{}", shopId, extraJson, ex);
            return Response.fail("category.find.fail");
        }
    }

    @Override
    public Response<List<Item>> findByShopIdsAndStatus(List<Long> shopIds, int status) {
        try {
            return Response.ok(itemDao.findByShopIdsAndStatus(shopIds, status));
        } catch (Exception ex) {
            log.error("failed to page count item (shopId in {},status in {}), cause:{}", shopIds, status, ex);
            return Response.fail("category.find.fail");
        }
    }

    @Override
    public Response<List<Item>> findByShopIdsAndLikeName(List<Long> shopIds, String name) {
        try {
            return Response.ok(itemDao.findByShopIdsAndLikeName(shopIds, name));
        } catch (Exception ex) {
            log.error("failed to page count item (shopId in {},name like {}%), cause:{}", shopIds, name, ex);
            return Response.fail("category.find.fail");
        }
    }

    @Override
    public Response<List<Item>> findByItemIdsAndLikeName(List<Long> itemIds, String name) {
        try {
            return Response.ok(itemDao.findByItemIdsAndLikeName(itemIds, name));
        } catch (Exception ex) {
            log.error("failed to page count item (itemId in {},name like {}%), cause:{}", itemIds, name, ex);
            return Response.fail("category.find.fail");
        }
    }

    @Override
    public Either<List<Long>> queryHotSellItem(long num) {
        try {
            return Either.ok(itemDao.queryHotSellItem(num));
        } catch (Exception ex) {
            log.error("fail to query the hot sell item", ex);
            return Either.error(ex);
        }
    }

    /**
     * 根据id查找商品
     *
     * @param itemId 商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    @Override
    public Response<Item> findById(Long itemId) {
        try {
            Item item = itemDao.findById(itemId);
            if (item == null) {
                log.error("item(id={}) not found", itemId);
                return Response.fail("item.not.found");
            }
            return Response.ok(item);
        } catch (Exception e) {
            log.error("failed to find item(id={}), cause:{}", itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    /**
     * 根据商铺id分页查询商品，根据是否上架筛选
     *
     * @param shopIds 商户id列表
     * @param offset  第几页
     * @param limit   每页展示数量
     * @param status  是否已经上架
     * @return 返回商品数据
     */
    @Override
    public Response<List<Item>> pageByShopIdsAndStatus(List<Long> shopIds, int offset, int limit, int status) {
        try {
            return Response.ok(itemDao.pageByShopIdsAndStatus(shopIds, offset, limit, status));
        } catch (Exception ex) {
            log.error("failed to page find item (shopId in {}), cause:{}", shopIds, ex);
            return Response.fail("category.find.fail");
        }
    }

    @Override
    public Response<List<Item>> pageByShopIdsAndStatusInWeShop(List<Long> shopIds, int offset, int limit, int status) {
        try {
            return Response.ok(itemDao.pageByShopIdsAndStatusInWeShop(shopIds, offset, limit, status));
        } catch (Exception ex) {
            log.error("failed to page find item (shopId in {}), cause:{}", shopIds, ex);
            return Response.fail("category.find.fail");
        }
    }

    /**
     * 根据商铺id分页查询商品
     *
     * @param shopIds 商户id列表
     * @param offset  第几页
     * @param limit   每页展示数量
     * @return 返回商品数据
     */
    @Override
    public Response<List<Item>> pageByShopIds(List<Long> shopIds, int offset, int limit) {
        try {
            return Response.ok(itemDao.pageByShopIds(shopIds, offset, limit));
        } catch (Exception ex) {
            log.error("failed to page find item (shopId in {}), cause:{}", shopIds, ex);
            return Response.fail("category.find.fail");
        }
    }

    @Override
    public Response<List<Item>> pageByShopIdsInWeShop(List<Long> shopIds, int offset, int limit) {
        try {
            return Response.ok(itemDao.pageByShopIdsInWeShop(shopIds, offset, limit));
        } catch (Exception ex) {
            log.error("failed to page find item (shopId in {}), cause:{}", shopIds, ex);
            return Response.fail("category.find.fail");
        }
    }

    /**
     * 根据shopId列表和对name右匹配进行分页查找
     *
     * @param shopIds shopId列表 不可为空
     * @param name    名字 不可为空
     * @param offset  第几页
     * @param limit   每页数据数量
     * @return 商品数据
     */
    @Override
    public Response<List<Item>> pageByShopIdsAndLikeNameInWeShop(List<Long> shopIds, String name, int offset, int limit) {
        try {
            return Response.ok(itemDao.pageByShopIdsAndLikeName(shopIds, name, offset, limit));
        } catch (Exception ex) {
            log.error("failed to page find item (shopId in {},name like {}), cause:{}", shopIds, name, ex);
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> pageByShopIdsAndLikeName(List<Long> shopIds, String name, int offset, int limit) {
        try {
            return Response.ok(itemDao.pageByShopIdsAndLikeName(shopIds, name, offset, limit));
        } catch (Exception ex) {
            log.error("failed to page find item (shopId in {},name like {}), cause:{}", shopIds, name, ex);
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> pageByShopIdsAndLikeNameAndStatusInWeShop(List<Long> shopIds, String name, int offset, int limit, int status) {
        try {
            return Response.ok(itemDao.pageByShopIdsAndLikeNameAndStatusInWeShop(shopIds, name, status, offset, limit));
        } catch (Exception ex) {
            log.error("failed to page find item (shopId in {},name like {},status:{}), cause:{}", shopIds, name, status, ex);
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> pageByShopIdsAndLikeNameAndStatus(List<Long> shopIds, String name, int offset, int limit, int status) {
        try {
            return Response.ok(itemDao.pageByShopIdsAndLikeNameAndStatus(shopIds, name, status, offset, limit));
        } catch (Exception ex) {
            log.error("failed to page find item (shopId in {},name like {},status:{}), cause:{}", shopIds, name, status, ex);
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> findByShopIdsAndLikeNameAndStatus(List<Long> shopIds, String name, int status) {
        try {
            return Response.ok(itemDao.findByShopIdsAndLikeNameAndStatus(shopIds, name, status));
        } catch (Exception ex) {
            log.error("failed to page count item (shopId in {},name like {}), cause:{}", shopIds, name, ex);
            return Response.fail("item.find.fail");
        }
    }

    /**
     * 根据店铺id查询id小于lastId内的limit个商品, 这个是dump搜素引擎用的
     *
     * @param itemIds 店铺id列表
     * @param name    名称
     * @param offset  第几页
     * @param limit   每页大小
     * @return 数据库查找到的id
     * added by wulianlei 2018/12/18 17:49
     */
    @Override
    public Response<List<Item>> pageByItemIdsAndLikeNameInWeShop(List<Long> itemIds, String name, int offset, int limit) {
        try {
            return Response.ok(itemDao.pageByItemIdsAndLikeNameInWeShop(itemIds, name, offset, limit));
        } catch (Exception ex) {
            log.error("failed to page find item (itemId in {},name like {}), cause:{}", itemIds, name, ex);
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> pageByItemIdsAndLikeName(List<Long> itemIds, String name, int offset, int limit) {
        try {
            return Response.ok(itemDao.pageByItemIdsAndLikeName(itemIds, name, offset, limit));
        } catch (Exception ex) {
            log.error("failed to page find item (itemId in {},name like {}), cause:{}", itemIds, name, ex);
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> pageByItemIdsAndLikeNameAndStatusInWeShop(List<Long> itemIds, String name, int offset, int limit, int status) {
        try {
            return Response.ok(itemDao.pageByItemIdsAndLikeNameAndStatusInWeshop(itemIds, name, offset, limit, status));
        } catch (Exception ex) {
            log.error("failed to page find item (itemId in {},name like {} status:{}), cause:{}", itemIds, name, status, ex);
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> pageByItemIdsAndLikeNameAndStatus(List<Long> itemIds, String name, int offset, int limit, int status) {
        try {
            return Response.ok(itemDao.pageByItemIdsAndLikeNameAndStatus(itemIds, name, offset, limit, status));
        } catch (Exception ex) {
            log.error("failed to page find item (itemId in {},name like {} status:{}), cause:{}", itemIds, name, status, ex);
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> findByItemIdsAndLikeNameAndStatus(List<Long> itemIds, String name, int status) {
        try {
            return Response.ok(itemDao.findByShopIdsAndLikeNameAndStatus(itemIds, name, status));
        } catch (Exception ex) {
            log.error("failed to page count item (itemId in {},name like {} status:{}), cause:{}", itemIds, name, status, ex);
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<Long> countByIdsAndStatus(List<Long> itemIds, int status) {
        try {
            return Response.ok(itemDao.countByIdsAndStatus(itemIds, status));
        } catch (Exception e) {
            log.error("failed to count items(ids={},status={}), cause:{}", itemIds, status, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> findByIdsAndStatusInWeShop(List<Long> itemIds, int status) {
        try {
            return Response.ok(itemDao.findByIdsAndStatusInWeShop(itemIds, status));
        } catch (Exception e) {
            log.error("failed to find items(ids={},status={}), cause:{}", itemIds, status, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> findByIdsAndStatus(List<Long> itemIds, int status) {
        try {
            return Response.ok(itemDao.findByIdsAndStatus(itemIds, status));
        } catch (Exception e) {
            log.error("failed to find items(ids={},status={}), cause:{}", itemIds, status, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<Long> countByIds(List<Long> itemIds) {
        try {
            return Response.ok(itemDao.countByIds(itemIds));
        } catch (Exception e) {
            log.error("failed to count items(ids={}, cause:{}", itemIds, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    /**
     * 根据ids查找商品列表
     *
     * @param itemIds 商品id列表
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    @Override
    public Response<List<Item>> findByIds(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Response.ok(Collections.emptyList());
        }
        try {
            List<Item> items = itemDao.findByIds(itemIds);
            if (CollectionUtils.isEmpty(items)) {
                return Response.ok(Collections.emptyList());
            }

            return Response.ok(items);
        } catch (Exception e) {
            log.error("failed to find items(ids={}), cause:{}", itemIds, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> findByIdsInWeShop(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Response.ok(Collections.emptyList());
        }
        try {
            List<Item> items = itemDao.findByIdsInWeShop(itemIds);
            if (items.isEmpty()) {
                return Response.ok(Collections.emptyList());
            }

            Map<Long, Item> itemMap = new HashMap<>(items.size());
            for (Item item : items) {
                itemMap.put(item.getId(), item);
            }
            Set<Long> existIds = new HashSet<>(items.size());
            List<Item> result = new ArrayList<>(items.size());
            for (Long itemId : itemIds) {
                if (existIds.contains(itemId) || !itemMap.containsKey(itemId)) {
                    continue;
                }
                existIds.add(itemId);
                result.add(itemMap.get(itemId));
            }

            return Response.ok(result);
        } catch (Exception e) {
            log.error("failed to find items(ids={}), cause:{}", itemIds, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    /**
     * 根据商品编码查询商品
     *
     * @param itemCode 商品编码
     * @return 对应的商品列表
     */
    @Override
    public Response<List<Item>> findByItemCode(String itemCode) {
        try {
            List<Item> items = itemDao.findByItemCode(itemCode);
            return Response.ok(items);
        } catch (Exception e) {
            log.error("failed to find item(itemCode={}), cause:{}",
                    itemCode, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    /**
     * 根据店铺id及商品编码来查找对应的商品
     *
     * @param shopId   店铺id
     * @param itemCode 商品编码
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    @Override
    public Response<List<Item>> findByShopIdAndCode(Long shopId, String itemCode) {
        try {
            List<Item> items = itemDao.findByShopIdAndCode(shopId, itemCode);
            return Response.ok(items);
        } catch (Exception e) {
            log.error("failed to find items by shopId={} and itemCode={}, cause:{}",
                    shopId, itemCode, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<List<Item>> findByShopIdAndCategoryIdLimit(Long shopId, Long categoryId, Integer limitNum) {
        try {
            List<Item> items = itemDao.findByShopIdAndCategoryIdLimit(shopId, categoryId, limitNum);
            return Response.ok(items);
        } catch (Exception e) {
            log.error("failed to find items by shopId={} and categoryId={}, cause:{}",
                    shopId, categoryId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Response<Paging<Item>> paging(ItemCriteria criteria) {
        try {
            PageInfo pageInfo = new PageInfo(criteria.getPageNo(), criteria.getPageSize());
            Paging<Item> itemPaging = itemDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), criteria.toMap());
            return Response.ok(itemPaging);
        } catch (Exception e) {
            log.error("fail to paging items by param={}, cause: {}", criteria, Throwables.getStackTraceAsString(e));
            return Response.fail("item.paging.fail");
        }
    }

    @Override
    public Response<List<Item>> list(ItemCriteria criteria) {
        try {
            if (criteria.getIds() != null && criteria.getIds().isEmpty()) {
                return Response.ok(new ArrayList<>());
            }
            List<Item> itemList = itemDao.list(criteria.toMap());
            return Response.ok(itemList);
        } catch (Exception e) {
            log.error("fail to list items by param={}, cause: {}", criteria, Throwables.getStackTraceAsString(e));
            return Response.fail("item.list.fail");
        }
    }

    /**
     * 商家后台商品列表,支持按状态查询,用于管理商品内商品，这个接口是被商家后台用的
     *
     * @param user     当前用户
     * @param itemCode 商品编码
     * @param itemId   商品id
     * @param itemName 商品名称
     * @param statuses 逗号分隔状态
     * @param status   状态
     * @param type     类型
     * @param pageNo   起始页码, 从1开始
     * @param pageSize 分页大小
     * @return 商品分页列表
     */
    @Override
    public Response<Paging<Item>> findBy(CommonUser user,
                                         List<Long> exShopIds,
                                         String itemCode,
                                         Long itemId,
                                         String itemName,
                                         String statuses,
                                         Integer status,
                                         Integer type,
                                         String typees,
                                         Integer sellOutStatus,
                                         Long shopCategoryId,
                                         Long restrictedSalesAreaTemplateId,
                                         Integer pageNo,
                                         Integer pageSize) {
        try {
            PageInfo page = new PageInfo(pageNo, pageSize);

            Map<String, Object> criteria = Maps.newHashMap();
            fillDefaultOrdinaryItemTypes(criteria);

            criteria.put("shopId", user.getShopId());
            criteria.put("offset", page.getOffset());
            criteria.put("limit", page.getLimit());
            if (!CollectionUtils.isEmpty(exShopIds)) {
                criteria.put("exShopIds", exShopIds);
            }
            if (StringUtils.hasText(statuses)) {
                criteria.put("statuses", Splitters.COMMA.split(statuses));
            } else if (status != null) {
                criteria.put("status", status);
            }
            if (StringUtils.hasText(typees)) {
                criteria.put("typees", Splitters.UNDERSCORE.split(typees));
            } else if (type != null) {
                criteria.put("type", type);
            }
            if (StringUtils.hasText(itemCode)) {
                criteria.put("itemCode", itemCode);
            }
            if (itemId != null) {
                criteria.put("ids", ImmutableList.of(itemId));
            }
            if (StringUtils.hasText(itemName)) {
                criteria.put("name", itemName);
            }
            if (sellOutStatus != null) {
                criteria.put("sellOutStatus", sellOutStatus);
            }
            if (shopCategoryId != null) {
                criteria.put("shopCategoryId", shopCategoryId);
            }
            if (restrictedSalesAreaTemplateId != null) {
                criteria.put("restrictedSalesAreaTemplateId", restrictedSalesAreaTemplateId);
            }

            return Response.ok(itemDao.paging(criteria));
        } catch (Exception e) {
            log.error("failed to find items for seller(shopId={}), pageNo={}, pageSize={}, status={}, cause: {}",
                    user.getShopId(), pageNo, pageSize, status, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    @Override
    public Item queryQuantity(Long itemId) {
        return itemDao.queryQuantity(itemId);
    }

    /**
     * 根据条件获取商品列表
     *
     * @param criteria 分页条件
     * @return 商品分页
     */
    @Override
    public Response<Paging<ItemForList>> pagingItemForList(ItemCriteria criteria) {
        try {
            Paging<Item> pItem = itemDao.paging(criteria.toMap());
            List<ItemForList> itemForLists = new ArrayList<>();
            for (Item item : pItem.getData()) {
                ItemForList itemForList = CopyUtil.copy(item, ItemForList.class);
                itemForList.setWxaUrl(criteria.getParanaWxaUrl() + "/goods/detail?storeId=" + criteria.getShopId() + "&skuSn=" + item.getId());
                itemForLists.add(itemForList);
            }
            return Response.ok(new Paging<>(pItem.getTotal(), itemForLists));
        } catch (Exception e) {
            log.error("pagingSettleOrderDetails fail, param={}, cause={}", criteria, Throwables.getStackTraceAsString(e));
            return Response.fail("settle.order.details.paging.fail");
        }
    }

    private void fillDefaultOrdinaryItemTypes(Map<String, Object> criteria) {
        criteria.put("sortBy", "id");
        criteria.put("sortType", 2);
    }

    /**
     * 根据商品id查找对应的商品及属性
     *
     * @param itemId 商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    @Override
    public Response<ItemWithAttribute> findItemWithAttributeById(Long itemId) {
        try {
            Item item = itemDao.findById(itemId);
            if (item == null) {
                log.error("item not found where itemId={}", itemId);
                return Response.fail("item.not.found");
            }

            ItemAttribute itemAttribute = itemAttributeDao.findByItemId(itemId);
            if (itemAttribute == null) {
                log.error("item attribute not found where itemId={}", itemId);
                return Response.fail("item.attribute.not.found");
            }

            ItemWithAttribute itemWithAttribute = new ItemWithAttribute();
            itemWithAttribute.setItem(item);
            itemWithAttribute.setItemAttribute(itemAttribute);
            return Response.ok(itemWithAttribute);
        } catch (Exception e) {
            log.error("failed to find item with attribute for item(id={}),cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.find.fail");
        }
    }

    /**
     * 根据商品id查找对应的商品信息及sku, 这个是给渲染商品详情页用的
     *
     * @param itemId 商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    @Override
    public Response<ViewedItem> findForView(Long itemId) {
        var rItem = findById(itemId);
        if (!rItem.isSuccess()) {
            return Response.fail(rItem.getError());
        }
        Item item = rItem.getResult();

        Shop shop = shopDao.findById(item.getShopId());
        if (shop != null) {
            item.setShopName(shop.getName());
        }

        ItemDetail itemDetail = itemDetailDao.findByItemId(itemId);
        if (itemDetail == null) {
            log.error("no item detail found for item(id={})", itemId);
            return Response.fail("item.detail.not.found");
        }

        ItemAttribute itemAttribute = itemAttributeDao.findByItemId(itemId);
        if (itemAttribute == null) {
            log.error("no item attribute found for item(id={})", itemId);
            return Response.fail("item.attribute.not.found");
        }

        List<Sku> skus = skuDao.findByItemId(itemId);
        String deliverDepotName = null;

        if (CollectionUtils.isEmpty(skus)) {
            log.warn("no sku found for item(id={})", itemId);
        }
        for (Sku sku : skus) {
            String pushSystem = Optional.ofNullable(sku.getTags())
                    .map(tags -> tags.get(SkuTagIndex.pushSystem.name())).orElse("");
            for (String systemCode : pushSystem.split(SkuTagIndex.pushSystem.getSplitter())) {
                if (systemCode.isEmpty() || ObjectUtils.isEmpty(sku.getOuterSkuId())) {
                    continue;
                }
                ThirdPartySystem system = ThirdPartySystem.fromInt(Integer.parseInt(systemCode));
                List<ThirdPartySkuStock> stockList = thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(sku.getShopId(),
                        system.Id(), sku.getOuterSkuId()).getResult();
                if (CollectionUtils.isEmpty(stockList)) {
                    continue;
                }
                for (ThirdPartySkuStock stock : stockList) {
                    if (null != stock.getDepotName()) {
                        deliverDepotName = stock.getDepotName();
                        break;
                    }
                }
            }
            if (null != deliverDepotName) {
                break;
            }
        }

        //活动生效期间的处理
        processItemForActivity(item, itemDetail);
        processSkuForActivity(skus);

        ViewedItem viewedItem = new ViewedItem();
        viewedItem.setItem(item);
        viewedItem.setImageInfos(itemDetail.getImages());
        viewedItem.setSkus(skus);
        viewedItem.setGroupedSkuAttrs(groupSkuAttrs(skus, itemAttribute.getSkuAttrs()));
        viewedItem.setCategoryLine(backCategoryCacher.buildBackCategoryLine(viewedItem.getItem().getCategoryId()));
        viewedItem.setVideoUrl(itemDetail.getVideoUrl());
        // fulfill the deliverDepotName
        viewedItem.setDeliverDepotName(deliverDepotName);

        IndexedItem indexedItem = new IndexedItem();
        BeanUtils.copyProperties(item, indexedItem);
        indexedItem.setPrice(item.getLowPrice());
        for (IndexedItemProcessor indexedItemProcessor : indexedItemProcessorList) {
            // skip other, only take the promotion one
            if (!indexedItemProcessor.getClass().getName().contains("Promotion")) {
                continue;
            }
            indexedItem = indexedItemProcessor.process(indexedItem);
        }
        viewedItem.setPreviewPrice(indexedItem.getPreviewPrice());
        viewedItem.setPromotionName(indexedItem.getPromotionName());
        if (viewedItem.getPromotionName() != null) {
            viewedItem.setPromotionStartAt(indexedItem.getPromotionStartAt());
            viewedItem.setPromotionEndAt(indexedItem.getPromotionEndAt());
        }
        // hack the item extra
        if (viewedItem.getItem().getExtra() == null) {
            viewedItem.getItem().setExtra(new HashMap<>());
        }
        viewedItem.getItem().getExtra().put("countryOfDeparture", deliverDepotName);

        // 跨境商品，需要获取海关信息
        if (!BondedType.fromInt(item.getIsBonded()).isBonded() || skus.isEmpty()) {
            // log.debug("Item {} Cost -> {}", itemId, System.currentTimeMillis() - start);
            return Response.ok(viewedItem);
        }
        for (int i = 0; i < skus.size(); i++) {
            Sku sku = skus.get(0);
            if (BondedType.fromInt(sku.getType()).isBonded()) {
                SkuCustom skuCustom = skuCustomDao.findNormalBySkuId(sku.getId());
                viewedItem.setDefaultOriginId(skuCustom.getCustomOriginId());
                viewedItem.setDefaultOrigin(skuCustom.getCustomOrigin());
                CountryImage countryImage = countryImageDao.findByCountryId(skuCustom.getCustomOriginId());
                if (countryImage != null) {
                    viewedItem.setDefaultOriginUrl(countryImage.getImageUrl());
                }
                viewedItem.setCustomTaxHolder(skuCustom.getCustomTaxHolder());
                // 原本的获取税率的方法
//                TaxAndOrigin taxAndOrigin = taxCacher.getOneTaxAndOrigin(sku);
                double defaultRate = 0.091;
                viewedItem.setRate(defaultRate);
                Optional.ofNullable(viewedItem.getRate())
                        .map(BigDecimal::valueOf)
                        .map(rate -> rate.multiply(new BigDecimal(sku.getPrice())))
                        .map(BigDecimal::longValue).ifPresent(viewedItem::setTax);
                break;
            } else {
                // 大贸商品默认认为是中国产地
                viewedItem.setDefaultOrigin("中国");
                Optional.ofNullable(countryImageDao.findByCountryName(viewedItem.getDefaultOrigin()))
                        .map(CountryImage::getImageUrl)
                        .ifPresent(viewedItem::setDefaultOriginUrl);
            }
        }
        // log.debug("Item {} Cost -> {}", itemId, System.currentTimeMillis() - start);
        return Response.ok(viewedItem);
    }

    /**
     * 若在活动生效期间，替换item的商品主图、明细图
     * <br/>PS: 仅在查询商品视图时使用
     */
    @Override
    public void processItemForActivity(Item item, ItemDetail itemDetail) {
        if (item == null || !item.isExtraActivityValid()) {
            return;
        }

        try {
            boolean isUseActivityData = false;
            //主图
            var mainImage = item.getMainImage_();
            String activityMainImage = item.getExtra().get(ItemExtraIndex.activityMainImage.name());
            if (StringUtils.hasText(activityMainImage)) {
                mainImage = activityMainImage;
                isUseActivityData = true;
            }

            //明细图
            var images = itemDetail.getImages();
            String activityDetailImages = item.getExtra().get(ItemExtraIndex.activityDetailImages.name());
            if (StringUtils.hasText(activityDetailImages)) {
                var list = Arrays.stream(activityDetailImages.split(","))
                        .filter(StringUtils::hasText)
                        .map(e -> new ImageInfo("", e))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(list)) {
                    images = ImageInfo.completeImageUrl(list);
                    isUseActivityData = true;
                }
            }

            item.setMainImage(mainImage);
            itemDetail.setImages(images);
            item.getExtra().put(ItemExtraIndex.isUseExtraActivity.name(), Boolean.toString(isUseActivityData));
        } catch (Exception ex) {
            log.error("processItemForActivity fail, itemId={}", item.getId(), ex);
        }
    }

    /**
     * 若在活动生效期间，替换 sku 的售价
     * <br/>PS: 仅在查询商品视图时使用
     */
    @Override
    public void processSkuForActivity(List<Sku> skus) {
        if (CollectionUtils.isEmpty(skus)) {
            return;
        }

        for (Sku sku : skus) {
            try {
                if (sku == null || !sku.isExtraActivityValid()) {
                    continue;
                }

                //更改售价
                String activitySalesPrice = sku.getExtraMap().get(SkuExtraIndex.activitySalesPrice.name());
                if (StringUtils.hasText(activitySalesPrice)) {
                    sku.setPrice(Integer.parseInt(activitySalesPrice));
                    sku.getExtraMap().put(SkuExtraIndex.isUseExtraActivity.name(), Boolean.TRUE.toString());
                }
            } catch (Exception ex) {
                log.error("processSkuFSorActivity fail, skuId={}", sku.getId(), ex);
            }
        }
    }

    @Override
    public Response<List<Item>> findAllByShopId(Long shopId) {
        try {
            return Response.ok(itemDao.findAllByShopId(shopId));
        } catch (Exception ex) {
            log.error("ItemReadServiceImpl.findAllByShopId error, shopId={}", shopId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Long> countByRestrictedSalesAreaTemplate(Long shopId, Long templateId) {
        try {
            if (shopId == null || templateId == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(itemDao.countByRestrictedSalesAreaTemplate(shopId, templateId));
        } catch (Exception ex) {
            log.error("ItemReadServiceImpl.countByRestrictedSalesAreaTemplate error, shopId={}, templateId={}",
                    shopId, templateId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<Item>> findAllByThirdParty(Long shopId, ThirdPartySystem thirdPartySystem) {
        try {
            if (shopId == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(itemDao.findAllByThirdParty(shopId, thirdPartySystem.Id()));
        } catch (Exception ex) {
            log.error("ItemReadServiceImpl.findAllV2 error, shopId={}", shopId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Long countByCondition(Map<String, Object> query) {
        return itemDao.countByCondition(query);
    }

    @Override
    public List<Item> pagesByCondition(Map<String, Object> query) {
        return itemDao.pagesByCondition(query);
    }

    /**
     * 根据商品id查找商品详细信息, 包括富文本, 包装参数, 服务信息, 分组属性信息等, 负责商品详情页的下半部分
     *
     * @param itemId 商品id
     * @return 商品详细信息
     */
    @Override
    public Response<ViewedItemDetailInfo> findItemDetailInfoByItemId(Long itemId) {
        try {
            ItemDetail itemDetail = itemDetailDao.findByItemId(itemId);
            if (itemDetail == null) {
                log.error("no item detail found for item (id={})", itemId);
                return Response.fail("item.detail.not.found");
            }

            //需求：定时改价改图片
            Item item = itemDao.findById(itemId);
            processItemForActivity(item, itemDetail);

            ItemAttribute itemAttribute = itemAttributeDao.findByItemId(itemId);
            if (itemAttribute == null) {
                log.error("no item attribute found for item (id={})", itemId);
                return Response.fail("item.attribute.not.found");
            }
            ViewedItemDetailInfo viewedItemDetailInfo = new ViewedItemDetailInfo();
            viewedItemDetailInfo.setProfitReVO(profitGain.getFirstProfitByItemId(itemId));
            viewedItemDetailInfo.setItemDetail(itemDetail);
            viewedItemDetailInfo.setItem(item);
            viewedItemDetailInfo.setGroupedOtherAttributes(itemAttribute.getOtherAttrs());
            return Response.ok(viewedItemDetailInfo);
        } catch (Exception e) {
            log.error("failed to find ViewedItemDetailInfo for item(id={}), cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));

            return Response.fail("item.detail.find.fail");
        }
    }

    /**
     * 将sku属性分组
     *
     * @param skus                 待分组的sku
     * @param groupedSkuAttributes 分组的销售属性
     * @return 分组的sku 属性
     */
    private List<GroupedSkuAttribute> groupSkuAttrs(List<Sku> skus, List<GroupedSkuAttribute> groupedSkuAttributes) {
        if (skus.isEmpty()) {
            return Collections.emptyList();
        }

        if (CollectionUtils.isEmpty(groupedSkuAttributes)) {
            return Collections.emptyList();
        }

        //因为sku中没有存图片等信息, 需要从item Attributes中将这些信息恢复出来

        //先将item Attribute 的销售属性按k,v 分组吧
        @SuppressWarnings("UnstableApiUsage") Table<String, String, SkuAttribute> byKeyVal = Tables.newCustomTable(Maps.newLinkedHashMap(), Maps::newLinkedHashMap);

        for (GroupedSkuAttribute skuAttr : groupedSkuAttributes) {
            String attrKey = skuAttr.getAttrKey();
            if (!CollectionUtils.isEmpty(skuAttr.getSkuAttributes())) {
                for (SkuAttribute skuAttribute : skuAttr.getSkuAttributes()) {
                    byKeyVal.put(attrKey, skuAttribute.getAttrVal(), skuAttribute);
                }
            }
        }

        //然后将sku选中的销售属性挑出来
        Multimap<String, SkuAttribute> attrKeyToAttrs = LinkedHashMultimap.create();
        for (Sku sku : skus) {
            //如果sku属性不为空, 则需要分组
            if (!CollectionUtils.isEmpty(sku.getAttrs())) {
                for (SkuAttribute skuAttribute : sku.getAttrs()) {
                    final String attrKey = skuAttribute.getAttrKey();
                    final String attrVal = skuAttribute.getAttrVal();
                    attrKeyToAttrs.put(attrKey, byKeyVal.get(attrKey, attrVal));
                }
            }
        }

        //收集并组装结果
        List<GroupedSkuAttribute> groupedSkuAttrs = Lists.newArrayList();
        for (String attrKey : attrKeyToAttrs.keySet()) {
            GroupedSkuAttribute gsa = new GroupedSkuAttribute();
            gsa.setAttrKey(attrKey);
            gsa.setSkuAttributes(Lists.newArrayList(attrKeyToAttrs.get(attrKey)));
            groupedSkuAttrs.add(gsa);
        }

        return groupedSkuAttrs;
    }


    /**
     * 根据商品id查找对应的商品的所有信息, 编辑商品信息也可以用这个接口
     *
     * @param itemId 商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    @Override
    public Response<FullItem> findFullInfoByItemId(Long itemId) {
        try {
            Item item = itemDao.findById(itemId);
            if (item == null) {
                log.error("item(id={}) not found", itemId);
                return Response.fail("item.not.found");
            }

            ItemDetail itemDetail = itemDetailDao.findByItemId(itemId);
            ItemAttribute itemAttribute = Optional.ofNullable(itemAttributeDao.findByItemId(itemId)).orElseGet(ItemAttribute::new);
            ItemDeliveryFee itemDeliveryFee = itemDeliveryFeeDao.findByItemId(itemId);
            List<Sku> skus = skuDao.findByItemId(itemId);
            FullItem fullItem = new FullItem();
            fullItem.setItem(item);
            fullItem.setItemDetail(itemDetail);
            fullItem.setItemDeliveryFee(itemDeliveryFee);
            List<SkuWithCustom> skuWithCustoms = new ArrayList<>();
            for (Sku sku : skus) {
                SkuWithCustom skuWithCustom = new SkuWithCustom();
                skuWithCustom.setSku(sku);
                if (BondedType.fromInt(sku.getType()).isBonded()) {
                    SkuCustom skuCustom = skuCustomDao.findNormalBySkuId(sku.getId());
                    skuWithCustom.setSkuCustom(skuCustom);
                }
                skuWithCustoms.add(skuWithCustom);
            }
            fullItem.setSkuWithCustoms(skuWithCustoms);
            fullItem.setGroupedOtherAttributes(itemAttribute.getOtherAttrs());
            fullItem.setGroupedSkuAttributes(itemAttribute.getSkuAttrs());
            return Response.ok(fullItem);
        } catch (Exception e) {
            log.error("failed to find full item info by itemId={}, cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.info.find.fail");
        }
    }


    /**
     * 根据商品id查找对应的商品详情富文本
     *
     * @param itemId 商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    @Override
    public Response<String> findRichTextById(Long itemId) {
        try {
            ItemDetail itemDetail = itemDetailDao.findByItemId(itemId);
            if (itemDetail == null) {
                log.error("item detail(itemId={}) not found", itemId);
                return Response.fail("item.detail.not.found");
            }
            return Response.ok(itemDetail.getDetail());
        } catch (Exception e) {
            log.error("failed to find item detail for item(id={}),cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.detail.find.fail");
        }
    }

    @Override
    public Response<Paging<ItemWithSkus>> findItemWithSkus(Long shopId, String itemCode, String itemName, Integer pageNo, Integer pageSize) {
        return findItemWithSkus(shopId, itemCode, itemName, null, null, pageNo, pageSize);
    }

    @Override
    public Response<Paging<ItemWithSkus>> findItemWithSkus(Long shopId, String itemCode, String itemName, Long shopCategoryId,
                                                           String typeStr, Integer pageNo, Integer pageSize) {
        try {
            Map<String, Object> criteria = Maps.newHashMap();
            fillDefaultOrdinaryItemTypes(criteria);
            if (StringUtils.hasText(typeStr)) {
                try {
                    List<Integer> typees = Optional.of(Stream.of(typeStr.split(",")).map(Integer::parseInt).collect(Collectors.toList())).filter(list -> !list.isEmpty())
                            .orElse(Collections.singletonList(-9999));
                    criteria.put("typees", typees);
                } catch (Exception ex) {
                    log.error("{} statuses:{} parse error", LogUtil.getClassMethodName(), typeStr, ex);
                    return Response.fail(new Translate("错误的状态码 [%s]", typeStr).toString());
                }
            }
            criteria.put("shopId", shopId);
            if (StringUtils.hasText(itemCode)) {
                criteria.put("itemCode", itemCode);
            }
            if (StringUtils.hasText(itemName)) {
                criteria.put("name", itemName);
            }
            if (shopCategoryId != null) {
                criteria.put("shopCategoryId", shopCategoryId);
            }
            PageInfo page = new PageInfo(pageNo, pageSize);
            criteria.put("offset", page.getOffset());
            criteria.put("limit", page.getLimit());

            Paging<Item> itemPaging = itemDao.paging(criteria);
            if (itemPaging.isEmpty()) {
                return Response.ok(Paging.empty());
            }
            List<Item> items = itemPaging.getData();

            List<Long> itemIds = Lists.transform(items, Item::getId);

            List<Sku> skus = skuDao.findByItemIds(itemIds);
            ListMultimap<Long, Sku> skusByItemIdIndex = ArrayListMultimap.create();
            for (Sku sku : skus) {
                skusByItemIdIndex.put(sku.getItemId(), sku);
            }

            List<ItemWithSkus> itemWithSkuses = Lists.newArrayListWithCapacity(items.size());
            for (Item item : items) {
                ItemWithSkus itemWithSkus = new ItemWithSkus();
                itemWithSkus.setItem(item);
                itemWithSkus.setSkus(Lists.newArrayList(skusByItemIdIndex.get(item.getId())));
                itemWithSkuses.add(itemWithSkus);
            }

            return Response.ok(new Paging<>(itemPaging.getTotal(), itemWithSkuses));
        } catch (Exception e) {
            log.error("fail to find item with skus,params(shopId={},itemCode={},itemName={},pageSize={},pageNo={}),cause:{},",
                    shopId, itemCode, itemName, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            return Response.fail("item.with.skus.find.fail");
        }
    }

    @Override
    public Response<List<Item>> listSince(Long lastId, Date since, Integer limit) {
        try {
            if (lastId == null) {
                lastId = itemDao.maxId() + 1;
            }
            List<Item> items = itemDao.listSince(lastId, DATE_TIME_FORMAT.print(new DateTime(since)), limit);
            return Response.ok(items);
        } catch (Exception e) {
            log.error("fail to list items by lastId={},since={},limit={},cause:{}",
                    lastId, since, limit, Throwables.getStackTraceAsString(e));
            return Response.fail("item.list.fail");
        }
    }

    @Override
    public Long countByBrand(Long shopId, Long brandId) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopId", shopId);
        map.put("brandId", brandId);
        return itemDao.countByCondition(map);
    }

    @Override
    public List<Item> findByBrandId(Long brandId) {
        return itemDao.list(ImmutableMap.of("brandId", brandId));
    }

}
