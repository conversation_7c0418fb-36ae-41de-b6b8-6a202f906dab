package moonstone.item.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.impl.dao.GbActivityInfoDao;
import moonstone.item.impl.dao.GbGroupInfoDao;
import moonstone.item.impl.dao.GbGroupMemberDao;
import moonstone.item.model.GbActivityInfo;
import moonstone.item.model.GbGroupInfo;
import moonstone.item.model.GbGroupMember;
import moonstone.item.service.GbGroupInfoReadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class GbGroupInfoReadServiceImpl implements GbGroupInfoReadService {

    @Resource
    private GbGroupInfoDao gbGroupInfoDao;

    @Resource
    private GbGroupMemberDao gbGroupMemberDao;

    @Resource
    private GbActivityInfoDao gbActivityInfoDao;

    @Override
    public boolean groupIsSuccess(Long shopOrderId) {
        GbGroupMember gbGroupMember = gbGroupMemberDao.findByShopOrderId(shopOrderId);
        if(gbGroupMember == null){
            log.info("订单无关联团成员 忽略 {}", shopOrderId);
            return false;
        }
        GbGroupInfo gbGroupInfo = gbGroupInfoDao.findById(gbGroupMember.getGroupId());
        if(gbGroupInfo == null){
            log.error("团ID不存在 忽略 {}", shopOrderId);
            return false;
        }
        Integer groupStatus = gbGroupInfo.getGroupStatus();
        if(groupStatus == 3){
            // 开团成功
            return true;
        }
        return false;
    }

    @Override
    public GbActivityInfo findActivityInfoById(Integer activityId) {
        return gbActivityInfoDao.findById(activityId);
    }

    @Override
    public GbGroupInfo findById(Long groupId) {
        return gbGroupInfoDao.findById(groupId);
    }
}
