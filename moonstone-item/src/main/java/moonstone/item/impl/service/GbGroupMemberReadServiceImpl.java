package moonstone.item.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.impl.dao.GbGroupInfoDao;
import moonstone.item.impl.dao.GbGroupMemberDao;
import moonstone.item.model.GbGroupInfo;
import moonstone.item.model.GbGroupMember;
import moonstone.item.service.GbGroupMemberReadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class GbGroupMemberReadServiceImpl implements GbGroupMemberReadService {

    @Resource
    private GbGroupMemberDao gbGroupMemberDao;

    @Resource
    private GbGroupInfoDao gbGroupInfoDao;

    @Override
    public List<GbGroupMember> findLeaderByActivityId(Integer activityId, Long userId) {
        List<GbGroupMember> gbGroupMember = gbGroupMemberDao.findLeaderByActivityId(activityId, userId);
        return gbGroupMember;
    }

    @Override
    public GbGroupMember findByShopOrderId(Long shopOrderId, Long userId) {
        GbGroupMember gbGroupMember = gbGroupMemberDao.findGroupIdByShopOrderId(shopOrderId, userId);
        return gbGroupMember;
    }

    @Override
    public Long countGroupMemberNum(Integer activityId, Long groupId) {
        Long countGroupMemberNum = gbGroupMemberDao.countGroupMemberNum(activityId, groupId);
        return countGroupMemberNum;
    }

    @Override
    public GbGroupInfo findGroupInfoByShopOrderId(Long shopOrderId, Long userId) {
        GbGroupMember gbGroupMember = gbGroupMemberDao.findGroupIdByShopOrderId(shopOrderId, userId);
        if (gbGroupMember != null) {
            GbGroupInfo gbGroupInfo = gbGroupInfoDao.findById(gbGroupMember.getGroupId());
            return gbGroupInfo;
        }
        return null;
    }

    @Override
    public GbGroupInfo findGroupInfo(GbGroupMember gbGroupMember) {
        GbGroupInfo gbGroupInfo = gbGroupInfoDao.findById(gbGroupMember.getGroupId());
        return gbGroupInfo;
    }
}
