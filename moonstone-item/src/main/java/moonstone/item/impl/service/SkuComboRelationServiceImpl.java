package moonstone.item.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.impl.dao.SkuComboRelationDao;
import moonstone.item.model.SkuComboRelation;
import moonstone.item.service.SkuComboRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SkuComboRelationServiceImpl implements SkuComboRelationService {

	@Resource
	private SkuComboRelationDao skuComboRelationDao;

	@Override
	public List<SkuComboRelation> getSkuComboRelationListBySkuIdList(List<Long> skuIdList) {
		Map<String, Object> query = new HashMap<>();
		query.put("skuIdList", skuIdList);
		return skuComboRelationDao.selectList(query);
	}

	@Override
	public List<SkuComboRelation> getSkuComboRelationListBySkuId(Long skuId) {
		Map<String,Object> query = new HashMap<>();
		query.put("skuId", skuId);
		return skuComboRelationDao.selectList(query);
	}

	@Override
	public List<SkuComboRelation> getSkuComboRelationListByComboItemId(Long comboItemId) {
		Map<String,Object> query = new HashMap<>();
		query.put("comboItemId", comboItemId);
		return skuComboRelationDao.selectList(query);
	}
}
