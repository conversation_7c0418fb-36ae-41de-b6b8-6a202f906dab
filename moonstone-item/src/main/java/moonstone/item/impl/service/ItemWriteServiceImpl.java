/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.impl.service;

import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.category.impl.dao.BackCategoryDao;
import moonstone.category.impl.dao.ShopCategoryItemDao;
import moonstone.category.model.BackCategory;
import moonstone.category.model.ShopCategory;
import moonstone.category.model.ShopCategoryItem;
import moonstone.common.exception.ApiException;
import moonstone.common.model.Either;
import moonstone.common.utils.Translate;
import moonstone.delivery.impl.dao.ItemDeliveryFeeDao;
import moonstone.delivery.model.DeliveryFeeTemplate;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.delivery.service.DeliveryFeeReadService;
import moonstone.delivery.service.DeliveryFeeWriteService;
import moonstone.item.common.Digestors;
import moonstone.item.dto.FullItem;
import moonstone.item.dto.SkuWithCustom;
import moonstone.item.emu.ItemStatusEnum;
import moonstone.item.impl.dao.*;
import moonstone.item.impl.manager.ItemManager;
import moonstone.item.model.*;
import moonstone.item.service.ItemWriteService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopWriteService;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商家对商品的写服务
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-26
 */
@Slf4j
@Service
@AllArgsConstructor
public class ItemWriteServiceImpl implements ItemWriteService {
    ItemManager itemManager;
    ItemDetailDao itemDetailDao;
    ItemDao itemDao;
    ShopWriteService shopWriteService;
    ItemAttributeDao itemAttributeDao;
    BackCategoryDao backCategoryDao;
    SkuDao skuDao;
    ItemDeliveryFeeDao itemDeliveryFeeDao;
    SkuCustomDao skuCustomDao;
    DeliveryFeeReadService deliveryFeeReadService;
    DeliveryFeeWriteService deliveryFeeWriteService;
    ThirdPartySkuStockReadService thirdPartySkuStockReadService;
    ShopCategoryItemDao shopCategoryItemDao;


    private Map<String, String> shapeExtra(Map<String, String> map) {
        return map == null ? new TreeMap<>() : map;
    }

    @Transactional(rollbackForClassName = "RunTimeException")
    public Response<Long> copy(Item item, Shop shop) {
        try {
            DeliveryFeeTemplate template;
            long de;
            try {
                de = Long.parseLong(shapeExtra(shop.getExtra()).getOrDefault("defaultDelievryTemplete", "0"));
            } catch (Exception ex) {
                de = 0L;
            }
            if (0L != de) {
                val rD = deliveryFeeReadService.findDeliveryFeeTemplateById(de);
                template = rD.getResult();
                if (!rD.isSuccess()) {
                    throw new RuntimeException(rD.getError());
                }
            } else {
                template = new DeliveryFeeTemplate();
                template.setShopId(shop.getId());
                template.setName("包邮");
                template.setIsFree(true);
                template.setIsDefault(false);
                template.setChargeMethod(1);
                template.setChargeMethod(2);
                template.setDeliverMethod(1);
                de = deliveryFeeWriteService.createDeliveryFeeTemplate(template).getResult();
                Map<String, String> extra = shapeExtra(shop.getExtra());
                extra.put("defaultDelievryTemplete", "" + de);
                shop.setExtra(extra);
                shopWriteService.update(shop);
            }
            Long itemId = item.getId();
            List<Sku> skus = skuDao.findByItemId(itemId);
            List<SkuCustom> skuCustoms = skuCustomDao.findBySkuIds(skus.parallelStream().map(Sku::getId).collect(Collectors.toList()));
            ItemDetail itemDetail = itemDetailDao.findByItemId(itemId);
            ItemAttribute attribute = itemAttributeDao.findByItemId(itemId);
            item.setShopId(shop.getId());
            item.setShopName(shop.getName());
            item.setId(null);
            item.setStatus(-1);
            itemDao.create(item);
            //add by liuchao 20190409
            ShopCategoryItem rootCategory = new ShopCategoryItem();
            rootCategory.setShopId(item.getShopId());
            rootCategory.setItemId(item.getId());
            rootCategory.setShopCategoryId(ShopCategory.ID_ROOT);
            shopCategoryItemDao.create(rootCategory);
            ItemDeliveryFee deliveryFee = new ItemDeliveryFee();
            deliveryFee.setItemId(item.getId());
            deliveryFee.setDeliveryFee(0);
            deliveryFee.setDeliveryFeeTemplateId(template.getId());
            if (!itemDeliveryFeeDao.create(deliveryFee)) {
                throw new RuntimeException("deliveryFee fail");
            }
            Long newitemId = item.getId();
            TreeMap<Long, SkuCustom> skuCustomTreeMap = new TreeMap<>();
            skuCustoms.forEach(skuCustom -> skuCustomTreeMap.put(skuCustom.getSkuId(), skuCustom));
            skus.forEach(sku -> {
                Long skuId = sku.getId();
                sku.setId(null);
                sku.setShopId(shop.getId());
                sku.setItemId(newitemId);
                sku.setStatus(-1);
                skuDao.create(sku);
                SkuCustom skuCustom = skuCustomTreeMap.get(skuId);
                if (skuCustom != null) {
                    skuCustom.setId(null);
                    skuCustom.setSkuId(sku.getId());
                    skuCustomDao.create(skuCustom);
                }
            });
            itemDetail.setItemId(item.getId());
            itemDetailDao.create(itemDetail);
            attribute.setItemId(newitemId);
            itemAttributeDao.create(attribute);
            return Response.ok(item.getId());
        } catch (Exception ex) {
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Long> create(Item item) {
        if (!itemDao.create(item)) {
            throw new RuntimeException("item创建失败");
        }

        return Response.ok(item.getId());
    }

    /**
     * 创建商品
     *
     * @param fullItem 待创建的商品, 这里已经过各种校验,且商品信息已经完善
     * @return 新创建的商品id
     */
    @Override
    public Response<Long> create(FullItem fullItem) {
        try {
            Item item = fullItem.getItem();

            //检查类目是否存在
            Long categoryId = item.getCategoryId();
            BackCategory backCategory = backCategoryDao.findById(categoryId);
            if (backCategory == null) {
                log.error("back category(id={}) not found", categoryId);
                return Response.fail("category.not.found");
            }
            //非叶子类目不允许挂item
            if (backCategory.getHasChildren()) {
                log.error("back category(id={}) is not leaf", categoryId);
                return Response.fail("category.not.leaf");
            }

            //初始化商品的排序值
            initItemIndex(item);

            ItemAttribute itemAttribute = new ItemAttribute();
            itemAttribute.setOtherAttrs(fullItem.getGroupedOtherAttributes());
            itemAttribute.setSkuAttrs(fullItem.getGroupedSkuAttributes());
            String itemInfoMd5 = Digestors.itemDigest(item, fullItem.getItemDetail(), itemAttribute);
            item.setItemInfoMd5(itemInfoMd5);
            if (ObjUtil.isEmpty(item.getActivityImageSwitch())) {
                // 默认关闭
                item.setActivityImageSwitch(0);
            }
            Long itemId = itemManager.createItem(item, fullItem.getItemDetail(), itemAttribute, fullItem.getItemDeliveryFee(), fullItem.getSkuWithCustoms());
            return Response.ok(itemId);
        } catch (Exception e) {
            log.error("failed to create {}, cause:{}", fullItem, Throwables.getStackTraceAsString(e));
            return Response.fail("item.create.fail");
        }
    }

    /**
     * 初始化商品的排序值
     *
     * @param item
     */
    private void initItemIndex(Item item) {
        var minIndex = itemDao.findSmallestIndex(item.getShopId());
        if (minIndex == null) {
            item.setIndex(0);
        } else {
            item.setIndex(minIndex - 1);
        }
    }


    /**
     * 更新商品
     *
     * @param fullItem 待更新的商品, 这里已经过各种校验,且商品信息已经完善
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> update(FullItem fullItem) {
        try {
            Item item = fullItem.getItem();

            if (item.getStatus() == null) {//取数据库当前商品状态吧
                Item itemInDB = itemDao.findById(item.getId());
                if (itemInDB == null) {
                    log.error("item(id={}) not found", item.getId());
                    return Response.fail("item.not.found");
                }
                item.setStatus(itemInDB.getStatus());
            }
            ItemAttribute itemAttribute = new ItemAttribute();
            itemAttribute.setOtherAttrs(fullItem.getGroupedOtherAttributes());
            itemAttribute.setSkuAttrs(fullItem.getGroupedSkuAttributes());

            //为了生成md5, 不得不捞出商品详情

            String richText = itemDetailDao.findByItemId(item.getId()).getDetail();
            ItemDetail itemDetail = fullItem.getItemDetail();
            if (itemDetail == null) {
                itemDetail = new ItemDetail();
            }
            itemDetail.setItemId(item.getId());
            itemDetail.setDetail(richText);
            String itemInfoMd5 = Digestors.itemDigest(item, itemDetail, itemAttribute);
            item.setItemInfoMd5(itemInfoMd5);
            // if sku is third-party-sku, query it and copy the depot-code
            for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                var outerSkuId = skuWithCustom.getSku().getOuterSkuId();
                var thirdPartyId = Optional.ofNullable(skuWithCustom.getSku().getTags())
                        .map(extra -> extra.get("pushSystem"))
                        .map(system -> system.split(",")[0])
                        .filter(StringUtils::isNumeric)
                        .map(Integer::parseInt)
                        .orElse(1);
                thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(item.getShopId(), thirdPartyId, outerSkuId)
                        .getResult().stream().filter(valid -> valid.getStatus() > 0)
                        .map(ThirdPartySkuStock::getDepotCode).filter(Objects::nonNull).findFirst().ifPresent(skuWithCustom.getSku()::setDepotCode);
            }
            itemManager.updateItem(item, itemDetail, itemAttribute, fullItem.getItemDeliveryFee(), fullItem.getSkuWithCustoms());
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update {}, cause:{}", fullItem, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }


    /**
     * 更新商品
     *
     * @param item 待更新的商品, 这里已经过各种校验,且商品信息已经完善
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> update(Item item) {
        try {
            Boolean result = itemDao.update(item);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("fail to update {}, cause:{}", item, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }

    /**
     * 删除商品, 先都逻辑删除吧
     *
     * @param shopId 商品id
     * @param itemId 待删除的商品id
     * @return 是否删除成功
     */
    @Override
    public Response<Boolean> delete(Long shopId, Long itemId) {
        try {
            itemManager.delete(shopId, itemId);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to delete item(id={}) of shop(id={}), cause:{}",
                    itemId, shopId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.delete.fail");
        }
    }

    /**
     * 商家更新店铺商品状态, 防止商家更新别家的商品
     *
     * @param shopId 店铺id
     * @param itemId 商品id
     * @param status 商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> updateStatusByShopIdAndItemId(Long shopId, Long itemId, Integer status) {
        try {
            // 已删除商品不允许更新状态
            Item originItem = itemDao.findById(itemId);
            if (Objects.equals(originItem.getStatus(), -3)) {
                log.error("try to update item status by shopId:{}, itemId:{}, status:{}, but item already deleted",
                        shopId, itemId, status);
                return Response.fail("item.update.fail");
            }
            // 库存不足则不允许上架
            if (originItem.getStockQuantity() <= 0 && status == 1) {
                log.warn("[refuse-sale-out-storage] item (id:{}) shop (id:{})", itemId, shopId);
                return Response.fail("stock.empty");
            }

            itemManager.updateStatusByShopIdAndItemId(shopId, itemId, status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update status of item(id={}) of shop(id={}), cause:{}",
                    itemId, shopId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }

    /**
     * 批量更新商品状态, 这是给商家用的
     *
     * @param shopId 店铺id
     * @param ids    商品id列表
     * @param status 商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     */
    @Override
    public Response<Boolean> batchUpdateStatusByShopIdAndItemIds(Long shopId, List<Long> ids, Integer status) {
        try {
            // 已删除商品不允许更新状态
            for (Long itemId : ids) {
                Item originItem = itemDao.findById(itemId);
                if (Objects.equals(originItem.getStatus(), -3)) {
                    log.error("try to update item status by shopId:{}, itemId:{}, status:{}, but item already deleted",
                            shopId, itemId, status);
                    throw new ApiException("已删除商品不允许更新状态");
                }
                // 库存不足则不允许上架
                if (originItem.getStockQuantity() <= 0 && status == 1) {
                    log.warn("[refuse-sale-out-storage] item (id:{}) shop (id:{})", itemId, shopId);
                    throw new ApiException("库存不足 不允许上架");
                }
            }

            itemManager.batchUpdateStatusByShopIdAndItemIds(shopId, ids, status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update status of items(ids={}) of shop(id={}), cause:{}",
                    ids, shopId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }

    /**
     * 根据id更新商品状态
     *
     * @param itemId 商品id
     * @param status 商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> updateStatusByItemId(Long itemId, Integer status) {
        try {
            itemManager.updateStatusByItemId(itemId, status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update status of item(id={}), cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }

    /**
     * 编辑商品详情富文本
     *
     * @param itemId   商品id
     * @param richText 详情富文本
     * @return 是否编辑成功
     */
    @Override
    public Response<Boolean> editRichText(Long itemId, String richText) {
        try {
            ItemDetail itemDetail = itemDetailDao.findByItemId(itemId);
            itemDetail.setDetail(richText);
            Item item = itemDao.findById(itemId);
            ItemAttribute itemAttribute = itemAttributeDao.findByItemId(itemId);
            String itemInfoMd5 = Digestors.itemDigest(item, itemDetail, itemAttribute);
            item.setItemInfoMd5(itemInfoMd5);
            itemManager.updateRichText(itemInfoMd5, itemDetail);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to edit rich text for item(id={}), cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.detail.update.fail");
        }
    }

    /**
     * 更新商品信息摘要
     *
     * @param itemId 商品id
     * @param digest 快照的摘要
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> updateDigest(Long itemId, String digest) {
        try {
            itemDao.updateItemInfoMd5(itemId, digest);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update item info md5 for item(id={}), cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateItem(Item item) {
        try {
            return Response.ok(itemDao.update(item));
        } catch (Exception e) {
            log.error("fail to update item:{},cause:{}",
                    item, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }

    @Override
    public Either<Boolean> setSaleQuantity(Long itemId, Integer saleQuantity) {
        try {
            if (itemId == null || saleQuantity == null) {
                return Either.error(Translate.exceptionOf("参数错误, 商品Id或者销量不得为空"));
            }
            itemDao.setSaleQuantity(itemId, saleQuantity);
            return Either.ok(true);
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    @Override
    public Response<Boolean> batchUpdateSellOutStatusByIds(List<Item> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return Response.ok(true);
        }
        return Response.ok(itemDao.batchUpdateSellOutStatusByIds(updateList) > 0);
    }

    @Override
    public Response<Boolean> updateRestrictedSalesAreaTemplate(Long shopId, List<Long> itemIds, Long templateId) {
        try {
            if (shopId == null || CollectionUtils.isEmpty(itemIds) || templateId == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(itemDao.updateRestrictedSalesAreaTemplate(shopId, itemIds, templateId) > 0);
        } catch (Exception ex) {
            log.error("ItemWriteServiceImpl.updateRestrictedSalesAreaTemplate error, shopId={}, itemIds={}, tempLateId={}",
                    shopId, JSON.toJSONString(itemIds), templateId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> initOldItemDefaultTemplateId(Long shopId, Long restrictedSalesAreaTemplateId) {
        try {
            if (shopId == null || restrictedSalesAreaTemplateId == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(itemDao.initOldItemDefaultTemplateId(shopId, restrictedSalesAreaTemplateId) > 0);
        } catch (Exception ex) {
            log.error("ItemWriteServiceImpl.initOldItemDefaultTemplateId error, shopId={}, restrictedSalesAreaTemplateId={}",
                    shopId, restrictedSalesAreaTemplateId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Boolean batchUpdateStatus(List<Long> ids, Integer status) {
        if (status.equals(ItemStatusEnum.DELETED.getCode())) {
            log.info("需要删除的商品id {}", ids);
        }
        itemDao.batchUpdateStatus(ids, status);
        skuDao.updateStatusByItemIds(ids, status);

        return true;
    }

    @Override
    public Boolean batchUpdateSellOutStatusByIds(List<Long> ids, Integer sellOutStatus) {
        itemDao.batchUpdateSellOutStatusByIds(ids, sellOutStatus);
        return true;
    }
}
