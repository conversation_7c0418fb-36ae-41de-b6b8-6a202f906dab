package moonstone.item.impl.dao;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.item.model.GbGroupInfo;
import moonstone.item.model.GbGroupMember;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class GbGroupMemberDao extends MyBatisDao<GbGroupMember> {

    public GbGroupMember findByShopOrderId(Long shopOrderId){
        GbGroupMember gbGroupMember = getSqlSession().selectOne(sqlId("findByShopOrderId"),
                ImmutableMap.of("shopOrderId", shopOrderId));
        return gbGroupMember;
    }

    public List<GbGroupMember> findLeaderByActivityId(Integer activityId, Long userId) {
        List<GbGroupMember> list = getSqlSession().selectList(sqlId("findLeaderByActivityId"),
                ImmutableMap.of("activityId", activityId, "userId", userId));
        return list;
    }

    public Long countGroupMemberNum(Integer activityId, Long groupId) {
        // 查询在团的成员
        List<GbGroupMember> list = getSqlSession().selectList(sqlId("findGroupMemberList"),
                ImmutableMap.of("activityId", activityId, "groupId", groupId));
        if(CollUtil.isEmpty(list)){
            return 0L;
        }
        return Long.valueOf(list.size());
    }

    public GbGroupMember findGroupIdByShopOrderId(Long shopOrderId, Long userId) {
        GbGroupMember gbGroupMember = getSqlSession().selectOne(sqlId("finByShopOrderId"),
                ImmutableMap.of("shopOrderId", shopOrderId));
        return gbGroupMember;
    }
}
