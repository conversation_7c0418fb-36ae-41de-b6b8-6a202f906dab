package moonstone.item.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.item.model.MerchantCommodityInventory;
import org.springframework.stereotype.Repository;

@Repository
public class MerchantCommodityInventoryDao extends MyBatisDao<MerchantCommodityInventory> {

    public Long sumAvailableQty(Long skuId) {
        return getSqlSession().selectOne(sqlId("sumAvailableQty"),
                ImmutableMap.of("skuId", skuId));
    }

    public MerchantCommodityInventory findByActivityIdAndSkuId(Integer activityId, Long skuId) {
        return getSqlSession().selectOne(sqlId("findByActivityIdAndSkuId"),
                ImmutableMap.of("activityId", activityId, "skuId", skuId));
    }
}
