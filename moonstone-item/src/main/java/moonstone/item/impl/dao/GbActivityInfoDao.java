package moonstone.item.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.item.model.GbActivityInfo;
import moonstone.item.model.GbGroupMember;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class GbActivityInfoDao extends MyBatisDao<GbActivityInfo> {

    public GbGroupMember findByShopOrderId(Long shopOrderId){
        GbGroupMember gbGroupMember = getSqlSession().selectOne(sqlId("findByShopOrderId"),
                ImmutableMap.of("shopOrderId", shopOrderId));
        return gbGroupMember;
    }

    public List<GbActivityInfo> selectListOnUse() {
        List<GbActivityInfo> gbActivityInfoList = getSqlSession().selectList(sqlId("selectListOnUse"));
        return gbActivityInfoList;
    }
}
