/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.impl.dao;

import com.google.common.base.MoreObjects;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.common.utils.JsonMapper;
import moonstone.item.model.Item;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-15
 */
@Repository
public class ItemDao extends MyBatisDao<Item> {

    public List<Item> pageFindByAll(Map<String, Object> criteria) {
        return getSqlSession().selectList(sqlId("pageFindByAll"), criteria);
    }

    public long countFindByAll(Map<String, Object> criteria) {
        return getSqlSession().selectOne(sqlId("countFindByAll"), criteria);
    }

    public Item queryQuantity(Long itemId) {
        return getSqlSession().selectOne(sqlId("queryQuantity"), Map.of("id", itemId));
    }

    /**
     * 根据店铺id查找其所有商品，用于weshop分销商
     *
     * @param shopIds 店铺id列表
     * @return 商品列表
     * added by wulianlei@2018/12/18 13:23
     */
    public List<Item> findByShopIdsOrderByTimeAndStatusUp(List<Long> shopIds) {
        if (CollectionUtils.isEmpty(shopIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("findByShopIdsOrderByTimeAndStatusUp"),
                ImmutableMap.of("shopIds", shopIds));
    }


    /**
     * 根据店铺id查找其所有商品，用于weshop分销商
     *
     * @param itemIds 商品id列表
     * @param name    商品匹配名称
     * @return 商品列表
     * added by wulianlei@2018/12/18 13:23
     */
    public List<Item> findByIdsAndName(List<Long> itemIds, String name) {
        if (CollectionUtils.isEmpty(itemIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("findByIdsAndName"),
                ImmutableMap.of("itemIds", itemIds, "name", name));
    }

    public List<Item> findByShopIdAndExtraJsonLike(Long shopId, String extraJson) {
        if (ObjectUtils.isEmpty(extraJson)) {
            extraJson = "";
        }
        return getSqlSession().selectList(sqlId("findByShopIdAndExtraJsonLike"),
                ImmutableMap.of("shopId", shopId, "extraJson", extraJson));
    }

    /**
     * 根据店铺id及商品编码查找对应的商品
     *
     * @param shopId   店铺id
     * @param itemCode 商品编码
     * @return 商品列表
     */
    public List<Item> findByShopIdAndCode(Long shopId, String itemCode) {
        return getSqlSession().selectList(sqlId("findByCode"),
                ImmutableMap.of("shopId", shopId, "itemCode", itemCode));
    }

    public List<Item> findByShopIdAndCategoryIdLimit(Long shopId, Long categoryId, Integer limitNum) {
        return getSqlSession().selectList(sqlId("findByShopIdAndCategoryIdLimit"),
                new HashMap<String, Object>() {{
                    put("shopId", shopId);
                    put("categoryId", categoryId);
                    put("limitNum", limitNum);
                }});
    }

    /**
     * 根据商品编码查找对应的商品列表
     *
     * @param itemCode 商品编码
     * @return 对应的商品列表
     */
    public List<Item> findByItemCode(String itemCode) {
        return getSqlSession().selectList(sqlId("findByCode"),
                ImmutableMap.of("itemCode", itemCode));
    }

    /**
     * 更新商品状态
     *
     * @param itemId 商品id
     * @param status 对应的状态
     */
    public void updateStatus(Long itemId, Integer status) {
        getSqlSession().update(sqlId("updateStatus"),
                ImmutableMap.of("id", itemId, "status", status));
    }

    /**
     * 更新商品状态
     *
     * @param itemId 商品id
     * @param status 对应的状态
     */
    public boolean updateStatusByShopIdAndItemId(Long shopId, Long itemId, Integer status) {
        return getSqlSession().update(sqlId("updateStatusByShopIdAndItemId"),
                ImmutableMap.of("id", itemId, "status", status, "shopId", shopId)) == 1;
    }

    /**
     * 更新商品信息摘要
     *
     * @param itemId      商品id
     * @param itemInfoMd5 更新的商品信息摘要
     */
    public void updateItemInfoMd5(Long itemId, String itemInfoMd5) {
        getSqlSession().update(sqlId("updateItemInfoMd5"),
                ImmutableMap.of("id", itemId, "itemInfoMd5", itemInfoMd5));
    }

    /**
     * 批量更新商品状态, 这是给运营使用的
     *
     * @param ids    商品id列表
     * @param status 对应的状态
     */
    public void batchUpdateStatus(List<Long> ids, Integer status) {
        getSqlSession().update(sqlId("batchUpdateStatus"),
                ImmutableMap.of("ids", ids, "status", status));
    }

    /**
     * 批量更新商品状态, 这是给商家用的
     *
     * @param shopId 店铺id
     * @param ids    商品id列表
     * @param status 对应的状态
     */
    public boolean batchUpdateStatusByShopIdAndIds(Long shopId, List<Long> ids, Integer status) {
        return getSqlSession().update(sqlId("batchUpdateStatusByShopIdAndIds"),
                ImmutableMap.of("shopId", shopId, "ids", ids, "status", status)) == ids.size();
    }

    /**
     * 批量更新一个店铺的所有状态, 这是给运营使用的
     *
     * @param shopId 店铺id
     * @param status 对应的状态
     */
    public void batchUpdateStatusByShopId(Long shopId, Integer status) {
        getSqlSession().update(sqlId("batchUpdateStatusByShopId"),
                ImmutableMap.of("shopId", shopId, "status", status));
    }


    /**
     * 运营更新商品标签
     *
     * @param itemId 商品id
     * @param tags   标签列表
     */
    public void updateTags(Long itemId, Map<String, String> tags, String itemInfoMd5) {
        getSqlSession().update(sqlId("updateTags"),
                ImmutableMap.of("itemId", itemId,
                        "tagsJson", JsonMapper.JSON_NON_DEFAULT_MAPPER.toJson(tags),
                        "itemInfoMd5", itemInfoMd5));
    }

    /**
     * 商品当前最大的id, 这个是dump搜素引擎用的
     *
     * @return 当前最大的id
     */
    public Long maxId() {
        Long id = getSqlSession().selectOne(sqlId("maxId"));
        return MoreObjects.firstNonNull(id, 0L);
    }

    /**
     * 指定店铺内商品当前最大的id, 这个是dump搜素引擎用的
     *
     * @param shopId 店铺id
     * @return 店铺内当前最大的商品id
     */
    public Long maxIdByShopId(Long shopId) {
        Long id = getSqlSession().selectOne(sqlId("maxIdByShopId"), shopId);
        return MoreObjects.firstNonNull(id, 0L);
    }

    /**
     * 查询id小于lastId内且更新时间大于since的limit个商品, 这个是dump搜素引擎用的
     *
     * @param lastId lastId 最大的店铺id
     * @param since  起始更新时间
     * @param limit  商品个数
     * @return id小于lastId内且更新时间大于since的limit个店铺
     */
    public List<Item> listSince(Long lastId, String since, int limit) {
        return getSqlSession().selectList(sqlId("listSince"),
                ImmutableMap.of("lastId", lastId, "limit", limit, "since", since));
    }

    /**
     * 根据店铺id查询id小于lastId内的limit个商品, 这个是dump搜素引擎用的
     *
     * @param shopId 店铺id
     * @param lastId lastId 最大的商品id
     * @param limit  商品个数
     * @return id小于lastId内的limit个店铺
     */
    public List<Item> listByShopId(Long shopId, Long lastId, int limit) {
        return getSqlSession().selectList(sqlId("listByShopId"),
                ImmutableMap.of("shopId", shopId, "lastId", lastId, "limit", limit));
    }

    /**
     * 根据itemIds和name进行查询数量
     *
     * @param itemIds 店铺id列表
     * @param name    名称
     * @return 数量
     * added by wulianlei 2018/12/18 17:49
     */
    public List<Item> findByItemIdsAndLikeName(List<Long> itemIds, String name) {
        if (name == null)
            name = "";
        if (CollectionUtils.isEmpty(itemIds))
            return Collections.emptyList();
        return getSqlSession().selectList(sqlId("findByItemIdsAndNameLike"),
                ImmutableMap.of("itemIds", itemIds, "name", name));
    }

    /**
     * 根据商品id和物品名查询物品列表
     *
     * @param itemIds 商品id列表
     * @param name    名称
     * @param offset  第几页
     * @param limit   每页大小
     * @return 查询结果
     * added by wulianlei 2018/12/18 17:49
     */
    public List<Item> pageByItemIdsAndLikeName(List<Long> itemIds, String name, int offset, int limit) {
        if (name == null)
            name = "";
        if (CollectionUtils.isEmpty(itemIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByItemIdsAndNameLike"),
                ImmutableMap.of("itemIds", itemIds, "name", name, "offset", offset, "limit", limit));
    }

    public List<Item> pageByItemIdsAndLikeNameInWeShop(List<Long> itemIds, String name, int offset, int limit) {
        if (name == null)
            name = "";
        if (CollectionUtils.isEmpty(itemIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByItemIdsAndLikeNameInWeShop"),
                ImmutableMap.of("itemIds", itemIds, "name", name, "offset", offset, "limit", limit));
    }

    public List<Item> pageByItemIdsAndLikeNameAndStatus(List<Long> itemIds, String name, int offset, int limit, int status) {
        if (name == null)
            name = "";
        if (CollectionUtils.isEmpty(itemIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("findByItemIdsAndLikeNameAndStatus"),
                ImmutableMap.of("itemIds", itemIds, "name", name, "offset", offset, "limit", limit, "status", status));
    }

    public List<Item> pageByItemIdsAndLikeNameAndStatusInWeshop(List<Long> itemIds, String name, int offset, int limit, int status) {
        if (name == null)
            name = "";
        if (CollectionUtils.isEmpty(itemIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByItemIdsAndLikeNameAndStatusInWeshop"),
                ImmutableMap.of("itemIds", itemIds, "name", name, "offset", offset, "limit", limit, "status", status));
    }

    /**
     * 根据商品id和物品名查询物品数量
     *
     * @param itemIds 商品id列表
     * @param name    名称
     * @return 商品数量
     * added by wulianlei 2018/12/18 17:49
     */
    public List<Item> findByItemIdsAndLikeNameAndStatus(List<Long> itemIds, String name, int status) {
        if (name == null)
            name = "";
        if (CollectionUtils.isEmpty(itemIds))
            return Collections.emptyList();
        return getSqlSession().selectList(sqlId("findByItemIdsAndLikeNameAndStatus"),
                ImmutableMap.of("itemIds", itemIds, "name", name, "status", status));
    }

    /**
     * 根据物品ID查询物品数量
     *
     * @param itemIds 物品id列表
     * @return 物品数量
     * added by wulianlei 2018/1/9 17:49
     */
    public Long countByIds(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds))
            return 0L;
        return getSqlSession().selectOne(sqlId("countByIds"),
                itemIds);
    }

    /**
     * 根据物品ID与状态查询物品数量
     *
     * @param itemIds 物品id列表
     * @param status  物品状态
     * @return 物品数量
     * added by wulianlei 2018/1/9 17:49
     */
    public Long countByIdsAndStatus(List<Long> itemIds, int status) {
        if (CollectionUtils.isEmpty(itemIds))
            return 0L;
        return getSqlSession().selectOne(sqlId("countByIdsAndStatus"),
                ImmutableMap.of("itemIds", itemIds, "status", status));
    }

    /**
     * 根据物品ID与物品状态查询物品
     *
     * @param itemIds 商品id列表
     * @param status  状态
     * @return 物品列表
     * added by wulianlei 2018/1/9 10:49
     */
    public List<Item> findByIdsAndStatus(List<Long> itemIds, int status) {
        if (CollectionUtils.isEmpty(itemIds))
            return Collections.emptyList();
        return getSqlSession().selectList(sqlId("findByIdsAndStatus"),
                ImmutableMap.of("itemIds", itemIds, "status", status));
    }

    public List<Item> findByIdsInWeShop(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds))
            return Collections.emptyList();
        return getSqlSession().selectList(sqlId("findByIdsInWeShop"),
                itemIds);
    }

    public List<Item> findByIdsAndStatusInWeShop(List<Long> itemIds, int status) {
        if (CollectionUtils.isEmpty(itemIds))
            return Collections.emptyList();
        return getSqlSession().selectList(sqlId("findByIdsAndStatusInWeShop"),
                ImmutableMap.of("itemIds", itemIds, "status", status));
    }

    /**
     * 根据商店ID与物品名字查询物品数量
     *
     * @param shopIds 店铺id列表
     * @param name    名称
     * @return 物品数量
     * added by wulianlei 2018/12/18 17:49
     */
    public List<Item> findByShopIdsAndLikeName(List<Long> shopIds, String name) {
        if (CollectionUtils.isEmpty(shopIds))
            return Collections.emptyList();
        return getSqlSession().selectList(sqlId("findByShopIdsAndLikeName"),
                ImmutableMap.of("shopIds", shopIds, "name", name));
    }

    /**
     * 根据店铺id查询id小于lastId内的limit个商品, 这个是dump搜素引擎用的
     *
     * @param shopIds 店铺id列表
     * @param name    名称
     * @param offset  第几页
     * @param limit   每页大小
     * @return id小于lastId内的limit个店铺
     * added by wulianlei 2018/12/18 17:49
     */
    public List<Item> pageByShopIdsAndLikeName(List<Long> shopIds, String name, int offset, int limit) {
        if (CollectionUtils.isEmpty(shopIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByShopIdsAndLikeName"),
                ImmutableMap.of("shopIds", shopIds, "name", name, "offset", offset, "limit", limit));
    }

    public List<Item> pageByShopIdsAndLikeNameInWeShop(List<Long> shopIds, String name, int offset, int limit) {
        if (CollectionUtils.isEmpty(shopIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByShopIdsAndLikeNameInWeShop"),
                ImmutableMap.of("shopIds", shopIds, "name", name, "offset", offset, "limit", limit));
    }

    /**
     * 根据商店id匹配name的符合状态的商品列表切页
     *
     * @param shopIds 店铺id列表
     * @param name    名称
     * @param status  状态
     * @param offset  第几页
     * @param limit   每页大小
     * @return id小于lastId内的limit个店铺
     * added by wulianlei 2018/1/9 10:49
     */
    public List<Item> pageByShopIdsAndLikeNameAndStatus(List<Long> shopIds, String name, int status, int offset, int limit) {
        if (CollectionUtils.isEmpty(shopIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByShopIdsAndLikeNameAndStatus"),
                ImmutableMap.of("shopIds", shopIds, "name", name, "offset", offset, "limit", limit, "status", status));
    }

    public List<Item> pageByShopIdsAndLikeNameAndStatusInWeShop(List<Long> shopIds, String name, int status, int offset, int limit) {
        if (CollectionUtils.isEmpty(shopIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByShopIdsAndLikeNameAndStatusInWeShop"),
                ImmutableMap.of("shopIds", shopIds, "name", name, "offset", offset, "limit", limit, "status", status));
    }

    /**
     * 根据商店id匹配name的符合状态的商品数量
     *
     * @param shopIds 店铺id列表
     * @param name    名称
     * @param status  状态
     * @return 商品数量
     * added by wulianlei 2018/1/9 10:49
     */
    public List<Item> findByShopIdsAndLikeNameAndStatus(List<Long> shopIds, String name, int status) {
        if (CollectionUtils.isEmpty(shopIds))
            return Collections.emptyList();
        return getSqlSession().selectList(sqlId("findByShopIdsAndLikeNameAndStatus"),
                ImmutableMap.of("shopIds", shopIds, "name", name, "status", status));
    }

    /**
     * 根据店铺id，状态查询物品数量
     *
     * @param shopIds 店铺id列表
     * @param status  是否已经上架
     * @return id小于lastId内的limit个店铺
     * added by wulianlei 2018/1/9 09:35
     */
    public List<Item> findByShopIdsAndStatus(List<Long> shopIds, int status) {
        if (CollectionUtils.isEmpty(shopIds))
            return Collections.emptyList();
        return getSqlSession().selectList(sqlId("findByShopIdsAndStatus"),
                ImmutableMap.of("shopIds", shopIds, "status", status));
    }

    public List<Item> findByShopIdAndStatus(Long shopId, List<Integer> statusList) {
        if (CollectionUtils.isEmpty(statusList) || shopId == null) {
            return Collections.emptyList();
        }

        return getSqlSession().selectList(sqlId("findByShopIdAndStatus"),
                ImmutableMap.of("shopId", shopId, "statusList", statusList));
    }

    /**
     * 根据店铺id查询id小于lastId内的limit个商品, 这个是dump搜素引擎用的
     *
     * @param shopIds 店铺id列表
     * @param offset  第几页
     * @param limit   每页大小
     * @param status  是否已经上架
     * @return id小于lastId内的limit个店铺
     * added by wulianlei 2018/1/9 09:35
     */
    public List<Item> pageByShopIdsAndStatus(List<Long> shopIds, int offset, int limit, int status) {
        if (CollectionUtils.isEmpty(shopIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByShopIdsAndStatus"),
                ImmutableMap.of("shopIds", shopIds, "offset", offset, "limit", limit, "status", status));
    }

    public List<Item> pageByShopIdsAndStatusInWeShop(List<Long> shopIds, int offset, int limit, int status) {
        if (CollectionUtils.isEmpty(shopIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByShopIdsAndStatusInWeShop"),
                ImmutableMap.of("shopIds", shopIds, "offset", offset, "limit", limit, "status", status));
    }

    /**
     * 根据店铺id查询物品的数量
     *
     * @param shopIds 店铺id列表
     * @return id小于lastId内的limit个店铺
     * added by wulianlei 2018/12/18 17:49
     */
    public List<Item> findByShopIds(List<Long> shopIds) {
        if (CollectionUtils.isEmpty(shopIds))
            return Collections.emptyList();
        return getSqlSession().selectList(sqlId("findByShopIds"),
                ImmutableMap.of("shopIds", shopIds));
    }

    /**
     * 根据店铺id查询id小于lastId内的limit个商品, 这个是dump搜素引擎用的
     *
     * @param shopIds 店铺id列表
     * @param offset  第几页
     * @param limit   每页大小
     * @return id小于lastId内的limit个店铺
     * added by wulianlei 2018/12/18 17:49
     */
    public List<Item> pageByShopIds(List<Long> shopIds, int offset, int limit) {
        if (CollectionUtils.isEmpty(shopIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByShopIds"),
                ImmutableMap.of("shopIds", shopIds, "offset", offset, "limit", limit));
    }

    public List<Item> pageByShopIdsInWeShop(List<Long> shopIds, int offset, int limit) {
        if (CollectionUtils.isEmpty(shopIds))
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("pageByShopIdsInWeShop"),
                ImmutableMap.of("shopIds", shopIds, "offset", offset, "limit", limit));
    }

    /**
     * 更新商品销量和商品库存
     *
     * @param itemId 商品id
     * @param delta  变化的销量,正数表示加销量减库存,负则表示减销量加库存
     */
    public void updateSaleQuantityAndStockQuantity(Long itemId, Integer delta) {
        getSqlSession().update(sqlId("updateSaleQuantityAndStockQuantity"),
                ImmutableMap.of("itemId", itemId, "quantity", delta));
    }

    /**
     * 更新商品销量
     *
     * @param itemId 商品id
     * @param delta  变化的销量,正数表示加,负则表示减
     */
    public void updateSaleQuantity(Long itemId, Integer delta) {
        getSqlSession().update(sqlId("updateSaleQuantity"),
                ImmutableMap.of("itemId", itemId, "quantity", delta));
    }

    /**
     * 根据店铺id统计上架的商品数量
     *
     * @param shopId 店铺id
     * @return 上架商品数量
     */
    public long countOnShelfByShopId(Long shopId) {
        return getSqlSession().selectOne(sqlId("countOnShelfByShopId"), shopId);
    }

    /**
     * 更新商品类目
     *
     * @param itemId     商品id
     * @param categoryId 更新的类目id
     */
    public void updateCategoryId(Long itemId, Long categoryId) {
        getSqlSession().update(sqlId("updateCategoryId"),
                ImmutableMap.of("id", itemId, "categoryId", categoryId));
    }

    public void setSaleQuantity(Long itemId, Integer saleQuantity) {
        getSqlSession().update(sqlId("setSaleQuantity"),
                ImmutableMap.of("saleQuantity", saleQuantity, "itemId", itemId));
    }

    public List<Long> queryHotSellItem(long num) {
        return getSqlSession().selectList(sqlId("queryHotSellItem"),
                ImmutableMap.of("num", num));
    }

    public int batchUpdateItemIndex(List<Item> list) {
        return getSqlSession().update(sqlId("batchUpdateItemIndex"), ImmutableMap.of("list", list));
    }

    public Integer findSmallestIndex(Long shopId) {
        return getSqlSession().selectOne(sqlId("findSmallestIndex"), ImmutableMap.of("shopId", shopId));
    }

    public List<Item> findAllByShopId(Long shopId) {
        return getSqlSession().selectList(sqlId("findAllByShopId"), ImmutableMap.of("shopId", shopId));
    }

    public int batchUpdateSellOutStatusByIds(List<Item> updateList) {
        return getSqlSession().update(sqlId("batchUpdateSellOutStatus"), ImmutableMap.of("updateList", updateList));
    }

    public int updateRestrictedSalesAreaTemplate(Long shopId, List<Long> itemIds, Long templateId) {
        return getSqlSession().update(sqlId("updateRestrictedSalesAreaTemplate"), Map.of("shopId", shopId,
                "itemIds", itemIds,
                "templateId", templateId));
    }

    public int initOldItemDefaultTemplateId(Long shopId, Long restrictedSalesAreaTemplateId) {
        return getSqlSession().update(sqlId("initOldItemDefaultTemplateId"), Map.of("shopId", shopId,
                "restrictedSalesAreaTemplateId", restrictedSalesAreaTemplateId));
    }

    public Long countByRestrictedSalesAreaTemplate(Long shopId, Long templateId) {
        return getSqlSession().selectOne(sqlId("count"), Map.of("shopId", shopId,
                "restrictedSalesAreaTemplateId", templateId));
    }

    public List<Item> findAllByThirdParty(Long shopId, Integer thirdParty) {
        return getSqlSession().selectList(sqlId("findAllByThirdParty"), Map.of("shopId", shopId,
                "thirdParty", thirdParty));
    }

    public Long countByCondition(Map<String, Object> query) {
        return getSqlSession().selectOne(sqlId("countByCondition"), query);
    }

    public List<Item> pagesByCondition(Map<String, Object> query) {
        return getSqlSession().selectList(sqlId("pagesByCondition"), query);
    }

    public void batchUpdateSellOutStatusByIds(List<Long> ids, Integer sellOutStatus) {
        Map<String, Object> query = new HashMap<>();
        query.put("ids", ids);
        query.put("sellOutStatus", sellOutStatus);
        getSqlSession().update(sqlId("batchUpdateSellOutStatusByIds"), query);
    }

}
