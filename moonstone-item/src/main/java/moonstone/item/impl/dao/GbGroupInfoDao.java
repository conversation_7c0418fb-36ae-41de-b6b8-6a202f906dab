package moonstone.item.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.item.model.GbGroupInfo;
import org.springframework.stereotype.Repository;

@Repository
public class GbGroupInfoDao extends MyBatisDao<GbGroupInfo> {

    public GbGroupInfo findById(Long groupId){
        GbGroupInfo gbGroupInfo = getSqlSession().selectOne(sqlId("findById"),
                ImmutableMap.of("id", groupId));
        return gbGroupInfo;
    }
}
