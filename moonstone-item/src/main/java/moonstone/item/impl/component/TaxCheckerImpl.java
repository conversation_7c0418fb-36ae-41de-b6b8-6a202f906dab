package moonstone.item.impl.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.danding.tax.utils.TaxCalculator;
import com.danding.tax.utils.dictionary.CCSTariffDictionary;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.Maps;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.common.utils.JsonMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Y800PlatformAPI;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.Either;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.model.FinderBean;
import moonstone.common.model.Y800ResponseModel;
import moonstone.common.model.rpcAPI.y800dto.ListDTO;
import moonstone.common.model.rpcAPI.y800dto.Y800OutSkuCodeWithPriceDTO;
import moonstone.common.model.rpcAPI.y800dto.Y800TaxRequestModelDTO;
import moonstone.common.model.rpcAPI.y800dto.Y800TaxResultDTO;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.component.dto.item.TaxAndOrigin;
import moonstone.component.item.component.TaxChecker;
import moonstone.countryImage.model.CountryImage;
import moonstone.countryImage.service.CountryImageReadService;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.item.service.SkuReadService;
import moonstone.thirdParty.impl.service.Y800V3ItemApi;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.model.ThirdSystemAID;
import moonstone.thirdParty.service.ThirdPartyUserShopReadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Stream;

/**
 * 税费检查计算
 */
@Component
@Slf4j
public class TaxCheckerImpl implements TaxChecker {

    @Value("${Y800.yang.support}")
    private String y800Support;

    @Value("${Y800.wd.support}")
    private String y800WdSupport;

    @Autowired
    private SkuCustomReadService skuCustomReadService;

    @Autowired
    private SkuReadService skuReadService;

    @Autowired
    private CountryImageReadService countryImageReadService;

    @Value("${environment}")
    private String env;

    @RemoteAPI
    private Y800PlatformAPI y800PlatformAPI;

    @RemoteAPI
    private Y800V3ItemApi y800V3ItemApi;

    @Autowired
    private FinderBean<ThirdSystemAID, Optional<ThirdPartyUserShop>> thirdSystemAIDThirdPartyUserShopLoadingCache;

    @Autowired
    private ThirdPartyUserShopReadService thirdPartyUserShopReadService;

    CompletableFuture<TaxCalculator> taxCalculatorFuture = new CompletableFuture<>();

    @EventListener(ContextRefreshedEvent.class)
    public void loadTaxCalculator() {
        CCSTariffDictionary ccsTariffDictionary = new CCSTariffDictionary();
        TaxCalculator taxCalculator = new TaxCalculator(ccsTariffDictionary);
        taxCalculatorFuture.complete(taxCalculator);
    }


    private final ForkJoinPool executor = new ForkJoinPool();

    private final LoadingCache<Long, List<ThirdPartyUserShop>> thirdPartyAccountCache = Caffeine.newBuilder().expireAfterWrite(2L, TimeUnit.HOURS)
            .build(key -> Optional.ofNullable(thirdPartyUserShopReadService.findByShopId(key).getResult()).orElse(new ArrayList<>()));

    private final LoadingCache<String, LoadingCache<Y800TaxRequestModelDTO, Either<ListDTO<Y800TaxResultDTO>>>> y800TaxCache = Caffeine.newBuilder()
            .expireAfterWrite(2, TimeUnit.HOURS)
            .maximumSize(1000)
            .build(accessCode -> Caffeine.newBuilder().expireAfterWrite(2, TimeUnit.HOURS)
                    .maximumSize(1000)
                    .build(requestModelDTO -> {
                        try (Y800PlatformAPI api = y800PlatformAPI) {
                            api.setAccessCode(accessCode);
                            return api.goodsTaxCal(requestModelDTO);
                        }
                    }));

    private final LoadingCache<ThirdPartyUserShop, LoadingCache<Y800TaxRequestModelDTO, Either<ListDTO<Y800TaxResultDTO>>>> y800TaxCacheV3 = Caffeine.newBuilder()
            .expireAfterWrite(2, TimeUnit.HOURS)
            .maximumSize(1000)
            .build(thirdPartyUserShop -> Caffeine.newBuilder().expireAfterWrite(2, TimeUnit.HOURS)
                    .maximumSize(1000)
                    .build(requestModelDTO -> {
                        try (Y800V3ItemApi api = y800V3ItemApi) {
                            api.setSecret(thirdPartyUserShop.getThirdPartyKey());
                            api.setAppId(thirdPartyUserShop.getThirdPartyCode());
                            requestModelDTO.setAccessCode(thirdPartyUserShop.getExtra().get(ShopExtra.Y800StorageAccessCode.getCode()));

                            return api.goodsTaxCal(requestModelDTO);
                        }
                    }));

    /**
     * 按sku原始价格计算税费
     *
     * @param sku      单品
     * @param quantity 数量
     * @return TODO 如果查找税率失败，则返回null
     */
    @Override
    public Long getTax(Sku sku, Integer quantity) {
        Future<Long> getTaxProtectFuture = executor.submit(() -> {
            try {
                if (BondedType.fromInt(sku.getType()).isBonded()) {
                    SkuCustom skuCustom = skuCustomReadService.findBySkuId(sku.getId());
                    if (skuCustom == null || skuCustom.getCustomTaxHolder() != 1) {
                        return 0L;
                    }
                    if (!Optional.ofNullable(sku.getTags()).map(tags -> tags.get(SkuTagIndex.pushSystem.name())).isPresent()) {
                        log.warn("{} sku[{} => {}] unknown the pushSystem [{}]", LogUtil.getClassMethodName(), sku.getId(), sku.getName(), sku.getTags());
                        return taxCalculatorFuture.get().run(skuCustom.getHsCode(), BigDecimal.valueOf(sku.getPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN))
                                .multiply(BigDecimal.valueOf(100)).longValue();
                    }
                    Set<ThirdPartySystem> pushSystemSet = new HashSet<>(ThirdPartySystem.values().length);
                    for (String pushSystem : sku.getTags().getOrDefault(SkuTagIndex.pushSystem.name(), "").split(SkuTagIndex.pushSystem.getSplitter())) {
                        if (ObjectUtils.isEmpty(pushSystem)) {
                            continue;
                        }
                        pushSystemSet.add(ThirdPartySystem.fromInt(Integer.parseInt(pushSystem)));
                    }
                    // try to take tax from sku custom
                    if (pushSystemSet.isEmpty()) {
                        try {
                            if (!ObjectUtils.isEmpty(skuCustom.getHsCode())) {
                                return taxCalculatorFuture.get().run(skuCustom.getHsCode(), BigDecimal.valueOf((long) sku.getPrice() * quantity).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                                        .multiply(BigDecimal.valueOf(100))
                                        .longValue();
                            }
                        } catch (Exception e) {
                            log.error("{} fail to take tax from sku[Id => {}] custom[code => {}]", LogUtil.getClassMethodName(), sku.getId(), skuCustom.getHsCode(), e);
                        }
                        return new BigDecimal(Optional.ofNullable(sku.getExtraMap()).map(extra -> extra.get(SkuExtraIndex.tax.name())).orElse("0.091")).multiply(new BigDecimal(sku.getPrice() * quantity)).longValue();
                    }
                    Integer pushSystem = ThirdPartySystem.Y800_V2.Id();
                    if (!pushSystemSet.contains(ThirdPartySystem.fromInt(pushSystem))) {
                        pushSystem = pushSystemSet.stream().findFirst().map(ThirdPartySystem::Id).orElseThrow(() -> Translate.exceptionOf("查找类型失败"));
                    }
                    if (pushSystemSet.contains(ThirdPartySystem.GongXiao)) {
                        return new BigDecimal(Optional.ofNullable(sku.getExtraMap()).map(extra -> extra.get(SkuExtraIndex.tax.name())).orElse("0.091")).multiply(new BigDecimal(sku.getPrice() * quantity)).longValue();
                    }
                    Double rate = getRate(pushSystem, sku, skuCustom);

                    log.debug("start to calculate tax, sku price={}, quantity={}, rate={}", sku.getPrice(), quantity, rate);

                    if (rate == null) {
                        return null;
                    }
                    return Math.round(sku.getPrice() * quantity * rate);
                } else {
                    return 0L;
                }
            } catch (Exception ex) {
                log.error("{} fail to calculate Tax for Sku[{}] quantity [{}]", LogUtil.getClassMethodName(), sku.getId(), quantity, ex);
                throw new JsonResponseException(Translate.of("为单品[%s](%s)计算税费失败, 无法预览订单或者购买", sku.getName(), sku.getId()));
            }
        });
        // time protect execute
        try {
            return getTaxProtectFuture.get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("{} timeout for gain tax for [{}], with current thread [{}], executor[active=>{}, poolSize=>{}, queued=>{}]"
                    , Thread.activeCount(), sku.getItemId(), executor.getActiveThreadCount(), executor.getPoolSize(), executor.getQueuedTaskCount(), e);
            throw new RuntimeException(Translate.of("税费计算超时"));
        }
    }

    /**
     * 按sku微分销价格计算税费
     *
     * @param sku      单品
     * @param quantity 数量
     * @return TODO 如果查找税率失败，则返回null
     */
    @Override
    @Deprecated
    public Long getDistributionTax(Sku sku, Integer quantity, Map<String, String> Extra) {
        if (BondedType.fromInt(sku.getType()).isBonded()) {
            Integer pushSystem = Integer.parseInt(sku.getTags().get("pushSystem"));
            SkuCustom skuCustom = skuCustomReadService.findBySkuId(sku.getId());
            if (skuCustom.getCustomTaxHolder() == 1) {
                Double rate = getRate(pushSystem, sku, skuCustom);

                log.debug("{} start to calculate tax, sku price={}, quantity={}, rate={}", LogUtil.getClassMethodName(), sku.getPrice(), quantity, rate);

                if (rate == null) {
                    return null;
                }
                if (sku.getExtraPrice().get(DistributionConstants.SKU_DISTRIBUTION_PRICE) == null) {
                    log.error("sku(id={}) has no distribution price", sku.getId());
                    return null;
                }
                // modify by liuchao 20190327
                if (Extra != null && Extra.get(DistributionConstants.WE_SHOP_OWER_PRICE) != null) {
                    return Math.round(Long.parseLong(Extra.get(DistributionConstants.WE_SHOP_OWER_PRICE)) * quantity * rate);

                } else {
                    return Math.round(sku.getExtraPrice().get(DistributionConstants.SKU_DISTRIBUTION_PRICE) * quantity * rate);
                }
            } else {
                return 0L;
            }
        } else {
            return 0L;
        }
    }

    /**
     * 按折后金额计算税费
     *
     * @param sku              单品
     * @param afterDiscountFee 折扣后价格
     * @return TODO 如果查找税率失败，则返回null
     */
    @Override
    public Long getRealTax(Sku sku, Long afterDiscountFee) {
        if (BondedType.fromInt(sku.getType()).isBonded()) {
            Integer pushSystem = Integer.parseInt(sku.getTags().get("pushSystem"));
            SkuCustom skuCustom = skuCustomReadService.findBySkuId(sku.getId());
            if (skuCustom.getCustomTaxHolder() == 1) {
                Double rate = getRate(pushSystem, sku, skuCustom);

                if (rate == null) {
                    return null;
                }
                return Math.round(afterDiscountFee * rate);
            } else {
                return 0L;
            }
        } else {
            return 0L;
        }
    }

    @Override
    public TaxAndOrigin getRateAndOrigin(Long skuId, Integer pushSystem) {
        TaxAndOrigin result = new TaxAndOrigin();

        Sku sku = skuReadService.findSkuById(skuId).getResult();
        SkuCustom skuCustom = skuCustomReadService.findBySkuId(skuId);
        if (skuCustom.getCustomTaxHolder() == 1) {
            Double rate = getRate(pushSystem, sku, skuCustom);

            result.setRate(rate);
        } else {
            result.setRate(0.0);
        }
        result.setOriginId(skuCustom.getCustomOriginId());
        result.setOrigin(skuCustom.getCustomOrigin());
        Response<CountryImage> rCountryImage = countryImageReadService.findByCountryId(skuCustom.getCustomOriginId());
        if (rCountryImage.isSuccess()) {
            CountryImage countryImage = rCountryImage.getResult();
            if (countryImage != null) {
                result.setOriginUrl(countryImage.getImageUrl());
            }
        }

        return result;
    }

    /**
     * 拆税   新方式
     *
     * @param skuId      单品数据
     * @param outerSkuId 单品数据(依赖 outerSkuId)
     * @param price      价格 (真实价格,使用BigDecimal表示真实价格, 100.00 元就是100元)
     * @return 拆税结果
     */
    @Override
    public Optional<TaxSplitResult> splitTax(Long shopId, Long skuId, String outerSkuId, BigDecimal price) {
        return Optional.ofNullable(splitTaxNew(shopId, skuId, outerSkuId, price)
                .orElseGet(() -> splitTaxAtOms(skuId, outerSkuId, price).orElse(null)));
    }

    /**
     * 拆税新系统使用
     *
     * @param skuId      单品数据
     * @param outerSkuId 单品数据(映射的outerSkuId 是Y800的SkuCode)
     * @param price      价格
     * @return 拆税结果
     */
    @Override
    public Optional<TaxSplitResult> splitTaxNew(Long shopId, Long skuId, String outerSkuId, BigDecimal price) {
        String pushSystem = null;
        if (skuId != null) {
            Sku targetSku = skuReadService.findSkuById(skuId).getResult();
            pushSystem = Optional.ofNullable(targetSku.getTags())
                    .orElseGet(HashMap::new)
                    .getOrDefault(SkuTagIndex.pushSystem.name(), "");
            SkuCustom skuCustom = skuCustomReadService.findBySkuId(skuId);
            try {
                if (skuCustom != null && skuCustom.getHsCode() != null) {
                    TaxCalculator taxCalculator = taxCalculatorFuture.get(3, TimeUnit.SECONDS);
                    return splitTaxAtSelf(originPrice -> taxCalculator.run(skuCustom.getHsCode(), originPrice), price);
                }
            } catch (Exception e) {
                log.error("{} fail to split the tax through tax-calculate sku[{}] price[{}]", LogUtil.getClassMethodName(), skuId, price, e);
                for (String one : pushSystem.split(SkuTagIndex.pushSystem.getSplitter())) {
                    if (ThirdPartySystem.GongXiao.Id().toString().equals(one)) {
                        BigDecimal tax = new BigDecimal(Optional.ofNullable(targetSku.getExtraMap()).orElseGet(HashMap::new).getOrDefault(SkuExtraIndex.tax.name(), "0.091"));
                        return splitTaxAtSelf(tax::multiply, price);
                    }
                }
            }
        }

        log.info("TaxCheckerImpl.splitTaxNew, shopId={}, skuId={}, outerSkuId={}, price={}, pushSystem={}",
                shopId, skuId, outerSkuId, price, pushSystem);
        // api-v3
        if (containsV3(pushSystem)) {
            return splitTaxNewByV3(shopId, skuId, outerSkuId, price);
        }

        // api-v2
        try (Y800PlatformAPI api = y800PlatformAPI) {
            api.setAccessCode(thirdSystemAIDThirdPartyUserShopLoadingCache.findBy(new ThirdSystemAID(ThirdPartySystem.Y800_V2.Id(), shopId))
                    .orElseThrow(() -> new RuntimeException(Translate.of("查找外部绑定信息失败"))).getThirdPartyCode());
            return api.goodsTaxCal(new Y800TaxRequestModelDTO(Collections.singletonList(new Y800OutSkuCodeWithPriceDTO(outerSkuId, price)), "1"))
                    .logErrorStr(errStr -> {
                        log.error("{} failed to split Tax by new version API (v:2.0),err:{}", LogUtil.getClassMethodName(), errStr);
                        EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("TAX-SPLIT", new Translate("店铺[%s] 单品[%s] 外部编码[%s] 于价格[%s] 拆税失败,原因[%s]", shopId, skuId, outerSkuId, price, errStr).toString(), EmailReceiverGroup.DEVELOPER));
                    })
                    .map(Optional::of)
                    .orElse(Optional.empty())
                    .map(ListDTO::list)
                    .map(Collection::stream)
                    .flatMap(Stream::findFirst)
                    .map(rawTaxSplit -> {
                        log.debug("{} split at {} for skuId:{}", LogUtil.getClassMethodName(), rawTaxSplit, skuId);
                        return new TaxSplitResult(rawTaxSplit.getTax(), rawTaxSplit.getPrice());
                    });
        } catch (Exception ex) {
            log.error("{} failed to set accessCode by shopId:{} skuId:{} outerSkuId:{} ", LogUtil.getClassMethodName(), shopId, skuId, outerSkuId, ex);
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("EXECUTE-TAX-SPLIT", new Translate("店铺[%s] 单品[%s] 外部编码[%s] 于价格[%s] 执行拆税失败", shopId, skuId, outerSkuId, price, ex.getCause()).toString(), ex, EmailReceiverGroup.DEVELOPER));
            return Optional.empty();
        }
    }

    private Optional<TaxSplitResult> splitTaxNewByV3(Long shopId, Long skuId, String outerSkuId, BigDecimal price) {
        log.info("TaxCheckerImpl.splitTaxNewByV3, shopId={}, skuId={}, outerSkuId={}, price={}, begin goodsTaxCal",
                shopId, skuId, outerSkuId, price);
        try {
            var config = thirdSystemAIDThirdPartyUserShopLoadingCache.findBy(new ThirdSystemAID(ThirdPartySystem.Y800_V3.Id(), shopId))
                    .orElseThrow(() -> new RuntimeException(Translate.of("APIv3配置信息查找失败")));

            var result = Objects.requireNonNull(Objects.requireNonNull(y800TaxCacheV3.get(config))
                            .get(new Y800TaxRequestModelDTO(Collections.singletonList(new Y800OutSkuCodeWithPriceDTO(outerSkuId, price)))))
                    .map(ListDTO::list)
                    .map(res -> res.get(0));
            if (!result.isSuccess()) {
                throw new RuntimeException(result.getErrorMsg());
            }

            var y800TaxResultDTO = result.getResult();
            log.info("TaxCheckerImpl.splitTaxNewByV3, shopId={}, skuId={}, outerSkuId={}, price={}, y800TaxResultDTO={}",
                    shopId, skuId, outerSkuId, price, JSON.toJSONString(y800TaxResultDTO));

            return Optional.of(new TaxSplitResult(y800TaxResultDTO.getTax(), y800TaxResultDTO.getPrice()));
        } catch (Exception ex) {
            log.error("TaxCheckerImpl.splitTaxNewByV3 error, shopId={}, skuId={}, outerSkuId={}, price={}",
                    shopId, skuId, outerSkuId, price, ex);
            return Optional.empty();
        }
    }

    private boolean containsV3(String pushSystem) {
        if (StringUtils.isBlank(pushSystem)) {
            return false;
        }

        for (String one : pushSystem.split(SkuTagIndex.pushSystem.getSplitter())) {
            if (ThirdPartySystem.Y800_V3.Id().toString().equals(one)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 拆税   老方式,仅仅使用旧商品
     *
     * @param skuId      单品数据
     * @param outerSkuId 单品数据(依赖 outerSkuId)
     * @param price      价格 (真实价格,使用BigDecimal表示真实价格, 100.00 元就是100元)
     * @return 拆税结果
     */
    @Override
    public Optional<TaxSplitResult> splitTaxAtOms(Long skuId, String outerSkuId, BigDecimal price) {
        String url = y800Support + "/api/hs/tax";
        try {
            String postUrl = url + "?skuSn=" + outerSkuId + "&goodsAmount=" + price;
            log.info("{} try get Tax by url:{}", LogUtil.getClassMethodName(), postUrl);
            HttpRequest request = HttpRequest.post(postUrl);
            if (request.ok()) {
                Y800ResponseModel<TaxEntity> model = JSON.parseObject(request.body(), new TypeReference<Y800ResponseModel<TaxEntity>>() {
                });
                if (!model.isSuccess()) {
                    log.error("{} skuId:{} price:{} error:{}", LogUtil.getClassMethodName(), skuId, price, model.getErrorMsg());
                    throw new RuntimeException(model.getErrorMsg());
                }
                TaxEntity entity = model.getData();
                if (entity.getTax() != null) {
                    return Optional.of(new TaxSplitResult(entity.getTax(), entity.getGoodsAmount()));
                }
                /// 以后全部都是错误日志
                log.error("{} response:{} skuId:{} outSkuId:{} price:{} url:{}", LogUtil.getClassMethodName(), entity, skuId, outerSkuId, price, url);
                return Optional.empty();
            }
            log.error("{} skuId:{} price:{} response:{}", LogUtil.getClassMethodName(), skuId, price, request.message());
            throw new RuntimeException("net-error");
        } catch (Exception ex) {
            log.error("{} skuId:{} outSkuId:{} price:{} url:{} msg:{}", LogUtil.getClassMethodName(), skuId, outerSkuId, price, url, ex.getMessage());
            ex.printStackTrace();
            return Optional.empty();
        }
    }

    @Data
    static public class TaxEntity {
        String skuSn;
        Long count;
        BigDecimal payAmount;
        BigDecimal goodsAmount;
        BigDecimal tax;
        BigDecimal discountAmount;
        Boolean split;

        public TaxEntity() {

        }
    }

    /**
     * 拆税逻辑
     * 将一个包含了税金的价格拆为一个货物价格和税金
     *
     * @param calTax 根据价格获取税金的函数
     * @param fee    货物价格
     * @return 如果可拆则返回一个TaxResult
     */
    @Override
    public Optional<TaxSplitResult> splitTaxAtSelf(Function<BigDecimal, BigDecimal> calTax, BigDecimal fee) {
        //尝试计算税费
        // 1. 计算商品总税
        BigDecimal totalTax = calTax.apply(fee);// 计算商品总税
        // 2. 计算税率 = 商品总税 / 商品总金额 + 商品总税
        BigDecimal taxRate = totalTax.divide(totalTax.add(fee), 4, BigDecimal.ROUND_HALF_UP);// 税率 = 总税 / 总价
        // 3. 获取预估税值
        BigDecimal tax1 = fee.multiply(taxRate);
        // 获取预估订单金额
        BigDecimal goodsPrice1 = fee.subtract(tax1);

        // 获取预估订单金额的税金
        BigDecimal totalTax2 = calTax.apply(goodsPrice1);// 计算商品总税
        // 获取第一次粗略获取的税金与第二次逼近后的差距
        BigDecimal diff = tax1.subtract(totalTax2);
        // 记录新价格和税费
        BigDecimal finalPrice = goodsPrice1;
        BigDecimal finalTax = totalTax2;
        // 如果两者差值大于5
        if (diff.compareTo(new BigDecimal("5")) >= 0) {
            /// 1. 设置一个不会触发消费税的价格
            final BigDecimal smallFee = new BigDecimal(9);
            BigDecimal totalTax3 = calTax.apply(smallFee);// 计算商品总税
            /// 2. 计算税率 = 商品总税 / 商品总金额 + 商品总税
            BigDecimal taxRate3 = totalTax3.divide(smallFee.add(totalTax3), 4, BigDecimal.ROUND_HALF_UP);// 税率 = 总税 / 总价
            /// 3. 获取税率后获取其税金
            BigDecimal tax3 = fee.multiply(taxRate3);
            /// 4. 根据以上无消费税的金额获取货物价格
            BigDecimal goodsPrice3 = (fee).subtract(tax3);
            /// 5. 根据猜测出的货物价格算取其真实税金
            BigDecimal tax4 = calTax.apply(goodsPrice3);// 计算商品总税
            diff = tax4.subtract(tax3);
            /// 6. 判断其差额 如果其大于可接受经验值则拒绝拆税
            if (diff.compareTo(new BigDecimal("5")) > 0) {
                /// 高于海关可接受经验值 拒绝拆税
                return Optional.empty();
            } else {
                // 可接受范围
                // 记录新价格和税费
                finalPrice = goodsPrice3;
                finalTax = tax3;
            }
        }
        return Optional.of(new TaxSplitResult(finalTax, finalPrice));
    }


    @Override
    public Double getRate(Integer pushSystemId, Sku sku, SkuCustom skuCustom) {
        if (skuCustom != null && org.springframework.util.StringUtils.hasLength(skuCustom.getHsCode())) {
            try {
                return taxCalculatorFuture.get().run(skuCustom.getHsCode(),
                                BigDecimal.valueOf(sku.getPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                        .divide(BigDecimal.valueOf(sku.getPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP), 4, RoundingMode.DOWN)
                        .doubleValue();
            } catch (Exception ex) {
                log.error("{} fail to calculate the tax for sku[Id => {}, HsCode => {}]", LogUtil.getClassMethodName(),
                        sku.getId(), skuCustom.getHsCode(), ex);
            }
        }
        if (pushSystemId == null) {
            return null;
        }
        if (ThirdPartySystem.GongXiao.Id().equals(pushSystemId)) {
            return Double.parseDouble(Optional.ofNullable(sku.getExtraMap())
                    .map(extra -> extra.get(SkuExtraIndex.tax.name())).orElse("0.091"));
        }

        Either<Y800TaxResultDTO> yTax;
        if (ThirdPartySystem.Y800_V3.Id().equals(pushSystemId)) {
            var thirdPartyUserShop = Objects.requireNonNull(thirdPartyAccountCache.get(sku.getShopId()))
                    .stream()
                    .filter(account -> ThirdPartySystem.Y800_V3.Id().equals(account.getThirdPartyId()))
                    .findFirst()
                    .orElseThrow();
            yTax = Objects.requireNonNull(Objects.requireNonNull(y800TaxCacheV3.get(thirdPartyUserShop))
                            .get(new Y800TaxRequestModelDTO(Collections.singletonList(new Y800OutSkuCodeWithPriceDTO(sku.getOuterSkuId(), new BigDecimal(sku.getPrice()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP))))))
                    .map(ListDTO::list)
                    .map(res -> res.get(0));
        } else {
            String accessCode = Objects.requireNonNull(thirdPartyAccountCache.get(sku.getShopId()))
                    .stream().filter(account -> ThirdPartySystem.Y800_V2.Id().equals(account.getThirdPartyId()))
                    .findFirst().map(ThirdPartyUserShop::getThirdPartyCode).orElse("");
            yTax = Objects.requireNonNull(Objects.requireNonNull(y800TaxCache.get(accessCode))
                            .get(new Y800TaxRequestModelDTO(Collections.singletonList(new Y800OutSkuCodeWithPriceDTO(sku.getOuterSkuId(), new BigDecimal(sku.getPrice()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP))))))
                    .map(ListDTO::list)
                    .map(res -> res.get(0));
        }

        log.debug("TaxCheckerImpl.getRate by thirdPartySystem, sku={}, pushSystemId={}, skuCustom={}, result={}",
                JSON.toJSONString(sku), pushSystemId, JSON.toJSONString(skuCustom), JSON.toJSONString(yTax));
        return yTax
                .map(Y800TaxResultDTO::getTax)
                .map(tax -> tax.divide(new BigDecimal(sku.getPrice()).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP), 8, RoundingMode.HALF_UP))
                .map(BigDecimal::doubleValue)
                // 新系统没有则去老系统查找 兼容代码 有问题 但是不要改
                .orElseGet(() -> getRate(pushSystemId, sku.getPrice().longValue(), skuCustom));
    }

    /**
     * 获取税费比率 依赖洋800的wd-support的接口
     *
     * @param pushSystemId 推送系统的Id 以后根据这个id扩展获取税金的位置
     * @param fee          用来计税的费用
     * @param skuCustom    海关编码
     * @return 税费比率
     */
    @Override
    public Double getRate(Integer pushSystemId, Long fee, SkuCustom skuCustom) {
        return getRateOld(pushSystemId, fee, skuCustom);
    }

    /**
     * 老系统
     */
    private Double getRateOld(Integer pushSystemId, Long fee, SkuCustom skuCustom) {
        BigDecimal taxFee;
        String url = y800WdSupport + "/api/hs/newTax";
        try {
            Map<String, String> params = Maps.newHashMapWithExpectedSize(1);
            params.put("hsCode", skuCustom.getHsCode());
            params.put("price", new BigDecimal(fee).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP).toString());
            HttpRequest request = HttpRequest.get(url, params, true);
            if (request.ok()) {
                Response<Double> result = JsonMapper.nonDefaultMapper().fromJson(request.body(), Response.class);
                log.debug("{} result:{}", LogUtil.getClassMethodName(), result);
                if (!result.isSuccess()) {
                    log.error("[" + url + "]Request failed, cause:{}", result.getResult() == null ? result.getError() : result.getResult());
                    return null;
                }
                taxFee = BigDecimal.valueOf(result.getResult());
                log.info("the rate of hsCode={} is {}", skuCustom.getHsCode(), taxFee);
                return taxFee.doubleValue() * 100 / fee;
            } else {
                log.error("{} getTax(skuId:{} skuCustomId:{} pushSystem:{}) from url:{} failed,cause:{}", LogUtil.getClassMethodName(), skuCustom.getSkuId(), skuCustom.getCustomOriginId(), pushSystemId, url, "request failed");
                if (!"online".equals(env)) {
                    // 非正式环境下，如果税费工程挂了的话，返回0.1作为税费
                    return 0.1;
                }
                return null;
            }
        } catch (Exception ex) {
            log.error("{} getTax(skuId:{} skuCustomId:{} pushSystem:{}) from url:{} failed,cause:{}", LogUtil.getClassMethodName(), skuCustom.getSkuId(), skuCustom.getCustomOriginId(), pushSystemId, url, ex.getMessage());
            return 0.1;
        }
    }
}
