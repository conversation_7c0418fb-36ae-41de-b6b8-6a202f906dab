application:
  name: supply-shop-extra

environment: dev

logging:
  config: classpath:config/dev/logback.xml

ext.messages.classpath: messages_zh_CN

mybatis:
  mapperLocations:  classpath*:mapper/*Mapper.xml
  type-aliases-package: >
    moonstone.(item|category|brand|shop|spu|user|cart|order|express|promotion|delivery|file|settle|membership|countryImage|integral).model,
    moonstone.(adv|thirdParty|shopWxa|wxa).model,
    moonstone.(weCart|weShop|weDistributionApplication).model,
    moonstone.user.address.model,
    moonstone.user.area.model,
    moonstone.pay.mock.model,
    moonstone.auth.model
  typeHandlersPackage: moonstone.common.type.handler

server:
  context-path: /
  port: 10083

hazelcast:
  config: hazelcast-config.xml
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  datasource:
    #driver-class-name: org.postgresql.Driver
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************
    #url: **************************************************************************************************
    #url: **********************************************************************
    username: root
    password: dd123456
  data:
    mongodb:
      #uri: mongodb://**************:27017/parana-dev
      uri: mongodb://mongodb.localhost:27017/parana-dev
  redis:
    host: *************
    port: 6379
    password: 123456
    timeout: 500
    database: 0
    jedis:
      pool:
        max-active: 100
        max-wait: -1
        max-idle: 8
        min-idle: 0

session:
  cookie-domain: wedev.parana.yang800.cn
  cookie-context-path: /
  cookie-name: msid
  cookie-max-age: 1800
  source: redis
  serialize-type: json
  redis-host: *************
  redis-port: 6379
  redis-index: 0
  redis-cluster: false
  redis-test-on-borrow: true
  redis-max-total: 10
  redis-max-idle: 0
  redis-prefix: afsession
  redis-auth: 123456
wechat:
  mp:
    appId: wx86fe5657944bcaa2
    secret: f72b211010c13e17141accd17fdbb23b
  authorize:
    redirectUrl: http://mall.yang800.com/backend/api/wechat/auth/jump
    resultUrl: http://mall.yang800.com/seller/authorize-success
  applet:
    enable: true
    mchId: 1488913512
    appId: wx86fe5657944bcaa2
    secret: 8ac1cbf290b0fe98ff1142acf94e7351
parana:
  mall:
    url: http://wedev.parana.yang800.cn:10093
  web:
    url: http://wedev.parana.yang800.cn:10093
  h5:
    url: https://m.mall.yang800.com
  wxa:
    url: https://m.mall.yang800.com
    appId: wx90e48a315747ccb9
    appSecret: ebb83ced7885897e4fd5ecfc403ed92d
  we:
    seller:
      url: https://m.mall.yang800.com
      appId: wx0fb538f6dc093892
      appSecret: 5d260bcf67eecf19e96a341b7b4eb95a
    buyer:
      url: https://m.mall.yang800.com
      appId: wxb25691b47b974630
      appSecret: 088dedd8ec3382f5ba981a2560afc187
  wxopen:
    componentAppId: wxfb7d4a7ab73f6ba6
    componentSecret: a9a41dcfd8e7b564a2ec93c0ee58fb9f
    componentToken: wx569abcf3c77ff1de
    componentAesKey: 0987654321qazwsxedcrfvtgbyhnujmik1234567890
    requestdomain: https://m.mall.yang800.com;https://www.yang800.com;https://m.yang800.com
    wsrequestdomain: wss://m.mall.yang800.com;wss://www.yang800.com
    uploaddomain: https://m.mall.yang800.com;https://www.yang800.com
    downloaddomain: https://m.mall.yang800.com;https://www.yang800.com
  substore:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3
  levelDistribution:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3

search:
  host: search.localhost
  new_host: search.localhost
  port: 9200

item.search:
  index-name: t_items
  index-type: item
  mapping-path: item_mapping.json
  full-dump-range: 3000
  batch-size: 100

shop.search:
  index-name: t_shops
  index-type: shop
  mapping-path: shop_mapping.json
  full-dump-range: 3000
  batch-size: 100

weShopItem.search:
  index-name: t_we_shop_items
  index-type: we_shop_item
  mapping-path: we_shop_item_mapping.json
  full-dump-range: 3000
  batch-size: 100

image:
  base:
    url: http://dante-img.oss-cn-hangzhou.aliyuncs.com
  protocol: http
  domain: dante-img.oss-cn-hangzhou.aliyuncs.com
  upload.allowedFileTypes: jpeg,jpg,gif,png

oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  appKey: LTAI5tNGqjjEfJ1n3xbV42v9
  appSecret: ******************************
  bucketName: dante-img

msg:
  current:
    smsService: aliYunSmsService
    emailService: javaxEmailService
  aliyun:
    appKey: LTAIMbTR37BKlN62
    appSecret: ******************************
  javaxemail:
    mailServerHost: smtp.exmail.qq.com
    mailServerPort: 465
    fromAddress: Y800<<EMAIL>>
    userName: <EMAIL>
    password: Dd111111

msg.template.list:
  - key: sms.user.login.code
    title: 登录验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_145495759","smsParam":{"code":"{{code}}"}}
  - key: sms.user.register.code
    title: 注册验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143863719","smsParam":{"code":"{{code}}"}}
  - key: sms.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868717","smsParam":{"code":"{{code}}"}}
  - key: sms.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868720","smsParam":{"code":"{{code}}"}}
  - key: sms.we.shop.wallet.set.password
    title: 设置提现密码验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_153325369","smsParam":{"code":"{{code}}"}}

msg.email:
  templates:
    - key: email.error.notice
      templateName: sendEmailErrorNotice
      subject: "但丁商城 - 异常通知"

cache.duration.in.minutes: 1

# open api配置开始
enable.open.api: true
# open api配置结束

pay:
  debug: false
  channel:
    alipay: enable
  notifyUrl: http://webdev.parana.yang800.cn:10081/api/order/paid
  refundNotifyUrl: http://webdev.parana.yang800.cn:10081/api/refund/notify
  returnUrl: http://webdev.parana.yang800.cn:10091/buyer/trade-success
  h5ReturnUrl: https://m.mall.yang800.com/order/pay-success
  unionpay.wap.token.verifyCerDir: e:/nfs/dev/logs/terminus-we
  unionpay.pc.token.verifyCerDir: e:/nfs/dev/logs/terminus-we
  unionpay.app.token.verifyCerDir: e:/nfs/dev/logs/terminus-we
  certFilePath: e:/nfs/dev/certFiles/terminus-web
  allinpay:
    pay: https://syb-test.allinpay.com/apiweb/unitorder/pay
    refund: https://syb-test.allinpay.com/apiweb/tranx/refund
    query: https://syb-test.allinpay.com/apiweb/tranx/query
    ystBaseUrl: http://test.allinpay.com/open/gateway

alipay:
  pid: ****************
  key: pil5t34m4qsdor9ujuffkqfvrgfjt3mp
  account: <EMAIL>

Y800:
  partnerCode: websc
  partnerKey: websc
  sourcePlatform: WEBSC
  sourcePlatformV3: WEBSC
  merchantCode:
  wd.support: http://*************:6086
  open.api.gate: http://localhost:4000/api/
  #wd.support: http://**************:6086
  yang.support: http://localhost:4000
  #yang.support: http://**************:8083
  #财务推送的url
  finance.project.url: http://127.0.0.1:8080/xhr
nio:
  post.url: http://127.0.0.1:8091/xhr

aoxin:
  gateway: http://************:61/API

#微分销商城前台域名
m.mall.yang800: https://m.mall.yang800.com

#积分商城生成二维码的路径
m.mall.jifen.path: /nfs/certFiles/terminus-web/levelDistribution/image/
#积分商城生成二维码的url
m.mall.jifen.url: https://m.mall.yang800.com/ladder-distribution/credits-shop

pay.integral.token:
  returnUrl: http://mall.yang800.com/buyer/trade-success
  refundNotifyUrl: http://mall.yang800.com/backend/api/refund/notify

#功能开关
function:
  switch:
    aoXinPush: false
    financePush: true
    unifiedPayment: true

mercury:
  pay:
    host: http://127.0.0.1:8080
    appCode: DDFXSC
    merchantCode: M2019030616533627870
    customs:
      notify: http://127.0.0.1:10082/api/customs/payment/declare/notify

ebpCode: 3301964J31

wx:
  backend:
    url: https://api.weixin.qq.com/cgi-bin


gx.api.mall: http://127.0.0.1:4000/gx

express.100:
  regularUrl:
  key: f93c3f9e5a89fb22
  customer: xxxx

express.tms:
  host: https://tms-server-test.yang800.com.cn/api/
  appKey: 10000
  secret: E6AC95EA2C0343A2
aliyun:
  realNameCertification:
    appKey: 204084473
    appSecret: ERZchuOkDUYUYhDEPz4upEaZlGEL6Q2p

order.auto.cancel.in.minutes: 15

withdraw.apply:
  onlinePay:
    certFilePath: /nfs/withdrawCertFiles/terminus-web
    callbackUrl: http://localhost:10081/api/withdrawPayment

agentPay.payment:
  callbackUrl: http://localhost:10081/backend/api/agentPayPayment

fundsTransfer.payment:
  callbackUrl: http://localhost:10081/backend/api/fundsTransferPayment