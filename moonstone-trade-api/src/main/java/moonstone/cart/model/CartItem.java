/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.cart.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.common.model.Indexable;
import io.terminus.common.utils.JsonMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 购物车对象
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-17
 */
@ToString
@EqualsAndHashCode(of = {"buyerId", "skuId"})
public class CartItem implements Serializable, Indexable {
    private static final long serialVersionUID = -3873400543343400909L;

    private static final ObjectMapper objectMapper = JsonMapper.JSON_NON_EMPTY_MAPPER.getMapper();
    private static final TypeReference<HashMap<String, Object>> EXTRA_MAP = new TypeReference<HashMap<String, Object>>() {
    };

    /**
     * 主键id
     */
    @Getter
    @Setter
    private Long id;

    /**
     * 买家id
     */
    @Getter
    @Setter
    private Long buyerId;

    /**
     * 店铺id
     */
    @Getter
    @Setter
    private Long shopId;

    /**
     * sku id
     */
    @Getter
    @Setter
    private Long skuId;

    /**
     * 在购物车中本sku的 数目
     */
    @Getter
    @Setter
    private Integer quantity;

    /**
     * 加入购物车时本商品的价格
     */
    @Getter
    @Setter
    private Integer snapshotPrice;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 修改时间
     */
    @Getter
    @Setter
    private Date updatedAt;

    @Getter
    @Setter
    @JsonIgnore
    private String extraJson;

    @Getter
    @Setter
    private Map<String, Object> extraMap;

}
