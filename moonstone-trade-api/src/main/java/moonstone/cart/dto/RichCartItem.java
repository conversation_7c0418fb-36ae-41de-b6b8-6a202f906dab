/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */

package moonstone.cart.dto;

import lombok.Data;
import moonstone.cart.model.CartItem;
import moonstone.item.model.Sku;
import moonstone.promotion.model.Promotion;

import java.io.Serializable;
import java.util.List;

/**
 * 买家购物车内的一条商品记录
 * <p>
 * Copyright (c) 2015 杭州端点网络科技有限公司
 * Date: 12/11/15
 * Time: 11:38 AM
 * Author: 2015年 <a href="mailto:<EMAIL>">张成栋</a>
 */
@Data
public class RichCartItem implements Serializable {
    private static final long serialVersionUID = 1537613794298226789L;

    private Sku sku;            // 商品详情
    private Long tax;         // 税费
    private CartItem cartItem;  // 购物车商品信息
    private String itemName;    // 商品名
    private String itemImage;   // 商品主图
    private Integer itemStatus; // 商品状态
    private Long skuPromotionPrice; //sku营销价格,用于单品营销
    private Long promotionId;   //选中的或者默认的sku级别优惠信息id
    private Integer sourceType;
    private List<Promotion> skuPromotions;//符合条件的sku级别优惠信息列表

    /**
     * 商品上的标记
     */
    private List<String> flagList;
    /**
     * 规格
     */
    private String specification;
}
