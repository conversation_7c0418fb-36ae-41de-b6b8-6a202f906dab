package moonstone.order.model.domain;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import com.google.common.collect.ImmutableBiMap;
import io.terminus.common.model.BaseUser;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.ApiException;
import moonstone.common.model.Either;
import moonstone.common.utils.Json;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.api.FlowPicker;
import moonstone.order.api.RefundExpressService;
import moonstone.order.api.TradeBatchNoGenerator;
import moonstone.order.dto.fsm.*;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.enu.RefundReasonType;
import moonstone.order.model.*;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.RefundWriteService;
import moonstone.order.service.ShopOrderWriteService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 退款领域层
 */
@Slf4j
public class RefundDomain {
    /**
     * 关联订单
     */
    @Getter
    protected List<OrderBase> relatedOrderList;
    /**
     * 支付单
     */
    @Getter
    protected List<Payment> paymentList;
    /**
     * 操作人
     */
    @Setter
    @Getter
    BaseUser operator;
    /**
     * 退款申请人
     */
    @Setter
    @Getter
    BaseUser applier;

    protected RefundWriteService refundWriteService;
    protected RefundReadService refundReadService;
    protected TradeBatchNoGenerator tradeBatchNoGenerator;
    protected RefundExpressService refundExpressService;
    protected ShopOrderWriteService shopOrderWriteService;

    /**
     * 退款单
     */
    @Getter
    protected Refund refund;

    protected RefundDomain() {
        inject();
    }

    /**
     * custom the refundDomain with inject method
     */
    protected void inject() {
        throw Translate.exceptionOf("请重载");
    }


    private final Flow refundFlow = FlowBook.REFUND_FLOW.getFlow();
    /**
     * 同步动作监听触发器
     */
    @Getter
    private final Map<Action, List<Consumer<Refund>>> actionListener = new HashMap<>(Action.values().length);
    /**
     * 状态机
     */
    protected FlowPicker flowPicker;

    /**
     * 申请退款单
     *
     * @return 成功的单号
     * @apiNote 不发送动作事件
     */
    public Either<List<Long>> apply(String reason, List<String> imageUrlList
            , Refund.RefundType refundType, RefundReasonType refundReasonType, Long fee) {
        try {
            long totalPaidSum = 0;
            long hasRefundMoneySum = 0;
            boolean hasShip = false;
            Map<Long, List<Refund>> refundRelatedMap = new HashMap<>(paymentList.size());
            // 限制退款订单金额
            relatedOrderList.stream().map(OrderBase::getFee).reduce(Long::sum).filter(orderFee -> orderFee >= fee).orElseThrow(() -> Translate.exceptionOf("退款金额不能超过订单金额"));
            // 是否具有可退款的支付单, 如果已经有退款流程 则拒绝退款
            for (Payment payment : paymentList) {
                if (OrderStatus.fromInt(payment.getStatus()) != OrderStatus.PAID) {
                    continue;
                }
                totalPaidSum += payment.getFee();
                refundRelatedMap.put(payment.getId(), new LinkedList<>());
                // 是否存在尚在流转的退款单
                for (Refund existsRefund : refundReadService.findByTradeNo(payment.getOutId()).getResult()) {
                    OrderStatus refundStatus = OrderStatus.fromInt(existsRefund.getStatus());
                    if (refundStatus == OrderStatus.REFUND || refundStatus == OrderStatus.RETURN) {
                        hasRefundMoneySum += existsRefund.getFee();
                        // 统计已经退款过的退款单金额
                        refundRelatedMap.get(payment.getId()).add(existsRefund);
                        continue;
                    }
                    switch (OrderStatus.fromInt(existsRefund.getStatus())) {
                        case REFUND:
                        case RETURN:
                        case REFUND_CANCEL:
                        case REFUND_APPLY_REJECTED:
                        case RETURN_APPLY_REJECTED:
                        case RETURN_REJECTED:
                            continue;
                        default:
                            break;
                    }
                    return Either.error(Translate.of("订单具有一单尚在流转的退款单[Id => %s]", existsRefund.getId()));
                }
            }
            // 退款金额判断
            if (!paymentList.isEmpty() && hasRefundMoneySum + fee > totalPaidSum) {
                return Either.error(Translate.of("总退款金额不得超过支付金额"));
            }
            OrderOperation refundOperation = refundType == Refund.RefundType.AFTER_SALE_RETURN
                    ? OrderEvent.RETURN_APPLY.toOrderOperation() : OrderEvent.REFUND_APPLY.toOrderOperation();
            // 订单是否可以退款
            for (OrderBase orderBase : relatedOrderList) {
                OrderLevel orderLevel = OrderLevel.ofOrderBase(orderBase);
                switch (OrderStatus.fromInt(orderBase.getStatus())) {
                    case SETTLED:
                    case CONFIRMED:
                    case SHIPPED:
                        if (!hasShip) {
                            hasShip = true;
                        }
                    default:
                        break;
                }
                boolean allow = flowPicker.pick(orderBase, orderLevel)
                        .operationAllowed(orderBase.getStatus(), refundOperation);
                if (!allow) {
                    log.error("{} order[Id => {}, type => {}, status => {}, desc => {}] break refund apply rule, [REJECT]"
                            , LogUtil.getClassMethodName(), orderBase.getId(), orderLevel, orderBase.getStatus(), OrderStatus.fromInt(orderBase.getStatus()).intoString());
                    return Either.error(Translate.of("订单[Id => %s, 类型 => %s]处于不可退款状态", orderBase.getId(), orderLevel.getDesc()));
                }
            }
            // limit the refund Type
            limitRefundTypeForSale(refundType, hasShip);
            // 选择可以退款的支付单
            // todo 确定余额支付的情况是否直接对应现金退款(PS: 目前不支持余额支付)
            // 创建退款单
            List<Refund> refunds = chooseValidFeePayment(refundRelatedMap, fee)
                    .stream().map(paymentForRefund ->
                            buildRefund(paymentForRefund, refundType, imageUrlList, refundReasonType, reason))
                    .peek(refund -> actionChain(refund, Action.apply))
                    .collect(Collectors.toList());
            return refundWriteService.create(refunds, relatedOrderList);
        } catch (Exception e) {
            log.error("{} fail to apply refund for Fee[{}] refundType[{}] orderList[Id => {}] payment[Id => {}]"
                    , LogUtil.getClassMethodName(), fee, refundType
                    , Arrays.toString(relatedOrderList.stream().map(OrderBase::getId).toArray())
                    , Arrays.toString(paymentList.stream().map(Payment::getId).toArray()), e);
            return Either.error(e);
        }
    }

    /**
     * limit the refund type for sale, no allow to refund return if has not ship
     *
     * @param refundType refund type
     * @param hasShip    has ship
     */
    private void limitRefundTypeForSale(Refund.RefundType refundType, boolean hasShip) {
        switch (refundType) {
            case AFTER_SALE_RETURN:
            case AFTER_SALE_REFUND:
                if (!hasShip) {
                    throw Translate.exceptionOf("订单尚未发货, 无法进行售后退款");
                }
                break;
            case ON_SALE_REFUND:
                break;
            default:
        }
    }

    private Refund buildRefund(RefundPayment paymentForRefund, Refund.RefundType refundType, List<String> imageUrlList, RefundReasonType refundReasonType, String reason) {
        Refund refund = new Refund();
        refund.setRefundType(refundType.value());
        refund.setBuyerNote(reason);
        refund.setReasonType(refundReasonType.getType());

        refund.setFee(paymentForRefund.refundFee);
        int totalPaidBalance = paymentList.stream().filter(paidPayment -> OrderStatus.fromInt(paidPayment.getStatus()) == OrderStatus.PAID)
                .map(Payment::getBalance).filter(Objects::nonNull).reduce(Integer::sum).orElse(0);
        // calculate the balance should be refund, balance = currentRefundFee / totalFee * totalBalance
        // > if balance is zero, skip the calculate
        BigDecimal refundBalance = totalPaidBalance == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(paymentForRefund.refundFee).divide(BigDecimal.valueOf(paymentForRefund.payment.getFee()), 4, RoundingMode.DOWN)
                .multiply(BigDecimal.valueOf(totalPaidBalance));
        refund.setBalance(refundBalance.intValue());
        relatedOrderList.stream().findFirst()
                .ifPresent(orderBase -> {
                    refund.setBuyerId(orderBase.getBuyerId());
                    refund.setBuyerName(orderBase.getBuyerName());
                    refund.setShopId(orderBase.getShopId());
                    refund.setShopName(Translate.of("[未指向店铺]"));
                    refund.setSourceType(1);
					refund.setBuyerReceivedStatus(orderBase.getStatus() == OrderStatus.CONFIRMED.getValue());
                    if (orderBase instanceof ShopOrder) {
                        refund.setShopName(((ShopOrder) orderBase).getShopName());
						refund.setShipping(orderBase.getStatus() >= OrderStatus.SHIPPED.getValue());
                    }
                    if (orderBase instanceof SkuOrder) {
                        refund.setShopName(((SkuOrder) orderBase).getShopName());
                        refund.setShipping(((SkuOrder) orderBase).getShipping());
                    }
                });

        Payment payment = paymentForRefund.payment;
        refund.setPaymentId(payment.getId());
        refund.setPaySerialNo(payment.getPaySerialNo());
        refund.setRefundAccountNo(payment.getPayAccountNo());
        refund.setTradeNo(payment.getOutId());
        refund.setChannel(payment.getChannel());

        refund.setImagesJson(Json.toJson(imageUrlList));
        refund.setStatus(refundType == Refund.RefundType.AFTER_SALE_RETURN ? OrderStatus.RETURN_APPLY.getValue() : OrderStatus.REFUND_APPLY.getValue());

        refund.setExtra(ImmutableBiMap.of(Action.apply.name(), applier.getId() + "@" + System.currentTimeMillis()));

        return refund;
    }

    /**
     * 选择一个可以退款的支付单
     * 但是不考虑交叉退款的问题
     *
     * @param fee 目前想要退款的金额
     * @return 用于退款的支付单
     * @apiNote 不发送动作事件
     */
    private List<RefundPayment> chooseValidFeePayment(Map<Long, List<Refund>> refundRelatedMap, Long fee) {
        List<RefundPayment> paidAble = new LinkedList<>();
        long restRequireRefund = fee;
        if (paymentList.isEmpty()) {
            Payment zeroMoneyPayment = new Payment();
            zeroMoneyPayment.setStatus(OrderStatus.NOT_PAID.getValue());
            zeroMoneyPayment.setFee(0L);
            zeroMoneyPayment.setBalance(0);
            zeroMoneyPayment.setId(0L);
            zeroMoneyPayment.setPaySerialNo("NOT_NEED_PAID");
            zeroMoneyPayment.setOutId("NOT_NEED_PAID");
            zeroMoneyPayment.setChannel("NO_PAY_CHANNEL");
            zeroMoneyPayment.setPayAccountNo("SYSTEM");
            // use the first order as outId
            zeroMoneyPayment.setOutId("NO_PAY_" + String.format("%8d", relatedOrderList.get(0).getId()));

            paidAble.add(new RefundPayment(0, zeroMoneyPayment));
            restRequireRefund = 0;
        }
        for (Payment payment : paymentList) {
            if (OrderStatus.fromInt(payment.getStatus()) != OrderStatus.PAID) {
                continue;
            }
            long alreadyRefund = refundRelatedMap.get(payment.getId()).stream().map(Refund::getFee).reduce(Long::sum).orElse(0L);
            if (payment.getFee() - alreadyRefund > 0) {
                if (restRequireRefund < payment.getFee() - alreadyRefund) {
                    // 重置一下支付单的金额 但是不会修改数据库, 仅仅用于设置退款的金额
                    paidAble.add(new RefundPayment(restRequireRefund, payment));
                    restRequireRefund = 0;
                    break;
                }
                paidAble.add(new RefundPayment(payment.getFee() - alreadyRefund, payment));
                restRequireRefund -= payment.getFee() + alreadyRefund;
            }
        }
        if (restRequireRefund > 0) {
            throw Translate.exceptionOf("支付单可退款金额低于要求的退款金额");
        }
        return paidAble;
    }

    /**
     * 取消退款申请
     *
     * @return 取消退款申请
     * @apiNote 不发送事件和回滚订单状态, 订单回滚状态由申请取消事件触发
     */
    public Either<Boolean> cancel() {
        try {
            OrderOperation cancelOperation = Refund.RefundType.from(refund.getRefundType()) == Refund.RefundType.AFTER_SALE_RETURN
                    ? OrderEvent.RETURN_APPLY_CANCEL.toOrderOperation() : OrderEvent.REFUND_APPLY_CANCEL.toOrderOperation();
            if (!refundFlow.operationAllowed(refund.getStatus(), cancelOperation)) {
                return Either.error(Translate.of("退款单[Id=>%s]不处于可取消状态", refund.getId()));
            }
            actionChain(Action.cancel);
            return Either.ok(refundWriteService.updateStatus(refund.getId(), OrderStatus.REFUND_CANCEL.getValue()).getResult());
        } catch (Exception exception) {
            log.error("{} fail to cancel the Refund[Id => {}]", LogUtil.getClassMethodName(),
                    refund.getId(), exception);
            return Either.error(exception);
        }
    }

    /**
     * 接受退款
     *
     * @return 是否这次操作成功的
     */
    public Either<Boolean> accept() {
        try {
            OrderOperation refundOperation = Refund.RefundType.from(refund.getRefundType()) == Refund.RefundType.AFTER_SALE_RETURN
                    ? OrderEvent.RETURN_APPLY_AGREE.toOrderOperation() : OrderEvent.REFUND_APPLY_AGREE.toOrderOperation();
            for (OrderBase orderBase : relatedOrderList) {
                boolean allow = flowPicker.pick(orderBase, OrderLevel.ofOrderBase(orderBase))
                        .operationAllowed(orderBase.getStatus(), refundOperation);
                if (!allow) {
                    log.warn("{} order[Id => {}, Status => {}, Desc => {}] break RefundAgree rule,[REJECT]"
                            , LogUtil.getClassMethodName(), orderBase.getId(), orderBase.getStatus(), OrderStatus.fromInt(orderBase.getStatus()).intoString());
                    return Either.error(Translate.of("订单[Id => %s, 类型 => %s]无法同意退款", orderBase.getId(), OrderLevel.ofOrderBase(orderBase).getDesc()));
                }
            }
            // Refund Status Flow didn't own
            if (!refundFlow.operationAllowed(refund.getStatus(), refundOperation)) {
                log.warn("{} order[Id => {}, Status => {}, Desc => {}] break RefundAgree rule,[REJECT]"
                        , LogUtil.getClassMethodName(), refund.getId(), refund.getStatus(), OrderStatus.fromInt(refund.getStatus()).intoString());
                return Either.error(Translate.of("退款单[Id => %s, 类型 => %s]无法同意退款", refund.getId(), Objects.requireNonNull(Refund.RefundType.from(refund.getRefundType())).toString()));
            }
            requireNoWorkingRefund(paymentList);
            // 动作监听
            actionChain(Action.accept);
            boolean ok = refundWriteService.updateStatus(refund.getId()
                    , refundFlow.target(refund.getStatus(), refundOperation)
                    , refund.getStatus()).take();
            // update refund extra to record op information
            if (ok) {
                updateRefundExtraRecordAction(Action.accept);
            }
            return Either.ok(ok);
        } catch (Exception e) {
            log.error("{} fail to accept Refund[Id => {}] by User[Id => {}]", LogUtil.getClassMethodName(), refund.getId(), operator.getId(), e);
            throw new ApiException(e.getMessage());
        }
    }

    private void updateRefundExtraRecordAction(Action action) {
        updateRefundExtraRecordAction(action, null);
    }

    private void updateRefundExtraRecordAction(Action action, Consumer<Map<String, String>> custom) {
        Refund updateExtra = new Refund();
        updateExtra.setId(refund.getId());
        Map<String, String> extra = Optional.ofNullable(refund.getExtra()).orElseGet(HashMap::new);
        extra.put(action.name(), Optional.ofNullable(operator).map(BaseUser::getId).map(Objects::toString)
                .orElse("System") + "@" + System.currentTimeMillis());
        if (Objects.nonNull(custom)) {
            custom.accept(extra);
        }
        refund.setExtra(extra);
        updateExtra.setExtra(extra);
        refundWriteService.update(updateExtra);
    }

    /**
     * 拒绝退款
     *
     * @param reason 拒绝理由
     * @return 是否拒绝成功
     */
    public Either<Boolean> reject(String reason) {
        try {
            OrderOperation refundOperation = Refund.RefundType.from(refund.getRefundType()) == Refund.RefundType.AFTER_SALE_RETURN
                    ? OrderEvent.RETURN_APPLY_REJECT.toOrderOperation() : OrderEvent.REFUND_APPLY_REJECT.toOrderOperation();
            if (!refundFlow.operationAllowed(refund.getStatus(), refundOperation)) {
                return Either.error(Translate.of("退款单[Id=>%s]拒绝申请失败", refund.getId()));
            }
            actionChain(Action.reject);
            boolean ok = refundWriteService.updateStatus(refund.getId()
                    , refundFlow.target(refund.getStatus(), refundOperation)
                    , refund.getStatus()
            ).take();
            if (ok) {
                refundWriteService.updateSellerNote(refund.getId(), reason);
                updateRefundExtraRecordAction(Action.reject);
            }
            // update Extra to record op information
            return Either.ok(true);
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    public Either<String> refund(Function<Refund, String> refundInterface) {
        return refund(refundInterface, OrderEvent.REFUND);
    }

    /**
     * 真实退款动作
     *
     * @return 外部交易流水号
     */
    public Either<String> refund(Function<Refund, String> refundInterface, OrderEvent orderEvent) {
        try {
            Integer refundStatus = refundReadService.findById(refund.getId()).getResult().getStatus();
            if (!refundStatus.equals(refund.getStatus())) {
                return Either.error(Translate.of("退款单[Id=>%s]的状态已发生变更！", refund.getId()));
            }

            if (!refundFlow.operationAllowed(refund.getStatus(), orderEvent.toOrderOperation())) {
                return Either.error(Translate.of("退款单[Id=>%s]不处于可以操作退款的状态中", refund.getId()));
            }
            requireNoWorkingRefund(paymentList);
            // 为了避开一些退款系统的限制, 将退款批次日期改为单日
            if (ObjectUtils.isEmpty(refund.getOutId()) || PaymentChannelEnum.ALLINPAY_YST.getCode().equals(refund.getChannel())) {
                refund.setOutId(tradeBatchNoGenerator.generateBatchNo(new Date(), refund.getId()));
                log.info("RefundDomain.refund, init refundId={}, paySerialNo={}, outId={}",
                        refund.getId(), refund.getPaySerialNo(), refund.getOutId());
                Refund updateOutId = new Refund();
                updateOutId.setId(refund.getId());
                updateOutId.setOutId(refund.getOutId());
                if (!Optional.ofNullable(refundWriteService.update(updateOutId).getResult()).orElse(false)) {
                    return Either.error(Translate.exceptionOf("初始化退款批次号失败"));
                }
            }
            actionChain(Action.refund);
            String refundSerial = refundInterface.apply(refund);
            boolean ok = refundWriteService.updateStatus(refund.getId(),
                            refundFlow.target(refund.getStatus(), OrderEvent.REFUND.toOrderOperation()), refund.getStatus())
                    .take();
            if (!ok) {
                log.error("{} Refund(Id => {}, TradeNo => {}, Fee => {}", LogUtil.getClassMethodName()
                        , refund.getId(), refund.getTradeNo(), refund.getFee());
            }
            return Either.ok(refundSerial);
        } catch (Exception e) {
            log.error("{} fail to refund Refund[Id => {}, TradeNo => {}]", LogUtil.getClassMethodName(), refund.getId(), refund.getTradeNo(), e);
            return Either.error(e);
        }
    }

    /**
     * 解决退款错误状态
     *
     * @param refundSuccess 最终是否退款成功
     * @param reason        这么处理的理由
     * @return 操作结果
     */
    public Either<Boolean> rewindError(boolean refundSuccess, String reason) {
        try {
            if (OrderStatus.fromInt(refund.getStatus()) != OrderStatus.REFUND_FAIL) {
                return Either.error(Translate.of("退款单[Id=>%s]只有在退款错误状态中才能手动处理"));
            }
            int successStatus;
            if (refundSuccess) {
                successStatus = refundFlow.target(OrderStatus.REFUND_PROCESSING.getValue()
                        , OrderEvent.REFUND_SUCCESS.toOrderOperation());
            } else {
                successStatus = refundFlow.target(OrderStatus.REFUND_APPLY.getValue()
                        , OrderEvent.REFUND_APPLY_REJECT.toOrderOperation());
            }
            Action rewind = refundSuccess ? Action.rewindSuccess : Action.rewindFailure;
            actionChain(rewind);
            refundWriteService.updateStatus(refund.getId(), successStatus, refund.getStatus())
                    .take();
            updateRefundExtraRecordAction(rewind, extra -> extra.put("reason", reason));
            return Either.error("not impl");
        } catch (Exception e) {
            log.error("{} fail to rewindError Refund[Id => {}] into Success[{}] with Reason[{}]", LogUtil.getClassMethodName(),
                    refund.getId(), refundSuccess, reason, e);
            return Either.error(e);
        }
    }

    /**
     * error callback, it's invoke from outer side
     * will mark the refund into Error Status
     *
     * @param error error reason
     * @return action success
     */
    public Either<Boolean> error(String error) {
        try {
            if (OrderStatus.fromInt(refund.getStatus()) == OrderStatus.REFUND_FAIL) {
                return Either.ok(false);
            }
            if (OrderStatus.fromInt(refund.getStatus()) != OrderStatus.REFUND_PROCESSING) {
                return Either.error(Translate.of("退款单[Id=>%s]已完成处理", refund.getId()));
            }
            actionChain(Action.error);
            refundWriteService.updateStatus(refund.getId(), OrderStatus.REFUND_FAIL.getValue(), OrderStatus.REFUND_PROCESSING.getValue())
                    .take();
            updateRefundExtraRecordAction(Action.error, extra -> extra.put("error", error));
            return Either.ok(true);
        } catch (Exception e) {
            log.error("{} fail to handle error for Refund[Id => {}] with error[{}]", LogUtil.getClassMethodName(), refund.getId(), error, e);
            return Either.error(e);
        }
    }

    /**
     * refund success, invoke when the money is refund
     *
     * @param refundTradeSerial the serial of out system to identify the refund
     * @return refund success at this time
     */
    public Either<Boolean> refundSuccess(String refundTradeSerial, Date refundAt) {
        try {
            if (OrderStatus.fromInt(refund.getStatus()) == OrderStatus.REFUND) {
                return Either.ok(false);
            }
            if (refundFlow.operationAllowed(refund.getStatus(), OrderEvent.REFUND.toOrderOperation())) {
                log.debug("{} refund[Id => {}, Status => {}] seems stay at early status", LogUtil.getClassMethodName(),
                        refund.getId(), OrderStatus.fromInt(refund.getStatus()));
            }
            if (!refundFlow.operationAllowed(refund.getStatus(), OrderEvent.REFUND_SUCCESS.toOrderOperation())) {
                return Either.error(Translate.of("退款单已经不处于退款中"));
            }
            boolean success = refundWriteService.updateStatus(refund.getId(), refundFlow.target(refund.getStatus(), OrderEvent.REFUND_SUCCESS.toOrderOperation())
                    , refund.getStatus()).take();
            if (!success) {
                return Either.error(Translate.of("退款单[Id=>%s]修改异常", refund.getId()));
            }
            updateRefundExtraRecordAction(Action.refundSuccess);
            refund.setRefundSerialNo(refundTradeSerial);
            Refund updateSerial = new Refund();
            updateSerial.setId(refund.getId());
            log.info("退款时间 {}", DateUtil.formatDateTime(refundAt));
            updateSerial.setRefundAt(ObjUtil.isEmpty(refundAt) ? new Date() : refundAt);
            updateSerial.setRefundSerialNo(refundTradeSerial);
            refundWriteService.update(updateSerial);
            return Either.ok(true);
        } catch (Exception e) {
            log.error("{} fail to refundSuccess Refund[Id => {}] at Date[{}] with RefundSerial[{}]", LogUtil.getClassMethodName(),
                    refund.getId(), refundAt, refundTradeSerial, e);
            return Either.error(e);
        }
    }

    private void actionChain(Refund refund, Action action) {
        // 动作监听
        actionListener.getOrDefault(action, Collections.emptyList())
                .forEach(actionChain -> actionChain.accept(refund));
    }

    private void actionChain(Action action) {
        // 动作监听
        actionChain(refund, action);
    }

    /**
     * extract the applier id from applier or refund's extra
     *
     * @return applierId    Nullable
     */
    public Long getApplierId() {
        if (Objects.nonNull(applier)) {
            return applier.getId();
        }
        if (Objects.nonNull(refund)) {
            return Optional.ofNullable(refund.getExtra())
                    .map(extra -> extra.get(Action.apply.name()))
                    .map(str -> str.split("@")[0])
                    .map(Long::parseLong).orElse(refund.getBuyerId());
        }
        return null;
    }

    /**
     * add actionChain of action
     *
     * @param action      action, inject the actionChain into action trigger chain
     * @param actionChain actionChain, be invoke when the action is invoke
     * @return domain self
     * @see RefundDomain#apply would save the refund until the apply actionChain complete
     */
    public RefundDomain addActionChain(Action action, Consumer<Refund> actionChain) {
        actionListener.putIfAbsent(action, new LinkedList<>());
        actionListener.get(action).add(actionChain);
        return this;
    }

    private void requireNoWorkingRefund(List<Payment> payments) {
        for (Payment payment : payments) {
            if (OrderStatus.fromInt(payment.getStatus()) != OrderStatus.PAID) {
                continue;
            }
            // 是否存在尚在流转的退款单
            for (Refund existsRefund : refundReadService.findByTradeNo(payment.getOutId()).getResult()) {
                if (existsRefund.getId().equals(refund.getId())) {
                    continue;
                }
                switch (OrderStatus.fromInt(existsRefund.getStatus())) {
                    case REFUND:
                    case RETURN:
                    case REFUND_CANCEL:
                    case REFUND_APPLY_REJECTED:
                    case RETURN_APPLY_REJECTED:
                    case RETURN_REJECTED:
                        continue;
                    default:
                        break;
                }
                throw Translate.exceptionOf("订单具有一单尚在流转的关联支付退款单[Id => %s], 请检查是否重复退款并优先处理", existsRefund.getId());
            }
        }
    }

    /**
     * deliver the return stock, link the express no into return order
     * add return express if there is already one
     * only work at Return or Return_Apply_Agreed status
     *
     * @param returnExpressNo return express no
     */
    public void deliver(String returnExpressNo, String companyCode) {
        // 1. throw error if refund is not on return status
        OrderStatus returnCurrentStatus = OrderStatus.fromInt(refund.getStatus());
        if (!refundFlow.reachStatus(OrderEvent.RETURN.toOrderOperation(), returnCurrentStatus.getValue())) {
            if (!refundFlow.operationAllowed(returnCurrentStatus.getValue(), OrderEvent.RETURN.toOrderOperation())) {
                log.warn("{} Refund[Id => {}] stay at wrong status[{}] for express return[Express No => {}]", LogUtil.getClassMethodName(), refund.getId(), refund.getStatus(), returnExpressNo);
                throw Translate.exceptionOf("请先与店铺客服沟通, 确定同意退货后再进行填写发货单");
            }
            refund.setStatus(refundFlow.target(returnCurrentStatus.getValue(), OrderEvent.RETURN.toOrderOperation()));
        }
        refundExpressService.linkRefundExpress(refund.getId(), returnExpressNo, companyCode).take();
        // record the action
        updateRefundExtraRecordAction(Action.express);
        Refund updateStatus = new Refund();
        updateStatus.setStatus(refund.getStatus());
        updateStatus.setId(refund.getId());
        if (!refundWriteService.update(updateStatus).getResult()) {
            throw Translate.exceptionOf("数据库异常, 请尽快联系客服处理, 退款单号[%s]", refund.getId());
        }
    }

    /**
     * confirm return
     *
     * @return success confirm
     */
    public Either<Boolean> confirmReturn() {
        // 1. throw error if refund is not on return status
        OrderStatus returnCurrentStatus = OrderStatus.fromInt(refund.getStatus());
        if (!refundFlow.operationAllowed(returnCurrentStatus.getValue(), OrderEvent.RETURN_CONFIRM.toOrderOperation())) {
            log.warn("{} Refund[Id => {}] stay at wrong status[{}] for confirm return", LogUtil.getClassMethodName(), refund.getId(), refund.getStatus());
            throw Translate.exceptionOf("退款单不处于退回发货状态, 请确认后再试");
        }
        try {
            refund.setStatus(refundFlow.target(returnCurrentStatus.getValue(), OrderEvent.RETURN_CONFIRM.toOrderOperation()));
            Refund updateStatus = new Refund();
            updateStatus.setId(refund.getId());
            updateStatus.setStatus(refund.getStatus());
            // record the confirm should we put any thing? not
            updateRefundExtraRecordAction(Action.confirm);
            // update won't return false, true only or null for error
            return Either.ok(refundWriteService.update(updateStatus).getResult());
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    public void clearOrderPushError() {
        if (CollectionUtils.isEmpty(relatedOrderList)) {
            return;
        }

        for (var order : relatedOrderList) {
            if (order instanceof ShopOrder) {
                shopOrderWriteService.deletePushError((ShopOrder) order);
            }
        }
    }

    public enum Action {
        /**
         * 动作触发链 同步
         */
        apply, cancel, accept, reject, refund, rewindSuccess, rewindFailure, refundSuccess, error, express, confirm
    }

    @AllArgsConstructor
    private static class RefundPayment {
        long refundFee;
        Payment payment;
    }
}