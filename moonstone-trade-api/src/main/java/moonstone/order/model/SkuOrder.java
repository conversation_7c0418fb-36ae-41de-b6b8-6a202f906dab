package moonstone.order.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.common.utils.ImageUrlHandler;
import moonstone.item.emu.SkuTagIndex;
import moonstone.order.dto.ShopSkuDiscountDetailDTO;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Desc: sku维度子订单
 * Mail: <EMAIL>
 * Data: 16/2/26
 * Author: yangzefeng
 */
@EqualsAndHashCode(of = {"orderId", "skuId", "quantity"}, callSuper = true)
public class SkuOrder extends OrderBase implements Serializable {
    private static final TypeReference<List<SkuAttribute>> SKU_ATTRIBUTE_TYPE = new TypeReference<>() {
    };
    @Serial
    private static final long serialVersionUID = 4458707475241223633L;

    @Getter
    @Setter
    Long skuImageId;

    @Getter
    @Setter
    Long feeId;
    @Getter
    @Setter
    Long extraId;
    @Getter
    @Setter
    Long depotId;
    @Getter
    @Setter
    Long profitId;

    /**
     * 销售属性id
     */
    @Getter
    @Setter
    private Long skuId;

    /**
     * sku对应的版本号
     */
    @Getter
    @Setter
    private Integer skuVersion;

    /**
     * 购买数量
     */
    @Getter
    @Setter
    private Integer quantity;

    /**
     * 对应的店铺订单id
     */
    @Getter
    @Setter
    private Long orderId;

    /**
     * 采购单Id
     */
    @Getter
    @Setter
    private Long gatherOrderId;

    /**
     * 外部子订单id
     */
    @Getter
    @Setter
    private String outId;

    /**
     * 外部买家id
     */
    @Getter
    @Setter
    private String outBuyerId;

    /**
     * 商品id
     */
    @Getter
    @Setter
    private Long itemId;

    /**
     * 商品名称
     */
    @Getter
    @Setter
    private String itemName;

    /**
     * 是否为第三方商品（0：为自建商品，1：第三方商品）
     */
    @Getter
    @Setter
    private Integer isThirdPartyItem;

    /**
     * 对应的主图
     */
    private String skuImage;

    /**
     * 店铺名称
     */
    @Getter
    @Setter
    private String shopName;

    /**
     * 外部店铺id
     */
    @Getter
    @Setter
    private String outShopId;

    /**
     * 公司id
     */
    @Getter
    @Setter
    private Long companyId;

    /**
     * 是否保税（1：保税，0完税，2：跨境直邮（保税））
     */
    @Getter
    @Setter
    private Integer isBonded;

    /**
     * 税费
     */
    @Getter
    @Setter
    private Long tax;

    /**
     * 外部销售属性id
     */
    @Getter
    @Setter
    private String outerSkuId;

    /**
     * 推送状态(0:不需要推送，1：待推送，2：完成推送，3：待商家审核)
     */
    @Getter
    @Setter
    private Integer pushStatus;

    /**
     * 推单失败原因
     */
    @Getter
    @Setter
    private String pushErrorMsg;

    /**
     * 推送时间
     */
    @Getter
    @Setter
    private Date pushTime;

    /**
     * sku属性 json
     */
    @Getter
    private String skuAttributes;

    @Getter
    private List<SkuAttribute> skuAttrs;


    /**
     * 订单渠道 1-pc, 2-手机
     */
    @Getter
    @Setter
    private Integer channel;

    /**
     * 支付类型 1-在线支付 2-货到付款
     */
    @Getter
    @Setter
    private Integer payType;

    /**
     * 配送方式 1-快递 2-线下配送 3-上门自提
     */
    @Getter
    @Setter
    private Integer shipmentType;

    /**
     * 原价
     */
    @Getter
    @Setter
    private Long originFee;

    /**
     * 优惠后费用（不含税费）
     */
    @Getter
    @Setter
    private Long afterDiscountFee;

    /**
     * 优惠金额
     */
    @Getter
    @Setter
    private Long discount;

    /**
     * 运费
     */
    @Getter
    @Setter
    private Long shipFee;

    /**
     * 运费减免金额
     */
    @Getter
    @Setter
    private Long shipFeeDiscount;

    /**
     * 积分减免金额
     */
    @Getter
    @Setter
    private Long integral;

    /**
     * 余额减免金额
     */
    @Getter
    @Setter
    private Long balance;


    /**
     * 商品快照id
     */
    @Getter
    @Setter
    private Long itemSnapshotId;

    /**
     * 是否申请过逆向流程
     */
    @Getter
    @Setter
    private Boolean hasRefund;


    /**
     * 是否已经开具过发票
     */
    @Getter
    @Setter
    private Boolean invoiced;

    /**
     * 评价标记位, 主要给前端判断用,
     * 创建订单时为null, 确认收货后为1, 评价后为2
     */
    @Getter
    @Setter
    private Integer commented;

    /**
     * 是否申请售后
     */
    @Getter
    @Setter
    private Boolean hasApplyAfterSale;

    /**
     * 电商平台佣金费率, 万分之一
     */
    @Getter
    @Setter
    private Integer commissionRate;

    /**
     * 分销抽佣费率, 万分之一
     */
    @Getter
    @Setter
    private Integer distributionRate;

    /**
     * 改价金额
     */
    @Getter
    @Setter
    private Integer diffFee;

    @Getter
    @Setter
    private Date shipmentAt;

    @Getter
    @Setter
    private String depotName;   //  仓库名字
    @Getter
    @Setter
    private String depotCode;   //  仓库编号

    @Getter
    @Setter
    private String supplierName;    //  货主名字

    /**
     * 订单上的各种标记(二进制位)
     *
     * @see moonstone.order.enu.OrderFlagEnum
     */
    @Deprecated
    @Getter
    @Setter
    private Integer flag;

    /**
     * 商品类目:下单时快照
     */
    @Getter
    @Setter
    private String categoryNameSnapshot;

    /**
     * 发货仓类型(1:代塔仓自有,2：京东云交易)
     */
    @Getter
    @Setter
    private Integer shippingWarehouseType;

    /**
     * 是否发货
     */
    @Getter
    @Setter
    private Boolean shipping;

    @Getter
    @Setter
    @ApiModelProperty(value = "独立库存ID")
    private Long inventoryId;

    /**
     * 优惠明细明细 ShopSkuDiscountDetailDTO.class
     */
    @Getter
    @Setter
    private String discountDetailJson;

    @Getter
    @Setter
    private ShopSkuDiscountDetailDTO discountDetail;

    @Getter
    @Setter
    private Long price;

    public Long getPrice() {
        if (this.quantity == null || this.quantity == 0) {
            return 0L;
        }
        return this.originFee / this.quantity;
    }


    @JsonIgnore
    public String getSkuImage() {
        return ImageUrlHandler.simplify(this.skuImage);
    }

    @JsonProperty("skuImage")
    public String getSkuImage_() {
        return this.skuImage;
    }

    @JsonSetter
    public void setSkuImage(String skuImage) {
        this.skuImage = ImageUrlHandler.complete(skuImage);
    }

    public void setSkuAttrs(List<SkuAttribute> attrs) {
        this.skuAttrs = attrs;
        if (attrs == null) {
            this.skuAttributes = null;
        } else {
            try {
                this.skuAttributes = objectMapper.writeValueAsString(attrs);
            } catch (Exception e) {
                //ignore this fucking exception
            }
        }
    }

    public void setSkuAttributes(String attrsJson) throws Exception {
        this.skuAttributes = attrsJson;
        if (Strings.isNullOrEmpty(attrsJson)) {
            this.skuAttrs = Collections.emptyList();
        } else {
            this.skuAttrs = objectMapper.readValue(attrsJson, SKU_ATTRIBUTE_TYPE);
        }
    }

    /**
     * 判断：是否包含指定推送系统code
     *
     * @param pushSystemId
     * @return
     */
    public boolean containPushSystem(Integer pushSystemId) {
        if (pushSystemId == null) {
            return false;
        }

        if (CollectionUtils.isEmpty(this.getTags()) ||
                !StringUtils.hasText(this.getTags().get(SkuTagIndex.pushSystem.name()))) {
            return false;
        }

        return Arrays.stream(this.getTags().get(SkuTagIndex.pushSystem.name()).split(","))
                .anyMatch(s -> pushSystemId.toString().equals(s));
    }
}
