package moonstone.order.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import moonstone.common.model.ReferenceAble;
import moonstone.order.dto.ShopOrderFeeDetailDTO;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Desc: 店铺维度订单
 * Mail: <EMAIL>
 * Data: 16/2/26
 * Author: yangzefeng
 */
@EqualsAndHashCode(of = {"type", "shipmentType", "payType", "outId"}, callSuper = true)
public class ShopOrder extends OrderBase implements Serializable, ReferenceAble {
    @Serial
    private static final long serialVersionUID = 6999941333076234706L;
    @Getter
    @Setter
    Long feeId;
    @Getter
    @Setter
    Long extraId;
    @Getter
    @Setter
    Long profitId;
    /**
     * 订单类型,1:普通订单  3.积分订单
     */
    @Getter
    @Setter
    private Integer type;
    /**
     * 外部买家id
     */
    @Getter
    @Setter
    private String outBuyerId;
    /**
     * 店铺名称
     */
    @Getter
    @Setter
    private String shopName;
    /**
     * 外部店铺id
     */
    @Getter
    @Setter
    private String outShopId;
    /**
     * 采购单Id
     */
    @Getter
    @Setter
    private Long gatherOrderId;
    /**
     * 公司id
     */
    @Getter
    @Setter
    private Long companyId;
    /**
     * 推荐人id
     */
    @Getter
    @Setter
    private Long refererId;
    /**
     * 推荐人名称
     */
    @Getter
    @Setter
    private String refererName;
    /**
     * 原价
     */
    @Getter
    @Setter
    private Long originFee;
    /**
     * 运费
     */
    @Getter
    @Setter
    private Integer shipFee;
    /**
     * 原始运费
     */
    @Getter
    @Setter
    private Integer originShipFee;
    /**
     * 运费对应的营销活动
     */
    @Getter
    @Setter
    private Long shipmentPromotionId;
    /**
     * 优惠金额
     */
    @Getter
    @Setter
    private Integer discount;
    /**
     * 积分减免金额
     */
    @Getter
    @Setter
    private Integer integral;
    /**
     * 余额减免金额
     */
    @Getter
    @Setter
    private Integer balance;
    /**
     * 配送方式 1-快递 2-线下配送 3-上门自提
     */
    @Getter
    @Setter
    private Integer shipmentType;
    /**
     * 支付类型 1-在线支付 2-货到付款
     */
    @Getter
    @Setter
    private Integer payType;
    /**
     * 订单渠道 1-pc, 2-手机
     */
    @Getter
    @Setter
    private Integer channel;
    /**
     * 是否申请过逆向流程
     */
    @Getter
    @Setter
    private Boolean hasRefund;
    /**
     * 是否已评论(所有子订单已评论)
     */
    @Getter
    @Setter
    private Boolean commented;
    /**
     * 买家备注
     */
    @Getter
    @Setter
    private String buyerNote;
    /**
     * 卖家备注
     */
    @Getter
    @Setter
    private String sellerNote;
    /**
     * 外部订单id
     */
    @Getter
    @Setter
    private String outId;
    /**
     * 仓库所在仓库名称
     */
    @Getter
    @Setter
    private String depotCustomName;
    /**
     * 海关申报用订单号
     */
    @Getter
    @Setter
    private String declaredId;
    /**
     * 电商平台佣金费率, 万分之一
     */
    @Getter
    @Setter
    private Integer commissionRate;
    /**
     * 分销抽佣费率, 万分之一
     */
    @Getter
    @Setter
    private Integer distributionRate;
    /**
     * 改价金额
     */
    @Getter
    @Setter
    private Integer diffFee;
    /**
     * 最早发货时间
     */
    @Getter
    @Setter
    private Date firstShipmentAt;
    /**
     * 最晚发货时间
     */
    @Getter
    @Setter
    private Date lastShipmentAt;

    /**
     * 最早确认时间
     */
    @Getter
    @Setter
    private Date firstConfirmAt;

    /**
     * 最晚确认时间
     */
    @Getter
    @Setter
    private Date lastConfirmAt;

    /**
     * 异常类型
     * @see moonstone.order.dto.fsm.OrderExceptionEnum
     */
    @Getter
    @Setter
    private Integer exceptionType;

    /**
     * 异常原因
     */
    @Getter
    @Setter
    private String exceptionReason;

    /**
     * 订单上的各种标记(二进制位)
     *
     * @see moonstone.order.enu.OrderFlagEnum
     */
    @Getter
    @Setter
    private Integer flag;

    /**
     * 金额明细json（用于存储下单时 各种金额的使用快照）
     */
    @Getter
    @Setter
    private String feeDetailJson;

    @Getter
    @Setter
    private ShopOrderFeeDetailDTO feeDetail;

    @Getter
    @Setter
    @ApiModelProperty(value = "订单活动来源")
    private Integer orderSource;

    @Getter
    @Setter
    @ApiModelProperty(value = "独立库存ID")
    private Long inventoryId;

    @Getter
    @Setter
    @ApiModelProperty(value = "活动ID")
    private Integer activityId;

    @Getter
    @Setter
    private Long groupId;

    @Getter
    @Setter
    private Long createdBy;

    @Getter
    @Setter
    private Long updatedBy;

    @Override
    public Long getReferenceId() {
        return refererId;
    }

    @Override
    public void setReferenceId(Long referenceId) {
        refererId = referenceId;
    }

    /**
     * 是否评价 1 否, 2 是
     */
    @Getter
    @Setter
    private Integer evaluateStatus;

    @Override
    public String toString() {
        return new StringBuilder("[ShopOrder:").append(" id=").append(getId()).append(", fee=").append(getFee()).append(
                ", outFromType=").append(outFromType).append(
                ", shopName='").append(shopName).append('\'').append(
                ", declaredId='").append(declaredId).append('\'').append(
                ", feeId=").append(feeId).append(
                ", extraId=").append(extraId).append(
                ", profitId=").append(profitId).append(
                ']').toString();
    }
}
