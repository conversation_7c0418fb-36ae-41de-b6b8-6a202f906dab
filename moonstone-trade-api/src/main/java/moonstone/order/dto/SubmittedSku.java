/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 订单提交的单个sku信息
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-25
 */
@Data
public class SubmittedSku implements Serializable {
    private static final long serialVersionUID = 2195209009467636745L;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * 外部sku编码
     */
    private String outerSkuId;

    /**
     * 微店Id
     */
    private Long weShopId;

    /**
     * sku 数量
     */
    private Integer quantity;

    /**
     * sku 级别的优惠id
     */
    private Long promotionId;

    /**
     * sku 级别的收货地址信息, 如果为空, 则表示继承店铺订单级别的收货信息id
     */
    private Long receiverInfoId;

    /**
     *  sku级别的发票id, 如果为空, 则表示继承店铺订单级别的发票id
     */
    private Long invoiceId;

    /**
     * sku级别的配送方式id
     */
    private Integer shipmentType;

    /**
     * 商品来源(1：代塔仓自有，2：京东云交易)
     */
    private Integer sourceType;

    /**
     * 其他附加信息, 扩展用
     */
    private Map<String, String> extra;

    /**
     * 优惠明细（用于社区运营）
     */
    private ShopSkuDiscountDetailDTO discountDetail;

    private Integer marketingToolId;

    private Integer activityId;
}
