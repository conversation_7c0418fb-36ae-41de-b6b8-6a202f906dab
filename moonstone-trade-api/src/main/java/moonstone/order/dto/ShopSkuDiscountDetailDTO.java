package moonstone.order.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ShopSkuDiscountDetailDTO implements Serializable {

    /**
     * 商品券
     */
    private BigDecimal itemCouponPrice;

    /**
     * 店铺券
     */
    private BigDecimal shopCouponPrice;

    /**
     * 使用优惠券idList
     */
    private List<Long> couponIdList;

    /**
     * 礼金
     */
    private BigDecimal giftPrice;

    /**
     * 福豆数量
     */
    private BigDecimal giftBeanNum;

    /**
     * 现金券
     */
    private BigDecimal cashPrice;

    /**
     * 实际费用
     */
    private BigDecimal fee;

    /**
     * 原价
     */
    private BigDecimal originFee;

    /**
     * 税费
     */
    private BigDecimal tax;

    /**
     * 运费
     */
    private BigDecimal shipFee;

    /**
     * 优惠
     */
    private BigDecimal discount;
}
