package moonstone.order.dto;

import lombok.Data;
import moonstone.order.api.ExpressTrackInfo;
import moonstone.order.dto.fsm.OrderOperation;
import moonstone.order.model.Refund;
import moonstone.order.model.RefundExpressInfo;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class RefundDetail implements Serializable {
    private static final long serialVersionUID = -3263971899940192641L;

    private Refund refund;

    private Set<OrderOperation> operations;

    private ShopOrder shopOrder;

    private List<SkuOrder> skuOrderList;

    private AfterSaleTimeRecord afterSaleTimeRecord;

    private RefundExpressInfo refundExpressInfo;

    /**
     * return express detail, work only if exists return express no
     */
    private ExpressTrackInfo returnExpressTrack;

    /**
     * 退货地址
     */
    private String sellerAddress;

    /**
     * 快递公司
     */
    private String expressCompany;

    /**
     * 快递单号
     */
    private String expressNo;

    /**
     * 申请回执类型（1：待回执，2：收到回执成功，3：收到回执失败）
     */
    private Integer applyReceiptType;

    /**
     * 申请回执的消息
     */
    private String applyReceiptMsg;

    /**
     * 退款回执类型（1：待回执，2：收到回执成功）
     */
    private Integer refundReceiptType;

    /**
     * 订单总税费
     */
    private Long totalTax;

}