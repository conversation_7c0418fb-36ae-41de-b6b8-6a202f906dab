/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import moonstone.order.model.ReceiverInfo;
import moonstone.promotion.model.Promotion;
import moonstone.shop.model.Shop;
import moonstone.weShop.model.WeShop;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 按店铺归组的sku信息
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-26
 */
@Data
public class RichSkusByShop implements Serializable {

    private static final long serialVersionUID = 411454314845155319L;

    /**
     * 对应的店铺信息
     */
    private Shop shop;

    /**
     * 对应的微分销店铺信息
     */
    private WeShop weShop;
    /// 订单来源
    private String outFrom;

    /// 订单来源店铺Id
    private String outShopId;
    /**
     * 推荐人id
     */
    private Long refererId;

    /**
     * 推荐人名称
     */
    private String refererName;

    /**
     * 对应的sku列表信息
     */
    private List<RichSku> richSkus;

    /**
     * 对应的买家留言信息
     */
    private String buyerNote;

    /**
     * 对应的收货信息, 如果为空, 则表示可能是合并支付订单或者子订单级别的收货地址信息
     */
    private ReceiverInfo receiverInfo;


    /**
     * 对应的发票信息, 如果为空, 则表示可能暂时不需要发票
     */
    private Long invoiceId;

    /**
     * 对应的配送方式信息
     */
    private Integer shipmentType;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 实付价
     */
    private Long fee;

    /**
     * 原价
     */
    private Long originFee;

    /**
     * 优惠金额
     */
    private Integer discount;

    /**
     * 运费
     */
    private Integer shipFee;

    /**
     * 原始运费
     */
    private Integer originShipFee;

    /**
     * 积分减免金额
     */
    private Integer integral;

    /**
     * 余额减免金额
     */
    private Integer balance;

    /**
     * 选中的或者默认的店铺级别优惠信息id
     */
    private Long promotionId;


    /**
     * 店铺级别的可用的优惠活动列表
     */
    private List<Promotion> shopPromotions;

    /**
     * 选中的或者默认的运费优惠信息id
     */
    private Long shipmentPromotionId;

    /**
     * 运费优惠活动列表
     */
    private List<Promotion> shipmentPromotions;

    /**
     * 订单渠道 1-pc, 2-小程序，3-其它移动端
     */
    private Integer channel;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 电商平台佣金费率
     */
    private Integer commissionRate;

    /**
     * 分销抽佣费率, 万分之一
     */
    private Integer distributionRate;

    /**
     * 发货仓类型
     */
    private Integer shippingWarehouseType;

    /**
     * 附加信息
     */
    private Map<String, String> extra;

    /**
     * 订单标签
     */
    private Map<String, String> tags;

    @ApiModelProperty(value = "订单活动来源")
    private Integer orderSource;

    @ApiModelProperty(value = "独立库存ID")
    private Long inventoryId;

    @ApiModelProperty(value = "团ID")
    private Long groupId;

    @ApiModelProperty(value = "活动ID")
    private Integer activityId;

    /**
     * 费用明细（用于社区运营业务）
     */
    private ShopOrderFeeDetailDTO shopOrderFeeDetailDTO;

    public String getSubStoreName() {
        return refererName;
    }
}
