package moonstone.order.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ShopOrderFeeDetailDTO implements Serializable {

    /**
     * 原商品价
     */
    private BigDecimal originItemPrice;

    /**
     * 优惠后商品价格
     */
    private BigDecimal itemPrice;

    /**
     * 运费
     */
    private BigDecimal shippingPrice;

    /**
     * 税费
     */
    private BigDecimal taxPrice;

    /**
     * 总优惠金额
     */
    private BigDecimal discountPrice;

    /**
     * 优惠券抵扣
     */
    private BigDecimal couponPrice;

    /**
     * 优惠券详情
     */
    private List<Long> couponIdList;

    /**
     * 礼金抵扣
     */
    private BigDecimal giftPrice;

    /**
     * 福豆数量
     */
    private BigDecimal giftBeanNum;

    /**
     * 账户现金抵扣
     */
    private BigDecimal cashPrice;

    /**
     * 总计
     */
    private BigDecimal totalPrice;

    /**
     * 实际支付
     */
    private BigDecimal actualPrice;
}
