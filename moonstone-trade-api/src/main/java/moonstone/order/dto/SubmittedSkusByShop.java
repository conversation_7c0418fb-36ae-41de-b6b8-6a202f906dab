/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 按照店铺归组的订单提交信息
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-25
 */
@Data
public class SubmittedSkusByShop implements Serializable {


    private static final long serialVersionUID = 8506160058417238668L;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 微店Id
     */
    private Long weShopId;

    /**
     * 提交的单个sku信息
     */
    private List<SubmittedSku> submittedSkus;

    /**
     * 买家备注
     */
    private String buyerNote;

    /**
     * 店铺级别的优惠id
     */
    private Long promotionId;

    /**
     * 运费的优惠id
     */
    private Long shipmentPromotionId;

    /**
     * 配送方式id
     */
    private Integer shipmentType;

    /**
     * 收货信息id
     */
    private Long receiverInfoId;

    /**
     * 发票信息id
     */
    private Long invoiceId;

    /**
     * 其他附加信息, 扩展用
     */
    private Map<String, String> extra;

    /**
     * 费用明细（用于社区运营业务）
     */
    private ShopOrderFeeDetailDTO shopOrderFeeDetailDTO;
}
