package moonstone.order.dto;

import lombok.Data;
import moonstone.order.dto.fsm.OrderOperation;
import moonstone.order.dto.view.SkuOrderView;
import moonstone.order.model.Refund;
import moonstone.order.model.RefundExpressInfo;
import moonstone.order.model.SkuOrder;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * Desc: 退款单列表需要展示的信息
 * Mail: <EMAIL>
 * Data: 16/6/17
 * Author: yangzefeng
 */
@Data
public class RefundList implements Serializable {
    private static final long serialVersionUID = -7568846388751669306L;

    private Refund refund;

    /**
     * 实际退款单类型，保存refundType原有的状态,  {@link Refund.RefundType}
     */
    private Integer actualRefundType;

    private List<SkuOrderView> skuOrders;

    private Set<OrderOperation> operations;

    private RefundExpressInfo refundExpressInfo;

    /**
     * 申报单号
     */
    private String declareId;

    /**
     * 门店名称
     */
    private String subStoreName;

    /**
     * 服务提供商名称
     */
    private String serviceProviderName;

    /**
     * 导购名
     */
    private String subStoreTStoreGuiderName;

    /**
     * 申请回执类型（1：待回执，2：收到申请成功回执，3：收到申请失败回执）
     */
    private Integer applyReceiptType;

    /**
     * 申请回执对应信息
     */
    private String applyReceiptMsg;

    /**
     * 退款回执类型（1：待回执，2：收到退款成功回执，3：收到退款失败回执）
     */
    private Integer refundReceiptType;

    /**
     * 卖家地址
     */
    private String sellerAddress;
}
