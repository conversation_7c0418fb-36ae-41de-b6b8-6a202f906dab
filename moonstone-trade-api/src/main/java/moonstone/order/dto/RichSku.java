/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.order.model.ReceiverInfo;
import moonstone.promotion.model.Promotion;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.model.WeShopSku;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-26
 */
@Data
public class RichSku implements Serializable {

    private static final long serialVersionUID = 5313637172576678217L;

    /**
     * 对应的商品信息
     */
    private Item item;

    /**
     * 对应的sku信息
     */
    private Sku sku;

    /**
     * 对应的微分销商品信息（如果没有，则为null）
     */
    private WeShopItem weShopItem;

    /**
     * 对应的微分销单品
     */
    private WeShopSku weShopSku;

    /**
     * 税费
     */
    private Long tax;

    /**
     * 对应的购买数量
     */
    private Integer quantity;

    /**
     * sku级别的收货信息id, 如果为空, 则代表是店铺订单级别的收货地址
     */
    private ReceiverInfo receiverInfo;

    /**
     * sku级别的发票id, 如果为空, 则表示使用店铺订单级别的发票id
     */
    private Long invoiceId;


    /**
     * 对应的配送方式
     */
    private Integer shipmentType;

    /**
     * 订单渠道 1-pc, 2-小程序，3-其它移动端
     */
    @Getter
    @Setter
    private Integer channel;

    /**
     * sku营销价格,用于单品营销
     */
    @Getter
    @Setter
    private Long skuPromotionPrice;

    /**
     * 实付价格（加上税费后的实付）
     */
    private Long fee;

    /**
     * 折后价格（不算税费）
     */
    private Long afterDiscountFee;

    /**
     * 原价
     */
    private Long originFee;

    /**
     * 优惠金额
     */
    private Long discount;

    /**
     * 运费
     */
    private Long shipFee;

    /**
     * 运费减免金额
     */
    private Long shipFeeDiscount;

    /**
     * 积分减免金额
     */
    private Long integral;

    /**
     * 余额减免金额
     */
    private Long balance;

    /**
     * 商品快照id
     */
    private Long itemSnapshotId;

    /**
     * 选中的或者默认的sku级别优惠信息id
     */
    private Long promotionId;


    /**
     * 符合条件的sku级别优惠信息列表
     */
    private List<Promotion> skuPromotions;

    /**
     * 子订单状态
     */
    private Integer orderStatus;

    /**
     * 掩码,用来标记商品是否缺货0表示缺货,1表示有货
     */
    private Integer mask;

    /**
     * 电商平台佣金费率
     */
    private Integer commissionRate;

    /**
     * 分销抽佣费率, 万分之一
     */
    private Integer distributionRate;

    /**
     * sku订单扩展信息
     */
    private Map<String, String> extra;

    /**
     * sku订单标签
     */
    private Map<String, String> tags;

    /**
     * 改价金额
     */
    private Integer diffFee;

    /**
     * 仓库名
     */
    private String depotName;

    /**
     * 货主名
     */
    private String supplierName;

    /**
     * 会员价使用的Id
     */
    private Long pid;

    /**
     * 标记：门店限定卷的限定商品
     */
    private boolean subStoreOnlyLimitedItem;

    private List<String> flagList;

    /**
     * 商品来源(1：代塔仓自有，2：京东云交易)
     */
    private Integer sourceType;

    /**
     * 优惠明细（用于社区运营）
     */
    private ShopSkuDiscountDetailDTO discountDetail;
}
