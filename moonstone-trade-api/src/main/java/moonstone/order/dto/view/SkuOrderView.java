package moonstone.order.dto.view;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import moonstone.order.dto.SkuComboDTO;
import moonstone.order.model.SkuOrder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SkuOrderView extends SkuOrder {

    String specification;

    /**
     * 组合品相关信息
     */
    private List<SkuComboDTO> skuComboList;
}