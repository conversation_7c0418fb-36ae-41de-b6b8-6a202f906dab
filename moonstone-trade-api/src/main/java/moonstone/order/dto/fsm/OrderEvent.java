/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.dto.fsm;

import lombok.Getter;

import java.util.Objects;

/**
 * 交易流程中典型的事件
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-24
 *
 * <AUTHOR>
 */
public enum OrderEvent {
    /**
     * 订单事件
     */
    Unknown(-9999, "unknown", "unknown", "未知操作"),
    PAY(1, "pay", "buyer", "支付"),
    SHIP(2, "ship", "seller", "发货"),
    CONFIRM(3, "confirm", "buyer", "确定收货"),
    COMMENT(4, "comment", "buyer", "评价"),
    BUYER_CANCEL(-1, "buyerCancel", "buyer", "取消订单"),
    SELLER_CANCEL(-2, "sellerCancel", "seller,admin", "取消订单"),
    REFUND_APPLY(-3, "refundApply", "buyer,seller", "退款申请"),
    REFUND_APPLY_AGREE(-4, "refundApplyAgree", "seller", "退款同意"),
    REFUND_APPLY_CANCEL(-5, "refundApplyCancel", "buyer", "取消退款"),
    REFUND_APPLY_REJECT(-6, "refundApplyReject", "seller", "拒绝退款"),
    REFUND(-7, "refund", "seller", "退款(将触发真实支付退款)"),
    RETURN_APPLY(-8, "returnApply", "buyer,seller", "退货申请"),
    RETURN_APPLY_AGREE(-9, "returnApplyAgree", "seller", "退货申请同意"),
    RETURN_APPLY_CANCEL(-10, "returnApplyCancel", "buyer", " 退货申请取消"),
    RETURN_APPLY_REJECT(-11, "returnApplyReject", "seller", "退货申请拒绝"),
    RETURN(-12, "return", "buyer", "确定退货, 提交快递单"),
    RETURN_REJECT(-13, "returnReject", "seller", "退货拒绝"),
    RETURN_CONFIRM(-14, "returnConfirm", "seller", "确定收到退货"),
    DELETE(-15, "delete", "buyer", "删除订单"),
    REFUND_SUCCESS(-16, "refundSuccess", "paymentPlatform", "退款成功"),

    FORCE_REFUND_AGREE(-17, "forceRefundAgree", "seller,admin", "强制退款同意"),
    ;

    @Getter
    private final int value;

    @Getter
    private final String text;

    /**
     * 事件的触发者, 可以有多个角色. 多个角色之间用,分割.
     */
    @Getter
    private final String operator;

    @Getter
    private final String desc;

    OrderEvent(int value, String text, String operator, String desc) {
        this.value = value;
        this.text = text;
        this.operator = operator;
        this.desc = desc;
    }

    public static OrderEvent fromInt(Integer value) {
        for (OrderEvent orderEvent : OrderEvent.values()) {
            if (Objects.equals(orderEvent.getValue(), value)) {
                return orderEvent;
            }
        }
        return Unknown;
    }

    public OrderOperation toOrderOperation() {
        return new OrderOperation(value, text, operator);
    }

}
