/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.dto.fsm;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import io.terminus.common.exception.JsonResponseException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;


/**
 * 流程包括正向部分及逆向部分, 一个订单只对应一个流程, 无论其是处于正向还是逆向状态
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-06
 */
@Slf4j
public abstract class Flow {
    /**
     * 流程名称
     */
    @Getter
    private final String name;

    /**
     * (子)订单状态流转FSM
     */
    private final Table<Integer, OrderOperation, Integer> orderFSM;

    public Flow(String name) {
        this.name = name;
        this.orderFSM = HashBasedTable.create();
        configure();
    }

    /**
     * check the targetStatus is operated by operation
     * usually used by check has the entity has reach the operation target status
     *
     * @param orderOperation operation
     * @param targetStatus   target status
     * @return has reach
     */
    public boolean reachStatus(OrderOperation orderOperation, Integer targetStatus) {
        return orderFSM.column(orderOperation).containsValue(targetStatus);
    }

    /**
     * 添加状态转移
     *
     * @param source         当前状态
     * @param orderOperation 迁移触发条件
     * @param target         目标状态
     */
    protected void addTransition(Integer source, OrderOperation orderOperation, Integer target) {
        if (orderFSM.contains(source, orderOperation)) {
            Integer exist = orderFSM.get(source, orderOperation);
            if (Objects.equal(exist, target)) {
                log.info("status transition(source={}, operation={}, target={}) has been registered, skip",
                        source, orderOperation, target);
                return;
            } else {  //允许被重载
                log.warn("status transition(source={}, operation={}, target={}) is OVERRIDING " +
                                "old transition(source={}, operation={}, target={})",
                        source, orderOperation, target, source, orderOperation, exist);
            }
        }
        orderFSM.put(source, orderOperation, target);
    }

    /**
     * 返回流程的下一个状态, 如果没有定义下一个状态, 则抛出异常
     *
     * @param source    当前状态
     * @param orderOperation   订单操作
     * @return 下一个状态
     */
    public Integer target(Integer source, OrderOperation orderOperation){
        if(!orderFSM.contains(source, orderOperation)){
            log.error("orderOperation={}, is not available on status({})", JSON.toJSONString(orderOperation), source);
            log.error("订单操作 {} 是不被允许变更成 {}", JSON.toJSONString(orderOperation), source);
            throw new JsonResponseException("当前订单状态变更失败");
        }
        return orderFSM.get(source, orderOperation);
    }

    /**
     * 判断操作是否允许作用在当前状态下
     *
     * @param source  当前状态
     * @param orderOperation  订单操作
     * @return  操作是否允许作用在当前状态下
     */
    public boolean operationAllowed(Integer source, OrderOperation orderOperation){
        return orderFSM.contains(source, orderOperation);
    }

    /**
     * 获取当前状态下允许的操作
     *
     * @param source  当前状态
     * @return  允许的操作
     */
    public Set<OrderOperation> availableOperations(Integer source){
        return orderFSM.row(source).keySet();
    }


    /**
     * 配置流程
     */
    protected abstract void configure();
}
