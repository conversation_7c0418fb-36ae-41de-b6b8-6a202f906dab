package moonstone.order.api.payoperation.agentpay;

import moonstone.order.api.PayChannelOperation;
import moonstone.order.model.AgentPayOrder;
import moonstone.order.model.AgentPayPayment;

import javax.servlet.http.HttpServletRequest;

/**
 * 托管代付
 */
public interface AgentPayOperation extends PayChannelOperation {

    /**
     * 发起代付
     *
     * @param agentPayPayment
     * @return
     */
    AgentPayRequestResult agentPayRequest(AgentPayPayment agentPayPayment);

    /**
     * 代付结果回调
     *
     * @param request
     * @return
     */
    AgentPayCallbackResult agentPayCallback(AgentPayPayment agentPayPayment, HttpServletRequest request);
}
