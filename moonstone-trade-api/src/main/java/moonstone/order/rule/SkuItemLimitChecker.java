package moonstone.order.rule;

import io.terminus.common.exception.JsonResponseException;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.model.Sku;
import moonstone.order.dto.RichSku;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

public abstract class SkuItemLimitChecker {
    public static void checkLimitThenAddQuantity(Sku sku, int quantity, HashMap<Long, Integer> skuIdMapBaseQuantity) {
        checkBuyLimit(sku, quantity + skuIdMapBaseQuantity.getOrDefault(sku.getId(), 0));
        skuIdMapBaseQuantity.put(sku.getId(), skuIdMapBaseQuantity.getOrDefault(sku.getId(), 0) + quantity);
    }

    private final Map<Long, Integer> baseQuantityMapBySkuId = new HashMap<>();


    public static SkuItemLimitChecker build() {
        return new SkuItemLimitChecker() {
        };
    }

    /**
     * 累计检查购买这个单品是否会超出限购数量
     *
     * @param richSku 单品订单
     */
    public void checkBuyLimit(RichSku richSku) {
        Sku sku = richSku.getSku();
        if (Objects.isNull(sku.getExtraMap()) || ObjectUtils.isEmpty(sku.getExtraMap().get(SkuExtraIndex.orderQuantityLimit.name())))
            return;
        checkBuyLimit(richSku, baseQuantityMapBySkuId.getOrDefault(sku.getId(), 0));
        baseQuantityMapBySkuId.put(sku.getId(), baseQuantityMapBySkuId.getOrDefault(sku.getId(), 0) + richSku.getQuantity());
    }

    public static void checkBuyLimit(Sku sku, int baseQuantity) {
        Map<String, String> extra = shapeMap(sku.getExtraMap());
        if (!ObjectUtils.isEmpty(extra.get(SkuExtraIndex.orderQuantityLimit.name()))) {
            if (baseQuantity > Integer.parseInt(extra.get(SkuExtraIndex.orderQuantityLimit.name()))) {
                throw new JsonResponseException("item.quantity.limit");
            }
        }
    }

    private static void checkBuyLimit(RichSku richSku, int baseQuantity) {
        Map<String, String> extra = richSku.getSku().getExtraMap();
        if (richSku.getQuantity() + baseQuantity > Integer.parseInt(extra.get(SkuExtraIndex.orderQuantityLimit.name()))) {
            throw new JsonResponseException("item.quantity.limit");
        }
    }

    private static Map<String, String> shapeMap(Map<String, String> map) {
        return map == null ? new TreeMap<>() : map;
    }
}
