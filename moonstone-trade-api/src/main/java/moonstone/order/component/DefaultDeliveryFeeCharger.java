package moonstone.order.component;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.common.utils.BeanMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import moonstone.delivery.constant.DeliveryFeeChargeMethod;
import moonstone.delivery.dto.RichItemDeliveryFee;
import moonstone.delivery.model.DeliveryFeeBase;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.delivery.model.SpecialDeliveryFee;
import moonstone.delivery.service.DeliveryFeeReadService;
import moonstone.item.emu.ItemExtraIndex;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.DeliveryFeeCharger;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;
import moonstone.order.model.ReceiverInfo;
import moonstone.user.address.service.AddressReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;

/**
 * Origin-Author:cp
 * Created on 6/14/16.
 *
 * <AUTHOR>
 */
@Slf4j
public class DefaultDeliveryFeeCharger implements DeliveryFeeCharger {

    @Autowired
    private SkuReadService skuReadService;

    @Autowired
    private ItemReadService itemReadService;

    @Autowired
    private DeliveryFeeReadService deliveryFeeReadService;

    @Autowired
    private AddressReadService addressReadService;

    /**
     * 计量单位存储倍数
     */
    private static final int AMOUNT_TIMES = 10;


    @Override
    public Integer charge(Long skuId, Integer quantity, Integer addressId) {
        Sku sku = findSku(skuId);
        Item item = findItem(sku);

        RichSku richSku = new RichSku();
        richSku.setItem(item);
        richSku.setSku(sku);
        richSku.setQuantity(quantity);

        RichItemDeliveryFee richItemDeliveryFee = findRichItemDeliveryFee(item.getId());
        if (richItemDeliveryFee == null) {
            log.warn("item delivery fee not existed where item id={}", item.getId());
            return 0;
        }

        DeliveryFeeBase deliveryFeeBase = determineDeliveryFee(item.getId(), richItemDeliveryFee, findAddressAncestors(addressId));
        ItemDeliveryFeeGroup itemDeliveryFeeGroup = makeRichItemDeliveryFee(richItemDeliveryFee.getDeliveryFeeTemplate().getId(), deliveryFeeBase, richSku);

        return doCharge(ImmutableSet.of(itemDeliveryFeeGroup));
    }

    @Override
    public Map<Long, Integer> charge(List<RichSkusByShop> richSkusByShops, ReceiverInfo receiverInfo) {
        Map<Long, Integer> deliveryFeeByShop = new HashMap<>(8);

        List<Long> itemIds = getItemIds(richSkusByShops);
        Map<Long, RichItemDeliveryFee> richItemDeliveryFeeByItemIdIndex = findRichItemDeliveryFee(itemIds);

        for (RichSkusByShop richSkusByShop : richSkusByShops) {
            Integer deliveryFee = chargeForShop(richSkusByShop, receiverInfo, richItemDeliveryFeeByItemIdIndex);

            deliveryFeeByShop.put(richSkusByShop.getShop().getId(), deliveryFee);
        }

        return deliveryFeeByShop;
    }

    protected Integer chargeForShop(RichSkusByShop richSkusByShop, ReceiverInfo globalReceiverInfo, Map<Long, RichItemDeliveryFee> richItemDeliveryFeeByItemIdIndex) {
        //根据运费模板和计量单位分组
        Set<ItemDeliveryFeeGroup> itemDeliveryFeeGroups = new HashSet<>();
        for (RichSku richSku : richSkusByShop.getRichSkus()) {
            Item item = richSku.getItem();

            RichItemDeliveryFee richItemDeliveryFee = richItemDeliveryFeeByItemIdIndex.get(item.getId());
            if (richItemDeliveryFee == null) {
                log.warn("item delivery fee not existed where item id={}", item.getId());
                continue;
            }

            ReceiverInfo receiverInfo = chooseReceiverInfo(richSku, richSkusByShop, globalReceiverInfo);
            if (receiverInfo == null) {
                continue;
            }

            List<Integer> receiverAddressAncestors = Arrays.asList(receiverInfo.getProvinceId(), receiverInfo.getCityId(), receiverInfo.getRegionId());
            DeliveryFeeBase deliveryFeeBase = determineDeliveryFee(item.getId(), richItemDeliveryFee, receiverAddressAncestors);

            richSku.setShipmentType(deliveryFeeBase.getDeliverMethod());

            ItemDeliveryFeeGroup itemDeliveryFeeGroup = makeRichItemDeliveryFee(richItemDeliveryFee.getDeliveryFeeTemplate().getId(), deliveryFeeBase, richSku);
            if (itemDeliveryFeeGroups.contains(itemDeliveryFeeGroup)) {
                addRealQuantity(itemDeliveryFeeGroups, itemDeliveryFeeGroup);
            } else {
                itemDeliveryFeeGroups.add(itemDeliveryFeeGroup);
            }
        }
        Integer deliveryFee = itemDeliveryFeeGroups.isEmpty() ? 0 : doCharge(itemDeliveryFeeGroups);

        richSkusByShop.setOriginShipFee(deliveryFee);
        richSkusByShop.setShipFee(richSkusByShop.getOriginShipFee());

        return deliveryFee;
    }

    private ReceiverInfo chooseReceiverInfo(RichSku richSku, RichSkusByShop richSkusByShop, ReceiverInfo globalReceiverInfo) {
        if (richSku.getReceiverInfo() != null) {
            return richSku.getReceiverInfo();
        }
        if (richSkusByShop.getReceiverInfo() != null) {
            return richSkusByShop.getReceiverInfo();
        }
        return globalReceiverInfo;
    }

    private Item findItem(Sku sku) {
        Response<Item> findItemResp = itemReadService.findById(sku.getItemId());
        if (!findItemResp.isSuccess()) {
            log.error("fail to find item by id:{},cause:{}", sku.getItemId(), findItemResp.getError());
            throw new JsonResponseException(findItemResp.getError());
        }
        return findItemResp.getResult();
    }

    private Sku findSku(Long skuId) {
        Response<Sku> findSkuResp = skuReadService.findSkuById(skuId);
        if (!findSkuResp.isSuccess()) {
            log.error("fail to find sku by id:{},cause:{}", skuId, findSkuResp.getError());
            throw new JsonResponseException(findSkuResp.getError());
        }
        return findSkuResp.getResult();
    }

    private List<Integer> findAddressAncestors(Integer addressId) {
        Response<List<Integer>> findResp = addressReadService.ancestorsOf(addressId);
        if (!findResp.isSuccess()) {
            log.error("fail to find ancestors by addressId:{},cause:{}", addressId, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        return findResp.getResult();
    }

    private Integer doCharge(Set<ItemDeliveryFeeGroup> itemDeliveryFeeGroups) {
        Integer deliveryFee = 0;
        for (ItemDeliveryFeeGroup itemDeliveryFeeGroup : itemDeliveryFeeGroups) {
            deliveryFee += chargeForGroup(itemDeliveryFeeGroup);
        }
        return deliveryFee;
    }

    private Integer chargeForGroup(ItemDeliveryFeeGroup itemDeliveryFeeGroup) {
        if (itemDeliveryFeeGroup.getIsFree()) {
            return 0;
        }
        //固定运费
        if (itemDeliveryFeeGroup.getChargeMethod() == DeliveryFeeChargeMethod.byStatic.getType()) {
            return itemDeliveryFeeGroup.getFee();
        }
        if (itemDeliveryFeeGroup.getRealQuantity() <= 1.0 * itemDeliveryFeeGroup.getInitAmount() / AMOUNT_TIMES) {
            return itemDeliveryFeeGroup.getInitFee();
        } else {
            int delta = (int) Math.ceil((itemDeliveryFeeGroup.getRealQuantity() - 1.0 * itemDeliveryFeeGroup.getInitAmount() / AMOUNT_TIMES) / (1.0 * itemDeliveryFeeGroup.getIncrAmount() / AMOUNT_TIMES));
            return itemDeliveryFeeGroup.getInitFee() + delta * itemDeliveryFeeGroup.getIncrFee();
        }

    }

    private void addRealQuantity(Set<ItemDeliveryFeeGroup> itemDeliveryFeeGroups, ItemDeliveryFeeGroup itemDeliveryFeeGroup) {
        for (ItemDeliveryFeeGroup group : itemDeliveryFeeGroups) {
            if (group.equals(itemDeliveryFeeGroup)) {
                group.setRealQuantity(group.getRealQuantity() + itemDeliveryFeeGroup.getRealQuantity());
                return;
            }
        }
    }

    private List<Long> getItemIds(List<RichSkusByShop> richSkusByShops) {
        Set<Long> itemIds = Sets.newHashSet();
        for (RichSkusByShop richSkusByShop : richSkusByShops) {
            for (RichSku richSku : richSkusByShop.getRichSkus()) {
                itemIds.add(richSku.getItem().getId());
            }
        }
        return Lists.newArrayList(itemIds);
    }

    protected RichItemDeliveryFee findRichItemDeliveryFee(Long itemId) {
        Map<Long, RichItemDeliveryFee> richItemDeliveryFeeByItemIdIndex = findRichItemDeliveryFee(Collections.singletonList(itemId));
        return richItemDeliveryFeeByItemIdIndex.get(itemId);
    }

    protected Map<Long, RichItemDeliveryFee> findRichItemDeliveryFee(List<Long> itemIds) {
        Response<List<RichItemDeliveryFee>> findResp = deliveryFeeReadService.findItemDeliveryFeeDetailByItemIds(itemIds);
        if (!findResp.isSuccess()) {
            log.error("fail to find item delivery fee detail by itemIds:{},cause:{}",
                    itemIds, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        List<RichItemDeliveryFee> richItemDeliveryFees = findResp.getResult();

        if (CollectionUtils.isEmpty(richItemDeliveryFees)) {
            return Collections.emptyMap();
        }
        return Maps.uniqueIndex(richItemDeliveryFees, Function.<RichItemDeliveryFee>identity().andThen(RichItemDeliveryFee::getItemDeliveryFee).andThen(ItemDeliveryFee::getItemId)::apply);
    }

    protected DeliveryFeeBase determineDeliveryFee(Long itemId, RichItemDeliveryFee richItemDeliveryFee, List<Integer> receiverAddressAncestors) {
        if (richItemDeliveryFee.getDeliveryFeeTemplate() == null) {
            log.error("item(id={}) not binding delivery fee template", itemId);
            throw new JsonResponseException("item.not.binding.delivery.fee.template");
        }

        //特殊区域运费
        if (!CollectionUtils.isEmpty(richItemDeliveryFee.getSpecialDeliveryFees())) {
            for (SpecialDeliveryFee specialDeliveryFee : richItemDeliveryFee.getSpecialDeliveryFees()) {
                if (matchSpecialRegion(receiverAddressAncestors, specialDeliveryFee.getAddressIds())) {
                    return specialDeliveryFee;
                }
            }
        }

        return richItemDeliveryFee.getDeliveryFeeTemplate();
    }

    protected boolean matchSpecialRegion(List<Integer> receiverAddressAncestors, List<Integer> specialRegionIds) {
        for (Integer addressId : receiverAddressAncestors) {
            if (specialRegionIds.contains(addressId)) {
                return true;
            }
        }
        return false;
    }

    private ItemDeliveryFeeGroup makeRichItemDeliveryFee(Long templateId, DeliveryFeeBase deliveryFeeBase, RichSku richSku) {
        ItemDeliveryFeeGroup itemDeliveryFeeGroup = new ItemDeliveryFeeGroup();
        itemDeliveryFeeGroup.setDeliveryTemplateId(templateId);
        BeanMapper.copy(deliveryFeeBase, itemDeliveryFeeGroup);

        Item item = richSku.getItem();
        if (item.getExtra() == null || !StringUtils.hasText(item.getExtra().get(ItemExtraIndex.unit.name()))) {
            log.error("item(id={}) unit not found", item.getId());
            throw new JsonResponseException("item.unit.not.found");
        }
        itemDeliveryFeeGroup.setItemUnit(item.getExtra().get(ItemExtraIndex.unit.name()));

        Sku sku = richSku.getSku();
        if (sku.getExtraMap() == null || !StringUtils.hasText(sku.getExtraMap().get(SkuExtraIndex.unitQuantity.name()))) {
            log.error("sku(id={}) unit quantity not found", sku.getId());
            throw new JsonResponseException("sku.unit.quantity.not.found");
        }
        itemDeliveryFeeGroup.setRealQuantity(Double.parseDouble(sku.getExtraMap().get(SkuExtraIndex.unitQuantity.name())) * richSku.getQuantity());

        return itemDeliveryFeeGroup;
    }

    @EqualsAndHashCode(of = {"deliveryTemplateId", "itemUnit"}, callSuper = true)
    private static class ItemDeliveryFeeGroup extends DeliveryFeeBase {
        @Getter
        @Setter
        private Long deliveryTemplateId;

        @Getter
        @Setter
        private String itemUnit;

        @Getter
        @Setter
        private double realQuantity;
    }
}
