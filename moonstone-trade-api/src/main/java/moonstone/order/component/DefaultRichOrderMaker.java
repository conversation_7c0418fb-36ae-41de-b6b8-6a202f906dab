package moonstone.order.component;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimaps;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import io.vertx.core.Vertx;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.*;
import moonstone.item.emu.ItemExtraIndex;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.model.Item;
import moonstone.item.model.ItemDetail;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.DistributionRateProvider;
import moonstone.order.api.RichOrderMaker;
import moonstone.order.dto.*;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.rule.SkuItemLimitChecker;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.settle.enums.CommissionBusinessType;
import moonstone.settle.model.CommissionRule;
import moonstone.settle.service.CommissionRuleReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import moonstone.user.model.StoreIntegral;
import moonstone.user.service.StoreIntegralReadService;
import moonstone.user.service.UserCertificationReadService;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopReadService;
import moonstone.weShop.service.WeShopSkuReadService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 这是B2B2C场景, 收货地址在店铺订单级别
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-26
 *
 * <AUTHOR>
 */
@Slf4j
public class DefaultRichOrderMaker implements RichOrderMaker {

    static private final Integer maxSize = 360;
    @Autowired
    Vertx vertx;
    @Autowired
    private SkuReadService skuReadService;
    @Autowired
    private ItemReadService itemReadService;
    @Autowired
    private ShopReadService shopReadService;
    @Autowired
    private WeShopReadService weShopReadService;
    @Autowired
    private WeShopItemReadService weShopItemReadService;
    @Autowired
    private WeShopSkuReadService weShopSkuReadService;
    @Autowired
    private UserCertificationReadService userCertificationReadService;
    @Autowired
    private ReceiverInfoReadService receiverInfoReadService;
    @Autowired
    private CommissionRuleReadService commissionRuleReadService;
    @Autowired
    private StoreIntegralReadService storeIntegralReadService;
    @Autowired
    private DistributionRateProvider distributionRateProvider;
    @Autowired
    private ItemCacheHolder itemCacheHolder;
    @Autowired
    private SkuCacheHolder skuCacheHolder;
    @Autowired
    private SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;
    @Autowired
    ThirdPartySkuStockReadService thirdPartySkuStockReadService;

    /**
     * 为订单预览页组装订单, 此时一般不包括发货区域, 以及优惠信息等
     *
     * @param submittedSkus 主要包括skuId和购买数量信息
     * @param buyer         买家信息
     * @param channel       下单渠道
     * @return 组装好的商品及sku信息
     */
    @Override
    public RichOrder partial(List<SubmittedSku> submittedSkus, CommonUser buyer, Integer channel) {
        boolean jdStockCheck = false;
        List<RichSku> richSkus = Lists.newArrayListWithCapacity(submittedSkus.size());
        for (SubmittedSku submittedSku : submittedSkus) {
            if (submittedSku.getQuantity() <= 0) {
                continue;
            }
            Sku sku = findSkuById(submittedSku.getSkuId());
            Item item = findItemById(sku.getItemId());
            if (Objects.equals(2, item.getSourceType())) {
                jdStockCheck = true;
            }

            RichSku richSku = new RichSku();
            richSku.setItem(item);
            richSku.setSku(sku);
            if (Objects.nonNull(submittedSku.getWeShopId())) {
                richSku.setWeShopItem(weShopItemReadService.findByWeShopIdAndItemId(submittedSku.getWeShopId(), sku.getItemId()).getResult());
                richSku.setWeShopSku(weShopSkuReadService.findByWeShopIdAndSkuId(submittedSku.getWeShopId(), sku.getId(), -999).take());
            }
            richSku.setQuantity(submittedSku.getQuantity());
            richSku.setPromotionId(submittedSku.getPromotionId());
            richSkus.add(richSku);
        }

        ListMultimap<Long, RichSku> byShopId = Multimaps.index(richSkus, richSku -> java.util.Objects.requireNonNull(richSku).getItem().getShopId());
        List<RichSkusByShop> richSkusByShops = Lists.newArrayListWithCapacity(byShopId.keySet().size());
        for (Long shopId : byShopId.keySet()) {

            Shop shop = findShopById(shopId);
            RichSkusByShop richSkusByShop = new RichSkusByShop();
            richSkusByShop.setShop(shop);
            richSkusByShop.setChannel(channel);
            richSkusByShop.setRichSkus(byShopId.get(shopId));
            Optional.ofNullable(richSkusByShop.getRichSkus().get(0)).map(RichSku::getWeShopItem).map(WeShopItem::getWeShopId)
                    .map(weShopReadService::findById).map(Response::getResult)
                    .ifPresent(richSkusByShop::setWeShop);
            richSkusByShops.add(richSkusByShop);

            Integer commissionRate = findCommissionRateByShopId(shopId);
            richSkusByShop.setCommissionRate(commissionRate);
        }

        RichOrder richOrder = new RichOrder();
        richOrder.setBuyer(buyer);
        richOrder.setRichSkusByShops(richSkusByShops);
        richOrder.setCheckStock(jdStockCheck);

        SkuItemLimitChecker skuItemLimitChecker = SkuItemLimitChecker.build();
        splitRichOrder(richOrder);
        for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
            richSkusByShop.getRichSkus().forEach(richSku -> {
                skuItemLimitChecker.checkBuyLimit(richSku);
                itemReadService.processItemForActivity(richSku.getItem(), new ItemDetail());
            });
        }
        return richOrder;
    }

    /**
     * 为【微分销】订单预览页组装订单, 此时一般不包括发货区域, 以及优惠信息等
     *
     * @param submittedSkus 主要包括skuId和购买数量信息
     * @param buyer         买家信息
     * @param channel       下单渠道
     * @return 组装好的商品及sku信息
     */
    @Override
    public RichOrder distributionPartial(List<DistributionPreviewSubmittedSku> submittedSkus, CommonUser buyer, Integer channel) {
        List<RichSku> richSkus = Lists.newArrayListWithCapacity(submittedSkus.size());
        for (DistributionPreviewSubmittedSku submittedSku : submittedSkus) {
            if (submittedSku.getQuantity() <= 0) {
                continue;
            }
            Sku sku = findSkuById(submittedSku.getSkuId());

            Item item = findItemById(sku.getItemId());

            WeShopItem weShopItem = findWeShopItemByWeShopIdAndItemId(submittedSku.getWeShopId(), item.getId());
            if (weShopItem == null) {
                log.error("weShop(id={}) not distribute item(id={})", submittedSku.getWeShopId(), item.getId());
                throw new JsonResponseException("weShop.not.distribute.item");
            }

            RichSku richSku = new RichSku();
            richSku.setItem(item);
            richSku.setSku(sku);
            richSku.setQuantity(submittedSku.getQuantity());
            richSku.setPromotionId(submittedSku.getPromotionId());
            richSku.setWeShopItem(weShopItem);
            richSku.setWeShopSku(weShopSkuReadService.findByWeShopIdAndSkuId(weShopItem.getWeShopId(), sku.getId(), -9999).take());
            richSkus.add(richSku);
        }

        ListMultimap<String, RichSku> byShopIdAndWeShopId = Multimaps.index(richSkus, richSku -> java.util.Objects.requireNonNull(richSku).getItem().getShopId() + "_" + richSku.getWeShopItem().getWeShopId());
        List<RichSkusByShop> richSkusByShops = Lists.newArrayListWithCapacity(byShopIdAndWeShopId.keySet().size());
        for (String shopIdAndWeShopId : byShopIdAndWeShopId.keySet()) {
            List<String> list = Splitter.on("_").splitToList(shopIdAndWeShopId);
            Long shopId = Long.parseLong(list.get(0));
            Long weShopId = Long.parseLong(list.get(1));

            Shop shop = findShopById(shopId);
            WeShop weShop = findWeShopById(weShopId);
            RichSkusByShop richSkusByShop = new RichSkusByShop();
            richSkusByShop.setShop(shop);
            richSkusByShop.setChannel(channel);
            richSkusByShop.setRichSkus(byShopIdAndWeShopId.get(shopIdAndWeShopId));
            richSkusByShop.setWeShop(weShop);
            richSkusByShops.add(richSkusByShop);

            Integer commissionRate = findCommissionRateByShopId(shopId);
            richSkusByShop.setCommissionRate(commissionRate);
        }

        RichOrder richOrder = new RichOrder();
        richOrder.setBuyer(buyer);
        richOrder.setRichSkusByShops(richSkusByShops);
        return splitRichOrder(richOrder);
    }

    private Map<String, String> shapeMap(Map<String, String> map) {
        // remove the formId
        Map<String, String> newMap = map == null ? new TreeMap<>() : map;
        newMap.remove("formId");
        return newMap;
    }

    private long queryPackageSize(Sku sku, Item item) {
        // if item has set up the order limit, just split it
        if (!BondedType.fromInt(item.getIsBonded()).isBonded()) {
            return -1;
        }

        String key = ItemExtraIndex.skuOrderSplitLine.name();
        if (!ObjectUtils.isEmpty(item.getExtra()) && item.getExtra().containsKey(key) && StringUtils.hasText(item.getExtra().get(key))) {
            try {
                var skuOrderSplitLine = new BigDecimal(item.getExtra().get(key));
                if (skuOrderSplitLine.compareTo(new BigDecimal("0.00")) <= 0) {
                    return 1;
                }

                BigDecimal volume = BigDecimal.valueOf(360).divide(skuOrderSplitLine, RoundingMode.DOWN);
                return volume.longValue();
            } catch (Exception e) {
                log.error("FAIL TO COVERT THE SIZE OF ITEM[{}]", item, e);
            }
        }
        return 1;
    }

    /// ## makeRichSku 由制定信息构造购买订单
    /// - channel 购买渠道
    /// - submittedSku 单品订单
    /// - sku 单品实体
    /// - shop 商店实体,便是这次提供货的商店
    /// - quantity 这次生成的订单中商品的数量
    /// - weShopId 微分销商铺id
    private RichSku makeRichSku(Integer channel, ThirdPartySkuStock thirdPartySkuStock, SubmittedSku submittedSku, Sku sku, Shop shop, int quantity, @Nullable Long weShopId) {
        RichSku richSku = new RichSku();
        if (thirdPartySkuStock != null) {
            BeanUtils.copyProperties(thirdPartySkuStock, richSku);
        }

        richSku.setPromotionId(submittedSku.getPromotionId());
        richSku.setShipmentType(submittedSku.getShipmentType());
        //richSku.setQuantity(submittedSku.getQuantity());
        richSku.setQuantity(quantity);
        richSku.setInvoiceId(submittedSku.getInvoiceId());
        richSku.setChannel(channel);
        richSku.setOrderStatus(OrderStatus.NOT_PAID.getValue());
        if (submittedSku.getReceiverInfoId() != null) {
            submittedSku.setReceiverInfoId(submittedSku.getReceiverInfoId());
        }

        richSku.setSku(sku);
        richSku.setDiffFee(0);

        Item item = findItemById(sku.getItemId());
        richSku.setItem(item);

        if (Objects.nonNull(weShopId)) {
            WeShopItem weShopItem = findWeShopItemByWeShopIdAndItemId(weShopId, item.getId());
            if (Objects.isNull(weShopItem)) {
                log.error("can not find weShop item by weShopId={}, itemId={}", weShopId, item.getId());
                throw new JsonResponseException("weShopItem.can.not.find");
            }
            richSku.setWeShopItem(weShopItem);
            richSku.setWeShopSku(weShopSkuReadService.findByWeShopIdAndSkuId(weShopItem.getWeShopId(), richSku.getSku().getId(), -999).take());
        }

        Integer distributionRateForSkuOrder = distributionRateProvider.retrieveRateForSkuOrder(shop, richSku);
        richSku.setDistributionRate(distributionRateForSkuOrder);
        richSku.setTags(sku.getTags());
        richSku.setSourceType(submittedSku.getSourceType());
        richSku.setDiscountDetail(submittedSku.getDiscountDetail());
        //  复制
        richSku.setExtra(Optional.ofNullable(richSku.getExtra()).orElseGet(HashMap::new));
        richSku.getExtra().putAll(shapeMap(sku.getExtraMap()));
        richSku.getExtra().putAll(shapeMap(submittedSku.getExtra()));
        return richSku;
    }

    /**
     * 提交创建订单请求, 此时应包括创建订单所需要的全部信息
     * todo:修改其中magical shopId 33为shopExtra读取值
     *
     * @param submittedOrder 包括skuId和购买数量信息, 以及收货地址, 发票, 各级别优惠等信息
     * @param buyer          买家信息
     * @return 组装好的订单信息
     */
    @Override
    public RichOrder full(SubmittedOrder submittedOrder, CommonUser buyer) {

        RichOrder richOrder = new RichOrder();
        Long globalReceiverInfoId = submittedOrder.getReceiverInfoId();
        if (globalReceiverInfoId != null) {
            final ReceiverInfo result = findReceiverInfoById(globalReceiverInfoId);
            richOrder.setReceiverInfo(result);
        }

        richOrder.setBuyer(buyer);
        richOrder.setChannel(submittedOrder.getChannel());
        richOrder.setPayType(submittedOrder.getPayType());
        richOrder.setExtra(shapeMap(submittedOrder.getExtra()));
        richOrder.setPromotionId(submittedOrder.getPromotionId());

        final List<SubmittedSkusByShop> submittedSkusByShops = submittedOrder.getSubmittedSkusByShops();
        if (CollectionUtils.isEmpty(submittedSkusByShops)) {
            log.error("no shop orders specified");
            throw new ServiceException("shop.orders.empty");
        }

        List<RichSkusByShop> richSkusByShops = new ArrayList<>(submittedSkusByShops.size());
        for (SubmittedSkusByShop submittedSkusByShop : submittedSkusByShops) {
            Map<Long, List<SubmittedSku>> submittedSkuListByShopId = new HashMap<>();
            for (SubmittedSku submittedSku : submittedSkusByShop.getSubmittedSkus()) {
                Sku sku = skuReadService.findSkuById(submittedSku.getSkuId()).getResult();
                if (!submittedSkuListByShopId.containsKey(sku.getShopId())) {
                    submittedSkuListByShopId.put(sku.getShopId(), new ArrayList<>());
                }
                submittedSkuListByShopId.get(sku.getShopId()).add(submittedSku);
            }
            for (Long shopId : submittedSkuListByShopId.keySet()) {

                List<SubmittedSku> submittedSkus = submittedSkuListByShopId.get(shopId);
                Shop shop = findShopById(shopId);
                Optional<WeShop> weShopOpt = Optional.ofNullable(submittedSkusByShop.getWeShopId())
                        .map(weShopReadService::findById)
                        .map(Response::getResult);

                RichSkusByShop richSkusByShop = new RichSkusByShop();

                weShopOpt.ifPresent(richSkusByShop::setWeShop);
                weShopOpt.map(WeShop::getUserId).ifPresent(richSkusByShop::setRefererId);
                weShopOpt.map(WeShop::getName).ifPresent(richSkusByShop::setRefererName);

                richSkusByShop.setShop(shop);
                richSkusByShop.setBuyerNote(submittedSkusByShop.getBuyerNote());
                richSkusByShop.setInvoiceId(submittedSkusByShop.getInvoiceId());
                richSkusByShop.setShipmentType(submittedSkusByShop.getShipmentType());
                richSkusByShop.setPromotionId(submittedSkusByShop.getPromotionId());
                richSkusByShop.setShipmentPromotionId(submittedSkusByShop.getShipmentPromotionId());
                richSkusByShop.setChannel(submittedOrder.getChannel());
                richSkusByShop.setOrderStatus(OrderStatus.NOT_PAID.getValue());
                richSkusByShop.setOrderType(1);
                richSkusByShop.setExtra(shapeMap(submittedSkusByShop.getExtra()));
                richSkusByShop.setShopOrderFeeDetailDTO(submittedSkusByShop.getShopOrderFeeDetailDTO());
                Optional.ofNullable(submittedOrder.getIdentityId())
                        .map(userCertificationReadService::findById)
                        .map(Response::getResult)
                        .ifPresent(userCertification -> {
                            submittedOrder.setPayerName(userCertification.getPaperName());
                            submittedOrder.setPayerNo(userCertification.getPaperNo());
                        });
                // 拷贝支付人信息
                if (ObjectUtils.isEmpty(richOrder.getPayerName())) {
                    Optional.ofNullable(submittedOrder.getPayerName())
                            .filter(StringUtils::hasText)
                            .ifPresent(richOrder::setPayerName);
                    Optional.ofNullable(submittedOrder.getExtra())
                            .map(map -> map.get(ShopOrderExtra.payerName.name()))
                            .filter(StringUtils::hasText)
                            .ifPresent(richOrder::setPayerName);
                }
                if (ObjectUtils.isEmpty(richOrder.getPayerId())) {
                    Optional.ofNullable(submittedOrder.getPayerNo()).filter(StringUtils::hasText)
                            .ifPresent(richOrder::setPayerId);
                    Optional.ofNullable(submittedOrder.getExtra())
                            .map(map -> map.get(ShopOrderExtra.payerNo.name()))
                            .filter(StringUtils::hasText)
                            .ifPresent(richOrder::setPayerId);
                }
                /// 设置订单外部来源
                setOrderOutFromAndSetOrderType(richSkusByShop, submittedSkusByShop, submittedOrder, buyer);
                final Long receiverInfoId = submittedSkusByShop.getReceiverInfoId();
                if (receiverInfoId != null) {
                    richSkusByShop.setReceiverInfo(findReceiverInfoById(receiverInfoId));
                }

                Integer commissionRate = findCommissionRateByShopId(shopId);
                richSkusByShop.setCommissionRate(commissionRate);

                richSkusByShops.addAll(splitSkuSubmittedOrderAndConstructIt(submittedSkus, shop, richSkusByShop, submittedOrder.getChannel()));
                log.info("before check buy limit richSkusByShops={}", JSON.toJSONString(richSkusByShops));

                for (RichSkusByShop shouldCheckRichSkusByShop : richSkusByShops) {
                    try {
                        SkuItemLimitChecker skuItemLimitChecker = SkuItemLimitChecker.build();
                        shouldCheckRichSkusByShop.getRichSkus().forEach(skuItemLimitChecker::checkBuyLimit);
                    } catch (Exception e) {
                        log.error("FAIL TO BUILD ORDER {}", Json.toJson(richOrder), e);
                        throw e;
                    }
                    if (submittedOrder.getReferId() != null) {
                        shouldCheckRichSkusByShop.setRefererId(submittedOrder.getReferId());
                    } else if (isSubStoreGuider(buyer.getId(), shouldCheckRichSkusByShop)) {
                        shouldCheckRichSkusByShop.setRefererId(buyer.getId());
                    }
                    if (!ObjectUtils.isEmpty(shouldCheckRichSkusByShop.getExtra())) {
                        shouldCheckRichSkusByShop.getExtra().remove("outFrom");
                        if (shouldCheckRichSkusByShop.getExtra().isEmpty()) {
                            shouldCheckRichSkusByShop.setExtra(null);
                        }
                    }
                }
            }
        }
        richOrder.setRichSkusByShops(richSkusByShops);
        return richOrder;
    }

    /**
     * 判断 userId 是不是这个订单所属门店的导购
     *
     * @param userId
     * @param richSkusByShop
     * @return
     */
    private boolean isSubStoreGuider(Long userId, RichSkusByShop richSkusByShop) {
        if (OrderOutFrom.SUB_STORE.Code().equals(richSkusByShop.getOutFrom()) &&
                !ObjectUtils.isEmpty(richSkusByShop.getOutShopId())) {
            try {
                var list = subStoreTStoreGuiderReadService.findBySubStoreIdInCache(
                        Long.parseLong(richSkusByShop.getOutShopId())).getResult();
                if (CollectionUtils.isEmpty(list)) {
                    return false;
                }

                return list.stream()
                        .filter(guider -> guider != null && guider.getStoreGuiderId() != null)
                        .anyMatch(guider -> guider.getStoreGuiderId().equals(userId));
            } catch (Exception ex) {
                log.error("isSubStoreGuider error, userId={}, outShopId={} ", userId, richSkusByShop.getOutShopId(), ex);
                return false;
            }
        }

        return false;
    }

    /**
     * 设置订单的外部来源和订单类型
     *
     * @param richSkusByShop      订单数据
     * @param submittedSkusByShop 提交的订单
     * @param submittedOrder      提交的主订单
     * @param buyer               买家
     */
    private void setOrderOutFromAndSetOrderType(RichSkusByShop richSkusByShop, SubmittedSkusByShop submittedSkusByShop, SubmittedOrder submittedOrder, BaseUser buyer) {
        richSkusByShop.setOutFrom(submittedOrder.getOutFrom());
        if (Objects.isNull(submittedSkusByShop.getExtra())) {
            return;
        }
        Shop shop = richSkusByShop.getShop();
        Long shopId = shop.getId();
        Optional.ofNullable(submittedSkusByShop.getWeShopId()).map(Objects::toString).ifPresent(richSkusByShop::setOutShopId);
        Optional.ofNullable(submittedSkusByShop.getExtra().get("outFrom")).ifPresent(richSkusByShop::setOutFrom);
        Optional.ofNullable(submittedSkusByShop.getExtra().get("outShopId")).ifPresent(richSkusByShop::setOutShopId);
        // to-do add 2019-07-18 积分订单设置为type=3
        if (!ObjectUtils.isEmpty(submittedSkusByShop.getExtra().get("OrderOutType"))
                && "integral".equals(submittedSkusByShop.getExtra().get("OrderOutType"))) {
            log.debug("{} 订单类型[{}]", LogUtil.getClassMethodName(), submittedSkusByShop.getExtra().get("OrderOutType"));
            richSkusByShop.setOrderType(3);
            // 导购门店模式首次购买新客礼后，不能再次购买
            if (OrderOutFrom.SUB_STORE.Code().equals(richSkusByShop.getOutFrom())) {
                limitTheSubStoreMode(submittedSkusByShop.getSubmittedSkus());
            }
        }
        if (ObjectUtils.isEmpty(richSkusByShop.getOutFrom())) {
            richSkusByShop.setOutFrom(null);
        }
        if (ObjectUtils.isEmpty(richSkusByShop.getOutShopId())) {
            richSkusByShop.setOutShopId(null);
        }
        if (!ObjectUtils.isEmpty(submittedSkusByShop.getExtra().get("refererId"))) {
            String refererIdStr = submittedSkusByShop.getExtra().get("refererId");
            NumberUtil.parseNumber(refererIdStr, Long.class).ifSuccess(richSkusByShop::setRefererId)
                    .logErrorStr(error -> log.error("{} failed to insert the refererId:{} because:{}", LogUtil.getClassMethodName("read-refererId"), refererIdStr, error));
        }
        // 积分商城商品不需要上级来源
        Map<String, String> shopExtra = Optional.ofNullable(shop.getExtra()).orElseGet(HashMap::new);
        boolean requireOrderSource = !java.util.Objects.equals(richSkusByShop.getOrderType(), 3) && !"true".equals(shopExtra.getOrDefault(ShopExtra.AllowNoSource.getCode(), "false"));
        if (requireOrderSource) {
//            if (OrderOutFrom.SUB_STORE.Code().equals(richSkusByShop.getOutFrom()) && ObjectUtils.isEmpty(richSkusByShop.getOutShopId())) {
//                throw new JsonResponseException(new Translate("信息丢失:请联系导购，重新分享商品二维码").toString());
//            }
            if (OrderOutFrom.LEVEL_Distribution.Code().equals(richSkusByShop.getOutFrom())) {
                if (null == richSkusByShop.getRefererId()) {
                    throw new JsonResponseException(new Translate("渠道信息丢失,请联系当地门店或者经销商").toString());
                }
                if (richSkusByShop.getRefererId().equals(buyer.getId()) && shopId == 33) {
                    throw new JsonResponseException(new Translate("请勿购买自己的商品").toString());
                }
            }
        }
    }

    /**
     * 限制礼品订单
     *
     * @param submittedSkus 订单列表
     */
    private void limitTheSubStoreMode(List<SubmittedSku> submittedSkus) {
        for (SubmittedSku submittedSku : submittedSkus) {
            Response<Sku> skuRes = skuReadService.findSkuById(submittedSku.getSkuId());
            if (Objects.isNull(skuRes.getResult())) {
                throw new JsonResponseException(new Translate("下单失败, 单品查找失败").toString());
            }
            Response<Item> itemRes = itemReadService.findById(skuRes.getResult().getItemId());
            if (Objects.isNull(itemRes.getResult())) {
                throw new JsonResponseException(new Translate("下单失败, 商品查找失败").toString());
            }
            if (!itemRes.getResult().getType().equals(4)) {
                return;
            }
            Either<Optional<StoreIntegral>> storeIntegralOptRes = storeIntegralReadService.findAvailableGradeByUserIdAndShopId(UserUtil.getCurrentUser().getId(), itemRes.getResult().getShopId());
            if (!storeIntegralOptRes.isSuccess() || storeIntegralOptRes.take().isEmpty()) {
                throw new JsonResponseException(new Translate("新客礼下单失败").toString());
            }
            if (storeIntegralOptRes.take().get().getExtra() != null && storeIntegralOptRes.take().get().getExtra().containsKey("firstBuy")
                    && "true".equals(storeIntegralOptRes.take().get().getExtra().get("firstBuy"))) {
                throw new JsonResponseException(new Translate("新客礼只能购买一次").toString());
            }
        }
    }

    /**
     * 拆单并且重新组装
     *
     * @param submittedSkus  订单中的sku聚集
     * @param shop           购买的店铺
     * @param richSkusByShop 组合订单模型
     * @param channel        购买渠道
     * @return 组合完毕的预组合订单列表
     */
    private List<RichSkusByShop> splitSkuSubmittedOrderAndConstructIt(List<SubmittedSku> submittedSkus, Shop shop, RichSkusByShop richSkusByShop, Integer channel) {
        List<RichSkusByShop> richSkusByShops = new ArrayList<>();
        Map<String, List<SkuSplitOrderList>> validPreInsertListDivideByDepotName = new HashMap<>(3);
        List<SkuSplitOrderList> sealedSkuForOrderConstructList = new ArrayList<>();
        for (SubmittedSku submittedSku : submittedSkus) {
            /// 填充数据以供商品组构使用，并进行粗略分单
            final Long skuId = submittedSku.getSkuId();
            Sku sku = findSkuById(skuId);
            Item item = Optional.ofNullable(itemCacheHolder.findItemById(sku.getItemId()))
                    .orElseThrow(() -> new JsonResponseException(Translate.of("商品找不到")));

            //定时改价改图
            itemReadService.processItemForActivity(item, new ItemDetail());
            itemReadService.processSkuForActivity(Lists.newArrayList(sku));

            Optional<Item> itemOpt = Optional.of(item);

            if (Objects.nonNull(richSkusByShop.getWeShop())) {
                checkWeShopItem(richSkusByShop.getWeShop(), sku);
            }

            Long packageSize = queryPackageSize(sku, item);
            /// 获取分单凭据
            // 初始化仓库分单  如果存在货主 则在该层分单
            var thirdPartySkuStock = readSkuStock(sku);
            String depotCode = String.format("[%s]_[%s]_[%s]", "no_depot_prepare_list"
                    , itemOpt.map(Item::getType).map(Object::toString).orElse("")
                    , itemOpt.map(Item::getIsThirdPartyItem).map(java.util.Objects::toString).orElse(""));
            if (packageSize > 0) {
                depotCode = String.format("[%s]_[%s]", thirdPartySkuStock.getDepotCode(), Optional.ofNullable(item.getType()).map(Objects::toString).orElse(""));
            } else if (Objects.equals(1, item.getIsBonded())) {
                log.warn("{} sku [{}] is bounded but failed to find the depotCode, so use the default [{}]", LogUtil.getClassMethodName(), skuId, depotCode);
            }

            if (!validPreInsertListDivideByDepotName.containsKey(depotCode)) {
                validPreInsertListDivideByDepotName.put(depotCode, new ArrayList<>());
            }
            List<SkuSplitOrderList> prepareList = validPreInsertListDivideByDepotName.get(depotCode);

            SkuForOrderConstruct construct = new SkuForOrderConstruct();
            construct.setItem(item);
            construct.setShop(shop);
            construct.setSku(sku);
            construct.setPackageSize(packageSize);
            construct.setSubmittedSku(submittedSku);
            construct.setQuantity(submittedSku.getQuantity());

            if (prepareList.isEmpty()) {
                prepareList.add(new SkuSplitOrderList(500000L, (long) maxSize, packageSize));
            }
            for (SkuSplitOrderList list : prepareList) {
                if (list.full()) {
                    sealedSkuForOrderConstructList.add(list);
                }
            }
            prepareList.removeAll(sealedSkuForOrderConstructList);

            while (construct.getQuantity() > 0) {
                prepareList.forEach(list -> list.insert(construct));
                if (construct.getQuantity() > 0) {
                    if (!prepareList.isEmpty() && packageSize != 0L && prepareList.get(prepareList.size() - 1).getPackVolumeSumUp() == 0L) {
                        throw new JsonResponseException(new Translate("商品包裹已超限").toString());
                    } else {
                        prepareList.add(new SkuSplitOrderList(500000L, (long) maxSize, packageSize));
                    }
                }
            }
        }

        validPreInsertListDivideByDepotName.forEach((k, v) -> sealedSkuForOrderConstructList.addAll(v));
        for (SkuSplitOrderList skuSplitOrderList : sealedSkuForOrderConstructList) {
            RichSkusByShop subRichSkusByShop = new RichSkusByShop();
            BeanUtils.copyProperties(richSkusByShop, subRichSkusByShop);
            subRichSkusByShop.setRichSkus(new ArrayList<>());
            for (SkuForOrderConstruct construct : skuSplitOrderList.getSkuForOrderConstructList()) {
                ThirdPartySkuStock thirdPartySkuStock = new ThirdPartySkuStock();
                if (construct.getSku().getOuterSkuId() != null) {
                    thirdPartySkuStock = readSkuStock(construct);
                }
                /// 由购买渠道,订单信息,单品信息,数量构造单品订单 移动该部分代码
                RichSku richSku = makeRichSku(channel, thirdPartySkuStock
                        , construct.getSubmittedSku(), construct.getSku(), construct.getShop(), construct.getQuantity()
                        , Optional.ofNullable(construct.getSubmittedSku().getWeShopId()).orElseGet(() -> Optional.ofNullable(richSkusByShop.getWeShop()).map(WeShop::getId).orElse(null)));
                /// 由map和list混合的多级订单结构构造出多个shop订单
                subRichSkusByShop.getRichSkus().add(richSku);
            }
            Integer subDistributeRateForShopOrder = distributionRateProvider.retrieveRateForShopOrder(shop, subRichSkusByShop.getRichSkus());
            subRichSkusByShop.setDistributionRate(subDistributeRateForShopOrder);
            richSkusByShops.add(subRichSkusByShop);
        }
        return richSkusByShops;
    }

    private ThirdPartySkuStock readSkuStock(Sku sku) {
        try {
            var pushSystem = sku.getTags().get(SkuTagIndex.pushSystem.name());
            var system = Integer.parseInt(pushSystem.split(SkuTagIndex.pushSystem.getSplitter())[0]);
            return thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(
                    sku.getShopId(), system, sku.getOuterSkuId()).getResult().get(0);
        } catch (Exception e) {
            log.error("FAIL TO READ THE THIRD-PARTY-SKU-STOCK", e);
            return new ThirdPartySkuStock();
        }
    }

    private ThirdPartySkuStock readSkuStock(SkuForOrderConstruct construct) {
        try {
            var pushSystem = construct.getSku().getTags().get(SkuTagIndex.pushSystem.name());
            var system = Integer.parseInt(pushSystem.split(SkuTagIndex.pushSystem.getSplitter())[0]);
            return thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(
                    construct.getShop().getId(), system, construct.getSku().getOuterSkuId()
            ).getResult().get(0);
        } catch (Exception e) {
            log.error("FAIL TO READ THE THIRD-PARTY-SKU-STOCK", e);
            return new ThirdPartySkuStock();
        }
    }

    /**
     * 检测微店下的商品状态
     *
     * @param weShop 店铺
     * @param sku    单品
     */
    private void checkWeShopItem(WeShop weShop, Sku sku) {
        if (!Objects.equals(weShop.getStatus(), 1)) {
            log.error("{} weShop[{}] user[{}] is not on auth status or frozen [{}]", LogUtil.getClassMethodName(), weShop.getId(), weShop.getUserId(), weShop.getStatus());
            throw new JsonResponseException(Translate.of("店铺状态异常"));
        }
        WeShopItem weShopItem = Optional.ofNullable(weShopItemReadService.findByWeShopIdAndItemId(weShop.getId(), sku.getItemId()).getResult())
                .orElseThrow(() -> new JsonResponseException(Translate.of("店铺商品状态异常")));
        if (!Objects.equals(1, weShopItem.getStatus())) {
            log.error("{} weShop[{}] weShopItem[{}] item[{}] not on sell, status[{}]", LogUtil.getClassMethodName(), weShop.getId(), weShopItem.getId(), weShopItem.getItemId(), weShopItem.getStatus());
            throw new JsonResponseException(Translate.of("商品未上架"));
        }
    }

    /**
     * 仅仅拆单但是不保证价格税费一致
     *
     * @param richOrder 订单信息
     * @return 订单
     */
    private RichOrder splitRichOrder(RichOrder richOrder) {
        java.util.function.Function<RichSku, SubmittedSku> fallBack = (richSku) -> {
            SubmittedSku submittedSku = new SubmittedSku();
            BeanUtils.copyProperties(richSku, submittedSku);
            submittedSku.setSkuId(richSku.getSku().getId());
            return submittedSku;
        };
        List<RichSkusByShop> richSkusByShops = new ArrayList<>();
        for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
            List<SubmittedSku> submittedSkus = richSkusByShop.getRichSkus().stream().map(fallBack).collect(Collectors.toList());
            richSkusByShops.addAll(splitSkuSubmittedOrderAndConstructIt(submittedSkus, richSkusByShop.getShop(), richSkusByShop, richOrder.getChannel()));
        }
        richOrder.setRichSkusByShops(richSkusByShops);
        return richOrder;
    }

    private Sku findSkuById(Long skuId) {
        Response<Sku> rSku = skuReadService.findSkuById(skuId);
        if (!rSku.isSuccess()) {
            log.error("failed to find sku(id={}), error code:{}", skuId, rSku.getError());
            throw new ServiceException(rSku.getError());
        }
        return rSku.getResult();
    }

    private Shop findShopById(Long shopId) {
        Response<Shop> rShop = shopReadService.findById(shopId);
        if (!rShop.isSuccess()) {
            log.error("failed to find shop(id={}), error code:{}", shopId, rShop.getError());
            throw new ServiceException(rShop.getError());
        }
        return rShop.getResult();
    }

    private WeShop findWeShopById(Long weShopId) {
        Response<WeShop> rWeShop = weShopReadService.findById(weShopId);
        if (!rWeShop.isSuccess()) {
            log.error("failed to find weShop(id={}), error code:{}", weShopId, rWeShop.getError());
            throw new ServiceException(rWeShop.getError());
        }
        return rWeShop.getResult();
    }

    private Item findItemById(Long itemId) {
        Response<Item> rItem = itemReadService.findById(itemId);
        if (!rItem.isSuccess()) {
            log.error("failed to find item(id={}), error code:{}", itemId, rItem.getError());
            throw new ServiceException(rItem.getError());
        }
        return rItem.getResult();
    }

    private WeShopItem findWeShopItemByWeShopIdAndItemId(Long weShopId, Long itemId) {
        Response<WeShopItem> rWeShopItem = weShopItemReadService.findByWeShopIdAndItemId(weShopId, itemId);
        if (!rWeShopItem.isSuccess()) {
            log.error("failed to find weShop item by weShopId={}, itemId={}, error code:{}", weShopId, itemId, rWeShopItem.getError());
            throw new ServiceException(rWeShopItem.getError());
        }
        return rWeShopItem.getResult();
    }

    private ReceiverInfo findReceiverInfoById(Long receiverInfoId) {
        Response<ReceiverInfo> rReceiverInfo = receiverInfoReadService.findById(receiverInfoId);
        if (!rReceiverInfo.isSuccess()) {
            log.error("receiverInfo(id={}) find fail, error code:{}", receiverInfoId, rReceiverInfo.getError());
            throw new ServiceException(rReceiverInfo.getError());
        }
        return rReceiverInfo.getResult();
    }

    private Integer findCommissionRateByShopId(Long shopId) {
        if (commissionRuleReadService == null) {
            return 0;
        }
        Response<CommissionRule> rComssionRule = commissionRuleReadService.findMatchCommissionRule(shopId, CommissionBusinessType.SHOP.value());
        if (!rComssionRule.isSuccess()) {
            log.error("findMatchCommissionRule for shop fail, shopId={}, cause={}", shopId, rComssionRule.getError());
            throw new ServiceException(rComssionRule.getError());
        }
        CommissionRule commissionRule = rComssionRule.getResult();
        if (commissionRule == null) {
            return 0;
        } else {
            return commissionRule.getRate();
        }
    }


}
