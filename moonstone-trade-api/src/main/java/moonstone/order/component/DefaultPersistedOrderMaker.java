/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.component;

import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.category.model.ShopCategory;
import moonstone.category.model.ShopCategoryItem;
import moonstone.category.service.ShopCategoryItemReadService;
import moonstone.category.service.ShopCategoryReadService;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.OrderSourceEnum;
import moonstone.common.model.WithExtraMap;
import moonstone.item.model.Item;
import moonstone.item.model.ItemDetail;
import moonstone.item.model.Sku;
import moonstone.item.model.WeShopSkuExtra;
import moonstone.item.service.ItemReadService;
import moonstone.order.api.AbstractPersistedOrderMaker;
import moonstone.order.api.OrderPushStatusPicker;
import moonstone.order.dto.PersistedOrderInfosByOrderStruct;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;
import moonstone.order.enu.OrderFlagEnum;
import moonstone.order.enu.SkuOrderExtra;
import moonstone.order.model.*;
import moonstone.shop.model.Shop;
import moonstone.user.application.UserWxCacheHolder;
import moonstone.user.cache.UserProfileCacheHolder;
import moonstone.user.model.UserProfile;
import moonstone.user.model.UserWx;
import moonstone.weShop.model.WeShopSku;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 使用拆单逻辑的订单组装
 *
 * <AUTHOR>
 */
@Slf4j
public class DefaultPersistedOrderMaker extends AbstractPersistedOrderMaker {

    private final OrderLevel orderReceiverInfoLevel;

    private final OrderLevel orderInvoiceLevel;

    @Autowired
    UserProfileCacheHolder userProfileCacheHolder;

    @Autowired
    UserWxCacheHolder userWxCacheHolder;

    @Autowired
    OrderPushStatusPicker orderPushStatusPicker;

    @Autowired
    ItemReadService itemReadService;

    @Autowired
    private ShopCategoryReadService categoryReadService;

    @Autowired
    private ShopCategoryItemReadService shopCategoryItemReadService;
    /**
     * @param orderReceiverInfoLevel 收货地址对应的订单级别
     * @param orderInvoiceLevel      发票信息对应的订单级别
     */
    public DefaultPersistedOrderMaker(OrderLevel orderReceiverInfoLevel, OrderLevel orderInvoiceLevel) {
        this.orderReceiverInfoLevel = orderReceiverInfoLevel;
        this.orderInvoiceLevel = orderInvoiceLevel;
    }

    /**
     * 从richOrder构建出需要持久化的收货信息
     *
     * @param richOrder 输入的richOrder信息
     * @return 收货信息列表, 以店铺id进行归组, 如果为空或者empty map, 则表示没有收货信息
     * 如果有某个(子)订单没有收货地址, 则可能该订单为虚拟订单, 那么也会生成对应的OrderReceiverInfo, 只是对应的receiverInfo字段为空
     */
    @Override
    protected ListMultimap<Long, OrderReceiverInfo> retrieveReceiverInfos(RichOrder richOrder) {
        ListMultimap<Long, OrderReceiverInfo> result = ArrayListMultimap.create();
        switch (orderReceiverInfoLevel) {
            case SHOP:
                for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                    Long shopId = richSkusByShop.getShop().getId();
                    ReceiverInfo receiverInfo = richSkusByShop.getReceiverInfo();
                    //如果店铺订单级别没有配置收货信息, 那么肯定是全局的收货信息
                    if (receiverInfo == null) {
                        receiverInfo = richOrder.getReceiverInfo();
                    }
                    OrderReceiverInfo ori = new OrderReceiverInfo();
                    ori.setOrderType(OrderLevel.SHOP.getValue());
                    ori.setReceiverInfo(receiverInfo);
                    result.put(shopId, ori);
                }
                break;
            case SKU:
                for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                    Long shopId = richSkusByShop.getShop().getId();
                    for (RichSku richSku : richSkusByShop.getRichSkus()) {
                        OrderReceiverInfo ori = new OrderReceiverInfo();
                        ori.setOrderType(OrderLevel.SKU.getValue());
                        ori.setReceiverInfo(richSku.getReceiverInfo());
                        result.put(shopId, ori);
                    }
                }
                break;
            default:
                throw new IllegalStateException("unknown orderReceiverInfoLevel: " + orderReceiverInfoLevel);
        }
        return result;
    }

    /**
     * 从richOrder构建出需要持久化的发票信息
     *
     * @param richOrder 输入的richOrder信息
     * @return 发票列表, 以店铺id进行归组, 如果为空或者empty map, 则表示没有发票信息
     * <p>
     * 如果有某个(子)订单没有收货地址, 则可能该订单可能暂时不需要发票, 那么也会生成对应的OrderInvoice, 只是对应的invoice字段为空
     */
    @Override
    protected ListMultimap<Long, OrderInvoice> retrieveOrderInvoices(RichOrder richOrder) {
        ListMultimap<Long, OrderInvoice> result = ArrayListMultimap.create();
        switch (orderInvoiceLevel) {
            case SHOP:
                for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                    Long shopId = richSkusByShop.getShop().getId();

                    OrderInvoice oi = new OrderInvoice();
                    oi.setOrderType(OrderLevel.SHOP.getValue());
                    oi.setInvoiceId(richSkusByShop.getInvoiceId());
                    oi.setStatus(1);
                    result.put(shopId, oi);
                }
                break;
            case SKU:
                for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                    Long shopId = richSkusByShop.getShop().getId();
                    for (RichSku richSku : richSkusByShop.getRichSkus()) {
                        OrderInvoice oi = new OrderInvoice();
                        oi.setOrderType(OrderLevel.SKU.getValue());
                        oi.setInvoiceId(richSku.getInvoiceId());
                        oi.setStatus(1);
                        result.put(shopId, oi);
                    }
                }
                break;
            default:
                throw new IllegalStateException("unknown orderInvoiceLevel: " + orderInvoiceLevel);
        }
        return result;
    }


    /**
     * @param persistedOrderInfosByOrderStruct 被打包的持久化结构
     * @param richOrder                        订单数据来源
     */
    @Override
    protected void packPersistedOrderInfosByOrderStruct(PersistedOrderInfosByOrderStruct persistedOrderInfosByOrderStruct, RichOrder richOrder) {
        /// 开始循环打包提取
        persistedOrderInfosByOrderStruct.setShopOrderAndSkuOrders(new ArrayList<>());
        for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
            PersistedOrderInfosByOrderStruct.ShopOrderAndSkuOrder shopOrderAndSkuOrder = persistedOrderInfosByOrderStruct.newStruct();
            /// a. 提取shopOrder
            ShopOrder packedShopOrder = new ShopOrder();
            decorateShopOrder(packedShopOrder, richSkusByShop, richOrder);
            shopOrderAndSkuOrder.setShopOrder(packedShopOrder);
            /// b. 提取SkuOrder列表
            List<SkuOrder> packedSkuOrderList = new ArrayList<>();
            fullFillSkuOrderList(packedSkuOrderList, richSkusByShop, richOrder);
            shopOrderAndSkuOrder.setSkuOrders(packedSkuOrderList);
            /// c. 提取收货人信息
            shopOrderAndSkuOrder.setReceiverInfos(packReceiverInfo(richSkusByShop, richOrder));
            /// d. 提取发票信息
            shopOrderAndSkuOrder.setOrderInvoices(packOrderInvoice(richSkusByShop));
            /// e. 加入到列表中
            persistedOrderInfosByOrderStruct.getShopOrderAndSkuOrders().add(shopOrderAndSkuOrder);
        }
        /// > 至此完成结构性打包
    }

    /**
     * ## fullFillSkuOrderList 打包skuOrders
     *
     * @param richSkusByShop 用来提供skuOrder来源信息的聚合订单信息
     * @param richOrder      用来提供订单支付信息的总订单
     * @apiNote @skuOrders 用来储存打包好的SkuOrder列表
     */
    private void fullFillSkuOrderList(List<SkuOrder> skuOrders, RichSkusByShop richSkusByShop, RichOrder richOrder) {
        Shop shop = richSkusByShop.getShop();
        long shopId = shop.getId();
        for (RichSku richSku : richSkusByShop.getRichSkus()) {
            final Sku sku = richSku.getSku();
            final Item item = richSku.getItem();
            SkuOrder skuOrder = new SkuOrder();
            BeanUtils.copyProperties(richSku, skuOrder);
            BeanUtils.copyProperties(sku, skuOrder);
            skuOrder.setSkuVersion(sku.getVersion());
            /// 1. 设置订单基础信息
            skuOrder.setShopId(shopId);

            skuOrder.setSkuId(sku.getId());
            skuOrder.setStatus(richSku.getOrderStatus());
            skuOrder.setIsBonded(sku.getType());
            skuOrder.setQuantity(richSku.getQuantity());
            /// 2. 设置订单购买人 购买物品 是否第三方商品 购买店铺
            skuOrder.setBuyerId(richOrder.getBuyer().getId());
            skuOrder.setBuyerName(richOrder.getBuyer().getName());
            Optional.ofNullable(userWxCacheHolder.findByUserIdAndShopId(richOrder.getBuyer().getId(), skuOrder.getShopId()))
                    .map(UserWx::getNickName)
                    .ifPresent(skuOrder::setBuyerName);
            if (skuOrder.getBuyerName() == null) {
                Optional.ofNullable(userProfileCacheHolder.getUserProfileByUserId(richOrder.getBuyer().getId()))
                        .map(UserProfile::getRealName)
                        .ifPresent(skuOrder::setBuyerName);
            }
            skuOrder.setItemId(item.getId());
            skuOrder.setItemName(item.getName());
            skuOrder.setIsThirdPartyItem(item.getIsThirdPartyItem());
            skuOrder.setShopName(MoreObjects.firstNonNull(shop.getName(), item.getShopName()));
            /// 3. 设置第三方Sku ID(如果是第三方商品)
            skuOrder.setOuterSkuId(sku.getOuterSkuId());
            if (richSkusByShop.getWeShop() != null) {
                /// 优先判断是否为微分销订单
                skuOrder.setOutShopId(richSkusByShop.getWeShop().getId().toString());
                skuOrder.setOutFrom(OrderOutFrom.WE_SHOP.Code());
            } else {
                /// 同shopOrder判断设置订单来源
                skuOrder.setOutShopId(richSkusByShop.getOutShopId());
                skuOrder.setOutFrom(richSkusByShop.getOutFrom());
            }
            /// 4. 设置价格
            skuOrder.setTax(richSku.getTax());
            skuOrder.setFee(richSku.getFee());
            skuOrder.setOriginFee(richSku.getOriginFee());
            skuOrder.setAfterDiscountFee(richSku.getAfterDiscountFee());
            /// 5. 设置优惠 减免
            skuOrder.setDiscount(richSku.getDiscount());
            skuOrder.setBalance(richSku.getBalance());
            skuOrder.setIntegral(richSku.getBalance());
            skuOrder.setPromotionId(richSku.getPromotionId());
            skuOrder.setDiffFee(richSku.getDiffFee());
            /// 6. 设置邮费
            skuOrder.setShipFee(richSku.getShipFee());
            skuOrder.setShipFeeDiscount(richSku.getShipFeeDiscount());
            skuOrder.setShipmentType(richSku.getShipmentType());
            /// 7. 设置快照
            skuOrder.setItemSnapshotId(richSku.getItemSnapshotId());
            /// 8. 设置购买渠道
            skuOrder.setChannel(richSku.getChannel());
            skuOrder.setInvoiced(false);
            skuOrder.setHasRefund(false);
            skuOrder.setPayType(richOrder.getPayType());
            /// 9. 设置平台税
            skuOrder.setCommissionRate(richSku.getCommissionRate());
            skuOrder.setDistributionRate(richSku.getDistributionRate());

            /// 10. 设置额外信息
            itemReadService.processItemForActivity(item, new ItemDetail());
            final String image = item.getMainImage_();
            skuOrder.setSkuImage(image);

            skuOrder.setSkuAttrs(sku.getAttrs());
            skuOrder.setExtra(richSku.getExtra());
            skuOrder.setTags(richSku.getTags());
            Map<String, String> extra = Optional.ofNullable(skuOrder.getExtra()).orElseGet(HashMap::new);
            /// 11. 设置分销价格
            //modify by liuchao 20190401
            if (Objects.nonNull(sku.getExtraPrice()) && sku.getExtraPrice().get(DistributionConstants.SUPPLY_PRICE) != null) {
                extra.put(DistributionConstants.SUPPLY_PRICE, sku.getExtraPrice().get(DistributionConstants.SUPPLY_PRICE).toString());
            }
            Optional.ofNullable(richSku.getPid()).map(Objects::toString).ifPresent(pid -> extra.put("pid", pid));
            Optional.ofNullable(richSku.getWeShopSku())
                    .map(WeShopSku::getTaxSellerBear)
                    .ifPresent(bear -> extra.put(SkuOrderExtra.weShopTaxBear.name(), bear.toString()));
            skuOrder.setExtra(extra);

            /// 12. 设置默认推送状态
            skuOrder.setPushStatus(orderPushStatusPicker.pickPushStatus(skuOrder).value());
            skuOrder.setId(null);
            Optional.ofNullable(richSku.getWeShopSku())
                    .map(WithExtraMap::getExtra)
                    .map(outSkuIdContainer -> outSkuIdContainer.get(WeShopSkuExtra.OutSkuId.name()))
                    .ifPresent(skuOrder::setOuterSkuId);

            // 各种标记
            skuOrder.setFlag(0);
            if (richSku.isSubStoreOnlyLimitedItem()) {
                skuOrder.setFlag(skuOrder.getFlag() | OrderFlagEnum.PROMOTION_LIMITED_ITEM.getValue());
            }
            Integer sourceType = richSku.getSourceType();
            skuOrder.setShippingWarehouseType(Objects.equals(sourceType, 2) ? 2 : 1);

            try {
                String categoryName = getCategoryName(item, shopId);
                skuOrder.setCategoryNameSnapshot(categoryName);
            } catch (Exception e) {
                log.error("getCategoryName error", e);
            }
            // 费用明细
            if (richSku.getDiscountDetail() != null) {
                skuOrder.setDiscountDetailJson(JSON.toJSONString(richSku.getDiscountDetail()));
            }

            /// > 修正推送Id
            /// final. 将打包好的SkuOrder加入列表
            skuOrders.add(skuOrder);
        }
    }

    private String getCategoryName(Item item, long shopId) {
        // 后台类目ID
        Long itemId = item.getId();

        // 一个商品可以有多个分类 join
        List<ShopCategoryItem> itemList = shopCategoryItemReadService.findByShopIdAndItemId(shopId, itemId).getResult();
        log.info("itemList {}", JSON.toJSONString(itemList));
        List<ShopCategory> rootShopCategoryList = new ArrayList<>();
        // 迭代每一个所属分类
        for (ShopCategoryItem shopCategoryItem : itemList) {
            // 查询该分类的root节点
            Response<ShopCategory> shopCategoryResponse = categoryReadService.findRootShopCategoryByShopId(shopCategoryItem.getShopCategoryId());
            log.info("shopCategoryResponse {}", JSON.toJSONString(shopCategoryResponse));
            if (shopCategoryResponse.isSuccess()) {
                rootShopCategoryList.add(shopCategoryResponse.getResult());
            }
        }

        if (CollectionUtils.isEmpty(rootShopCategoryList)) {
            return "#[空白店铺类别]";
        }

        List<String> categoryNameList = new HashSet<>(rootShopCategoryList).stream().map(ShopCategory::getName).collect(Collectors.toList());
        return String.join(",", categoryNameList);
    }


    /**
     * ## decorateShopOrder 生产ShopOrder订单
     *
     * @param richSkusByShop 打包用的店铺级订单信息
     * @param richOrder      总订单信息,记录支付和购买人
     * @apiNote decorate shopOrder 导出一个打包好的ShopOrder
     */
    private void decorateShopOrder(ShopOrder shopOrder, RichSkusByShop richSkusByShop, RichOrder richOrder) {
        Shop shop = richSkusByShop.getShop();
        /// 1. 设置购买人 店铺信息
        shopOrder.setBuyerName(richOrder.getBuyer().getName());
        // 设置创建人
        shopOrder.setCreatedBy(richOrder.getBuyer().getId());
        shopOrder.setUpdatedBy(richOrder.getBuyer().getId());
        shopOrder.setEvaluateStatus(1);
        Optional.ofNullable(userWxCacheHolder.findByUserIdAndShopId(richOrder.getBuyer().getId(), shop.getId()))
                .map(UserWx::getNickName)
                .ifPresent(shopOrder::setBuyerName);
        if (shopOrder.getBuyerName() == null) {
            Optional.ofNullable(userProfileCacheHolder.getUserProfileByUserId(richOrder.getBuyer().getId()))
                    .map(UserProfile::getRealName)
                    .ifPresent(shopOrder::setBuyerName);
        }
        shopOrder.setBuyerId(richOrder.getBuyer().getId());
        shopOrder.setShopId(shop.getId());
        shopOrder.setCompanyId(richOrder.getCompanyId());
        shopOrder.setRefererId(richSkusByShop.getRefererId());
        shopOrder.setRefererName(richSkusByShop.getRefererName());
        shopOrder.setShopName(shop.getName());
        //微分销增加
        if (richSkusByShop.getWeShop() != null) {
            shopOrder.setOutFrom(OrderOutFrom.WE_SHOP.Code());
            shopOrder.setOutShopId(richSkusByShop.getWeShop().getId().toString());
            shopOrder.setRefererId(richSkusByShop.getWeShop().getUserId());
        } else {
            /// 设置订单来源
            if (richSkusByShop.getOutFrom() != null) {
                shopOrder.setOutFrom(richSkusByShop.getOutFrom());
                shopOrder.setOutShopId(richSkusByShop.getOutShopId());
            }
            /// 如果订单来源ShopId不存在则设置商品来源Id
            if (shopOrder.getOutShopId() == null) {
                shopOrder.setOutShopId(shop.getOuterId());
            }
        }
        /// 2. 设置基础信息
        shopOrder.setStatus(richSkusByShop.getOrderStatus());
        shopOrder.setType(richSkusByShop.getOrderType());
        shopOrder.setPromotionId(richSkusByShop.getPromotionId());
        /// 3. 设置价格
        shopOrder.setFee(richSkusByShop.getFee());
        shopOrder.setOriginFee(richSkusByShop.getOriginFee());
        shopOrder.setDiscount(richSkusByShop.getDiscount());
        shopOrder.setShipFee(richSkusByShop.getShipFee());
        shopOrder.setOriginShipFee(richSkusByShop.getOriginShipFee());
        /// 4. 设置减免 与 优惠
        shopOrder.setShipmentPromotionId(richSkusByShop.getShipmentPromotionId());
        shopOrder.setIntegral(richSkusByShop.getIntegral());
        shopOrder.setBalance(richSkusByShop.getBalance());
        shopOrder.setPayType(richOrder.getPayType());
        /// 5. 设置备注与购买渠道
        shopOrder.setBuyerNote(richSkusByShop.getBuyerNote());
        shopOrder.setChannel(richSkusByShop.getChannel());
        /// 6. 设置平台佣金税率 设置分销税率
        shopOrder.setCommissionRate(richSkusByShop.getCommissionRate());
        shopOrder.setDistributionRate(richSkusByShop.getDistributionRate());
        /// 7. 设置额外信息
        shopOrder.setTags(richSkusByShop.getTags() == null ? richOrder.getTags() : richSkusByShop.getTags());
        shopOrder.setExtra(combineMap(richSkusByShop.getExtra(), richOrder.getExtra()));
        if (richSkusByShop.getShopOrderFeeDetailDTO() != null) {
            shopOrder.setFeeDetailJson(JSON.toJSONString(richSkusByShop.getShopOrderFeeDetailDTO()));
        }
        // 设置订单活动来源
        shopOrder.setOrderSource(ObjUtil.isNotEmpty(richSkusByShop.getOrderSource()) ?
                richSkusByShop.getOrderSource() : OrderSourceEnum.DEFAULT.getCode());
        shopOrder.setInventoryId(richSkusByShop.getInventoryId());
        shopOrder.setActivityId(richSkusByShop.getActivityId());
        shopOrder.setGroupId(richSkusByShop.getGroupId());
        appendFlag(shopOrder, richSkusByShop);
    }

    private void appendFlag(ShopOrder shopOrder, RichSkusByShop richSkusByShop) {
        shopOrder.setFlag(0);

        // 标记：限定商品
        var richSkus = richSkusByShop.getRichSkus();
        if (!CollectionUtils.isEmpty(richSkus) && richSkus.stream().anyMatch(RichSku::isSubStoreOnlyLimitedItem)) {
            shopOrder.setFlag(shopOrder.getFlag() | OrderFlagEnum.PROMOTION_LIMITED_ITEM.getValue());
        }
    }

    /**
     * 结合两者原有的map 为一个新的map，如果为null则视为空map
     *
     * @param aMap 原map A
     * @param bMap 原map B
     * @return 结合过的map
     */
    private Map<String, String> combineMap(Map<String, String> aMap, Map<String, String> bMap) {
        if (aMap == null) {
            return bMap;
        }
        if (bMap == null) {
            return new HashMap<>(8);
        }
        Map<String, String> result = new HashMap<>(aMap);
        result.putAll(bMap);
        return result;
    }


    /**
     * ## 打包提取收货人信息
     *
     * @param richSkusByShop 获取订单信息的实体来源
     * @param richOrder      获取订单信息用的全局订单
     * @return 返回一个List<OrderReceiverInfo>
     */
    private List<OrderReceiverInfo> packReceiverInfo(RichSkusByShop richSkusByShop, RichOrder richOrder) {
        List<OrderReceiverInfo> orderReceiverInfoList = new ArrayList<>();
        switch (orderReceiverInfoLevel) {
            case SHOP: {
                ReceiverInfo receiverInfo = richSkusByShop.getReceiverInfo();
                //如果店铺订单级别没有配置收货信息, 那么肯定是全局的收货信息
                if (receiverInfo == null) {
                    receiverInfo = richOrder.getReceiverInfo();
                }
                OrderReceiverInfo ori = new OrderReceiverInfo();
                ori.setOrderType(OrderLevel.SHOP.getValue());
                ori.setReceiverInfo(receiverInfo);
                orderReceiverInfoList.add(ori);
                break;
            }
            case SKU: {
                for (RichSku richSku : richSkusByShop.getRichSkus()) {
                    OrderReceiverInfo ori = new OrderReceiverInfo();
                    ori.setOrderType(OrderLevel.SKU.getValue());
                    ori.setReceiverInfo(richSku.getReceiverInfo());
                    orderReceiverInfoList.add(ori);
                }
                break;
            }
            default:
                throw new IllegalStateException("unknown orderReceiverInfoLevel: " + orderReceiverInfoLevel);
        }
        return orderReceiverInfoList;
    }


    /**
     * ## 打包提取订单发票信息
     *
     * @param richSkusByShop 获取订单信息的实体来源
     * @return 返回一个List<OrderInvoice>
     */
    private List<OrderInvoice> packOrderInvoice(RichSkusByShop richSkusByShop) {
        List<OrderInvoice> orderInvoiceList = new ArrayList<>();
        switch (orderInvoiceLevel) {
            case SHOP: {
                OrderInvoice oi = new OrderInvoice();
                oi.setOrderType(OrderLevel.SHOP.getValue());
                oi.setInvoiceId(richSkusByShop.getInvoiceId());
                oi.setStatus(1);
                orderInvoiceList.add(oi);
                break;
            }
            case SKU: {
                for (RichSku richSku : richSkusByShop.getRichSkus()) {
                    OrderInvoice oi = new OrderInvoice();
                    oi.setOrderType(OrderLevel.SKU.getValue());
                    oi.setInvoiceId(richSku.getInvoiceId());
                    oi.setStatus(1);
                    orderInvoiceList.add(oi);
                }
                break;
            }
            default:
                throw new IllegalStateException("unknown orderInvoiceLevel: " + orderInvoiceLevel);

        }
        return orderInvoiceList;
    }
}
