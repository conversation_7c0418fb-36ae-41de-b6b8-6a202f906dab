/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.order.model.ReceiverInfo;

import java.util.List;

/**
 * 收货地址信息写服务
 *
 * Author  : panxin
 * Date    : 1:55 PM 3/5/16
 * Mail    : <EMAIL>
 */
public interface ReceiverInfoWriteService {

    /**
     * 创建收货地址
     *
     * @param receiverInfo 收货地址信息
     * @return  新创建的地址id
     */
    Response<Long> createReceiverInfo(ReceiverInfo receiverInfo);

    /**
     * 更新收货地址
     *
     * @param receiverInfo 收货地址信息
     * @return   是否更新成功
     */
    Response<Boolean> updateReceiverInfo(ReceiverInfo receiverInfo);

    /**
     * 更新身份证信息
     *
     * @param receiverInfo 收货地址信息
     * @return 是否更新成功
     */
    Response<Boolean> updateReceiverPaper(ReceiverInfo receiverInfo);

    /**
     * 更新地址Id相关信息
     *
     * @param receiverInfo 收货地址信息
     * @return   是否更新成功
     */
    Response<Boolean> updateAreaId(ReceiverInfo receiverInfo);

    /**
     * 根据用户ID设置默认地址
     *
     * @param receiverInfoId 收货地址ID
     * @param userId 收货地址所属用户ID
     * @return  是否设置成功
     */
    Response<Boolean> makeDefault(Long receiverInfoId, Long userId);

    /**
     * 删除收货地址
     * 根据用户ID和收货地址ID
     *
     * @param receiverInfoId 收货地址ID
     * @param userId 收货地址所属用户ID
     * @return  是否删除成功
     */
    Response<Boolean> deleteAddressByAddressIdAndUserId(Long receiverInfoId, Long userId);

    Response<Boolean> makeDefaultById(Long receiverInfoId, Long userId);

    /**
     * 填充区域对应的id
     *
     * @param tradeAddress 收货地址信息
     */
    void appendAreaId(ReceiverInfo tradeAddress);

    boolean batchUpdateReceiverInfos(List<ReceiverInfo> receiverInfoList);
}
