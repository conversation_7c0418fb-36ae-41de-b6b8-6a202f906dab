package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.order.model.ShopOrder;

import java.util.List;

public interface ShopOrderWriteService {

    Response<Boolean> update(ShopOrder parameter);

    Response<Boolean> batchUpdateConfirmAtInfo(List<ShopOrder> updateList);

    Response<Boolean> deletePushError(ShopOrder shopOrder);

    void updateOrderActualPurchaser(List<Long> orderIds, Long buyerId);
}
