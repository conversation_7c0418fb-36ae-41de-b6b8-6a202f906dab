/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderReceiverInfo;
import moonstone.order.model.ReceiverInfo;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 用户收货地址读服务
 * <p>
 * Author  : panxin
 * Date    : 12:14 PM 3/5/16
 * Mail    : <EMAIL>
 */
public interface ReceiverInfoReadService {

    /**
     * 通过用户ID查询收货地址
     *
     * @param userId 用户ID
     * @return 用户可用的收货地址
     */
    Response<List<ReceiverInfo>> findByUserId(Long userId);

    /**
     * 通过用户ID查询收货地址
     *
     * @param userId 用户ID
     * @return 用户可用的收货地址
     */
    Response<Paging<ReceiverInfo>> findPageByUserId(Long userId, Integer pageNo, Integer pageSize);

    /**
     * 通过收货地址ID获取地址信息
     *
     * @param id 收货地址ID
     * @return 需要查询的收货地址信息
     */
    Response<ReceiverInfo> findById(Long id);


    /**
     * 根据(子)订单查找对应的收货人信息
     *
     * @param orderId    (子)订单id
     * @param orderLevel 订单级别
     * @return 符合条件的收货人信息列表
     */
    Response<List<ReceiverInfo>> findByOrderId(Long orderId, OrderLevel orderLevel);

    /**
     * 查询用户的默认收货地址
     *
     * @param userId 用户id
     * @return 默认收货地址
     */
    Response<Optional<ReceiverInfo>> findDefaultByUserId(Long userId);

    /**
     * 获得地址信息最大id
     */
    Response<Long> maxId();

    Response<List<ReceiverInfo>> findByUserIdLimitOne(Long userId);

    Response<List<OrderReceiverInfo>> findByOrderIds(List<Long> orderIds, OrderLevel orderLevel);

    /**
     * @param orderIds
     * @param orderLevel
     * @return key = OrderId
     */
    Map<Long, ReceiverInfo> findMapByOrderIds(List<Long> orderIds, OrderLevel orderLevel);

    Paging<ReceiverInfo> paging(Map<String, Object> query);

    boolean batchUpdateReceiverInfos(List<ReceiverInfo> receiverInfoList);
}
