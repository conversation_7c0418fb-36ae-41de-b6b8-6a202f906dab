/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.order.dto.RefundCriteria;
import moonstone.order.dto.RefundDetail;
import moonstone.order.dto.RefundList;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderRefund;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.result.RefundExportDO;
import moonstone.order.model.result.RefundItemDO;

import java.util.List;
import java.util.Map;

/**
 * 退款单读服务
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-22
 */
public interface RefundReadService {

    /**
     * 根据id查询sku退款单, 如果查询不到,报错
     *
     * @param id sku退款单id
     * @return sku退款单
     */
    Response<Refund> findById(Long id);

    /**
     * 根据(子)订单和级别查询退款单列表
     * 如果是店铺订单，则会把下面的子订单对应的也查出来
     *
     * @param orderId    (子)订单id
     * @param orderLevel 订单级别
     * @return 对应的退款单列表
     */
    Response<List<Refund>> findAllByOrderIdAndOrderLevel(Long orderId, OrderLevel orderLevel);

    /**
     * 根据(子)订单和级别查询退款单
     *
     * @param orderId    (子)订单id
     * @param orderLevel 订单级别
     * @return 对应的退款单
     */
    Response<List<Refund>> findByOrderIdAndOrderLevel(Long orderId, OrderLevel orderLevel);

    /**
     * sku退款单分页列表
     *
     * @param refundCriteria 退款单查询条件
     * @return 分页的sku退款单列表
     */
    Response<Paging<RefundList>> findBy(RefundCriteria refundCriteria);

    /**
     * 根据退款单id查询关联的(子)订单id列表
     * 多个子订单合并退的时候，会对应多条的
     *
     * @param refundId 退款单id
     * @return 关联的(子)订单列表
     */
    Response<List<OrderRefund>> findOrderIdsByRefundId(Long refundId);

    /**
     * 根据退款内部流水号查询退款单(默认退款内部流水号为退款单id,每个退款单唯一)
     *
     * @param outId 退款内部流水号
     * @return 退款单
     */
    Response<Refund> findByOutId(String outId);


    /**
     * 根据退款支付流水号查询退款单（会存在多条）
     *
     * @param tradeNo 支付内部流水号
     * @return 退款单
     */
    Response<List<Refund>> findByTradeNo(String tradeNo);

    /**
     * sku退款单分页列表, 供开放平台使用
     *
     * @param pageNo         页码
     * @param size           每页大小
     * @param refundCriteria 退款单查询条件
     * @return 分页的sku退款单列表
     */
    Response<Paging<Refund>> findRefundBy(Integer pageNo, Integer size, RefundCriteria refundCriteria);


    /**
     * 查询退款单详情(商品分页展示)
     *
     * @param refundId 退款单id
     * @return 退款单详情
     */
    Response<RefundDetail> findDetailById(Long refundId);

    Response<List<Refund>> findByShopOrderIds(List<Long> orderIdList);

    Response<OrderRefund> findAllByOrderIdAndOrderLevelLimitShopOrder(Long orderId, OrderLevel shop, Integer status);

    /**
     * 查询退款单对应的商品信息
     *
     * @param refundIds
     * @return key=refund_id
     */
    Response<Map<Long, List<RefundItemDO>>> findRefundItems(List<Long> refundIds);

    /**
     * 查询订单与退款单关系表
     *
     * @param orderLevel
     * @param orderIds
     * @return
     */
    Response<List<OrderRefund>> findOrderRefund(OrderLevel orderLevel, List<Long> orderIds);

    /**
     * 查询退款单对应的主订单信息
     *
     * @param refundIds
     * @return key=refund_id
     */
    Response<Map<Long, ShopOrder>> findShopOrder(List<Long> refundIds);

    Response<List<RefundExportDO>> findForExport(RefundCriteria criteria);

    Response<List<Refund>> findByIds(List<Long> ids);

    /**
     * @param ids
     * @return key=退款单id
     */
    Map<Long, Refund> findMapByIds(List<Long> ids);

    Response<List<OrderRefund>> findRefundedByOrderIds(List<Long> orderIds, OrderLevel orderLevel);

    /**
     * 根据退款单id查询退款单对应的主订单信息
     * @param refundId 退款单id
     * @return 主订单信息
     */
    ShopOrder findShopOrderByRefundId(Long refundId);
}
