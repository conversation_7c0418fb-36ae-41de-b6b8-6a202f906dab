package moonstone.order.enu;

import moonstone.shop.enums.ShopPayInfoPayChannelEnum;

/**
 * parana_payment.channel 支付渠道编码
 */
public enum PaymentChannelEnum {
    ALIPAY_PC("alipay-pc", "支付宝PC端支付"),
    ALIPAY_WAP("alipay-wap", "支付宝手机网站支付"),
    WE_CHAT_PAY_JSAPI("wechatpay-jsapi", "微信JSAPI支付"),
    WE_CHAT_PAY_QR("wechatpay-qr", "微信扫码支付"),
    UMF("umf", "联动优势支付"),
    ALLINPAY("allinpay", "通联支付"),
    ALLINPAY_YST("allinpay-yst", "通联支付云商通"),

    INTEGRAL_PAY("Integral-pay", "使用积分进行支付（无需推送的）"),
    MOCK_PAY("mockpay", "模拟支付"),
    ;

    private String code;
    private String description;

    PaymentChannelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static PaymentChannelEnum from(String code) {
        for (PaymentChannelEnum current : PaymentChannelEnum.values()) {
            if (current.getCode().equals(code)) {
                return current;
            }
        }
        return null;
    }

    public static String getDesc(String code) {
        for (PaymentChannelEnum current : PaymentChannelEnum.values()) {
            if (current.getCode().equals(code)) {
                return current.getDescription();
            }
        }
        return "未知支付方式";
    }

    /**
     * 由支付配置（ShopPayInfo.java）到支付单（Payment）上的支付渠道的映射，有问题。。。
     *
     * @param payChannel
     * @return
     */
    public static PaymentChannelEnum convert(ShopPayInfoPayChannelEnum payChannel) {
        return switch (payChannel) {
            case UMF -> PaymentChannelEnum.UMF;
            case WECHATPAY -> PaymentChannelEnum.WE_CHAT_PAY_JSAPI;
            case ALIPAY -> PaymentChannelEnum.ALIPAY_WAP;
            case ALLINPAY -> PaymentChannelEnum.ALLINPAY;
            case ALLINPAY_YST -> PaymentChannelEnum.ALLINPAY_YST;
            default -> null;
        };
    }

    /**
     * 是否需要线上托管代付
     *
     * @param channel
     * @return
     */
    public static boolean needAgentPay(String channel) {
        var payChannel = from(channel);
        if (payChannel == null) {
            return false;
        }

        return switch (payChannel) {
            case ALLINPAY_YST -> true;
            default -> false;
        };
    }
}
