package moonstone.showcase;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.mongo.ReqUrlStatistics;
import org.springframework.core.MethodParameter;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Resource;

/**
 * author：书生
 */
@RestControllerAdvice
@Slf4j
public class ResponseBodyLoggingAdvice implements ResponseBodyAdvice<Object> {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        log.info("请求路径为 {}",request.getURI().getPath());
        String serviceName = "web";
        String url = request.getURI().getPath();
        ReqUrlStatistics reqUrlStatistics = mongoTemplate.findOne(Query.query(Criteria.where("serviceName").is(serviceName).and("url").is(url)), ReqUrlStatistics.class);
        if (reqUrlStatistics == null) {
            reqUrlStatistics = new ReqUrlStatistics();
            reqUrlStatistics.setCount(0L);
            reqUrlStatistics.setUrl(url);
            reqUrlStatistics.setServiceName(serviceName);
        }
        reqUrlStatistics.setCount(reqUrlStatistics.getCount() +1);
        mongoTemplate.save(reqUrlStatistics);
        log.info("响应数据 {}", JSONUtil.toJsonStr(body));
        return body;
    }
}
