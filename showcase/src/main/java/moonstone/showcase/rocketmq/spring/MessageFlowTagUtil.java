package moonstone.showcase.rocketmq.spring;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.showcase.rocketmq.EnvProperty;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.annotation.SelectorType;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.util.StringUtils;

/**
 * 消息路由原则：
 *  带有envTag的实例：
 *      消费消息：
 *          对应envTag的消息会被消费
 *          其他envTag的消息不会被消费
 *          没有envTag的消息不会被消费
 *      生产消息：
 *          生产出来的消息默认不会打上envTag 按染色规则打上envTag
 *  没有envTag的实例：
 *      消费消息：
 *          没有envTag的消息会被消费
 *          有envTag的消息不会被消费
 *      生产消息：
 *          生产出来的消息默认不会打上envTag 按染色规则打上envTag
 *
 */
@Slf4j
public class MessageFlowTagUtil {

    private static final String ENV_MSG_TAG = "ENV_TAG";

    /**
     *
     * @param annotation
     * @return 一定是SQL92
     */
    public static SelectorType getSelectorType(RocketMQMessageListener annotation) {
        String envTag = EnvProperty.getTag();
        if(StringUtils.isEmpty(envTag)){
            return SelectorType.SQL92;
        }else{
            return SelectorType.SQL92;
        }
    }

    public static String getTag(RocketMQMessageListener annotation, StandardEnvironment environment) {
        SelectorType selectorType = annotation.selectorType();
        String selectorExpression = environment.resolvePlaceholders(annotation.selectorExpression());

        /**
         * Grammar please see {@link SelectorType#TAG} and {@link SelectorType#SQL92}
         * old AND IS NOT NULL AND msgTag = 'envTag'
         */
        String newSelectorExpression = "";

        // 没有实例TAG的时候 不做任何处理
        String envTag = EnvProperty.getTag();
        if(StringUtils.isEmpty(envTag)){
            if("*".equals(selectorExpression)){
                // 没有envTag的消息会被消费
                newSelectorExpression = String.format("%s IS NULL", ENV_MSG_TAG);
            }else{
                // 原tag过滤 & 没有envTag的消息
                newSelectorExpression = String.format("(TAGS=%s) AND (%s IS NULL)", selectorExpression, ENV_MSG_TAG);
            }
            return newSelectorExpression;
        }

        if(SelectorType.TAG.equals(selectorType)){
            if("*".equals(selectorExpression)){
                newSelectorExpression = String.format("ENV_TAG IS NOT NULL AND %s=%s", ENV_MSG_TAG, envTag);
            }else{
                /**
                 * 需要用sql92兼容tag订阅
                 *      Tag是一种系统属性，所以SQL过滤方式也兼容Tag标签过滤。在SQL语法中，Tag的属性名称为TAGS。
                 * 保留原TAG条件 and 新增ENV_TAG匹配条件
                 */
                newSelectorExpression = String.format("(TAGS=%s) AND (ENV_TAG IS NOT NULL AND %s=%s)", selectorExpression, ENV_MSG_TAG, envTag);
            }
        }else if (SelectorType.SQL92.equals(selectorType)) {
            /**
             * 保留原SQL92条件 and 新增ENV_TAG匹配条件
             */
            newSelectorExpression = String.format("(%s) AND (ENV_TAG IS NOT NULL AND %s=%s)", selectorExpression, ENV_MSG_TAG, envTag);
            log.info("[MQ]流量隔离 topic: {} group: {} selectorExpression: {}->{}", annotation.topic(), annotation.consumerGroup(), selectorExpression, newSelectorExpression);
        }

        return newSelectorExpression;
    }

    public static String getConsumerGroup(RocketMQMessageListener annotation, StandardEnvironment environment) {
        String consumerGroup = environment.resolvePlaceholders(annotation.consumerGroup());
        String envTag = EnvProperty.getTag();
        if(StringUtils.isEmpty(envTag)){
            return consumerGroup;
        }
        String newGroup = String.format("%s_%s", consumerGroup, envTag);
        log.info("[MQ]流量隔离 topic: {} group: {}->{}", annotation.topic(), annotation.consumerGroup(), newGroup);
        return newGroup;
    }

    @Data
    public static class MessageTagType{
        private SelectorType selectorType;

        private String selectorExpression;
    }
}
