//package moonstone.showcase.rocketmq;
//
//import com.danding.component.property.EnvProperty;
//import com.danding.component.spi.spring.autoconfigure.SpringAutoconfigureExclude;
//import com.danding.x.spi.dubbo2.extension.Activate;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.util.StringUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Slf4j
//@Activate
//public class RocketmqSpringAutoconfigureExclude implements SpringAutoconfigureExclude {
//
//    /**
//     * 排除springboot的rocketmq自动配置，使用包装后的模块
//     */
//    private static final String className = "org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration";
//
//    @Override
//    public List<String> getExcludeList() {
//        List<String> list = new ArrayList<>();
//
//        String envTag = EnvProperty.getTag();
//        if(StringUtils.isEmpty(envTag)){
//            log.debug("当前实例无tag");
//            return list;
//        }
//
//        list.add(className);
//        return list;
//    }
//
////    private static void setEnvTag() {
////        System.setProperty("spring.autoconfigure.exclude", "org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration");
////    }
//}
