/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.showcase;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-01
 */
@EnableAsync
@ServletComponentScan
@SpringBootApplication
public class ShowcaseApplication {
    public static void main(String[] args) {
        // 排除rocketmq 自动装载
        System.setProperty("spring.autoconfigure.exclude", "org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration");

        SpringApplication application = new SpringApplication(ShowcaseApplication.class);
        application.run(args);
    }

}
