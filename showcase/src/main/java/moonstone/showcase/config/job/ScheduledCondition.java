package moonstone.showcase.config.job;

import moonstone.common.utils.StringUtils;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

public class ScheduledCondition implements Condition {

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        String property = context.getEnvironment().getProperty("scheduled.enable");
        if(StringUtils.isEmpty(property)) {
            System.out.println("定时任务判断 允许执行");
            return true;
        }
        System.out.println("定时任务判断 全局禁止");
        return false;
    }

}