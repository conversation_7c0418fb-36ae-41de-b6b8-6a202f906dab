package moonstone.showcase.config.job;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor;

@Configuration
public class ScheduledConfig {

    /**
     * 通过配置文件控制是否加载 ScheduledAnnotationBeanPostProcessor 类，从而决定是否启用定时任务。
     * @return
     */
    @Conditional(ScheduledCondition.class)
    @Bean
    public ScheduledAnnotationBeanPostProcessor processor() {
        return new ScheduledAnnotationBeanPostProcessor();
    }

}