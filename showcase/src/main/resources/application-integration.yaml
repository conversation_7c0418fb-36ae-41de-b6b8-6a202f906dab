application:
  name: supply-shop-front

environment: integration

logging:
  #  file: /nfs/terminus-web/terminus-web.log
  #  level.*: INFO
  #  level.io.terminus: DEBUG
  #  level.org.mybatis: ERROR
  config: classpath:config/integration/logback.xml

ext.messages.classpath: messages_zh_CN

soul:
  dubbo:
    adminUrl: http://soul-admin-test.yang800.cn
    contextPath: /shop
    appName: shop
  http:
    adminUrl: http://soul-admin-test.yang800.cn
    contextPath: /shop
    appName: shop
dubbo:
  application:
    name: parana-web
  port: 20880
  address: ${nacos.discovery.serverAddr}
  registry:
    protocol: nacos
    address: ${nacos.discovery.serverAddr}
nacos:
  discovery:
    serverAddr: *************:8848
ucenter:
  url: http://ucenter-server.services--29bc3b30866ae02888b33ed871fc0fb3.svc.cluster.local:8080/sdk-api/gateway
  systemCode: PARANA_WEB
  appKey: PARANA_WEB_test
  appSecret: test
  packages:

mybatis:
  mapperLocations:  classpath*:mapper/*Mapper.xml
  type-aliases-package: >
    moonstone.(item|category|brand|shop|spu|user|cart|order|express|promotion|delivery|file|settle|membership|countryImage|integral).model,
    moonstone.(adv|thirdParty|shopWxa|wxa|shopMini).model,
    moonstone.(weCart|weShop|weDistributionApplication).model,
    moonstone.user.address.model,
    moonstone.user.area.model,
    moonstone.pay.mock.model,
    moonstone.auth.model
  typeHandlersPackage: moonstone.common.type.handler

server:
  context-path: /
  port: 8080

hazelcast:
  config: hazelcast-config.xml
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************************
    username: parana_test
    password: b80c157@c3791
  elasticsearch:
    uris: http://es7177-svc.db.svc.cluster.local:9200
    connection-timeout: 3s
  data:
    mongodb:
      uri: mongodb://mongodb-svc.db.svc.cluster.local:27017/parana-test
  http:
    converters:
      preferred-json-mapper: gson
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  redis:
    host: redis-svc
    port: 6379
    password: dd654321
    timeout: 500
    database: 0
    jedis:
      pool:
        max-active: 100
        max-wait: -1
        max-idle: 8
        min-idle: 0

parana:
  app_backend:
    url: http://mall.yang800.cn/backend
  web:
    url: http://mall.yang800.cn
  mall:
    url: http://mall.yang800.cn
  h5:
    url: http://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com
  wxa:
    url: https://m.mall.yang800.com
    appId: wx90e48a315747ccb9
    appSecret: ebb83ced7885897e4fd5ecfc403ed92d
  we:
    seller:
      url: https://m.mall.yang800.com
      appId: wx0fb538f6dc093892
      appSecret: 5d260bcf67eecf19e96a341b7b4eb95a
    buyer:
      url: https://m.mall.yang800.com
      appId: wxb25691b47b974630
      appSecret: 088dedd8ec3382f5ba981a2560afc187
  wxopen:
    componentAppId: wxfb7d4a7ab73f6ba6
    componentSecret: a9a41dcfd8e7b564a2ec93c0ee58fb9f
    componentToken: wx569abcf3c77ff1de
    componentAesKey: 0987654321qazwsxedcrfvtgbyhnujmik1234567890
    requestdomain: https://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com;https://www.yang800.com;https://m.yang800.com
    wsrequestdomain: wss://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com;wss://www.yang800.com
    uploaddomain: https://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com;https://www.yang800.com
    downloaddomain: https://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com;https://www.yang800.com
  alipayOpen:
    serviceAppId: 2021004143615727
    serviceAppPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp2EifbZj3XISeVR4odPh1kW1R+Ae0MuPVALJOSeXLqlqGZxyRckqXvNQPOL4MxPU4RGL9Q4ADoA7Y7IK8IBvBrdSUIzTIBzcB20T9X1q16JOeuKe1muONNb08sXYGyDeX4OGi0Bd6JWxeXix4LG8PZXXt9Q2MP/WfSeWe2UUyRTgaq+lkja2J6BsEESn52h0WiU02Sw3fC+PkqmDInLuhAp4k8FVe1P4Xt9CMcOdMOIfGAFsq118Md7s0er9j5uzBzx6DWQwK0SDREiqSk+/0dap5MUgZqYiUaBiZo3WgcDEicMyiM/qmKg/eyrG7s7y6t6PM+n8F4H2IDJIHNytywIDAQAB
    serviceAppPrivateKey: MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCr/o2Vrl75G8fVkm59AqE06Ux8sBA+vPbm820NU+gGG54Q1ADdfCfEIiUXtL4BIUCP+xJw2zVAsCsBA5eyp1W5miweZ4l1WTKx3eQWEIZSDfhKuOEb1rwRsMh18HGSSJFSip1ApvO6gsVs8FItKoI52uy1FPXMm2sioERuf5+FLnWmLnS0FgbLdhjw7dQs/Bbaf5CWRhO90DZusx/WOXRSZLqatk9lXCyQYWJZu9EJfdRHMlCoezNB4lqEN4wujvbEJHY4dONJ+Rv7iQ3jsq9WZCdJAwSlNS12DixgUMwsv+n7kPQBQD88QhBg8y2suCygECI9kpN/AWQRoK7zecIzAgMBAAECggEBAJdZKrIafQxse87eSkfLNpxe56WZMNJkakgU0VO3ECe/ybo5NIGyLTQUb9uKeuB3qF6Kq1GQNC42V8FOrgDR60el+UjQB0+yLEAEaB9abzhpfqISSlS7A0f17pCKAwacwPSHcZVg1Hy9colIeKeyDmJm2YEHbrybzwzrRwyNVYxVQjJSKqCTGIkPigOyHwafO5EljZJIcdTgmmH+bC1lAn8GLqe8IN9NRY+mQkQdGoQDDH03xDZLz/aRWPHcZOJnqMu1BLWDfLZ+n/GxbxbX++/X1tC3CekVKUV3H6oI0L0T7VC8p8ZQH2ct5ALmF26KmlVfBCLovVuvFhriOdOAgxECgYEA2iVMX68RlO1XYag3K03LO9FJMgWh1dG6VdyxMfaXSeRYBNkH1RAV+sSyeWFO0IlW5EzxRBkRV43qNgpDbVJvf6SW9yUUSk8aCpDGzd+OOkMxJxJMUfl3I0p1W5Kh9WO1ld5ml+ruui82DLf0Y1q++/d27QgNIBcQUbtVo/LO08UCgYEAydcR8tgxbNnQU5GRDH+ifE4TJ9maCloy10NxEIICNaJGyHaNNXxogSlo/tFY4aKnggDbt4sSbzYAQl0TeYT7YSBKWXc4Ncj4ctXCqfLMeM34AwaPO2Sfvv8kfWEzB81BJiq0Zyvv43a54LHWYDBH5kBYiHfdCnuWQiwjcYl4BZcCgYBalY5nVT1JyFy2srFmDAHOrxNj8C7JqTNlICV8h9c+2dErAixcbzeRUEiEWsHtmno4hzLQglBPOSD+m7hwFwEx0djWe9E3Ii3AIWpMZv6advhcLx7+E0dDolGlWvhh+6uVtiqH/whuy9f35BNZhn80BgieWO+KWclvBwmC8PqtuQKBgQCk5eauBd5rVZATlitwkJ4I/Cg+OFLW/lXm9BxdJjnz4338rI41Eky2qUQ0WohI3wAG//iBfUc0QGP509zjPLe65AyaBwKPPn9sIiOFgO14DJ4Aqs098rln6z6+iDiOuSvuqJj2aTj1FrxJTEEeGJPFEvDBnVujAtEBgtn6GhtKRQKBgQDD0DibDThMs0Vh2NvLX2vqr3Ufd7FJqmJ2vXp/1ypODjxeqjTu8v8yZZaG54mmvdenNlkpIpymMdfWNC9v1mWjfqG2XM8WnaXVnjkfrMcDGfdFkSQTB5IyZIWlTrkMES7zefQ1xdYkE+TaY3WIcwqPX4zEGwAiAsQfbCf5CLPWTw==
    serviceAppDecryptKey: huqzAm2wBuWhTVFG8Lcfhw==
    authNotifyUrl: https://mtest.mall.yang800.cn/backend/api/shopAppAuth/alipay/authNotify
  substore:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3
  levelDistribution:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3

search:
  host: es7177-svc.db.svc.cluster.local
  port: 9200

item.search:
  index-name: items
  index-type: item
  mapping-path: item_mapping.json
  full-dump-range: 3000
  batch-size: 100

shop.search:
  index-name: shops
  index-type: shop
  mapping-path: shop_mapping.json
  full-dump-range: 3000
  batch-size: 100

weShopItem.search:
  index-name: t_we_shop_items
  index-type: we_shop_item
  mapping-path: we_shop_item_mapping.json
  full-dump-range: 3000
  batch-size: 100

session:
  cookie-domain: none
  cookie-context-path: /
  cookie-name: msid
  cookie-max-age: 360000
  source: redis
  serialize-type: json
  redis-host: redis-svc
  redis-port: 6379
  redis-index: 0
  redis-cluster: false
  redis-test-on-borrow: true
  redis-max-total: 10
  redis-max-idle: 0
  redis-prefix: afsession
  redis-auth: dd654321
  max-inactive-interval: 360000
  # use test env for now test on test env
  jwt_key: WEB_TEST_FCHcgv1NCx1NHhjLPdYeFrUu87C8RPYa
  jwt_name: test_app_token

oms:
  api:
    admin: http://npc.daily.yang800.com/backend/
#  zk.host: 127.0.0.1
#  zk.port: 2181

express.100:
  key: bAWWUdnn9775_test
  customer: 837CEA86CF64D4FEC5B4CEC59589E2D5_test
  regularUrl: http://poll.kuaidi100.com/poll/query.do

# ip地址库目录
ip.dic: data/ip.txt

wechat:
  mp:
    appId: wx86fe5657944bcaa2
    secret: f72b211010c13e17141accd17fdbb23b
  authorize:
    redirectUrl: http://mall.yang800.cn/backend/api/wechat/auth/jump
    resultUrl: http://mall.yang800.cn/seller/authorize-success
  applet:
    enable: true
    mchId: **********
    appId: wx86fe5657944bcaa2
    secret: 8ac1cbf290b0fe98ff1142acf94e7351

pay:
  easy-pay:
    gateway: https://test_nucc.bhecard.com:9088/api_gateway.do
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2WTfvas1JvvaRuJWIKmKlBLmkRvr2O7Fu3k/zvhJs+X1JQorPWq/yZduY6HKu0up7Qi3T6ULHWyKBS1nRqhhHpmLHnI3sIO8E/RzNXJiTd9/bpXMv+H8F8DW5ElLxCIVuwHBROkBLWS9fIpslkFPt+r13oKFnuWhXgRr+K/YkJQIDAQAB
  allinpay:
    pay: https://syb.allinpay.com/apiweb/unitorder/pay
    refund: https://syb.allinpay.com/apiweb/tranx/refund
    query: https://syb.allinpay.com/apiweb/tranx/query
    ystBaseUrl: https://open.allinpay.com/gateway
  debug: false
  channel:
    alipay: enable
  notifyUrl: http://mall.yang800.cn/backend/api/order/paid
  notifyUrlByRegisterPay: http://mall.yang800.cn/backend/api/order/paid/userRegister
  refundNotifyUrl: http://mall.yang800.cn/backend/api/refund/notify
  returnUrl: http://mall.yang800.cn/buyer/trade-success
  h5ReturnUrl: http://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com/order/pay-success
  unionpay.wap.token.verifyCerDir: /nfs/terminus-web
  unionpay.pc.token.verifyCerDir: /nfs/terminus-web
  unionpay.app.token.verifyCerDir: /nfs/terminus-web
  certFilePath: /nfs/certFiles/terminus-web

pay.mockpay.token:
  notifyUrl: http://mall.yang800.cn/backend/api/order/paid
  returnUrl: http://mall.yang800.cn/buyer/trade-success
  refundNotifyUrl: http://mall.yang800.cn/backend/api/refund/notify

pay.integral.token:
  returnUrl: http://mall.yang800.cn/buyer/trade-success
  refundNotifyUrl: http://mall.yang800.cn/backend/api/refund/notify

wechat.applet:
  enable: true
  mchId: **********
  appId: wx86fe5657944bcaa2
  secret: 8ac1cbf290b0fe98ff1142acf94e7351

alipay:
  pid: ****************
  key: pil5t34m4qsdor9ujuffkqfvrgfjt3mp
  account: <EMAIL>

cache.duration.in.minutes: 10

settle:
  enable: true
  listener:
    enable: false

msg:
  current:
    smsService: aliYunSmsService
    emailService: javaxEmailService
  aliyun:
    appKey: LTAI5tCR5p7wUaszs7VDrv9g
    appSecret: ******************************
  javaxemail:
    mailServerHost: smtp.exmail.qq.com
    mailServerPort: 465
    fromAddress: Y800<<EMAIL>>
    userName: <EMAIL>
    password: Dd111111

msg.template.list:
  - key: sms.28.user.login.code
    title: 登录验证
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168826876","smsParam":{"code":"{{code}}"}}
  - key: sms.user.login.code
    title: 登录验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_145495759","smsParam":{"code":"{{code}}"}}
  - key: sms.28.user.register.code
    title: 注册验证
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168821990","smsParam":{"code":"{{code}}"}}
  - key: sms.user.register.code
    title: 注册验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143863719","smsParam":{"code":"{{code}}"}}
  - key: sms.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868717","smsParam":{"code":"{{code}}"}}
  - key: sms.28.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168821992","smsParam":{"code":"{{code}}"}}
  - key: sms.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868720","smsParam":{"code":"{{code}}"}}
  - key: sms.28.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168826869","smsParam":{"code":"{{code}}"}}
  - key: sms.28.user.action
    title: 通用验证码
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168821997","smsParam":{"code":"{{code}}"}}
  - key: sms.user.action
    title: 通用验证码
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_166865728","smsParam":{"code":"{{code}}","name":"{{name}}","operation":"{{operation}}"}}
  - key: sms.user.set.withdraw.password
    title: 设置提现密码验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_153325369","smsParam":{"code":"{{code}}"}}
  - key: sms.28.user.set.withdraw.password
    title: 通用验证码
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168821997","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_170840997","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.set.withdraw.password
    title: 设置提现密码验证
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_170835997","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.register.code
    title: 注册验证
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_176532281","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_170835976","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.login.code
    title: 登录验证
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_176537313","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.set.withdraw.password
    title: 通用验证码
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_170840987","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.auth.apply
    title: 门店审核通知
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_171856413","smsParam":{"p_name":"{{p_name}}","c_name":"{{c_name}}"}}
  - key: withdraw.certification.identify
    title: 提实实名审核
    content: >
      {"smsName":"奥利派商场","smsTemplate":"SMS_201455761","smsParam":{"code":"{{code}}"}}

msg.email:
  templates:
    - key: email.error.notice
      templateName: sendEmailErrorNotice
      subject: "但丁商城 - 异常通知"

# open api配置开始
enable.open.api: true
# open api配置结束

oss:
  #endpoint: oss-cn-hangzhou-internal.aliyuncs.com
  endpoint: https://oss-cn-hangzhou.aliyuncs.com
  appKey: LTAI5tNGqjjEfJ1n3xbV42v9
  appSecret: ******************************
  bucketName: dante-img

image:
  base.url: https://dante-img.oss-cn-hangzhou.aliyuncs.com
  upload.allowedFileTypes: jpeg,jpg,gif,png,mp4,webp
  protocol: https
  domain: dante-img.oss-cn-hangzhou.aliyuncs.com

enable.shop.search: true
enable.item.search: true

Y800:
  partnerCode: webB2C
  partnerKey: yang800testWebSC
  sourcePlatform: WEBSC
  sourcePlatformV3: webB2CV3
  merchantCode:
  wd.support: http://wd-support
  yang.support: http://yang-support-svc
  finance.project.url: http://staticstest.yang800.cn/xhr
  open.api.gate: http://outtest.order.yang800.cn/open/apiv2
nio:
  post.url: http://sync.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com/xhr/

aoxin:
  gateway: http://************:61/API

# 支付宝app支付
pay.alipay.app.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
pay.alipay.app.account.list:
  - accountNo: default
    accountName: default app account
    pid: ${alipay.pid}
    account: ${alipay.account}
    md5Key: ${alipay.key}
#    rsaPriKey: ${alipay.merchant.privateKey}
#    rsaPubKey: ${alipay.publicKey}
pay.job.trans.alipay.app.enable: true

# 支付宝PC支付
pay.alipay.pc.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
pay.alipay.pc.account.list:
  - accountNo: default
    accountName: default app account
    pid: ${alipay.pid}
    account: ${alipay.account}
    md5Key: ${alipay.key}
#    rsaPriKey: ${alipay.merchant.privateKey}
#    rsaPubKey: ${alipay.publicKey}
pay.job.trans.alipay.pc.enable: true

#支付宝WAP支付
pay.alipay.wap.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.h5ReturnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
pay.alipay.wap.account.list:
  - accountNo: default
    accountName: default app account
    pid: ${alipay.pid}
    account: ${alipay.account}
    md5Key: ${alipay.key}
#    rsaPriKey: ${alipay.merchant.privateKey}
#    rsaPubKey: ${alipay.publicKey}
pay.job.trans.alipay.wap.enable: true

#微信APP支付
pay.wechatpay.app.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
  caFilePath: /nfs/certFiles/terminus-web/default/apiclient_cert_app.p12
pay.wechatpay.app.account.list:
  - accountNo: default
    accountName: default app account
    mchId: ${wechat.applet.mchId}
    appId: ${wechat.applet.appId}
    partnerKey: ${wechat.applet.secret}
pay.job.trans.wechatpay.app.enable: true

#微信JSAPI支付(公众号支付)
pay.wechatpay.jsapi.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.h5ReturnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
  caFilePath: /nfs/certFiles/terminus-web/default/apiclient_cert_app.p12
pay.wechatpay.jsapi.account.list:
  - accountNo: default
    accountName: default app account
    mchId: ${wechat.applet.mchId}
    appId: ${parana.wxa.appId}
    partnerKey: ${wechat.applet.secret}
pay.job.trans.wechatpay.jsapi.enable: true

#微信扫码支付(Native支付)
pay.wechatpay.qr.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
  caFilePath: /nfs/certFiles/terminus-web/default/apiclient_cert_app.p12
pay.wechatpay.qr.account.list:
  - accountNo: default
    accountName: default app account
    mchId: ${wechat.applet.mchId}
    appId: ${wechat.applet.appId}
    partnerKey: ${wechat.applet.secret}
pay.job.trans.wechatpay.qr.enable: true

#微分销商城前台域名
m.mall.yang800: https://m.mall.yang800.com

#积分商城生成二维码的路径
m.mall.jifen.path: /nfs/certFiles/terminus-web/levelDistribution/image/
#积分商城生成二维码的url
m.mall.jifen.url: https://mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com
#功能开关
function:
  switch:
    aoXinPush: false
    financePush: true
    unifiedPayment: true

mercury:
  pay:
    host: http://paytest.yang800.cn
    appCode: DDFXSC
    merchantCode: M2019030614100738125
    customs:
      notify: http://admin.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com/backend/api/customs/payment/declare/notify

ebpCode: 3301964J31

wx:
  backend:
    url: https://api.weixin.qq.com/cgi-bin

scheduled:
  cron:
    orderAutoConfirm: 0 0/15 * * * ?
    AccountStatementAutoCreate: 0 30 0 1 * ?
    subStoreOnlyAutoGive: 0 0 0 */1 * ?
    autoCreateAgentPayOrder: 0 0/5 * * * ?
    autoAgentPayPayment: 0 0/8 * * * ?
    autoQueryWithdrawPaymentResult: 0 0/30 * * * ?

express.tms:
  host: http://tms-server-test.yang800.com.cn:8830/api/
  appKey: 10000
  secret: E6AC95EA2C0343A2
aliyun:
  realNameCertification:
    appKey: *********
    appSecret: ERZchuOkDUYUYhDEPz4upEaZlGEL6Q2p

withdraw.apply:
  onlinePay:
    certFilePath: /nfs/withdrawCertFiles/terminus-web
    callbackUrl: http://mall.yang800.cn/backend/api/withdrawPayment

agentPay.payment:
  callbackUrl: http://mall.yang800.cn/backend/api/agentPayPayment

fundsTransfer.payment:
  callbackUrl: http://mall.yang800.cn/backend/api/fundsTransferPayment

enterpriseWithdraw:
  callbackUrl: http://mall.yang800.cn/backend/api/enterpriseWithdraw

order.auto.cancel.in.minutes: 15



# 这里只用到了url与两个回调
parana.allinpay:
  #  url: http://test.allinpay.com/open/gateway
  url: https://open.allinpay.com/gateway
  #  appId: 1283233906081939458
  #  secretKey: eMDVxpfvt1k8mIiCZvDLWx9wtJcdtVBQ
  #  privateKeyPath: /nfs/certFiles/allinpay/test/1283233906081939458.pfx
  #  pwd: 939458
  #  publicKey: /nfs/certFiles/allinpay/test/TLCert-test.cer
  companyBackUrl: https://mall.yang800.cn/backend/allinPay/setCompanyInfoBack.json
  companyBackUrlH5: https://mall.yang800.cn/backend/allinPay/setCompanyInfoBackByH5.json
  signAcctProtocolUrl: https://mall.yang800.cn/backend/allinPay/signAcctProtocolBack.json


parana.signature:
  merId: "80000000002001294"
  #  url: http://test.allinpay.com/open/gateway
  url: https://open.allinpay.com/gateway
  # 未签约的模板文件 用于预览
  templatePDF: https://daita-front.oss-cn-shanghai.aliyuncs.com/mincroapp/xianhui-2023-agreement.pdf
  templateId: "3258994925998131944"
  downloadUrl: https://cloud.allinpay.com/op/downloadFile

# MQ
rocketmq:
  name-server: rocketmq-nameserver-svc.mq.svc.cluster.local:9876
  producer:
    group: "PARANA"
    enableMsgTrace: false
imm:
  end-point: imm.cn-hangzhou.aliyuncs.com
  project—name: app_imm_project

# 阿里云走测试环境的mercury
y800:
  gateApi: http://outtest.order.yang800.com.cn:8830/open/apiv3

address.base:
  host: https://danding-gateway-pre.yang800.com
  path: /ares-web/area/listAllAreaInfo

switch:
  pay-online: false