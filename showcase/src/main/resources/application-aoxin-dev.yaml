application:
  name: AXFXSC_WEB_FRONT

environment: dev

logging:
  file: e:/nfs/dev/logs/terminus-web/terminus-web.log
  level.*: INFO
  level.io.terminus: DEBUG

ext.messages.classpath: messages_zh_CN

mybatis:
  mapperLocations:  classpath*:mapper/*Mapper.xml
  type-aliases-package: >
    moonstone.(item|category|brand|shop|spu|user|cart|order|express|promotion|delivery|file|settle|membership|countryImage).model,
    moonstone.(adv|thirdParty|shopWxa|wxa).model,
    moonstone.(weCart|weShop|weDistributionApplication).model,
    moonstone.user.address.model,
    moonstone.user.area.model,
    moonstone.pay.mock.model,
    moonstone.auth.model

server:
  context-path: /
  port: 10081

spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: jdbc:mysql://**************:3306/parana?useUnicode=true&characterEncoding=utf-8&autoReconnect=true
    username: root
    password: dd123456
  data:
    mongodb:
      uri: mongodb://**************:27017/parana-dev

parana:
  web:
    url: http://webdev.parana.yang800.cn:10091
  h5:
    url: https://m.myazgo.com
  wxa:
    url: https://m.myazgo.com
    appId: wx90e48a315747ccb9
    appSecret: ebb83ced7885897e4fd5ecfc403ed92d
  we:
    seller:
      url: https://m.myazgo.com
      appId: wxecba51a00597fa06
      appSecret: e47a924398259259af4b551851c2567c
    buyer:
      url: https://m.myazgo.com
      appId: wxf379b227de26d16f
      appSecret: 9063daeff996cfa259ea2386b7861c5a
  wxopen:
    componentAppId:
    componentSecret:
    componentToken:
    componentAesKey:
    requestdomain: https://m.mall.yang800.com;https://www.yang800.com;https://m.yang800.com
    wsrequestdomain: wss://m.mall.yang800.com;wss://www.yang800.com
    uploaddomain: https://m.mall.yang800.com;https://www.yang800.com
    downloaddomain: https://m.mall.yang800.com;https://www.yang800.com

search:
#  host: localhost
  host: **************
  port: 9200

item.search:
  index-name: t_items
  index-type: item
  mapping-path: item_mapping.json
  full-dump-range: 3000
  batch-size: 100

shop.search:
  index-name: t_shops
  index-type: shop
  mapping-path: shop_mapping.json
  full-dump-range: 3000
  batch-size: 100

weShopItem.search:
  index-name: t_we_shop_items
  index-type: we_shop_item
  mapping-path: we_shop_item_mapping.json
  full-dump-range: 3000
  batch-size: 100

session:
  cookie-domain: webdev.parana.yang800.cn
  cookie-context-path: /
  cookie-name: msid
  cookie-max-age: 1800
  source: redis
  serialize-type: json
  redis-host: **************
  redis-port: 6379
  redis-index: 0
  redis-cluster: false
  redis-test-on-borrow: true
  redis-max-total: 10
  redis-max-idle: 0
  redis-prefix: afsession

#curator:
#  zk.host: 127.0.0.1
#  zk.port: 2181

express.100:
  key: f93c3f9e5a89fb22
  customer: xxxx

# ip地址库目录
ip.dic: data/ip.txt

wechat:
  mp:
    appId: wx86fe5657944bcaa2
    secret: xxxx
  authorize:
    redirectUrl: http://webdev.parana.yang800.cn:10081/api/wechat/auth/jump
    resultUrl: http://webdev.parana.yang800.cn:10091/seller/authorize-success

pay:
  debug: false
  channel:
    alipay: enable
  notifyUrl: http://webdev.parana.yang800.cn:10081/api/order/paid
  refundNotifyUrl: http://webdev.parana.yang800.cn:10081/api/refund/notify
  returnUrl: http://webdev.parana.yang800.cn:10091/buyer/trade-success
  h5ReturnUrl: https://m.myazgo.com/order/pay-success
  unionpay.wap.token.verifyCerDir: e:/nfs/dev/logs/terminus-web
  unionpay.pc.token.verifyCerDir: e:/nfs/dev/logs/terminus-web
  unionpay.app.token.verifyCerDir: e:/nfs/dev/logs/terminus-web
  certFilePath: e:/nfs/dev/certFiles/terminus-web

pay.mockpay.token:
  notifyUrl: http://webdev.parana.yang800.cn:10081/api/order/paid
  returnUrl: http://webdev.parana.yang800.cn:10091/buyer/trade-success
  refundNotifyUrl: http://webdev.parana.yang800.cn:10081/api/refund/notify

wechat.applet:
  enable: true
  mchId: **********
  appId: wx86fe5657944bcaa2
  secret: 8ac1cbf290b0fe98ff1142acf94e7351

alipay:
    pid: ****************
    key: pil5t34m4qsdor9ujuffkqfvrgfjt3mp
    account: <EMAIL>

cache.duration.in.minutes: 1

settle.enable: true

msg:
  current:
    smsService: aliYunSmsService
    emailService: javaxEmailService
  aliyun:
    appKey: LTAIWWKkoh4OumKF
    appSecret: hPAK2215S6ZZ7uGw974hXD8tGw5Mp9
  javaxemail:
    mailServerHost: smtp.exmail.qq.com
    mailServerPort: 465
    fromAddress: Y800<<EMAIL>>
    userName: <EMAIL>
    password: Dd111111

msg.template.list:
  - key: sms.user.login.code
    title: 登录验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_158545082","smsParam":{"code":"{{code}}"}}
  - key: sms.user.register.code
    title: 注册验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_158545078","smsParam":{"code":"{{code}}"}}
  - key: sms.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_158490334","smsParam":{"code":"{{code}}"}}
  - key: sms.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_158490349","smsParam":{"code":"{{code}}"}}
  - key: sms.foreign.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_161570257","smsParam":{"code":"{{code}}"}}
  - key: sms.foreign.user.register.code
    title: 注册验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_161575124","smsParam":{"code":"{{code}}"}}
  - key: sms.foreign.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_161575129","smsParam":{"code":"{{code}}"}}
  - key: sms.foreign.we.shop.wallet.set.password
    title: 设置提现密码验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_161570250","smsParam":{"code":"{{code}}"}}
  - key: sms.foreign.user.login.code
    title: 登录验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_161570255","smsParam":{"code":"{{code}}"}}
  - key: sms.payment.identity.fail
    title: 订单实名验证失败
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_162198348","smsParam":{"name":"{{name}}","orderSn":"{{orderSn}}"}}
  - key: sms.foreign.payment.identity.fail
    title: 订单实名验证失败
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_162198356","smsParam":{"name":"{{name}}","orderSn":"{{orderSn}}"}}

msg.email:
  templates:
    - key: email.error.notice
      templateName: sendEmailErrorNotice
      subject: "澳新购商城 - 异常通知"
    - key: email.payment.pushError
      templateName: sendEmailErrorNotice
      subject: "澳新购商城 - 订单推送通知"

# open api配置开始
enable.open.api: true
# open api配置结束

oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  appKey: LTAI5tNGqjjEfJ1n3xbV42v9
  appSecret: ******************************
  bucketName: dante-img

image:
  base.url: https://dante-img.oss-cn-hangzhou.aliyuncs.com
  upload.allowedFileTypes: jpeg,jpg,gif,png
  protocol: https
  domain: dante-img.oss-cn-hangzhou.aliyuncs.com

enable.shop.search: false
enable.item.search: true

Y800:
  partnerCode: websc
  partnerKey: websc
  sourcePlatform: WEBSC
  sourcePlatformV3: WEBSC
  merchantCode:
  wd.support: http://192.168.199:6086
  #wd.support: http://**************:6086
  yang.support: http://**************:8083
  #yang.support: http://**************:8083
  finance.project.url: http://127.0.0.1:8080/xhr
nio:
  post.url: http://127.0.0.1:8091/xhr

aoxin:
  gateway: http://************:61/API

# 支付宝app支付
pay.alipay.app.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
pay.alipay.app.account.list:
  - accountNo: default
    accountName: default app account
    pid: ${alipay.pid}
    account: ${alipay.account}
    md5Key: ${alipay.key}
#    rsaPriKey: ${alipay.merchant.privateKey}
#    rsaPubKey: ${alipay.publicKey}
pay.job.trans.alipay.app.enable: true

# 支付宝PC支付
pay.alipay.pc.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
pay.alipay.pc.account.list:
  - accountNo: default
    accountName: default app account
    pid: ${alipay.pid}
    account: ${alipay.account}
    md5Key: ${alipay.key}
#    rsaPriKey: ${alipay.merchant.privateKey}
#    rsaPubKey: ${alipay.publicKey}
pay.job.trans.alipay.pc.enable: true

#支付宝WAP支付
pay.alipay.wap.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.h5ReturnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
pay.alipay.wap.account.list:
  - accountNo: default
    accountName: default app account
    pid: ${alipay.pid}
    account: ${alipay.account}
    md5Key: ${alipay.key}
#    rsaPriKey: ${alipay.merchant.privateKey}
#    rsaPubKey: ${alipay.publicKey}
pay.job.trans.alipay.wap.enable: true

#微信APP支付
pay.wechatpay.app.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
  caFilePath: e:/nfs/dev/certFiles/terminus-web/default/apiclient_cert_app.p12
pay.wechatpay.app.account.list:
  - accountNo: default
    accountName: default app account
    mchId: ${wechat.applet.mchId}
    appId: ${wechat.applet.appId}
    partnerKey: ${wechat.applet.secret}
    certFilePath: /nfs/certFiles/terminus-web/default/apiclient_cert.p12
pay.job.trans.wechatpay.app.enable: true

#微信JSAPI支付(公众号支付)
pay.wechatpay.jsapi.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.h5ReturnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
  caFilePath: e:/nfs/dev/certFiles/terminus-web/default/apiclient_cert_app.p12
pay.wechatpay.jsapi.account.list:
  - accountNo: default
    accountName: default app account
    mchId: ${wechat.applet.mchId}
    appId: ${parana.wxa.appId}
    partnerKey: ${wechat.applet.secret}
    certFilePath: /nfs/certFiles/terminus-web/default/apiclient_cert.p12
pay.job.trans.wechatpay.jsapi.enable: true

#微信扫码支付(Native支付)
pay.wechatpay.qr.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
  caFilePath: e:/nfs/dev/certFiles/terminus-web/default/apiclient_cert_app.p12
pay.wechatpay.qr.account.list:
  - accountNo: default
    accountName: default app account
    mchId: ${wechat.applet.mchId}
    appId: ${wechat.applet.appId}
    partnerKey: ${wechat.applet.secret}
    certFilePath: /nfs/certFiles/terminus-web/default/apiclient_cert.p12
pay.job.trans.wechatpay.qr.enable: true

#微分销商城前台域名
m.mall.yang800: https://m.myazgo.com

#功能开关
function:
  switch:
    aoXinPush: true
    financePush: false
    unifiedPayment: false
    foreignSms: true
    editPrivilegeRelevel: true
    thirdPartyStockLimit: false
    allowCreateItem: false

mercury:
  pay:
    host: http://127.0.0.1:8080
    appCode: AXFXSC
    merchantCode: M2019030616533627870
    customs:
      notify: http://127.0.0.1:10082/api/customs/payment/declare/notify

ebpCode: 3301964J31
