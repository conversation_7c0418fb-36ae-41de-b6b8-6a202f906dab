application:
  name: supply-shop-front

environment: online

logging:
  #  file: /nfs/terminus-web/terminus-web.log
  #  level.*: INFO
  #  level.io.terminus: DEBUG
  config: classpath:config/online/logback.xml

ext.messages.classpath: messages_zh_CN
app:
  export:
    cert: true
soul:
  http:
    adminUrl: http://soul-admin.yang800.com
    contextPath: /shop
    appName: shop
  dubbo:
    adminUrl: http://soul-admin.yang800.com
    contextPath: /shop
    appName: shop
dubbo:
  application:
    name: parana-web
  port: 20880
  address: ${nacos.discovery.serverAddr}
  registry:
    protocol: nacos
    address: ${nacos.discovery.serverAddr}
nacos:
  discovery:
    serverAddr: mse-c5aa9303-nacos-ans.mse.aliyuncs.com:8848

ucenter:
  url: http://ucenter-server.services--6c9081051e4a0943253a15a5a5b75c81.svc.cluster.local:8080/sdk-api/gateway
  systemCode: PARANA_WEB
  appKey: PARANA_WEB_online_appKey
  appSecret: online_appSecret
  packages:
mybatis:
  mapperLocations: classpath*:mapper/*Mapper.xml
  type-aliases-package: >
    moonstone.(item|category|brand|shop|spu|user|cart|order|express|promotion|delivery|file|settle|membership|countryImage|integral).model,
    moonstone.(adv|thirdParty|shopWxa|wxa).model,
    moonstone.(weCart|weShop|weDistributionApplication).model,
    moonstone.user.address.model,
    moonstone.user.area.model,
    moonstone.pay.mock.model,
    moonstone.auth.model
  typeHandlersPackage: moonstone.common.type.handler

server:
  main:
    allow-bean-definition-overriding: true
  context-path: /
  port: 8080

hazelcast:
  config: hazelcast-config.xml
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************
    #    url: *******************************************************************************************************************************
    username: y800
    password: Danding$%654
  data:
    mongodb:
      uri: mongodb://root:<EMAIL>:3717,dds-bp10fd1c07b1aac42.mongodb.rds.aliyuncs.com:3717/parana-online

  http:
    converters:
      preferred-json-mapper: gson
    multipart:
      max-file-size: 50Mb
      max-request-size: 50Mb
parana:
  app_backend:
    url: http://mall.yang800.com/backend/
  web:
    url: http://mall.yang800.com
  mall:
    url: http://mall.yang800.com
  h5:
    url: https://m.mall.yang800.com
  wxa:
    url: https://m.mall.yang800.com
    appId: wx90e48a315747ccb9
    appSecret: ebb83ced7885897e4fd5ecfc403ed92d
  we:
    seller:
      url: https://m.mall.yang800.com
      appId: wx0fb538f6dc093892
      appSecret: 5d260bcf67eecf19e96a341b7b4eb95a
    buyer:
      url: https://m.mall.yang800.com
      appId: wxb25691b47b974630
      appSecret: 088dedd8ec3382f5ba981a2560afc187
  wxopen:
    componentAppId: wxfb7d4a7ab73f6ba6
    componentSecret: a9a41dcfd8e7b564a2ec93c0ee58fb9f
    componentToken: wx569abcf3c77ff1de
    componentAesKey: 0987654321qazwsxedcrfvtgbyhnujmik1234567890
    requestdomain: https://m.mall.yang800.com;https://www.yang800.com;https://m.yang800.com
    wsrequestdomain: wss://m.mall.yang800.com;wss://www.yang800.com
    uploaddomain: https://m.mall.yang800.com;https://www.yang800.com
    downloaddomain: https://m.mall.yang800.com;https://www.yang800.com
  substore:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3
  levelDistribution:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3
https://m:
  mall:
    yang800:
      com:
search:
  host: terminus-search-svc
  port: 9200

item.search:
  index-name: items
  index-type: item
  mapping-path: item_mapping.json
  full-dump-range: 3000
  batch-size: 100

shop.search:
  index-name: shops
  index-type: shop
  mapping-path: shop_mapping.json
  full-dump-range: 3000
  batch-size: 100

weShopItem.search:
  index-name: t_we_shop_items
  index-type: we_shop_item
  mapping-path: we_shop_item_mapping.json
  full-dump-range: 3000
  batch-size: 100

session:
  cookie-domain: none
  cookie-context-path: /
  cookie-name: msid
  cookie-max-age: 360000
  source: redis
  serialize-type: json
  redis-host: parana-redis-svc
  redis-port: 6379
  redis-index: 0
  redis-cluster: false
  redis-test-on-borrow: true
  redis-max-total: 10
  redis-max-idle: 0
  redis-prefix: afsession
  redis-auth: dd654321
  max-inactive-interval: 360000
  # use test env for now test on test env
  jwt_key: WEB_ONLINE_lMhtTYFDEGV93OYnsbHYm79zB2NEje4h
  jwt_name: online_app_token

oms:
  api:
    admin: http://pangu-admin

#curator:
#  zk.host: 127.0.0.1
#  zk.port: 2181

express.100:
  key: bAWWUdnn9775_old
  customer: 837CEA86CF64D4FEC5B4CEC59589E2D5_old
  regularUrl: http://poll.kuaidi100.com/poll/query.do

# ip地址库目录
ip.dic: data/ip.txt

wechat:
  mp:
    appId: wx86fe5657944bcaa2
    secret: f72b211010c13e17141accd17fdbb23b
  authorize:
    redirectUrl: http://mall-new.yang800.com/api/wechat/auth/jump
    resultUrl: http://mall-new.yang800.com
  applet:
    enable: true
    mchId: 1488913512
    appId: wx86fe5657944bcaa2
    secret: 8ac1cbf290b0fe98ff1142acf94e7351

pay:
  xinbada:
    url: https://open.sinbaad.com
  easy-pay:
    gateway: https://newpay.bhecard.com/api_gateway.do
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2WTfvas1JvvaRuJWIKmKlBLmkRvr2O7Fu3k/zvhJs+X1JQorPWq/yZduY6HKu0up7Qi3T6ULHWyKBS1nRqhhHpmLHnI3sIO8E/RzNXJiTd9/bpXMv+H8F8DW5ElLxCIVuwHBROkBLWS9fIpslkFPt+r13oKFnuWhXgRr+K/YkJQIDAQAB
  debug: false
  channel:
    alipay: enable
  notifyUrl: http://mall.yang800.com/backend/api/order/paid
  refundNotifyUrl: http://mall.yang800.com/backend/api/refund/notify
  returnUrl: http://mall.yang800.com/buyer/trade-success
  h5ReturnUrl: https://m.mall.yang800.com/order/pay-success
  unionpay.wap.token.verifyCerDir: /nfs/terminus-web/
  unionpay.pc.token.verifyCerDir: /nfs/terminus-web/
  unionpay.app.token.verifyCerDir: /nfs/terminus-web/
  certFilePath: /nfs/certFiles/terminus-web

pay.mockpay.token:
  notifyUrl: http://mall.yang800.com/backend/api/order/paid
  returnUrl: http://mall.yang800.com/buyer/trade-success
  refundNotifyUrl: http://mall.yang800.com/backend/api/refund/notify


pay.integral.token:
  returnUrl: http://mall.yang800.com/buyer/trade-success
  refundNotifyUrl: http://mall.yang800.com/backend/api/refund/notify

alipay:
  pid: ****************
  key: pil5t34m4qsdor9ujuffkqfvrgfjt3mp
  account: <EMAIL>

cache.duration.in.minutes: 10

settle:
  enable: true
  listener:
    enable: false

msg:
  current:
    smsService: aliYunSmsService
    emailService: javaxEmailService
  aliyun:
    appKey: LTAIMbTR37BKlN62
    appSecret: pfyIGNaXWhAUKdjD5FybkGQedZGXg1
  javaxemail:
    mailServerHost: smtp.exmail.qq.com
    mailServerPort: 465
    fromAddress: Y800<<EMAIL>>
    userName: <EMAIL>
    password: Dd111111

msg.template.list:
  - key: sms.28.user.login.code
    title: 登录验证
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168826876","smsParam":{"code":"{{code}}"}}
  - key: sms.user.login.code
    title: 登录验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_145495759","smsParam":{"code":"{{code}}"}}
  - key: sms.28.user.register.code
    title: 注册验证
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168821990","smsParam":{"code":"{{code}}"}}
  - key: sms.user.register.code
    title: 注册验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143863719","smsParam":{"code":"{{code}}"}}
  - key: sms.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868717","smsParam":{"code":"{{code}}"}}
  - key: sms.28.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168821992","smsParam":{"code":"{{code}}"}}
  - key: sms.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868720","smsParam":{"code":"{{code}}"}}
  - key: sms.28.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168826869","smsParam":{"code":"{{code}}"}}
  - key: sms.28.user.action
    title: 通用验证码
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168821997","smsParam":{"code":"{{code}}"}}
  - key: sms.user.action
    title: 通用验证码
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_166865728","smsParam":{"code":"{{code}}","name":"{{name}}","operation":"{{operation}}"}}
  - key: sms.user.set.withdraw.password
    title: 设置提现密码验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_153325369","smsParam":{"code":"{{code}}"}}
  - key: sms.28.user.set.withdraw.password
    title: 通用验证码
    content: >
      {"smsName":"港版皇家","smsTemplate":"SMS_168821997","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_170840997","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.set.withdraw.password
    title: 设置提现密码验证
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_170835997","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.register.code
    title: 注册验证
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_176532281","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_170835976","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.login.code
    title: 登录验证
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_176537313","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.set.withdraw.password
    title: 通用验证码
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_170840987","smsParam":{"code":"{{code}}"}}
  - key: sms.33.user.auth.apply
    title: 门店审核通知
    content: >
      {"smsName":"贝拉米商城","smsTemplate":"SMS_171856413","smsParam":{"p_name":"{{p_name}}","c_name":"{{c_name}}"}}
  - key: withdraw.certification.identify
    title: 提实实名审核
    content: >
      {"smsName":"奥利派商场","smsTemplate":"SMS_201455761","smsParam":{"code":"{{code}}"}}

msg.email:
  templates:
    - key: email.error.notice
      templateName: sendEmailErrorNotice
      subject: "但丁商城 - 异常通知"

# open api配置开始
enable.open.api: true

oss:
  endpoint: oss-cn-hangzhou-internal.aliyuncs.com
  appKey: LTAI5tNGqjjEfJ1n3xbV42v9
  appSecret: ******************************
  bucketName: dante-img

image:
  base.url: https://dante-img.oss-cn-hangzhou.aliyuncs.com
  upload.allowedFileTypes: jpeg,jpg,gif,png,mp4,webp
  protocol: https
  domain: dante-img.oss-cn-hangzhou.aliyuncs.com
# open api配置结束

enable.shop.search: true
enable.item.search: true

Y800:
  partnerCode: webB2C
  partnerKey: webB2C123
  sourcePlatform: WEBSC
  merchantCode:
  wd.support: http://wd.api.yang800.com
  yang.support: http://support.yang800.com
  finance.project.url: http://statics.yang800.com/xhr
  open.api.gate: ${Y800.yang.support}/api/
nio:
  post.url: http://sync.yang800.com/xhr

aoxin:
  gateway: http://www.kuajing.com.au/API

# 支付宝app支付
pay.alipay.app.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
pay.alipay.app.account.list:
  - accountNo: default
    accountName: default app account
    pid: ${alipay.pid}
    account: ${alipay.account}
    md5Key: ${alipay.key}
#    rsaPriKey: ${alipay.merchant.privateKey}
#    rsaPubKey: ${alipay.publicKey}
pay.job.trans.alipay.app.enable: true

# 支付宝PC支付
pay.alipay.pc.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
pay.alipay.pc.account.list:
  - accountNo: default
    accountName: default app account
    pid: ${alipay.pid}
    account: ${alipay.account}
    md5Key: ${alipay.key}
#    rsaPriKey: ${alipay.merchant.privateKey}
#    rsaPubKey: ${alipay.publicKey}
pay.job.trans.alipay.pc.enable: true

#支付宝WAP支付
pay.alipay.wap.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.h5ReturnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
pay.alipay.wap.account.list:
  - accountNo: default
    accountName: default app account
    pid: ${alipay.pid}
    account: ${alipay.account}
    md5Key: ${alipay.key}
#    rsaPriKey: ${alipay.merchant.privateKey}
#    rsaPubKey: ${alipay.publicKey}
pay.job.trans.alipay.wap.enable: true

#微信APP支付
pay.wechatpay.app.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
  caFilePath: /nfs/certFiles/terminus-web/default/apiclient_cert_app.p12
pay.wechatpay.app.account.list:
  - accountNo: default
    accountName: default app account
    mchId: ${wechat.applet.mchId}
    appId: ${wechat.applet.appId}
    partnerKey: ${wechat.applet.secret}
pay.job.trans.wechatpay.app.enable: true

#微信JSAPI支付(公众号支付)
pay.wechatpay.jsapi.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.h5ReturnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
  caFilePath: /nfs/certFiles/terminus-web/default/apiclient_cert_app.p12
pay.wechatpay.jsapi.account.list:
  - accountNo: default
    accountName: default app account
    mchId: ${wechat.applet.mchId}
    appId: ${parana.wxa.appId}
    partnerKey: ${wechat.applet.secret}
pay.job.trans.wechatpay.jsapi.enable: true

#微信扫码支付(Native支付)
pay.wechatpay.qr.token:
  notifyUrl: ${pay.notifyUrl}
  returnUrl: ${pay.returnUrl}
  refundNotifyUrl: ${pay.refundNotifyUrl}
  caFilePath: /nfs/certFiles/terminus-web/default/apiclient_cert_app.p12
pay.wechatpay.qr.account.list:
  - accountNo: default
    accountName: default app account
    mchId: ${wechat.applet.mchId}
    appId: ${wechat.applet.appId}
    partnerKey: ${wechat.applet.secret}
pay.job.trans.wechatpay.qr.enable: true

#微分销商城前台域名
m.mall.yang800: https://m.mall.yang800.com


#积分商城生成二维码的路径
m.mall.jifen.path: /nfs/certFiles/terminus-web/levelDistribution/image/
#积分商城生成二维码的url
m.mall.jifen.url: https://m.bellamy.shop

#功能开关
function:
  switch:
    aoXinPush: false
    financePush: true
    unifiedPayment: true

mercury:
  pay:
    host: http://ccs.order.yang800.com/mercury/pay/proxyAPI
    appCode: DDFXSC
    merchantCode: M2019032719271231031
    customs:
      notify: http://admin.mall.yang800.com//backend/api/customs/payment/declare/notify

ebpCode: 3301964J31

wx:
  backend:
    url: https://api.weixin.qq.com/cgi-bin

y800:
  gateApi: http://out.order.yang800.com/open/apiv3
gx.api.mall: http://supply-user.yang800.com
