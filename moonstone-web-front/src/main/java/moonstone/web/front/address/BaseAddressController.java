package moonstone.web.front.address;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.exception.ApiException;
import moonstone.common.mongo.BaseAddress;
import moonstone.web.core.fileNew.vo.BaseAddressVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 地址库相关接口
 * author：书生
 */
@RestController
@RequestMapping("/api/baseAddress")
@Slf4j
public class BaseAddressController {


    @Autowired
    private MongoTemplate mongoTemplate;

    @Value("${address.base.host}")
    private String host;

    @Value("${address.base.path}")
    private String path;

    @Value("${address.base.street-path}")
    public String streetPath;

    /**
     * 更新地址库
     */
    @GetMapping("update")
    public Result<String> updateBaseAddress() {
        log.info("更新地址库");
        // 删除旧的地址库
        String COLLECTION_NAME = "baseAddress";
        if (mongoTemplate.collectionExists(COLLECTION_NAME)) {
            mongoTemplate.dropCollection(COLLECTION_NAME);
            log.info("删除旧的地址库已完成");
        }
        String url = host + path;
        log.info("更新地址库的url {}", url);
        Map<String,String> headers = new HashMap<>();
        headers.put("clientId", "9E514E70AD7D485986D687F64616C662");
        headers.put("sign", RandomUtil.randomString(10));
        headers.put("timestamp", String.valueOf(System.currentTimeMillis()));
        HttpRequest request = HttpRequest.get(url).addHeaders(headers);
        try (HttpResponse response = request.execute()) {
            if (!response.isOk()) {
                log.error("更新地址库失败 失败原因 {}", response.body());
            }
            String body = response.body();
            log.info("获取到的数据 {}", body);
            JSONObject entries = JSONUtil.parseObj(body);
            String baseAddressInfo = entries.get("data", String.class);
            List<BaseAddress> baseAddresses = JSONUtil.toList(baseAddressInfo, BaseAddress.class);
            mongoTemplate.insert(baseAddresses, COLLECTION_NAME);
        } catch (Exception e) {
            log.error("更新地址库失败 失败原因 {}", e.getMessage(), e);
            throw new ApiException("更新地址库失败 " + e.getMessage());
        }
        return Result.success("更新地址库成功");
    }

    /**
     * 获取所有地址库列表
     *
     * @return 所有地址库列表
     */
    @GetMapping("/all/list")
    public Result<List<BaseAddressVo>> allList() {
        log.info("获取所有地址库");
        List<BaseAddress> allAddressList = mongoTemplate.find(Query.query(new Criteria()), BaseAddress.class);
        return Result.data(BeanUtil.copyToList(allAddressList, BaseAddressVo.class));
    }

    /**
     * 获取所有省份信息
     */
    @GetMapping("/province")
    public Result<List<BaseAddressVo>> provinceList() {
        log.info("获取所有省份信息");
        List<BaseAddress> allAddressList = mongoTemplate.find(Query.query(new Criteria().and("level").is(1)), BaseAddress.class);
        return Result.data(convertAndFilter(allAddressList,false));
    }

    private List<BaseAddressVo> convertAndFilter(List<BaseAddress> allAddressList,Boolean isFilter) {
        List<BaseAddressVo> result = new ArrayList<>();
        for (BaseAddress baseAddress : allAddressList) {
            BaseAddressVo entity = BeanUtil.copyProperties(baseAddress, BaseAddressVo.class);
            if (isFilter) {
                if (entity.getAreaName().equals("其它区")) {
                    continue;
                }
            }
            long count = mongoTemplate.count(Query.query(new Criteria().and("parentId").is(entity.getId())), BaseAddress.class);
            // 判断是否是叶子节点 前端组件需要 取反
            entity.setLeaf(!(count > 0));
            result.add(entity);
        }
        return result;
    }

    /**
     * 根据父级id获取子级地址列表
     */
    @GetMapping("/children")
    public Result<List<BaseAddressVo>> childrenList(@RequestParam("parentId") String parentId,@RequestParam("isFilter") Boolean isFilter) {
        log.info("根据父级id获取子级地址列表 请求参数 parentId {} isFilter {}", parentId,isFilter);
        List<BaseAddress> allAddressList = mongoTemplate.find(Query.query(new Criteria().and("parentId").is(parentId)), BaseAddress.class);
        return Result.data(convertAndFilter(allAddressList,isFilter));
    }


    /**
     * 根据地区编码获取街道列表
     *
     * @param parentId 地区父级id
     * @return 街道列表
     */
    @GetMapping("/street")
    public Result<List<BaseAddressVo>> streetList(@RequestParam("parentId") String parentId) {
        log.info("根据地区父级id获取街道列表 地区编码 {}", parentId);
        String url = host + streetPath;
        log.info("根据地区父级id获取街道列表url {}", url);
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId", "9E514E70AD7D485986D687F64616C662");
        headers.put("sign", RandomUtil.randomString(10));
        headers.put("timestamp", String.valueOf(System.currentTimeMillis()));
        Map<String, Object> body = new HashMap<>();
        body.put("parentId", parentId);
        HttpRequest request = HttpRequest.post(url).addHeaders(headers).body(JSONUtil.toJsonStr(body));
        try (HttpResponse response = request.execute()) {
            if (!response.isOk()) {
                log.error("根据地区父级id获取街道列表失败 失败原因 {}", response.body());
                throw new RuntimeException("未获取到街道信息，请稍后重试");
            }
            String responseBody = response.body();
            log.info("根据地区父级id获取街道列表 获取到的数据 {}", responseBody);
            JSONObject entries = JSONUtil.parseObj(responseBody);
            String baseAddressInfo = entries.get("data", String.class);
            return Result.data(JSONUtil.toList(baseAddressInfo, BaseAddressVo.class));
        } catch (Exception e) {
            log.error("根据地区父级id获取街道列表失败 失败原因 {}", e.getMessage(), e);
            throw new RuntimeException("未获取到街道信息，请稍后重试");
        }
    }

}
