/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.cart;

import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.ModelHelper;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import moonstone.cart.dto.RichCart;
import moonstone.cart.dto.RichCartItem;
import moonstone.cart.model.CartItem;
import moonstone.cart.service.CartReadService;
import moonstone.cart.service.CartWriteService;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.component.item.component.TaxChecker;
import moonstone.component.membership.component.MembershipPriceChecker;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.dto.CartsModel;
import moonstone.order.rule.SkuItemLimitChecker;
import moonstone.web.front.component.cart.CartReader;
import moonstone.web.front.component.promotion.CartPromotionComposer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (c) 2015 杭州端点网络科技有限公司
 * Date: 3/10/16
 * Time: 5:29 PM
 * Author: 2015年 <a href="mailto:<EMAIL>">张成栋</a>
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class Carts {

    @RpcConsumer
    private CartReadService cartReadService;

    @RpcConsumer
    private CartWriteService cartWriteService;

    @RpcConsumer
    private SkuReadService skuReadService;

    @RpcConsumer
    private CartReader cartReader;

    @RpcConsumer
    private MembershipPriceChecker membershipPriceChecker;

    @RpcConsumer
    private ItemReadService itemReadService;

    @Autowired
    private TaxChecker taxChecker;

    @Autowired
    private CartPromotionComposer cartPromotionComposer;

    /**
     * 获取当前登录用户的永久购物车
     *
     * @return 购物车信息
     */
    @RequestMapping(value = "/user/cart", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<RichCart> findByUser() {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Response<List<RichCart>> findResp = cartReader.findByUser(commonUser);
        if (!findResp.isSuccess()) {
            log.error("fail to find cart by user:{},cause:{}",
                    commonUser, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        List<RichCart> richCarts = findResp.getResult();

        for (RichCart richCart : richCarts) {
            for (RichCartItem richCartItem : richCart.getCartItems()) {
                membershipPriceChecker.check(richCartItem.getSku(), commonUser.getId());
                try {
                    Long tax = taxChecker.getTax(richCartItem.getSku(), 1);
                    richCartItem.setTax(tax);
                }catch (Exception taxEx){
                    log.warn("Tax timeout at sku: {}", richCartItem.getSku().getId(), taxEx);
                }
            }
        }

        cartPromotionComposer.composePromotions(commonUser, richCarts);
        return richCarts;
    }
    /**
     * 支持json
     */
    @RequestMapping(value = "/cartsNew",method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Integer changeCartJson(@RequestBody CartsModel cartsNew) {
        log.info("[cartsNew] quantity => [{}]", cartsNew.getQuantity());
        return changeCart(cartsNew.getSkuId(),cartsNew.getQuantity(),"");
    }


    /**
     * 登录用户修改购物车商品的数量,若添加或者减少商品数量后,
     * 最终数量小于0,就将商品从购物车中删除.
     *
     * @param skuId    商品id
     * @param quantity 商品数量,可以为负数
     * @return 最终购物车商品的数量
     */
    @RequestMapping(value = "/carts", method = RequestMethod.POST)
    @ResponseBody
    public Integer changeCart(
            @RequestParam("skuId") Long skuId,
            @RequestParam(value = "quantity", defaultValue = "1", required = false) Integer quantity,
            @RequestParam(value = "buyerNote", required = false) String buyerNote
    ) {
        Long userId = UserUtil.getUserId();
        // 获取商品详情
        Response<Sku> findSku = skuReadService.findSkuById(skuId);
        if (!findSku.isSuccess()) {
            log.error("when changing cart, fail to find sku(id={}) for user(id={}), cause:{}",
                    skuId, UserUtil.getUserId(), findSku.getError());
            throw new JsonResponseException(findSku.getError());
        }

        // 检查商品是否上架
        Sku sku = findSku.getResult();
        if (!Objects.equals(sku.getStatus(), 1)) {
            throw new JsonResponseException("item.not.available");
        }

        // 检查库存
        Response<List<CartItem>> rCartItems = cartReadService.findByUserIdAndSkuId(userId, skuId);
        if (!rCartItems.isSuccess()) {
            log.error("failed to list cart by skuId={} for user(id={}), error code: {}", skuId, userId, rCartItems.getError());
            throw new JsonResponseException(rCartItems.getError());
        }
        List<CartItem> cartItems = rCartItems.getResult();
        Integer cartItemQuantity = 0;
        if (!cartItems.isEmpty()) {
            cartItemQuantity = cartItems.get(0).getQuantity();
        }
        if (MoreObjects.firstNonNull(sku.getStockQuantity(), 0) - quantity - cartItemQuantity < 0) {
            throw new JsonResponseException("stock.empty");
        }
        //限制购买数量
        SkuItemLimitChecker.checkBuyLimit(sku, cartItemQuantity + quantity);
        //TODO 如果澳新商品限制实际数量6件
        Response<Item> rItem = itemReadService.findById(sku.getItemId());
        if (!rItem.isSuccess()) {
            log.error("failed to find item by id={}, error code: {}", sku.getItemId(), rItem.getError());
            throw new JsonResponseException(rItem.getError());
        }
        // 更改购物车
        Response<Integer> tryChange = cartWriteService.changeCart(sku, quantity, userId, buyerNote);
        if (!tryChange.isSuccess()) {
            log.error("fail to change cart by skuId={}, quantity={}, userId={}, error code:{}",
                    skuId, quantity, userId, tryChange.getError());
            throw new JsonResponseException(tryChange.getError());
        }
        return tryChange.getResult();
    }

    /**
     * 返回当前登录用户购物车内的商品总数
     *
     * @return 购物车内商品总数
     */
    @ResponseBody
    @RequestMapping(value = "/carts/count", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Integer count() {
        if (UserUtil.getCurrentUser() == null) {
            return 0;
        }

        Response<Integer> getCount = cartReadService.count(UserUtil.getUserId(), null);
        if (!getCount.isSuccess()) {
            log.error("fail to get cart item quantity count for user:{}, cause:{}",
                    UserUtil.getUserId(), getCount.getError());
            throw new JsonResponseException(getCount.getError());
        }
        return getCount.getResult();
    }

    /**
     * 批量根据sku id删除购物车商品
     *
     * @param skuData 使用逗号分隔的 sku id 字符串
     */
    @ResponseBody
    @RequestMapping(value = "/carts/batchDelete", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean batchDelete(@RequestParam("skuIds") String skuData) {
        Long userId = UserUtil.getUserId();

        // 获取sku id列表
        @SuppressWarnings("UnstableApiUsage") List<String> stringSkuIds = Splitters.COMMA.splitToList(skuData);
        List<Long> skuIds = new ArrayList<>();
        for (String skuId : stringSkuIds) {
            skuIds.add(Long.valueOf(skuId));
        }

        if (!skuIds.isEmpty()) {
            Response<Boolean> tryDelete = cartWriteService.batchDelete(skuIds, userId);
            if (!tryDelete.isSuccess()) {
                log.error("fail to batch delete permanent skuIds={}, userId={},error code={}",
                        skuIds, userId, tryDelete.getError());
                throw new JsonResponseException(tryDelete.getError());
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 清除购物车内失效商品
     *
     * @return 被清除的商品
     */
    @RequestMapping(value = "/carts/out-date", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<Long> cleanOutDateSku() {
        // 获取用户购物车所有商品
        Response<List<CartItem>> listCart = cartReadService.findByUserId(UserUtil.getUserId());
        if (!listCart.isSuccess()) {
            log.error("fail to list cart item by user(id={}), cause:{}", UserUtil.getUserId(), listCart.getError());
        }

        List<CartItem> cartItems = listCart.getResult();
        if (cartItems.isEmpty()) {
            return Collections.emptyList();
        }

        // find and filter sku by status 1
        List<Long> skuIds = ModelHelper.extractFiled(cartItems, "skuId");
        Response<List<Sku>> findSkus = skuReadService.findSkusByIds(skuIds);
        if (!findSkus.isSuccess()) {
            log.error("fail to find sku by ids({}), cause:{}", skuIds, findSkus.getError());
            return Collections.emptyList();
        }

        List<Sku> skuList = findSkus.getResult();
        List<Sku> available = Lists.newArrayList();
        for (Sku sku : skuList) {
            if (Objects.equals(sku.getStatus(), 1)) {
                available.add(sku);
            }
        }

        // 如果有不可用商品,从购物删除,并返回它们的id
        if (available.size() != skuIds.size() && !available.isEmpty()) {
            List<Long> availableIds = ModelHelper.extradIds(available);
            skuIds.removeAll(availableIds);

            Response<Boolean> tryDelete = cartWriteService.batchDelete(skuIds, UserUtil.getUserId());
            if (!tryDelete.isSuccess()) {
                log.error("fail to delete cart items by skuIds({}), cause:{}", skuIds, tryDelete.getError());
                return Collections.emptyList();
            }
            return skuIds;
        }

        // 所有商品都可用
        return Collections.emptyList();
    }
}
