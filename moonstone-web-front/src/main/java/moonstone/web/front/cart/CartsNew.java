package moonstone.web.front.cart;

import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.ModelHelper;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.cart.dto.RichCart;
import moonstone.cart.dto.RichCartItem;
import moonstone.cart.model.CartItem;
import moonstone.cart.service.CartReadService;
import moonstone.cart.service.CartWriteService;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.*;
import moonstone.component.item.component.TaxChecker;
import moonstone.component.membership.component.MembershipPriceChecker;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.dto.CartsModel;
import moonstone.order.rule.SkuItemLimitChecker;
import moonstone.web.front.component.cart.CartReader;
import moonstone.web.front.component.promotion.CartPromotionComposer;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Date: 2019/7/31 14:07
 */
@Slf4j
@RestController
@RequestMapping("/api/new")
public class CartsNew {
    @Autowired
    private CartReadService cartReadService;

    @Autowired
    private CartWriteService cartWriteService;

    @Autowired
    private SkuReadService skuReadService;

    @Autowired
    private CartReader cartReader;

    @Autowired
    private MembershipPriceChecker membershipPriceChecker;

    @Autowired
    private ItemReadService itemReadService;

    @Autowired
    private TaxChecker taxChecker;

    @Autowired
    private CartPromotionComposer cartPromotionComposer;

    private final int CART_COUNT_MAX = 99;
    /**
     * 获取当前登录用户的永久购物车
     *
     * @return 购物车信息
     */
    @RequestMapping(value = "/user/cart", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public R findByUser(@RequestParam(required = false) Long shopId, @RequestParam(required = false) Long weShopId) {//兼容单店铺和多店铺的购物车
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();

            if (commonUser == null) {
                return R.ok().add("code", "-1").add("msg", "未登录！！");
            }
            Long currentShopId = Optional.ofNullable(shopId).orElseGet(commonUser::getShopId);
            List<RichCart> richCarts = Optional.ofNullable(cartReader.findByUserAndShopId(commonUser, currentShopId).getResult())
                    .orElseThrow(() -> Translate.exceptionOf("购物车查找失败"));
            // recheck the price and tax
            for (RichCart richCart : richCarts) {
                richCart.setWeShopId(weShopId);
                for (RichCartItem richCartItem : richCart.getCartItems()) {
                    membershipPriceChecker.check(richCartItem.getSku(), commonUser.getId());
                    Long tax = taxChecker.getTax(richCartItem.getSku(), 1);
                    richCartItem.setTax(tax);
                }
            }
            cartPromotionComposer.composePromotions(commonUser, richCarts);
            return buildCarts(richCarts);
        } catch (Exception e) {
            log.error("{} fail to query cart for shopId[{}] weShopId[{}] user[{}]", LogUtil.getClassMethodName(),
                    shopId, weShopId, UserUtil.getUserId(), e);
            return R.error().add("msg", e.getMessage());
        }
    }

    /**
     * 加入购物车
     * TODO  目前主页加入购物车，不支持多个规格，默认取itemId 对应sku的第一条
     */
    @RequestMapping(value = "/carts", method = RequestMethod.POST)
    public R changeCart(@RequestBody CartsModel cartsNew) {
        Long itemId = cartsNew.getItemId();
        Long skuId = cartsNew.getSkuId();
        Long id = cartsNew.getId();

        if (EmptyUtils.isEmpty(skuId) && EmptyUtils.isNotEmpty(itemId)) {
            Response<List<Sku>> skus = skuReadService.findSkusByItemId(itemId);
            if (skus.isSuccess() && EmptyUtils.isNotEmpty(skus.getResult())) {
                if (skus.getResult().stream().noneMatch(entity -> Objects.equals(entity.getStatus(), 1))) {
                    return R.error(-1, "该商品已下架");
                }
                skuId = skus.getResult().stream().filter(entity -> Objects.equals(entity.getStatus(), 1))
                        .findFirst().map(Sku::getId).orElse(-1L);
            }
        }
        if (Objects.equals(skuId, -1L)) {
            return R.error(-1, "该商品已下架");
        }

        Integer quantity = cartsNew.getQuantity();

        if (quantity > CART_COUNT_MAX) {
            return R.error(-1, "单品添加上限为99");
        }

        Long userId = UserUtil.getUserId();

        if (skuId == null) {
            Either<CartItem> cats = cartReadService.findById(id);
            if (!cats.isSuccess() || cats.take() == null) {
                return R.ok().add("msg", "未查询到数据！！").add("data", null);
            } else {
                skuId = cats.take().getSkuId();
            }
        }
        // 获取商品详情
        Response<Sku> findSku = skuReadService.findSkuById(skuId);
        if (!findSku.isSuccess()) {
            log.error("when changing cart, fail to find sku(id={}) for user(id={}), cause:{}",
                    skuId, UserUtil.getUserId(), findSku.getError());
            throw new JsonResponseException(findSku.getError());
        }

        // 检查商品是否上架
        Sku sku = findSku.getResult();
        if (!Objects.equals(sku.getStatus(), 1)) {
            throw new JsonResponseException("item.not.available");
        }

        // 检查库存
        Response<List<CartItem>> rCartItems = cartReadService.findByUserIdAndSkuId(userId, skuId);
        if (!rCartItems.isSuccess()) {
            log.error("failed to list cart by skuId={} for user(id={}), error code: {}", skuId, userId, rCartItems.getError());
            throw new JsonResponseException(rCartItems.getError());
        }
        List<CartItem> cartItems = rCartItems.getResult();
        Integer cartItemQuantity = 0;
        if (!cartItems.isEmpty()) {
            cartItemQuantity = cartItems.get(0).getQuantity();
        }
        if (quantity > 0) {
            if (MoreObjects.firstNonNull(sku.getStockQuantity(), 0) - quantity - cartItemQuantity < 0) {
                throw new JsonResponseException("stock.empty");
            }
            //限制购买数量
            SkuItemLimitChecker.checkBuyLimit(sku, cartItemQuantity + quantity);
        }

        Response<Item> rItem = itemReadService.findById(sku.getItemId());
        if (!rItem.isSuccess()) {
            log.error("failed to find item by id={}, error code: {}", sku.getItemId(), rItem.getError());
            throw new JsonResponseException(rItem.getError());
        }
        Item item = rItem.getResult();
        // 更改购物车
        Response<Integer> tryChange = cartWriteService.changeCart(sku, quantity, userId, "");
        if (!tryChange.isSuccess()) {
            log.error("fail to change cart by skuId={}, quantity={}, userId={}, error code:{}",
                    skuId, quantity, userId, tryChange.getError());
            throw new JsonResponseException(tryChange.getError());
        }
        return R.ok().add("data", tryChange.getResult());

    }

    /**
     * 返回当前登录用户购物车内的商品总数
     *
     * @return 购物车内商品总数
     */
    @RequestMapping(value = "/carts/count", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public R count() {
        if (UserUtil.getCurrentUser() == null) {
            return R.ok().add("msg", "未查询到数据！！").add("data", null);
        }

        Response<Integer> getCount = cartReadService.count(UserUtil.getUserId(), null);
        if (!getCount.isSuccess()) {
            log.error("fail to get cart item quantity count for user:{}, cause:{}",
                    UserUtil.getUserId(), getCount.getError());
            throw new JsonResponseException(getCount.getError());
        }
        return R.ok().add("data", getCount.getResult());
    }

    /**
     * 批量根据sku id删除购物车商品
     */
    @RequestMapping(value = "/carts/batchDelete/{id}", method = RequestMethod.GET)
    public R batchDelete(@PathVariable("id") String ido) {
        Long userId = UserUtil.getUserId();
        List<String> ids = Splitters.COMMA.splitToList(ido);
        if (ObjectUtils.isEmpty(ids) || ids.isEmpty()) {
            return R.error(-1, "缺少必要参数");
        }
        for (String d : ids) {
            if (!ObjectUtils.isEmpty(d)) {
                Long id = Long.valueOf(d);
                // 获取sku id列表
                Either<CartItem> cats = cartReadService.findById(id);
                String skuData;
                if (!cats.isSuccess() || cats.take() == null) {
                    return R.ok().add("msg", "未查询到数据！！").add("data", null);
                } else {
                    skuData = cats.take().getSkuId().toString();
                }

                // 获取sku id列表
                List<String> stringSkuIds = Splitters.COMMA.splitToList(skuData);
                List<Long> skuIds = new ArrayList<>();
                for (String skuId : stringSkuIds) {
                    skuIds.add(Long.valueOf(skuId));
                }

                if (!skuIds.isEmpty()) {
                    Response<Boolean> tryDelete = cartWriteService.batchDelete(skuIds, userId);
                    if (!tryDelete.isSuccess()) {
                        log.error("fail to batch delete permanent skuIds={}, userId={},error code={}",
                                skuIds, userId, tryDelete.getError());
                        throw new JsonResponseException(tryDelete.getError());

                    }
                }
            }
        }
        return R.ok();
    }

    /**
     * 清除购物车内失效商品
     *
     * @return 被清除的商品
     */
    @RequestMapping(value = "/carts/out-date", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public R cleanOutDateSku() {
        // 获取用户购物车所有商品
        Response<List<CartItem>> listCart = cartReadService.findByUserId(UserUtil.getUserId());
        if (!listCart.isSuccess()) {
            log.error("fail to list cart item by user(id={}), cause:{}", UserUtil.getUserId(), listCart.getError());
        }

        List<CartItem> cartItems = listCart.getResult();
        if (cartItems.isEmpty()) {
            return R.ok().add("data", Collections.emptyList());
        }

        // find and filter sku by status 1
        List<Long> skuIds = ModelHelper.extractFiled(cartItems, "skuId");
        Response<List<Sku>> findSkus = skuReadService.findSkusByIds(skuIds);
        if (!findSkus.isSuccess()) {
            log.error("fail to find sku by ids({}), cause:{}", skuIds, findSkus.getError());
            return R.ok().add("data", Collections.emptyList());
        }

        List<Sku> skuList = findSkus.getResult();
        List<Sku> available = Lists.newArrayList();
        for (Sku sku : skuList) {
            if (Objects.equals(sku.getStatus(), 1)) {
                available.add(sku);
            }
        }

        // 如果有不可用商品,从购物删除,并返回它们的id
        if (available.size() != skuIds.size() && !available.isEmpty()) {
            List<Long> availableIds = ModelHelper.extradIds(available);
            skuIds.removeAll(availableIds);

            Response<Boolean> tryDelete = cartWriteService.batchDelete(skuIds, UserUtil.getUserId());
            if (!tryDelete.isSuccess()) {
                log.error("fail to delete cart items by skuIds({}), cause:{}", skuIds, tryDelete.getError());
                return R.ok().add("data", Collections.emptyList());
            }
            return R.ok().add("data", skuIds);
        }

        // 所有商品都可用
        return R.ok().add("data", Collections.emptyList());
    }

    private R buildCarts(List<RichCart> richCarts) {
        List<RichCartsNew> list = new ArrayList<>();

        if (richCarts == null || richCarts.isEmpty()) {
            return R.ok().add("msg", "未查询到数据！！").add("data", null);
        }
        //目前只做单店铺的购物车统计
        if (richCarts.get(0).getCartItems() == null || richCarts.get(0).getCartItems().isEmpty()) {
            return R.ok().add("msg", "未查询到数据！！").add("data", null);
        }

        richCarts = richCarts.stream().sorted(Comparator.comparing(RichCart::getUpdatedAt)).collect(Collectors.toList());

        for (RichCartItem rtm : richCarts.get(0).getCartItems()) {
            RichCartsNew rnw = new RichCartsNew();
            rnw.setId(rtm.getCartItem().getId());

            rnw.setItemId(rtm.getSku().getItemId());
            rnw.setMainPic(rtm.getItemImage());
            rnw.setTitle(rtm.getItemName());
            rnw.setStatus(rtm.getItemStatus());
            rnw.setShopName(richCarts.get(0).getShopName());
            rnw.setQuantity(rtm.getCartItem().getQuantity());
            SkuNew snw = new SkuNew();
            BeanUtils.copyProperties(rtm.getSku(), snw);
            StringBuilder name = new StringBuilder();
            //SkuAttribute s:rtm.getSku().getAttrs()
            if (rtm.getSku().getAttrs() != null && !rtm.getSku().getAttrs().isEmpty()) {
                for (SkuAttribute s : rtm.getSku().getAttrs()) {
                    name.append(s.getAttrVal()).append(" ");
                }
            }
            snw.setName(name.toString());
            rnw.setSku(snw);
            list.add(rnw);
        }
        CartsVO cartsVO = new CartsVO();
        if (EmptyUtils.isNotEmpty(list)) {
            List<RichCartsNew> active = list.stream().filter(entity -> Objects.equals(entity.getStatus(), 1)).collect(Collectors.toList());
            if (EmptyUtils.isNotEmpty(active)) {
                cartsVO.setActive(active);
            }
            List<RichCartsNew> noActive = list.stream().filter(entity -> !Objects.equals(entity.getStatus(), 1)).collect(Collectors.toList());
            if (EmptyUtils.isNotEmpty(noActive)) {
                cartsVO.setNoActive(noActive);
            }
        }
        return R.ok().add("data", cartsVO);
    }

    @Data
    static
    class CartsVO {
        List<RichCartsNew> active;
        List<RichCartsNew> noActive;
    }


    @Data
    static
    class RichCartsNew {
        Long id;
        Long itemId;
        String title;    // 商品名
        String mainPic;   // 商品主图
        String shopName;
        Integer status;
        SkuNew sku;
        Integer quantity;
    }

    @Data
    static
    class SkuNew {
        Long id;//skuid
        Integer type;//类型（1:保税，0:完税）
        Integer status;//sku状态 1: 上架, -1:下架, -2:冻结, -3:删除
        String name;
        Integer stockQuantity;//库存
        Integer price;
    }
}
