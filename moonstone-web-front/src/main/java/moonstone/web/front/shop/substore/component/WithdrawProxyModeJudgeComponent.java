package moonstone.web.front.shop.substore.component;

import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.model.CommonUser;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.shop.dto.SubStoreCriteria;
import moonstone.shop.model.Shop;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.ShopReadService;
import moonstone.web.core.model.dto.WithDrawProfitApplyCriteriaDTO;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.core.shop.model.ServiceProvider;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@AllArgsConstructor
@Component
@Slf4j
public class WithdrawProxyModeJudgeComponent {
    private final ShopCacheHolder shopCacheHolder;
    private final SubStoreCache subStoreCache;
    private final ServiceProviderCache serviceProviderCache;
    private final ShopReadService shopReadService;
    private final SubStoreUserIdentityComponent subStoreUserIdentityComponent;

    /**
     * 判断提现申请的店铺类型
     *
     * @param user        搜索人
     * @param criteriaDTO 申请搜索条件
     * @return 类型, 1 => 分销代购模式 , 2 =>  门店+代购员模式 , 3 => 微店
     */
    public int judgeProxyModeAndFillTheSubStoreUserIdList(CommonUser user, WithDrawProfitApplyCriteriaDTO criteriaDTO) {
        // 是微店模式
        if (criteriaDTO.getSourceId() == null) {
            criteriaDTO.setSourceId(user.getShopId());
        }
        Shop shop = shopCacheHolder.findShopById(criteriaDTO.getSourceId());
        if (criteriaDTO.getSourceId() == 0L) {
            shop = shopCacheHolder.findShopById(user.getShopId());
        }
        String pattern = Optional.ofNullable(shop.getExtra())
                .map(extra -> extra.get(ShopExtra.SalesPattern.getCode()))
                .orElse(SalePattern.Common.getCode());
        // update the user-id by the mobile or name
        if (SalePattern.SubStore.getCode().equals(pattern)) {
            if (!ObjectUtils.isEmpty(criteriaDTO.getName()) || !ObjectUtils.isEmpty(criteriaDTO.getMobile()) ||
                    !ObjectUtils.isEmpty(criteriaDTO.getNameType())) {
                SubStoreCriteria subStoreCriteria = new SubStoreCriteria();
                subStoreCriteria.setPageSize(5000);
                subStoreCriteria.setShopId(shop.getId());
                subStoreCriteria.setName(Optional.ofNullable(criteriaDTO.getName())
                        .map(String::trim).orElse(null));
                subStoreCriteria.setMobile(Optional.ofNullable(criteriaDTO.getMobile())
                        .map(String::trim).orElse(null));

                Integer userRole = null;
                List<Long> userIds = new ArrayList<>();
                if ("1".equals(criteriaDTO.getNameType())) {
                    userRole = SubStoreUserIdentityEnum.SUB_STORE.getCode();

                    //sub_store名称
                    userIds.addAll(subStoreCache.searchInCache(shop.getId(), subStoreCriteria).getData().stream()
                            .map(SubStore::getUserId).collect(Collectors.toList()));
                } else if ("2".equals(criteriaDTO.getNameType())) {
                    userRole = SubStoreUserIdentityEnum.SERVICE_PROVIDER.getCode();

                    //service_provider名称
                    userIds.addAll(serviceProviderCache.pagingCache(shop.getId(), subStoreCriteria.getName(), subStoreCriteria.getMobile())
                            .stream().map(ServiceProvider::getUserId).collect(Collectors.toList()));
                } else {
                    userIds.addAll(subStoreCache.searchInCache(shop.getId(), subStoreCriteria).getData().stream()
                            .map(SubStore::getUserId).collect(Collectors.toList()));
                    userIds.addAll(serviceProviderCache.pagingCache(shop.getId(), subStoreCriteria.getName(), subStoreCriteria.getMobile())
                            .stream().map(ServiceProvider::getUserId).collect(Collectors.toList()));
                }
                criteriaDTO.setUserIds(userIds);

                setUserIdsBySnapshot(criteriaDTO, userRole);
            }
        }
        return switch (SalePattern.from(pattern)) {
            case SubStore -> 2;
            case StoreProxy -> 1;
            case WeShop -> 3;
            case Common -> 0;
            case CommunityOperation -> 5;
        };
    }

    /**
     * 从角色快照中匹配
     *
     * @param criteria
     * @param userRole
     */
    private void setUserIdsBySnapshot(WithDrawProfitApplyCriteriaDTO criteria, Integer userRole) {
        var bySnapshot = subStoreUserIdentityComponent.findUserIdsLike(criteria.getSourceId(), criteria.getName(),
                criteria.getMobile(), userRole, OrderRoleSnapshotOrderTypeEnum.WITHDRAW_APPLY);
        if (CollectionUtils.isEmpty(bySnapshot)) {
            return;
        }

        if (CollectionUtils.isEmpty(criteria.getUserIds())) {
            criteria.setUserIds(bySnapshot);
        } else {
            criteria.getUserIds().addAll(bySnapshot);
        }
    }

    public void setUserId(WithDrawProfitApplyCriteriaDTO criteriaDTO) {
        Response<Shop> byId = shopReadService.findById(criteriaDTO.getSourceId());
        criteriaDTO.setShopUserId(byId.getResult().getUserId());
    }
}