package moonstone.web.front.shop;

import com.alibaba.fastjson.JSON;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.pay.component.MultiTokenProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.service.ShopPayInfoReadService;
import moonstone.shop.service.ShopPayInfoWriteService;
import moonstone.shop.service.ShopReadService;
import moonstone.web.core.registers.shop.TokenRegister;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/shop/payInfo")
public class PayTokens {
    @RpcConsumer
    private ShopPayInfoWriteService shopPayInfoWriteService;
    @RpcConsumer
    private ShopPayInfoReadService shopPayInfoReadService;
    @RpcConsumer
    private ShopReadService shopReadService;
    @RpcConsumer
    private TokenRegister tokenRegister;
    @Autowired
    private List<MultiTokenProvider> tokenProvider;
    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    @GetMapping("/token/list")
    public Map<String, Map<String, String>> getTokens(String pw) {
        if (!pw.equals("fuckU-U-U"))
            return new HashMap<>();
        Map<String, Map<String, String>> result = new HashMap<>();
        for (MultiTokenProvider provider : tokenProvider) {
            Map<String, String> tokenMap = new HashMap<>();
            for (Object account : provider.listAllAccounts()) {
                //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
                tokenMap.put(account.toString(), JSON.toJSONString(provider.findToken(account.toString())));
            }
            result.put(provider.getClass().getSimpleName(), tokenMap);
        }
        return result;
    }
}
