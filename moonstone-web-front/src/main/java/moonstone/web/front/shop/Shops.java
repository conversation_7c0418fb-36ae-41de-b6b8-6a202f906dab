package moonstone.web.front.shop;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Objects;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableBiMap;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.cache.PromotionCacher;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.api.Result;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.*;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.WithdrawPrinciple;
import moonstone.order.service.ShopDetailsfwbReadService;
import moonstone.order.service.ShopDetailsfwbWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.WithdrawPrincipleManager;
import moonstone.promotion.constants.PromotionExtraKeys;
import moonstone.promotion.model.Promotion;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.service.ShopWriteService;
import moonstone.shopWxa.enums.ShopWxaStatus;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.model.ShopDetailsfwb;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserRelationEntityWriteService;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.component.cache.UserRelationCacher;
import moonstone.web.core.component.pay.xinbada.domain.dto.AccountDetailDTO;
import moonstone.web.core.component.pay.xinbada.domain.slice.XinBaDaPayAccountHelperSlice;
import moonstone.web.core.events.UserReachShopEvent;
import moonstone.web.core.events.promotion.listener.PromotionAutoGiver;
import moonstone.web.core.events.shop.ShopUpdateEvent;
import moonstone.web.core.exports.common.Exporter;
import moonstone.web.core.model.AccessToken;
import moonstone.web.core.model.ShopShareImageData;
import moonstone.web.core.model.YunAccount;
import moonstone.web.core.model.YunRequestModel;
import moonstone.web.core.user.WxAccessTokenByProjectCacheHolder;
import moonstone.web.core.user.service.StoreProxySubUserManager;
import moonstone.web.core.util.OutSystemIdProviderImpl;
import moonstone.web.core.util.YunAccountEncryptUtil;
import moonstone.web.front.shop.dto.ShopDetailsfwbReqDto;
import moonstone.web.front.shop.dto.ShopFwbSaveReqDto;
import moonstone.web.front.shop.dto.ShopView;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import net.sf.json.JSONObject;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.MediaType;
import org.springframework.util.Base64Utils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 店铺相关接口
 */
@RestController
@Slf4j
@RequestMapping("/api/shop")
@AllArgsConstructor
public class Shops {

    private final ShopReadService shopReadService;

    private final ShopWriteService shopWriteService;

    private final ShopCacheHolder shopCacheHolder;

    private final ShopDetailsfwbWriteService shopDetailsfwbWriteService;

    private final ShopDetailsfwbReadService shopDetailsfwbReadService;

    private final UserRelationEntityWriteService userRelationEntityWriteService;

    private final ShopOrderReadService shopOrderReadService;

    private final ShopWxaCacheHolder shopWxaCacheHolder;

    private final UserRelationCacher userRelationCacher;

    private final StoreProxySubUserManager storeProxySubUserManager;

    private final ShopWxaProjectReadService shopWxaProjectReadService;

    private final WxOpenParanaComponentService wxOpenParanaComponentService;

    private final MongoTemplate mongoTemplate;

    private final OutSystemIdProviderImpl outSystemIdProvider;

    private final WithdrawPrincipleManager withdrawPrincipleManager;

    private final PromotionCacher promotionCacher;
    private final XinBaDaPayAccountHelperSlice xinBaDaPayAccountHelperSlice;
    private final PromotionAutoGiver promotionAutoGiver;
    private final Exporter exporter;
    WxAccessTokenByProjectCacheHolder wxAccessTokenByProjectCacheHolder;

    private final ShopWxaReadService shopWxaReadService;

    @GetMapping("/export")
    public void exportShops(HttpServletResponse response) throws IOException {
        List<Shop> shopList = shopReadService.pagination(null, null, null, null, 1, Integer.MAX_VALUE).getResult().getData();
        List<ShopView> shopViews = ShopView.from(shopList);
        Workbook workbook = exporter.export(shopViews, ShopView.class);
        workbook.write(response.getOutputStream());
        //这里设置一下让浏览器弹出下载提示框，而不是直接在浏览器中打开
        response.setHeader("Content-Disposition", "attachment; filename=\"shop.xlsx\"");
        //response.setContentType("application/vnd.ms-excel;charset=utf-8;");
        response.setContentType("application/octet-stream;charset=utf-8");
    }

    @RequestMapping(value = "/reach")
    public Response<Boolean> reachStatus(Long shopId, Long refererId, Long subStoreId) {
        Long userId = UserUtil.getUserId();
        if (userId == null) {
            return Response.fail("user.not.login");
        }
        if (userRelationCacher.get(userId, shopId, UserRelationEntity.UserRelationType.INVITOR).map(UserRelationEntity::getAdditionRelationB).isEmpty()) {
            userRelationEntityWriteService.createWhenNotExists(userId, shopId, subStoreId, refererId, UserRelationEntity.UserRelationType.INVITOR);
        }
        tryToCheckPromotionThatShouldReceived(userId, shopId);
        EventSender.sendApplicationEvent(new UserReachShopEvent(userId, shopId));
        Optional<UserRelationEntity> entityOpt = userRelationCacher.get(userId, shopId, UserRelationEntity.UserRelationType.Member);
        if (new Date().before(Date.from(LocalDateTime.of(2019, 12, 11, 0, 0, 0).atZone(ZoneId.systemDefault()).toInstant()))) {
            log.debug("{} if shopId:{} refererId:{} subStoreId:{} reg:{} v:{}", LogUtil.getClassMethodName(), shopId, refererId, subStoreId, entityOpt.isPresent(), entityOpt.orElse(null));
        }
        if (entityOpt.isEmpty()) {
            userRelationCacher.invalidate(userId, shopId, UserRelationEntity.UserRelationType.Member);
            return userRelationEntityWriteService.updateOrCreate(userId, shopId, subStoreId, refererId, UserRelationEntity.UserRelationType.Member);
        }
        if (!java.util.Objects.equals(refererId, entityOpt.get().getInvitorId()) || !java.util.Objects.equals(subStoreId, entityOpt.get().getSubShopId())) {
            UserRelationEntity entity = entityOpt.get();
            entity.setInvitorId(refererId);
            entity.setSubShopId(subStoreId);
            userRelationCacher.put(userId, shopId, UserRelationEntity.UserRelationType.Member, entity);
            userRelationEntityWriteService.updateOrCreate(userId, shopId, subStoreId, refererId, UserRelationEntity.UserRelationType.Member);
            userRelationCacher.invalidate(userId, shopId, UserRelationEntity.UserRelationType.Member);
        }
        return Response.ok(true);
    }

    private void tryToCheckPromotionThatShouldReceived(Long userId, Long shopId) {
        try {
            promotionCacher.findOngoingPromotionOf(shopId).stream()
                    .filter(java.util.Objects::nonNull)
                    .filter(promotion -> promotion.getExtra() != null && promotion.getExtra().getOrDefault(PromotionExtraKeys.AUTO_GIVE, "").equals("true"))
                    .filter(promotion -> Optional.ofNullable(promotion.getConditionParams()).orElse(new HashMap<>())
                            .getOrDefault("firstBought", "false").equals("false") || hasBoughtNoThing(userId, shopId))
                    .map(Promotion::getId)
                    .forEach(promotionId -> promotionAutoGiver.tryToGiveOnePromotionOnlyOnce(userId, shopId, promotionId));
        } catch (Exception ex) {
            log.error("{} fail to give promotion when user reach(userId:{} shopId:{})", LogUtil.getClassMethodName(), userId, shopId, ex);
        }
    }

    private boolean hasBoughtNoThing(Long userId, Long shopId) {
        OrderCriteria orderCriteria = new OrderCriteria();
        orderCriteria.setBuyerId(userId);
        orderCriteria.setShopId(shopId);
        orderCriteria.setStatus(OrderStatus.getValidStatusForFirstBuyPromotion().stream().map(OrderStatus::getValue).collect(Collectors.toList()));
        return shopOrderReadService.count(orderCriteria).getResult() == 0;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Shop findShopById(@PathVariable("id") Long shopId) {
        return shopCacheHolder.findShopById(shopId);
    }

    /**
     * 查询店铺基本信息
     * @param shopId 店铺id
     * @return 店铺基本信息
     */
    @RequestMapping(value = "v2/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Shop> findShopByIdV2(@PathVariable("id") Long shopId) {
        return Result.data(shopCacheHolder.findShopById(shopId));
    }

    @RequestMapping(value = "/")
    public Shop myShop() {
        return findShopOfMine();
    }

    @RequestMapping(value = "/my-shop", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Shop findShopOfMine() {
        Shop shop = getLoggedShopExact(true);
        if (shop == null) {
            return new Shop();
        }
        return shop;
    }

    @RequestMapping(value = "/subStore-invite-code")
    public String getSubStoreInviteCode() {
        Shop shop = Optional.ofNullable(getLoggedShopExact(false)).orElseThrow(() -> new JsonResponseException("shop.not.found"));
        return encode(shop.getId());
    }

    /**
     * 当前只返回微信的码
     *
     * @param response
     * @param projectId
     * @param shopId
     */
    @RequestMapping("/getShopQrCode")
    public void getShopQrCode(HttpServletResponse response, @RequestParam(defaultValue = "21") Long projectId, Long shopId) {
        if (EmptyUtils.isEmpty(shopId)) {
            throw new JsonResponseException("店铺id不能为空!!!");
        }
        ShopWxa shopWxa = null;
        // load the ShopWxa By shopId first, use custom WxApp for Shop that has define self's app
        try {
            shopWxa = shopWxaReadService.findByShopId(shopId).getResult().stream()
                    .filter(entity -> entity.getAppType() == null || AppTypeEnum.WECHAT.getCode().equals(entity.getAppType()))
                    .filter(entity -> entity.getStatus() >= ShopWxaStatus.NORMAL.getValue())
                    .findFirst().orElse(null);
        } catch (Exception ignore) {

        }
        // use the projectId for the far query, check if shopWxa match the same projectId
        if (shopWxa == null) {
            shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
        } else {
            projectId = shopWxaProjectReadService.findByShopWxaId(shopWxa.getId())
                    .getResult().stream().filter(project -> project.getStatus() > -1)
                    .findFirst().map(ShopWxaProject::getId).orElse(projectId);
        }
        OutputStream out = null;
        BufferedImage image;
        try {
            ShopShareImageData shopShareImage = mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopId)).
                    addCriteria(Criteria.where("status").is(1)).addCriteria(Criteria.where("projectId").is(projectId)), ShopShareImageData.class);
            if (EmptyUtils.isNotEmpty(shopShareImage) && EmptyUtils.isNotEmpty(shopShareImage.getBaseInformation())) {
                byte[] bytes1 = Base64Utils.decodeFromString(shopShareImage.getBaseInformation());
                ByteArrayInputStream ins = Byte2InputStream.byte2InputStream(bytes1);
                image = ImageIO.read(ins);
            } else {
                String access_token;
                if (shopWxa.getAppSecret() != null) {
                    AccessToken accessToken = wxAccessTokenByProjectCacheHolder.findAccessToken(projectId);
                    access_token = accessToken.getAccessToken();
                } else {
                    access_token = wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxa.getAppId(), false);
                }
                Map<String, Object> map = new HashMap<>();
                map.put("width", "280");
                map.put("path", "pages/design/index?shopId=" + shopId);
                map.put("scene", "shopId=" + shopId);
                String url = "https://api.weixin.qq.com/wxa/getwxacode?access_token=" + access_token;
                HttpRequest request = HttpRequest.post(url).contentType("application/json", "utf-8").send(JSON.toJSONBytes(map));
                if (request.ok()) {
                    ByteArrayInputStream in = new ByteArrayInputStream(request.bytes());    //将b作为输入流；
                    byte[] bytes = Byte2InputStream.inputStream2byte(in);
                    String baseInformation = Base64Utils.encodeToString(bytes);
                    Update update = Update.update("shopId", shopId).set("status", 1).
                            set("baseInformation", baseInformation)
                            .set("createDate", System.currentTimeMillis());
                    mongoTemplate.upsert(Query.query(Criteria.where("shopId").is(shopId).
                                            andOperator(Criteria.where("status").is(1)))
                                    .addCriteria(Criteria.where("projectId").is(projectId)),
                            update, ShopShareImageData.class);
                    byte[] bytes1 = Base64Utils.decodeFromString(baseInformation);
                    ByteArrayInputStream ins = Byte2InputStream.byte2InputStream(bytes1);
                    image = ImageIO.read(ins);
                } else {
                    log.error("[op:auth.getAccessToken] HttpRequest failed");
                    throw new JsonResponseException(500, "获取小程序二维码失败");
                }
            }
            out = response.getOutputStream();
            response.setContentType("image/png");
            ImageIO.write(image, "png", out);
            out.flush();
        } catch (Exception e) {
            log.error("fail to create shopShareImage, cause: {}", Throwables.getStackTraceAsString(e));
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    log.error("fail to close image OutputStream, cause: {}", Throwables.getStackTraceAsString(e));
                }
            }
        }
    }

    private String encode(long id) {
        String coded = Base64.getEncoder().encodeToString((id + "").getBytes()).replaceAll("=", "P");
        if (coded.length() >= 12) {
            return "G" + coded.substring(3, 11) + "D" + id;
        }
        if (coded.length() < 8) {
            return "G" + coded + Strings.repeat("X", 8 - coded.length()) + "D" + id;
        }
        return "G" + coded.substring(0, 8) + "D" + id;
    }

    @RequestMapping(value = "", method = RequestMethod.PUT)
    public Boolean updateShop(@RequestBody Shop shop) {
        checkShopNameIfDuplicated(shop.getName());

        Shop exist = getLoggedShopExact(false);
        if (exist == null || !Objects.equal(exist.getStatus(), 1)) {
            throw new JsonResponseException("shop.update.fail.status.invalid");
        }
        Long shopId = exist.getId();

        Shop toUpdate = new Shop();
        toUpdate.setId(shopId);
        toUpdate.setName(shop.getName());
        toUpdate.setImageUrl(shop.getImageUrl());
        toUpdate.setPhone(shop.getPhone());
        toUpdate.setAddress(shop.getAddress());
        toUpdate.setExtra(Iters.deltaUpdate(exist.getExtra(), shop.getExtra()));
        Response<Boolean> resp = shopWriteService.update(toUpdate);
        if (!resp.isSuccess()) {
            log.error("update shop failed, shopId={}, error={}", shopId, resp.getError());
            throw new JsonResponseException(500, resp.getError());
        }
        EventSender.publish(new ShopUpdateEvent(shopId));
        return resp.getResult();
    }

    /**
     * 修改店铺基本信息
     * @param shop 店铺信息
     * @return 是否修改成功
     */
    @RequestMapping(value = "v2", method = RequestMethod.POST)
    public Result<Boolean> updateShopPost(@RequestBody Shop shop) {
        checkShopNameIfDuplicated(shop.getName());

        Shop exist = getLoggedShopExact(false);
        if (exist == null || !Objects.equal(exist.getStatus(), 1)) {
            throw new JsonResponseException("shop.update.fail.status.invalid");
        }
        Long shopId = exist.getId();

        Shop toUpdate = new Shop();
        toUpdate.setId(shopId);
        toUpdate.setName(shop.getName());
        toUpdate.setImageUrl(shop.getImageUrl());
        toUpdate.setPhone(shop.getPhone());
        toUpdate.setAddress(shop.getAddress());
        toUpdate.setExtra(Iters.deltaUpdate(exist.getExtra(), shop.getExtra()));
        toUpdate.setJdStockCheck(shop.getJdStockCheck());
        Response<Boolean> resp = shopWriteService.update(toUpdate);
        if (!resp.isSuccess()) {
            log.error("update shop failed, shopId={}, error={}", shopId, resp.getError());
            throw new JsonResponseException(500, resp.getError());
        }
        EventSender.publish(new ShopUpdateEvent(shopId));
        return Result.data(resp.getResult());
    }

    private Shop getLoggedShopExact(boolean allowNullShop) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            throw new JsonResponseException(401, "user.not.login");
        }
        Long userId = user.getId();
        if (!ObjectUtils.isEmpty(user.getShopId())) {
            Boolean b = storeProxySubUserManager.isAuthProxy(userId, user.getShopId());
            if (b) {
                Response<Shop> shopResponse = shopReadService.findById(user.getShopId());
                if (shopResponse.isSuccess() && shopResponse.getResult() != null) {
                    return shopResponse.getResult();
                } else {
                    log.warn("shop find failed by dataIsolationUsers, userId={}, error={}", userId, shopResponse.getError());
                    throw new JsonResponseException(shopResponse.getError());
                }
            }
        }


        val rExist = shopReadService.checkExistByUserId(userId);
        if (!rExist.isSuccess()) {
            log.warn("shop check exist failed, userId={}, error={}", userId, rExist.getError());
            throw new JsonResponseException(rExist.getError());
        }
        if (allowNullShop && !rExist.getResult()) {
            return null;
        }
        val rShop = shopReadService.findByUserId(userId);
        if (!rShop.isSuccess()) {
            log.warn("shop find failed, userId={}, error={}", userId, rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        return rShop.getResult();
    }

    private void checkShopNameIfDuplicated(String updatedShopName) {
        if (!StringUtils.hasText(updatedShopName)) {
            return;
        }

        Response<Boolean> checkResp = shopReadService.checkExistByName(updatedShopName);
        if (!checkResp.isSuccess()) {
            log.error("fail to check shop if existed by name:{},cause:{}",
                    updatedShopName, checkResp.getError());
            throw new JsonResponseException(checkResp.getError());
        }

        if (!checkResp.getResult()) {
            return;
        }

        Response<Shop> findShop = shopReadService.findByName(updatedShopName);
        if (!findShop.isSuccess()) {
            log.error("fail to find shop by name:{},cause:{}",
                    updatedShopName, findShop.getError());
            throw new JsonResponseException(findShop.getError());
        }
        Shop shop = findShop.getResult();

        final Long currentShopId = UserUtil.<CommonUser>getCurrentUser().getShopId();
        if (!Objects.equal(shop.getId(), currentShopId)) {
            log.error("shop name({}) duplicated", updatedShopName);
            throw new JsonResponseException("shop.name.duplicated");
        }
    }

    @RequestMapping(value = "/inviteCode", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getInviteCode() {
        CommonUser seller = UserUtil.getCurrentUser();
        try {
            if (seller.getShopId() == null) {
                log.error("[op:inviteCode] user(id={}) is not a seller", seller.getId());
                throw new JsonResponseException("user.not.seller");
            }
            //获取当前商家信息
            Response<Shop> rShop = shopReadService.findById(seller.getShopId());
            if (!rShop.isSuccess()) {
                log.error("failed to find shop by id={}, error code: {}", seller.getShopId(), rShop.getError());
                throw new JsonResponseException(rShop.getError());
            }
            Shop shop = rShop.getResult();
            //判断是否开启微分销功能
            Map<String, String> extra = shop.getExtra();
            if (extra == null || !Objects.equal("true", extra.get(DistributionConstants.SHOP_OPEN_WE_SHOP))) {
                log.error("shop(id={}) not open weShop", shop.getId());
                throw new JsonResponseException("shop.not.open.weShop");
            }
            String inviteCode = shop.getInviteCode();
            if (ObjectUtils.isEmpty(inviteCode)) {
                //若还没有邀请码，则创建
                inviteCode = ShareCodeUtil.shopPrefix + ShareCodeUtil.toSerialCode(shop.getId());
                Shop toUpdate = new Shop();
                toUpdate.setId(shop.getId());
                toUpdate.setInviteCode(inviteCode);
                Response<Boolean> response = shopWriteService.update(toUpdate);
                if (!response.isSuccess()) {
                    log.error("failed to update shop by shopId={}, inviteCode={}, error code: {}",
                            toUpdate.getId(), toUpdate.getInviteCode(), response.getError());
                    throw new JsonResponseException(response.getError());
                }
            }

            return inviteCode;
        } catch (Exception e) {
            log.error("[op:inviteCode] fail to get shop(id={}) invite code by userId={}, cause: {}",
                    seller.getShopId(), seller.getId(), e);
            throw new JsonResponseException("shop.invite.code.get.fail");
        }
    }

    @RequestMapping(value = "/isOpenWeShop", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean isOpenWeShop() {
        CommonUser commonUser = UserUtil.getCurrentUser();
        try {
            Response<Shop> rShop = shopReadService.findById(commonUser.getShopId());
            if (!rShop.isSuccess()) {
                log.error("failed to find shop by id={}, error code: {}", commonUser.getShopId(), rShop.getError());
                throw new JsonResponseException(rShop.getError());
            }
            Shop shop = rShop.getResult();
            Map<String, String> extra = shop.getExtra();
            if (extra == null || !Objects.equal("true", extra.get(DistributionConstants.SHOP_OPEN_WE_SHOP))) {
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("fail to check shop(id={}) is open weShop, cause: {}", commonUser.getShopId(), e);
            throw new JsonResponseException("shop.check.is.open.weShop.fail");
        }
    }

    @RequestMapping(value = "/inviteCodeWithShopId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String getInviteCode(@RequestParam Long shopId) {
        try {
            //获取当前商家信息
            Response<Shop> rShop = shopReadService.findById(shopId);
            if (!rShop.isSuccess()) {
                log.error("failed to find shop by id={}, error code: {}", shopId, rShop.getError());
                throw new JsonResponseException(rShop.getError());
            }
            Shop shop = rShop.getResult();
            //判断是否开启微分销功能
            Map<String, String> extra = shop.getExtra();
            if (extra == null || !Objects.equal("true", extra.get(DistributionConstants.SHOP_OPEN_WE_SHOP))) {
                log.error("shop(id={}) not open weShop", shop.getId());
                throw new JsonResponseException("shop.not.open.weShop");
            }
            String inviteCode = shop.getInviteCode();
            if (ObjectUtils.isEmpty(inviteCode)) {
                //若还没有邀请码，则创建
                inviteCode = ShareCodeUtil.shopPrefix + ShareCodeUtil.toSerialCode(shop.getId());
                Shop toUpdate = new Shop();
                toUpdate.setId(shop.getId());
                toUpdate.setInviteCode(inviteCode);
                Response<Boolean> response = shopWriteService.update(toUpdate);
                if (!response.isSuccess()) {
                    log.error("failed to update shop by shopId={}, inviteCode={}, error code: {}",
                            toUpdate.getId(), toUpdate.getInviteCode(), response.getError());
                    throw new JsonResponseException(response.getError());
                }
            }

            return inviteCode;
        } catch (Exception e) {
            log.error("[op:inviteCode] fail to get shop(id={}) invite code by userId={}, cause: ",
                    shopId, UserUtil.getUserId(), e);
            throw new JsonResponseException("shop.invite.code.get.fail");
        }
    }

    /**
     * 20190611-yjp
     * 设置分销佣金比
     */
    @RequestMapping(value = "/set-level-distribution-rate", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean getSetLevelDistributionRate(@RequestParam String shopJson) {

        CommonUser user = UserUtil.getCurrentUser();
        try {
            if (user == null) {
                log.debug("{}", LogUtil.getClassMethodName(new Translate("用户未登录").toString()));
                return false;
            }
            Response<Shop> rShop = shopReadService.findById(user.getShopId());
            if (!rShop.isSuccess()) {
                log.error("failed to find shop by id={}, error code: {}", user.getShopId(), rShop.getError());
                throw new JsonResponseException(rShop.getError());
            }

            Shop existShop = rShop.getResult();

            Shop shop = new Shop();
            shop.setId(existShop.getId());


            Map<String, String> extra = existShop.getExtra();
            if (extra == null || extra.isEmpty()) {
                extra = new HashMap<>();
            }
            JSONObject json = JSONObject.fromObject(shopJson);

            for (Object map : json.entrySet()) {
                extra.put(((Map.Entry<?, ?>) map).getKey().toString(), ((Map.Entry<?, ?>) map).getValue().toString());
            }
            shop.setExtra(extra);

            Response<Boolean> response = shopWriteService.update(shop);
            if (!response.isSuccess()) {
                log.error("failed to update shop({}), error code: {}", shop, response.getError());
                throw new JsonResponseException(response.getError());
            }
            EventSender.publish(new ShopUpdateEvent(shop.getId()));
            return response.getResult();
        } catch (Exception e) {
            log.error("fail to update weShop openWeShop by shopId={}, shopJson={}, cause:{}", user == null ? null : user.getShopId(),
                    shopJson, e);
            throw new JsonResponseException("shop.open.weShop.fail");
        }
    }

    /**
     * 富文本插入
     */
    @RequestMapping(value = "/set-shop-fwb", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Either<Boolean> writeShopFWB(@RequestParam Long shopId, @RequestParam String fwb, @RequestParam Long status) {

        if (shopId == null || status == null) {
            log.error("{}  shopId:{} status:{}", LogUtil.getClassMethodName(), shopId, status);
            throw new JsonResponseException(new Translate("shopId is null ").toString());
        }
        Either<Optional<ShopDetailsfwb>> shopDetailsfwb = shopDetailsfwbReadService.findShopDetailsfwbByShop(shopId, status);

        if (!shopDetailsfwb.isSuccess()) {
            log.error("{}  shopId:{} status:{}", LogUtil.getClassMethodName(), shopId, status);
            throw new JsonResponseException(new Translate("shopId is fail ").toString());
        }

        if (shopDetailsfwb.take().isEmpty()) {
            ShopDetailsfwb entity = new ShopDetailsfwb();
            entity.setFwb(fwb);
            entity.setShopId(shopId);
            entity.setStatus(status);
            entity.setCreateAt(new Date());
            Either<Boolean> s = shopDetailsfwbWriteService.create(entity);
            return Either.ok(s.take());
        } else {
            Either<Boolean> s1 = shopDetailsfwbWriteService.updateData(shopId, fwb, status);
            return Either.ok(s1.take());
        }
    }

    /**
     * 富文本插入
     */
    @RequestMapping(value = "v2/set-shop-fwb", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Boolean> writeShopFWBV2(@RequestBody @Valid ShopFwbSaveReqDto req) {
        log.info("富文本插入 请求参数 {}", JSONUtil.toJsonStr(req));
        Long shopId = req.getShopId();
        Long status = req.getStatus();
        String fwb = req.getFwb();
        Either<Optional<ShopDetailsfwb>> shopDetailsfwb = shopDetailsfwbReadService.findShopDetailsfwbByShop(req.getShopId(), req.getStatus());

        if (!shopDetailsfwb.isSuccess()) {
            log.error("{}  shopId:{} status:{}", LogUtil.getClassMethodName(), shopId, status);
            throw new JsonResponseException(new Translate("shopId is fail ").toString());
        }

        if (shopDetailsfwb.take().isEmpty()) {
            ShopDetailsfwb entity = new ShopDetailsfwb();
            entity.setFwb(fwb);
            entity.setShopId(shopId);
            entity.setStatus(status);
            entity.setCreateAt(new Date());
            Either<Boolean> s = shopDetailsfwbWriteService.create(entity);
            return Result.data(s.take());
        } else {
            Either<Boolean> s1 = shopDetailsfwbWriteService.updateData(shopId, fwb, status);
            return Result.data(s1.take());
        }

    }


    /**
     * 富文本读取
     */
    @RequestMapping(value = "/get-shop-fwb", method = RequestMethod.GET)
    public ShopDetailsfwb getShopFWDInfo(@RequestParam Long shopId, @RequestParam Long status) {

        if (shopId == null || status == null) {
            log.error("{}  shopId:{} status:{}", LogUtil.getClassMethodName(), shopId, status);
            throw new JsonResponseException(new Translate("shopId is null ").toString());
        }

        Either<Optional<ShopDetailsfwb>> shopDetailsfwb = shopDetailsfwbReadService.findShopDetailsfwbByShop(shopId, status);

        if (!shopDetailsfwb.isSuccess()) {
            log.error("{}  shopId:{} status:{}", LogUtil.getClassMethodName(), shopId, status);
            throw new JsonResponseException(new Translate("shopId is fail ").toString());
        }
        if (shopDetailsfwb.take().isEmpty()) {
            return new ShopDetailsfwb();
        }
        String title = switch (shopDetailsfwb.take().get().getStatus().toString()) {
            case "1" -> "隐私协议";
            case "2" -> "用户协议";
            case "3" -> "消费者服务条款";
            case "4" -> "权益说明";
            case "5" -> "积分规则";
            case "6" -> "共享经济合作伙伴协议";
            case "7" -> "到账时间";
            case "8" -> "提现注意事项";
            case "9" -> "提现审核时间";
            case "10" -> "新客服务费秘籍";
            case "11" -> "营销推广协议";
            case "12" -> "营销合作协议";
            case "13" -> "服务协议";
            default -> "";
        };
        shopDetailsfwb.take().get().setTitle(title);
        return shopDetailsfwb.take().get();
    }

    /**
     * 富文本读取
     */
    @PostMapping(value = "v2/get-shop-fwb")
    public Result<ShopDetailsfwb> getShopFWDInfoV2(@RequestBody @Valid ShopDetailsfwbReqDto req) {
        Long status = req.getStatus();
        Long shopId = req.getShopId();
        Either<Optional<ShopDetailsfwb>> shopDetailsfwb = shopDetailsfwbReadService.findShopDetailsfwbByShop(shopId, status);

        if (!shopDetailsfwb.isSuccess()) {
            log.error("{}  shopId:{} status:{}", LogUtil.getClassMethodName(), shopId, status);
            throw new JsonResponseException(new Translate("shopId is fail ").toString());
        }
        if (shopDetailsfwb.take().isEmpty()) {
            return Result.data(new ShopDetailsfwb());
        }
        String title = switch (shopDetailsfwb.take().get().getStatus().toString()) {
            case "1" -> "隐私协议";
            case "2" -> "用户协议";
            case "3" -> "消费者服务条款";
            case "4" -> "权益说明";
            case "5" -> "积分规则";
            case "6" -> "共享经济合作伙伴协议";
            case "7" -> "到账时间";
            case "8" -> "提现注意事项";
            case "9" -> "提现审核时间";
            case "10" -> "新客服务费秘籍";
            case "11" -> "营销推广协议";
            case "12" -> "营销合作协议";
            case "13" -> "服务协议";
            case "14" -> "拼团协议";
            default -> "";
        };
        shopDetailsfwb.take().get().setTitle(title);
        return Result.data(shopDetailsfwb.take().get());
    }

    @GetMapping("/{accountName}/lastMoney")
    public Map<String, Map<String, BigDecimal>> getLastMoney(@PathVariable("accountName") String accountName, @RequestParam(required = false) Long shopId) {
        if (shopId == null) {
            CommonUser user = UserUtil.getCurrentUser();
            if (user == null || user.getShopId() == null) {
                throw new RuntimeException(new Translate("权限不足,请重新登录").toString());
            }
            shopId = user.getShopId();
        }
        try {
            switch (accountName) {
                case "yunAccount" -> {
                    String gateWay = "https://api-jiesuan.yunzhanghu.com";
                    String apiUrl = "/api/payment/v1/query-accounts";
                    String url = gateWay + apiUrl;
                    YunAccount yunAccount = mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopId)).addCriteria(Criteria.where("status").gt(0)), YunAccount.class);
                    if (yunAccount == null) {
                        return new HashMap<>();
                    }
                    Map<String, String> request = ImmutableBiMap.of("dealer_id", yunAccount.getDealerId());
                    String afterEncode = YunAccountEncryptUtil.tripleDesEncrypt(JSON.toJSONString(request), yunAccount.getKey_3Des())
                            .map(ByteArrayOutputStream::toByteArray)
                            .map(Base64.getEncoder()::encodeToString)
                            .elseThrow(() -> new RuntimeException(new Translate("数据加密失败").toString()));
                    YunRequestModel yunRequestModel = new YunRequestModel(afterEncode, yunAccount.getAppKey());
                    String requestId = outSystemIdProvider.getId(shopId * 1000 + System.currentTimeMillis() % 1000, OutSystemIdProviderImpl.type.OutSideQueryByShop);
                    log.debug("{} requestId:{} request:{}", LogUtil.getClassMethodName(), requestId, yunRequestModel);
                    HttpRequest httpRequest = HttpRequest.get(url, JSON.parseObject(JSON.toJSONString(yunRequestModel)), true)
                            .header("dealer-id", yunAccount.getDealerId())
                            .header("request-id", requestId);
                    if (httpRequest.ok()) {
                        com.alibaba.fastjson.JSONObject response = JSON.parseObject(httpRequest.body());
                        log.debug("{} requestId:{} response:{}", LogUtil.getClassMethodName(), requestId, response);
                        if (response.getString("code").equals("0000")) {
                            return dealByYunAccountCashDetail(response.getObject("data", com.alibaba.fastjson.JSONObject.class)
                                    .getJSONArray("dealer_infos"));
                        } else {
                            log.error("{} requestId:{} response:{}", LogUtil.getClassMethodName(), requestId, response);
                        }
                    }
                }
                case "xin_ba_da" -> {
                    AccountDetailDTO accountDetailDTO = xinBaDaPayAccountHelperSlice.getAccountLeftMoney(xinBaDaPayAccountHelperSlice.getPayToken(shopId).take()).take();
                    return ImmutableBiMap.of(accountDetailDTO.getAccountName(), ImmutableBiMap.of("bank", accountDetailDTO.getValid()));
                }
                default -> throw new RuntimeException(new Translate("未知的渠道:%s", accountName).toString());
            }
            return null;
        } catch (Exception ex) {
            log.error("{} shop(id:{}) try to query account(name:{}) money failed:{},ex:", LogUtil.getClassMethodName(), shopId, accountName, ex.getMessage(), ex);
            throw new RuntimeException(new Translate("查询失败").toString());
        }
    }

    /**
     * 读取数据取出需要的余额数据
     *
     * @param jsonArray a
     * @return 由纳税主体分割的不同钱包的值
     */
    private Map<String, Map<String, BigDecimal>> dealByYunAccountCashDetail(JSONArray jsonArray) {
        Map<String, Map<String, BigDecimal>> cashMap = new HashMap<>();
        for (YunAccountCashInfo struct : jsonArray.toJavaList(YunAccountCashInfo.class)) {
            Map<String, BigDecimal> accountCash = new HashMap<>();
            if (java.util.Objects.equals(true, struct.getIs_alipay())) {
                accountCash.put("alipay", struct.getAlipay_balance());
            }
            if (java.util.Objects.equals(struct.getIs_bank_card(), true)) {
                accountCash.put("bank", struct.getBank_card_balance());
            }
            if (java.util.Objects.equals(true, struct.getIs_wxpay())) {
                accountCash.put("wxpay", struct.getWxpay_balance());
            }
            cashMap.put(struct.getBroker_id(), accountCash);
        }
        return cashMap;
    }

    /**
     * 查询平台提现限制设置
     */
    @GetMapping("/ruler/withdraw/{shopId}")
    public WithdrawPrinciple findWithdrawRulerByShopId(@PathVariable Long shopId) {
        return withdrawPrincipleManager.findByShopId(shopId).orElse(null);
    }

    /**
     * 添加平台提现限制设置
     */
    @PostMapping("/ruler/withdraw/{shopId}")
    public Response<WithdrawPrinciple> setWithdrawRulerByShopId(@PathVariable Long shopId, @RequestBody WithdrawPrinciple principle) {
        CommonUser user = UserUtil.getCurrentUser();
        log.debug("{} operator [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(user));
        if (user == null || user.getShopId() == null || !shopId.equals(user.getShopId())) {
            return Response.fail(new Translate("权限不足请重新登录").toString());
        }
        principle.set_id(withdrawPrincipleManager.findByShopId(shopId).map(WithdrawPrinciple::get_id).orElse(null));
        principle.setShopId(shopId);
        withdrawPrincipleManager.save(principle);
        return Response.ok(principle);
    }

    @PostMapping("/ruler/withdraw/setMaxMoney")
    public Response<Boolean> setMaxMoney(Long fee) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Response.fail(new Translate("权限不足请重新登录").toString());
        }
        Long shopId = user.getShopId();
        WithdrawPrinciple principle = withdrawPrincipleManager.findByShopId(shopId).orElseGet(WithdrawPrinciple::new);
        principle.setShopId(shopId);
        principle.setMoneyMaxLimit(new BigDecimal(fee).multiply(new BigDecimal("100")));
        if (principle.get_id() == null) {
            return Response.ok(withdrawPrincipleManager.save(principle) != null);
        } else {
            return Response.ok(withdrawPrincipleManager.modify(principle).orElse(false));
        }
    }

    /**
     * 配置Y800Storage仓库编码
     *
     * @param data 仓库编码聚合
     * @return 更新成功或者失败
     */
    @PostMapping("/whCode")
    public Response<Boolean> setWhCode(@RequestBody String data) {
        String[] form = data.split("&");
        if (form.length < 2) {
            return Response.fail("请输入完整参数");
        }
        String code = form[0].split("=")[1];
        String accessCode = form[1].split("=")[1];
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Response.fail(new Translate("请使用具有权限的用户登录").toString());
        }
        Shop shop = shopReadService.findById(user.getShopId()).getResult();
        Shop update = new Shop();
        update.setId(shop.getId());
        Map<String, String> shopExtra = Optional.ofNullable(shop.getExtra()).orElseGet(HashMap::new);
        shopExtra.put(ShopExtra.WhCode.getCode(), code);
        shopExtra.put(ShopExtra.Y800StorageAccessCode.getCode(), accessCode);
        update.setExtra(shopExtra);
        return Response.ok(shopWriteService.update(update).getResult());
    }

    @Data
    public static class YunAccountCashInfo {
        // 代征主体ID
        String broker_id;
        // 银⾏行行卡余额
        BigDecimal bank_card_balance;
        // 是否开通银⾏行行卡通道
        Boolean is_bank_card;
        // ⽀支付宝余额
        BigDecimal alipay_balance;
        // 是否开通⽀支付宝通道
        Boolean is_alipay;
        // 微信余额
        BigDecimal wxpay_balance;
        // 是否开通微信通道
        Boolean is_wxpay;
        // 服务费返点余额
        BigDecimal rebate_fee_balance;
    }
}

