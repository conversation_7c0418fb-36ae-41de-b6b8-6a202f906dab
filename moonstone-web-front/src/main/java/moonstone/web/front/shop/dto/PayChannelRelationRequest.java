package moonstone.web.front.shop.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PayChannelRelationRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 配置id
     */
    private Long proFileId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 类型: 1 灰度, 2 全量
     */
    private Integer relationType;

    /**
     * 0 大贸,1 保税
     */
    private Integer tradeType;

    /**
     * 灰度成员的手机号
     */
    private List<String> memberList;

}
