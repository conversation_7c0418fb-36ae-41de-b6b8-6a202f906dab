package moonstone.web.front.shop;

import com.alibaba.fastjson.JSON;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.enums.Customs;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.shop.dto.shoppayinfo.PayChannelRelationDTO;
import moonstone.shop.dto.shoppayinfo.ShopPayChannelDTO;
import moonstone.shop.dto.shoppayinfo.ShopPayInfoDTO;
import moonstone.shop.enums.ShopPayInfoExtraIndexEnum;
import moonstone.shop.enums.ShopPayInfoPayChannelEnum;
import moonstone.shop.enums.ShopPayInfoStatusEnum;
import moonstone.shop.model.PayChannelRelation;
import moonstone.shop.model.Shop;
import moonstone.shop.model.ShopPayCustoms;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.*;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.events.shop.ShopPayInfoChangeEvent;
import moonstone.web.core.events.shop.ShopPayInfoDeleteEvent;
import moonstone.web.core.model.YunAccount;
import moonstone.web.core.registers.shop.TokenRegister;
import moonstone.web.core.shop.application.ShopPayInfoComponent;
import moonstone.web.core.shop.application.ShopYunAccountManager;
import moonstone.web.core.util.LockKeyUtils;
import moonstone.web.front.shop.convert.ShopPayInfoConvertor;
import moonstone.web.front.shop.dto.PayChannelRelationRequest;
import moonstone.web.front.shop.dto.ShopPayInfoGreyReleaseRequest;
import org.redisson.api.RedissonClient;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 店铺支付信息
 */
@RestController
@Slf4j
public class ShopPayInfos {

    @Resource
    private ShopYunAccountManager shopYunAccountManager;

    @Resource
    private ShopPayInfoWriteService shopPayInfoWriteService;

    @Resource
    private ShopPayInfoReadService shopPayInfoReadService;

    @Resource
    private ShopPayCustomsReadService shopPayCustomsReadService;

    @Resource
    private ShopPayCustomsWriteService shopPayCustomsWriteService;

    @Resource
    private ShopPayInfoConvertor shopPayInfoConvertor;

    @Resource
    private ShopPayInfoComponent shopPayInfoComponent;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private UserReadService<User> userReadService;

    @Resource
    private TokenRegister tokenRegister;
    @Resource
    private ShopReadService shopReadService;
    @Resource
    private PayChannelRelationService payChannelRelationService;

    /**
     * 新增店铺支付信息
     *
     * @param shopPayInfo 支付信息
     * @return 新增记录ID
     */
    @Deprecated
    @RequestMapping(method = RequestMethod.POST, path = "/api/shop/profile/pay", produces = MediaType.APPLICATION_JSON_VALUE)
    public Long createShopPayInfo(@RequestBody ShopPayInfo shopPayInfo) {
        try {
            //获取当前登录账户的店铺ID
            CommonUser commonUser = UserUtil.getCurrentUser();
            Long shopId = commonUser.getShopId();
            //判断该店铺是否已有该类型的payInfo
            checkRepeat(null, shopId, shopPayInfo.getPayChannel());
            //设置所属店铺ID
            shopPayInfo.setShopId(shopId);
            //设置默认状态
            shopPayInfo.setStatus(ShopPayInfoStatusEnum.INACTIVE.getCode());
            Response<Long> response = shopPayInfoWriteService.create(shopPayInfo);
            if (!response.isSuccess()) {
                log.error("failed to create shopPayInfo, cause:{}", response.getError());
                throw new JsonResponseException(response.getError());
            }
            //发出支付信息改变事件
            EventSender.publish(new ShopPayInfoChangeEvent(shopPayInfo));
            return response.getResult();
        } catch (Exception e) {
            log.error("{} failed to create shopPayInfo, cause:", LogUtil.getClassMethodName(), e);
            throw new JsonResponseException("shop.pay.info.create.fail");
        }
    }

    /**
     * 新增支付配置
     *
     * @param file
     * @param payChannel
     * @param detail
     * @return
     */
    @PostMapping("/api/shop/profile/pay/create")
    public Result<Boolean> create(@RequestParam(name = "file", required = false) MultipartFile file,
                                  @RequestParam(name = "file2", required = false) MultipartFile file2,
                                  @RequestParam("payChannel") String payChannel, @RequestParam("detail") String detail) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Result.fail("请重新登录");
        }

        var lock = redissonClient.getLock(LockKeyUtils.shopPayInfoCreate(user.getShopId()));
        if (!lock.tryLock()) {
            return Result.fail("处理中，请稍后重试");
        }
        try {
            var shopPayInfo = shopPayInfoConvertor.convert(user.getShopId(), payChannel, detail, file);

            // 同一支付渠道，只能有一个配置
            checkRepeat(null, user.getShopId(), shopPayInfo.getPayChannel());

            return Result.data(shopPayInfoComponent.create(shopPayInfo, file, file2));
        } catch (DuplicateKeyException e) {
            return Result.fail("同一支付账号配置只能存在一个有效状态");
        } catch (Exception ex) {
            log.error("ShopPayInfos.create error, shopId={}, payChannel={}, detail={} ", user.getShopId(), payChannel, detail, ex);
            return Result.fail(ex.getMessage());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 根据登录用户查找支付配置信息
     *
     * @return 支付记录
     */
    @RequestMapping(value = "/api/shop/profile/pay/list", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<ShopPayInfoDTO>> findShopPayInfoByShopId() {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {

            return Result.fail("请重新登录");
        }

        try {
            var list = shopPayInfoReadService.findByShopId(user.getShopId()).getResult();

            return Result.data(shopPayInfoConvertor.convert(list));
        } catch (Exception e) {
            log.error("shopPayInfos.findShopPayInfoByShopId error, shopId={} ", user.getShopId(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * llyj
     */
    @RequestMapping(value = "/api/shop/profile/pay/list/new", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<ShopPayInfo>> findShopPayInfoByShopIdNew() {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            //查找支付信息
            Response<List<ShopPayInfo>> rShopPayInfoList = shopPayInfoReadService.findByShopId(commonUser.getShopId());
            if (!rShopPayInfoList.isSuccess()) {
                log.error("failed to find ShopPayInfo by id = {}", rShopPayInfoList);
                throw new JsonResponseException(rShopPayInfoList.getError());
            }
            for (ShopPayInfo shopPayInfo : rShopPayInfoList.getResult()) {
                //查找支付海关信息
                Response<List<ShopPayCustoms>> rShopPayCustoms = shopPayCustomsReadService.findListByPayInfoId(shopPayInfo.getId());
                if (!rShopPayCustoms.isSuccess()) {
                    log.warn("not find ShopPayCustoms by payInfoId = {},error : {}", shopPayInfo.getId(), rShopPayCustoms.getError());
                }
                shopPayInfo.encryptionSecretKey();
                shopPayInfo.setHasChildren(!CollectionUtils.isEmpty(rShopPayCustoms.getResult()));
                shopPayInfo.setShopPayCustoms(rShopPayCustoms.getResult());
            }
            return Result.data(rShopPayInfoList.getResult());
        } catch (Exception e) {
            log.error("{} not find ShopPayInfo,cause:", LogUtil.getClassMethodName(), e);
            throw new JsonResponseException("shop.pay.info.not.find");
        }
    }

    /**
     * 更改店铺支付信息
     */
    @RequestMapping(value = "/api/shop/profile/pay/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Boolean> updateShopPayInfoById(@RequestParam(name = "file", required = false) MultipartFile file,
                                                 @RequestParam(name = "file2", required = false) MultipartFile file2,
                                                 @RequestParam("payChannel") String payChannel, @RequestParam("detail") String detail,
                                                 @PathVariable("id") Long shopPayInfoId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }

        try {
            // 查询
            var existedObject = shopPayInfoReadService.getById(shopPayInfoId).getResult();

            // 构造待更新对象
            var updateObject = shopPayInfoConvertor.convert(commonUser.getShopId(), existedObject, payChannel, detail, file);

            // 更新
            return Result.data(shopPayInfoComponent.update(updateObject, file, file2));
        } catch (Exception e) {
            log.error("ShopPayInfos.updateShopPayInfoById error, shopPayInfoId={}, payChannel={}, detail={}",
                    shopPayInfoId, payChannel, detail, e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 删除支付信息记录
     *
     * @param shopPayInfoId 支付记录ID
     * @return 成功返回true
     */
    @Deprecated
    @RequestMapping(value = "/api/shop/profile/pay/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean deleteShopPayInfoById(@PathVariable("id") Long shopPayInfoId) {
        try {
            //查找当前登录账户的店铺ID
            CommonUser commonUser = UserUtil.getCurrentUser();
            Long shopId = commonUser.getShopId();

            //检查操作权限
            checkShopPayInfoUser(shopId, shopPayInfoId);

            Response<ShopPayInfo> rShopPayInfo = shopPayInfoReadService.findById(shopPayInfoId);
            if (!rShopPayInfo.isSuccess()) {
                log.error("fail to find shopPayInfo, by id={}, cause:{}", shopPayInfoId, rShopPayInfo.getError());
                throw new JsonResponseException(rShopPayInfo.getError());
            }
            ShopPayInfo shopPayInfo = rShopPayInfo.getResult();
            shopPayInfo.setStatus(ShopPayInfoStatusEnum.DELETED.getCode());

            Response<Boolean> resp = shopPayInfoWriteService.update(shopPayInfo);
            if (!resp.isSuccess()) {
                log.error("delete shopPayInfo fail by id = {},cause={}", shopPayInfoId, resp.getError());
                throw new JsonResponseException(resp.getError());
            }
            //发出支付信息删除事件
            EventSender.publish(new ShopPayInfoDeleteEvent(shopPayInfo.getShopId(), shopPayInfo.getPayChannel(), null));
            return resp.getResult();
        } catch (Exception e) {
            log.error("{} failed to update shopPayInfo by id = {}, cause:", LogUtil.getClassMethodName(), shopPayInfoId, e);
            throw new JsonResponseException("shop.pay.info.delete.fail");
        }
    }

    /**
     * 检查登录用户对该支付记录是否有操作权限
     *
     * @param shopId        店铺ID
     * @param shopPayInfoId 支付记录ID
     */
    private void checkShopPayInfoUser(Long shopId, Long shopPayInfoId) {
        //查找原有的记录
        Response<ShopPayInfo> rShopPayInfoOld = shopPayInfoReadService.findById(shopPayInfoId);
        if (!rShopPayInfoOld.isSuccess()) {
            log.error("shopPayInfo is not find by id = {}", shopPayInfoId);
            throw new JsonResponseException(rShopPayInfoOld.getError());
        }
        ShopPayInfo shopPayInfoOld = rShopPayInfoOld.getResult();
        //检查修改的记录是否属于当前登录用户
        if (!shopPayInfoOld.getShopId().equals(shopId)) {
            log.error("shopPayInfo not belong to user");
            throw new JsonResponseException("shop.pay.info.not.belong.to.user");
        }
    }

    /**
     * 检查该店铺是否已存在该支付渠道的支付信息
     *
     * @param id      店铺支付信息id
     * @param shopId  店铺id
     * @param channel 支付渠道
     */
    private void checkRepeat(Long id, Long shopId, String channel) {
        //判断该店铺是否已有该类型的payInfo
        var rShopPayInfo = shopPayInfoReadService.findAllByShopIdAndPayChannel(shopId, ShopPayInfoPayChannelEnum.parse(channel),
                List.of(ShopPayInfoStatusEnum.ACTIVE, ShopPayInfoStatusEnum.GRAY_RELEASE, ShopPayInfoStatusEnum.INACTIVE));
        if (!rShopPayInfo.isSuccess()) {
            log.error("failed to find shopPayInfo, cause:{}", rShopPayInfo.getError());
            throw new JsonResponseException(rShopPayInfo.getError());
        }

        var shopPayInfos = rShopPayInfo.getResult();
        if (!CollectionUtils.isEmpty(shopPayInfos) && shopPayInfos.stream().noneMatch(shopPayInfo -> shopPayInfo.getId().equals(id))) {
            log.error("shopPayInfo(shopId={}, payChannel={}) is exist", shopId, channel);
            throw new RuntimeException("该渠道的店铺支付信息已存在");
        }
    }

    /**
     * 新增支付海关信息
     *
     * @param shopPayCustoms 支付海关信息
     * @return 新增记录ID
     */
    @RequestMapping(value = "/api/shop/profile/pay/customs", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long createShopPayCustoms(@RequestBody ShopPayCustoms shopPayCustoms) {
        try {
            //获取当前登录用户
            CommonUser commonUser = UserUtil.getCurrentUser();
            Long shopId = commonUser.getShopId();
            //将该记录的拥有者设为当前登录用户
            shopPayCustoms.setShopId(shopId);
            shopPayCustoms.setStatus(1);
            shopPayCustoms.setCustomsName(Customs.fromInt(shopPayCustoms.getCustomsCode()).Name());
            //添加操作
            Response<Long> resp = shopPayCustomsWriteService.create(shopPayCustoms);
            if (!resp.isSuccess()) {
                log.error("failed ro create shopPayCustoms {}", shopPayCustoms);
                throw new JsonResponseException(resp.getError());
            }
            return resp.getResult();
        } catch (Exception e) {
            log.error("{} failed to create shopPayCustoms {}, cause:", LogUtil.getClassMethodName(), shopPayCustoms, e);
            throw new JsonResponseException("shop.pay.customs.create.fail");
        }
    }

    /**
     * 根据ID查找支付海关信息
     *
     * @param shopPayCustomsId ID
     * @return 支付海关信息
     */
    @RequestMapping(value = "/api/shop/profile/pay/customs/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ShopPayCustoms findShopPayCustomsById(@PathVariable("id") Long shopPayCustomsId) {
        try {
            Response<ShopPayCustoms> rShopPayCustoms = shopPayCustomsReadService.findById(shopPayCustomsId);
            if (!rShopPayCustoms.isSuccess()) {
                log.error("failed to find shopPayCustoms by id = {}", shopPayCustomsId);
                throw new JsonResponseException(rShopPayCustoms.getError());
            }
            return rShopPayCustoms.getResult();
        } catch (Exception e) {
            log.error("{} failed to find shopPayCustoms by id = {}, cause:", LogUtil.getClassMethodName(), shopPayCustomsId, e);
            throw new JsonResponseException("shop.pay.customs.not.find");
        }
    }

    /**
     * 编辑支付海关信息
     */
    @RequestMapping(value = "/api/shop/profile/pay/customs/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean updateShopPayCustomsById(@RequestBody ShopPayCustoms shopPayCustoms, @PathVariable("id") Long shopPayCustomsId) {
        try {
            //检查用户操作权限
            checkUserShopPayCustoms(shopPayCustomsId);

            shopPayCustoms.setId(shopPayCustomsId);
            shopPayCustoms.setStatus(null);
            shopPayCustoms.setShopId(null);
            shopPayCustoms.setPayInfoId(null);
            shopPayCustoms.setCustomsName(Customs.fromInt(shopPayCustoms.getCustomsCode()).Name());

            Response<Boolean> resp = shopPayCustomsWriteService.update(shopPayCustoms);
            if (!resp.isSuccess()) {
                log.error("failed to update shopPayCustoms {}", shopPayCustoms);
                throw new JsonResponseException(resp.getError());
            }
            return resp.getResult();
        } catch (Exception e) {
            log.error("{} failed to update shopPayCustoms {}, cause:", LogUtil.getClassMethodName(), shopPayCustoms, e);
            throw new JsonResponseException("shop.pay.customs.update.fail");
        }
    }

    /**
     * 删除支付海关信息
     *
     * @param shopPayCustomsId 记录ID
     * @return 成功返回true
     */
    @RequestMapping(value = "/api/shop/profile/pay/customs/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean deleteShopPayCustomsById(@PathVariable("id") Long shopPayCustomsId) {
        try {
            //检查操作权限
            checkUserShopPayCustoms(shopPayCustomsId);

            ShopPayCustoms shopPayCustoms = new ShopPayCustoms();
            shopPayCustoms.setId(shopPayCustomsId);
            shopPayCustoms.setStatus(-1);
            Response<Boolean> resp = shopPayCustomsWriteService.update(shopPayCustoms);
            if (!resp.isSuccess()) {
                log.error("failed to delete shopPayCustoms by id = {}", shopPayCustomsId);
                throw new JsonResponseException(resp.getError());
            }
            return resp.getResult();
        } catch (Exception e) {
            log.error("{} failed to delete shopPayCustoms by id = {}, cause:", LogUtil.getClassMethodName(), shopPayCustomsId, e);
            throw new JsonResponseException("shop.pay.customs.delete.fail");
        }
    }

    @RequestMapping(value = "/api/shop/profile/pay/customs/list", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Map<String, Object>> customsList() {
        List<Map<String, Object>> result = new ArrayList<>();

        for (Customs customs : Customs.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("customsCode", customs.Code());
            map.put("customsName", customs.Name());
            result.add(map);
        }

        return result;
    }

    private void checkUserShopPayCustoms(Long shopPayCustomsId) {
        //获取登录用户
        CommonUser user = UserUtil.getCurrentUser();
        Long shopId = user.getShopId();
        //获取原有记录
        Response<ShopPayCustoms> rShopPayCustomsOld = shopPayCustomsReadService.findById(shopPayCustomsId);
        if (!rShopPayCustomsOld.isSuccess()) {
            log.error("failed to find ShopPayCustoms by id = {}", shopPayCustomsId);
            throw new JsonResponseException("shop.pay.customs.not.find");
        }
        //检查shopId是否一致
        if (!shopId.equals(rShopPayCustomsOld.getResult().getShopId())) {
            log.error("failed to find ShopPayCustoms by id = {}", shopPayCustomsId);
            throw new JsonResponseException("shop.pay.customs.not.belong.to.user");
        }
    }

    @PostMapping("/api/shop/profile/pay/yunAccount/add")
    public boolean addYunAccount(String appKey, String desKey, String brokerId, String dealerId) {
        CommonUser shopUser = UserUtil.getCurrentUser();
        if (shopUser == null || shopUser.getShopId() == null) {
            throw new RuntimeException(new Translate("未登录或者非店铺帐号").toString());
        }
        YunAccount yunAccount = new YunAccount();
        yunAccount.setAppKey(appKey);
        yunAccount.setKey_3Des(desKey);
        yunAccount.setDealerId(dealerId);
        yunAccount.setBrokerId(brokerId);
        yunAccount.setShopId(shopUser.getShopId());
        yunAccount.setCreatedAt(new Date());
        return shopYunAccountManager.saveYunAccount(yunAccount).orElse(false);
    }

    @PostMapping("/api/shop/profile/pay/yunAccount/modify")
    public boolean modify(String appKey, String desKey, String brokerId, String dealerId) {
        CommonUser shopUser = UserUtil.getCurrentUser();
        if (shopUser == null || shopUser.getShopId() == null) {
            throw new RuntimeException(new Translate("未登录或者非店铺帐号").toString());
        }
        YunAccount yunAccount = shopYunAccountManager.getOneByShopId(shopUser.getShopId()).orElse(null);
        if (yunAccount == null) {
            throw new RuntimeException(new Translate("该数据不正常请刷新重试").toString());
        }
        if (null != appKey) {
            yunAccount.setAppKey(appKey);
        }
        if (null != desKey) {
            yunAccount.setKey_3Des(desKey);
        }
        if (null != brokerId) {
            yunAccount.setBrokerId(brokerId);
        }
        if (null != dealerId) {
            yunAccount.setDealerId(dealerId);
        }
        return shopYunAccountManager.saveYunAccount(yunAccount).orElse(false);
    }

    /**
     * 全量发布
     *
     * @param id
     * @return
     */
    @Deprecated
    @PostMapping("/api/shop/profile/pay/{id}/fullRelease")
    public Result<Boolean> fullRelease(@PathVariable("id") Long id) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        long shopId = commonUser.getShopId();

        var lock = redissonClient.getLock(LockKeyUtils.shopPayInfoFullRelease(shopId));
        if (!lock.tryLock()) {
            return Result.fail("处理中，请稍后重试");
        }

        try {
            var target = shopPayInfoReadService.getById(id).getResult();
            fullReleaseCheck(target, shopId);

            // 发布
            var deleteList = shopPayInfoComponent.fullRelease(target);
            tokenRegister.regPayTokenFromShopPayInfo(shopPayInfoReadService.getById(id).getResult());

            // 事件通知
            EventSender.publish(new ShopPayInfoChangeEvent(target));
            Optional.of(deleteList).ifPresent(list -> list.forEach(deleteObject ->
                    EventSender.publish(new ShopPayInfoDeleteEvent(deleteObject.getShopId(), deleteObject.getPayChannel(),null))));

            return Result.data(true);
        } catch (Exception ex) {
            log.error("ShopPayInfos.fullRelease error, id={}", id, ex);
            return Result.fail(ex.getMessage());
        } finally {
            lock.unlock();
        }
    }

    private void fullReleaseCheck(ShopPayInfo target, Long shopId) {
        if (target == null) {
            throw new RuntimeException("该支付配置已不存在");
        }
        if (!target.getShopId().equals(shopId)) {
            throw new RuntimeException("该支付配置不属于当前商家");
        }
        if (!ShopPayInfoStatusEnum.GRAY_RELEASE.getCode().equals(target.getStatus()) &&
                !ShopPayInfoStatusEnum.INACTIVE.getCode().equals(target.getStatus())) {
            throw new RuntimeException("当前支付配置的状态不满足条件");
        }
    }

    /**
     * 灰度发布
     *
     * @param request
     * @return
     */
    @Deprecated
    @PostMapping("/api/shop/profile/pay/greyRelease")
    public Result<Boolean> greyRelease(@RequestBody ShopPayInfoGreyReleaseRequest request) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        long shopId = commonUser.getShopId();

        var lock = redissonClient.getLock(LockKeyUtils.shopPayInfoGreyRelease(shopId));
        if (!lock.tryLock()) {
            return Result.fail("处理中，请稍后重试");
        }

        try {
            var target = shopPayInfoReadService.getById(request.getShopPayInfoId()).getResult();
            greyReleaseCheck(target, shopId, request);

            // 发布
            shopPayInfoComponent.greyRelease(target, request.getMembers());
            tokenRegister.regPayTokenFromShopPayInfo(shopPayInfoReadService.getById(request.getShopPayInfoId()).getResult());

            // 事件通知
            EventSender.publish(new ShopPayInfoChangeEvent(target));

            return Result.data(true);
        } catch (Exception ex) {
            log.error("ShopPayInfos.greyRelease error, shopId={}, request={}", shopId, JSON.toJSONString(request), ex);
            return Result.fail(ex.getMessage());
        } finally {
            lock.unlock();
        }
    }

    private void greyReleaseCheck(ShopPayInfo target, Long shopId, ShopPayInfoGreyReleaseRequest request) {
        if (target == null) {
            throw new RuntimeException("该支付配置已不存在");
        }
        if (!target.getShopId().equals(shopId)) {
            throw new RuntimeException("该支付配置不属于当前商家");
        }
        if (!ShopPayInfoStatusEnum.INACTIVE.getCode().equals(target.getStatus())) {
            throw new RuntimeException("当前支付配置的状态不满足条件");
        }
        if (CollectionUtils.isEmpty(request.getMembers())) {
            throw new RuntimeException("灰度成员不能为空");
        }
        if (request.getMembers().size() > 5) {
            throw new RuntimeException("灰度成员数量不能超过5");
        }
    }

    /**
     * 判断指定手机号用户是否为商家的会员
     *
     * @param mobile
     * @return
     */
    @PostMapping("/api/shop/profile/pay/{mobile}/judgeMemberUser")
    public Result<Boolean> judgeMemberUser(@PathVariable("mobile") String mobile) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        long shopId = commonUser.getShopId();

        try {
            var targetUser = userReadService.findByMobile(mobile).getResult();
            if (targetUser == null) {
                return Result.data(false);
            }

            return Result.data(shopPayInfoComponent.judgeMemberUser(shopId, targetUser.getId()));
        } catch (Exception ex) {
            log.error("ShopPayInfos.judgeMemberUser error, shopId={}, mobile={}", shopId, mobile, ex);
            return Result.fail(ex.getMessage());
        }
    }

    /**
     * 停止灰度发布
     *
     * @param id
     * @return
     */
    @Deprecated
    @PostMapping("/api/shop/profile/pay/{id}/stopGreyRelease")
    public Result<Boolean> stopGreyRelease(@PathVariable Long id) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        long shopId = commonUser.getShopId();

        try {
            var target = shopPayInfoReadService.getById(id).getResult();
            stopGreyReleaseCheck(target, shopId);

            // 停止
            shopPayInfoComponent.stopGreyRelease(id);

            // 事件通知
            EventSender.publish(new ShopPayInfoDeleteEvent(target.getShopId(), target.getPayChannel(),null));

            return Result.data(true);
        } catch (Exception ex) {
            log.error("ShopPayInfos.stopGreyRelease error, shopId={}, id={}", shopId, id, ex);
            return Result.fail(ex.getMessage());
        }
    }

    private void stopGreyReleaseCheck(ShopPayInfo target, long shopId) {
        if (target == null) {
            throw new RuntimeException("该支付配置已不存在");
        }
        if (!target.getShopId().equals(shopId)) {
            throw new RuntimeException("该支付配置不属于当前商家");
        }
        if (!ShopPayInfoStatusEnum.GRAY_RELEASE.getCode().equals(target.getStatus())) {
            throw new RuntimeException("当前支付配置的状态不满足条件");
        }
    }

    /**
     * 停用支付配置
     *
     * @param id
     * @return
     */
    @Deprecated
    @PostMapping("/api/shop/profile/pay/{id}/stopUsage")
    public Result<Boolean> stopUsage(@PathVariable Long id) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        long shopId = commonUser.getShopId();

        try {
            var target = shopPayInfoReadService.getById(id).getResult();
            stopUsageCheck(target, shopId);

            // 停止
            shopPayInfoComponent.stopUsage(id);

            // 事件通知
            EventSender.publish(new ShopPayInfoDeleteEvent(target.getShopId(), target.getPayChannel(),null));

            return Result.data(true);
        } catch (Exception ex) {
            log.error("ShopPayInfos.stopUsage error, shopId={}, id={}", shopId, id, ex);
            return Result.fail(ex.getMessage());
        }
    }

    private void stopUsageCheck(ShopPayInfo target, long shopId) {
        if (target == null) {
            throw new RuntimeException("该支付配置已不存在");
        }
        if (!target.getShopId().equals(shopId)) {
            throw new RuntimeException("该支付配置不属于当前商家");
        }
        if (!ShopPayInfoStatusEnum.ACTIVE.getCode().equals(target.getStatus())) {
            throw new RuntimeException("当前支付配置的状态不满足条件");
        }
    }

    /**
     * 支付配置详情
     *
     * @return 支付配置
     */
    @RequestMapping(value = "/api/shop/pay/channel/detail")
    public Result<ShopPayChannelDTO> findShopPayChannelDetail() {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        try {
            Shop shop = shopReadService.findById(user.getShopId()).getResult();
            if (Objects.isNull(shop)) {
                return Result.fail("店铺信息不存在");
            }
            ShopPayChannelDTO payChannelDTO = new ShopPayChannelDTO();
            payChannelDTO.setTradeType(shop.getType());

            var relationList = payChannelRelationService.findByShopId(user.getShopId()).getResult();
            if (!CollectionUtils.isEmpty(relationList)) {
                // 不为空,说明有关系存在
                List<Long> idList = relationList.stream().map(relation -> relation.getProFileId()).collect(Collectors.toList());
                var list = shopPayInfoReadService.findByIds(idList).getResult();
                Map<Long, ShopPayInfo> payInfoMap = list.stream().collect(Collectors.toMap(ShopPayInfo::getId, o -> o, (k1, k2) -> k1));
                for (PayChannelRelation relation : relationList) {
                    ShopPayInfo shopPayInfo = payInfoMap.get(relation.getProFileId());
                    if (shopPayInfo == null) {
                        continue;
                    }
                    ShopPayChannelDTO.ShopPayChannelRelationDTO relationDTO = new ShopPayChannelDTO.ShopPayChannelRelationDTO();
                    relationDTO.setId(relation.getId());
                    relationDTO.setProFileId(relation.getProFileId());
                    relationDTO.setShopId(relation.getShopId());
                    relationDTO.setPayChannel(shopPayInfo.getPayChannel());
                    relationDTO.setPayChannelName(ShopPayInfoPayChannelEnum.parseDescription(shopPayInfo.getPayChannel()));

                    if (Objects.equals(0, relation.getTradeType())) {
                        payChannelDTO.setChannelDutyPaid(buildPayChannel(payChannelDTO.getChannelDutyPaid(), relationDTO, relation, shopPayInfo));
                    } else if (Objects.equals(1, relation.getTradeType())) {
                        payChannelDTO.setChannelBonded(buildPayChannel(payChannelDTO.getChannelBonded(), relationDTO, relation, shopPayInfo));
                    }
                }
            }
            return Result.data(payChannelDTO);
        } catch (Exception e) {
            log.error("shopPayInfos.findShopPayChannelDetail error, shopId={} ", user.getShopId(), e);
            return Result.fail(e.getMessage());
        }
    }

    private ShopPayChannelDTO.ShopPayChannelTypeDTO buildPayChannel(ShopPayChannelDTO.ShopPayChannelTypeDTO payChannelTypeDTO
            , ShopPayChannelDTO.ShopPayChannelRelationDTO relationDTO
            , PayChannelRelation relation, ShopPayInfo shopPayInfo){

        if (Objects.isNull(payChannelTypeDTO)) {
            payChannelTypeDTO = new ShopPayChannelDTO.ShopPayChannelTypeDTO();
        }
        if (Objects.equals(1, relation.getRelationType())) { //灰度
            if (!CollectionUtils.isEmpty(shopPayInfo.getExtra())) {
                String members = shopPayInfo.getExtra().get(ShopPayInfoExtraIndexEnum.GREY_RELEASE_MEMBERS.getCode() + relation.getTradeType());
                if (!StringUtils.isBlank(members)) {
                    relationDTO.setMemberList(JSON.parseArray(members, String.class));
                }
            }
            payChannelTypeDTO.setGrayType(relationDTO);
        } else if (Objects.equals(2, relation.getRelationType())) { //全量
            payChannelTypeDTO.setAllType(relationDTO);
        }
        return payChannelTypeDTO;
    }

    /**
     * 灰度/全量发布
     *
     */
    @PostMapping(value = "/api/shop/pay/channel/release")
    public Result<Boolean> findShopPayChannelRelease(@RequestBody PayChannelRelationRequest request) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        var lock = redissonClient.getLock(LockKeyUtils.shopPayInfoGreyRelease(user.getShopId()));
        if (!lock.tryLock()) {
            return Result.fail("处理中，请稍后重试");
        }
        try {
            List<PayChannelRelation> grayList = null;
            PayChannelRelation channelRelation = null;
            PayChannelRelationDTO relationDTO = CopyUtil.copy(request, PayChannelRelationDTO.class);
            if (Objects.isNull(request.getId())) {
                relationDTO.setCreatedBy(user.getName());
            } else {
                //查询历史配置
                channelRelation = payChannelRelationService.findById(request.getId()).getResult();
            }
            //全量发布,查询历史灰度数据
            if (Objects.equals(2, request.getRelationType())) {
                grayList = payChannelRelationService.findByType(user.getShopId(), request.getTradeType(), 1).getResult();
            }
            //更新配置操作
            relationDTO.setUpdatedBy(user.getName());
            Boolean result = payChannelRelationService.shopPayChannelRelease(relationDTO).getResult();

            //配置修改成功后,做事件通知
            if (result) {
                if (Objects.nonNull(channelRelation)) { //不为空,说明为编辑,有历史配置
                    if (!Objects.equals(request.getProFileId(), channelRelation.getProFileId())) { //配置不同,需要先删除历史配置
                        //查询是否存在相同配置
                        List<PayChannelRelation> relationList = payChannelRelationService.findByProFileId(channelRelation.getShopId(), channelRelation.getProFileId()).getResult();
                        if (CollectionUtils.isEmpty(relationList)) { //不存在,说明保税和完税都没有绑定关系
                            //删除店铺注册
                            EventSender.publish(new ShopPayInfoDeleteEvent(channelRelation.getShopId(), null, null));
                        }
                        EventSender.publish(new ShopPayInfoDeleteEvent(channelRelation.getShopId(), channelRelation.getPayChannel(), channelRelation.getTradeType()));
                    }
                }
                //全量发布,删除灰度
                if (Objects.equals(2, request.getRelationType()) && !CollectionUtils.isEmpty(grayList)) { //不为空,说明有灰度
                    for (PayChannelRelation relation : grayList) {
                        //查询是否存在相同配置
                        List<PayChannelRelation> relations = payChannelRelationService.findByProFileId(relation.getShopId(), relation.getProFileId()).getResult();
                        //没有绑定关系, 或者 有关系,且是同一个
                        if (CollectionUtils.isEmpty(relations)
                                || (relations.size() == 1) && Objects.equals(relation.getTradeType(), relations.get(0).getTradeType())) {
                            //删除店铺注册
                            EventSender.publish(new ShopPayInfoDeleteEvent(relation.getShopId(), null, null));
                        }
                        EventSender.publish(new ShopPayInfoDeleteEvent(relation.getShopId(), relation.getPayChannel(), relation.getTradeType()));
                    }
                }
                ShopPayInfo shopPayInfo = shopPayInfoReadService.getById(request.getProFileId()).getResult();
                shopPayInfo.setTradeType(request.getTradeType());

                //事件通知
                tokenRegister.regPayTokenFromShopPayInfo(shopPayInfo);
//                EventSender.publish(new ShopPayInfoChangeEvent(shopPayInfo));
            }
            return Result.data(Boolean.TRUE);
        } catch (DuplicateKeyException e) {
            log.error("shopPayInfos.findShopPayChannelRelease error 重复添加, shopId={} ", user.getShopId(), e);
            return Result.fail("支付类型已存在");
        } catch (Exception e) {
            log.error("shopPayInfos.findShopPayChannelRelease error, shopId={} ", user.getShopId(), e);
            return Result.fail(e.getMessage());
        } finally {
            lock.unlock();
        }
    }


    /**
     * 取消支付配置
     *
     * @return 支付配置
     */
    @PostMapping(value = "/api/shop/pay/channel/cancel")
    public Result<Boolean> shopPayChannelCancel(@RequestBody PayChannelRelationRequest request) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        PayChannelRelation relation = payChannelRelationService.findById(request.getId()).getResult();
        if (Objects.isNull(relation)) {
            return Result.fail("取消失败,未配置关系");
        }

        var lock = redissonClient.getLock(LockKeyUtils.shopPayInfoGreyRelease(relation.getShopId()));
        if (!lock.tryLock()) {
            return Result.fail("处理中，请稍后重试");
        }
        try {
            Boolean result = payChannelRelationService.shopPayChannelCancel(relation).getResult();
            if (result) {
                //查询是否存在相同配置
                List<PayChannelRelation> relationList = payChannelRelationService.findByProFileId(relation.getShopId(), relation.getProFileId()).getResult();
                if (CollectionUtils.isEmpty(relationList)) { //不存在,说明保税和完税都没有绑定关系
                    //删除店铺注册
                    EventSender.publish(new ShopPayInfoDeleteEvent(relation.getShopId(), null, null));
                }
                EventSender.publish(new ShopPayInfoDeleteEvent(relation.getShopId(), relation.getPayChannel(), relation.getTradeType()));
            }
            return Result.data(Boolean.TRUE);
        } catch (Exception e) {
            log.error("shopPayInfos.shopPayChannelCancel error, shopId={} ", user.getShopId(), e);
            return Result.fail(e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 支付配置,1 启用, 2 禁用
     *
     * @return 支付配置
     */
    @PostMapping(value = "/api/shop/profile/pay/disable")
    public Result<Boolean> shopPayChannelDisable(@RequestBody ShopPayInfoDTO shopPayInfoDTO) {
        log.info("支付配置启用禁用入参={}", JSON.toJSONString(shopPayInfoDTO));
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        try {
            if (Objects.equals(2, shopPayInfoDTO.getDisable())) {
                var relationList = payChannelRelationService.findByProFileId(user.getShopId(), shopPayInfoDTO.getId()).getResult();
                if (!CollectionUtils.isEmpty(relationList)) {
                    return Result.fail("支付方式存在[灰度发布]或[全量发布]，不允许关闭");
                }
            }
            shopPayInfoComponent.shopPayChannelStatus(shopPayInfoDTO.getId(), Objects.equals(2, shopPayInfoDTO.getDisable()) ? ShopPayInfoStatusEnum.INACTIVE : ShopPayInfoStatusEnum.ACTIVE);
            return Result.success("");
        } catch (Exception e) {
            log.error("shopPayInfos.shopPayChannelDisable error, shopId={} ", user.getShopId(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 支付配置下拉
     *
     * @return 支付配置
     */
    @PostMapping(value = "/api/shop/profile/pay/select")
    public Result<List<ShopPayInfoDTO>> findShopPayChannelSelect() {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        try {
            //查询启用状态的支付配置信息
            var list = shopPayInfoReadService.findByStatus(user.getShopId(), ShopPayInfoStatusEnum.ACTIVE).getResult();
            return Result.data(shopPayInfoConvertor.convert(list));
        } catch (Exception e) {
            log.error("shopPayInfos.findShopPayChannelSelect error, shopId={} ", user.getShopId(), e);
            return Result.fail(e.getMessage());
        }
    }

}
