package moonstone.web.front.distribution.web.drop;

import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.exception.InvalidException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.UserUtil;
import moonstone.item.dto.ItemDistributionPrice;
import moonstone.item.dto.SkuDistributionPrice;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.item.service.SkuWriteService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopItemWriteService;
import moonstone.web.core.events.item.DistributionPriceSetEvent;
import moonstone.web.front.component.item.DistributionPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Created by CaiZhy on 2018/12/4.
 */
@Slf4j
@RestController
@RequestMapping("/api/distributionPrice")
public class DistributionPrices {
    @RpcConsumer
    private ItemReadService itemReadService;

    @RpcConsumer
    private SkuReadService skuReadService;

    @RpcConsumer
    private SkuWriteService skuWriteService;

    @RpcConsumer
    private ShopReadService shopReadService;

    @Autowired
    private DistributionPriceService distributionPriceService;
    @Autowired
    private WeShopItemWriteService weShopItemWriteService;
    @Autowired
    private WeShopItemReadService weShopItemReadService;


    @RequestMapping(value = "/setItemPrice", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean setItemPrice(@RequestBody ItemDistributionPrice itemDistributionPrice) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            //检查商家是否开启微分销
            checkIsOpenWeShop(commonUser.getShopId());

            Response<Item> rItem = itemReadService.findById(itemDistributionPrice.getItemId());
            if (!rItem.isSuccess()) {
                log.error("failed to find item by id={}, error code: {}", itemDistributionPrice.getItemId(), rItem.getError());
                throw new JsonResponseException(rItem.getError());
            }
            Item item = rItem.getResult();
            if (!Objects.equal(item.getShopId(), commonUser.getShopId())) {
                log.error("item(id={} is not belong to user(id={})", item.getId(), commonUser.getId());
                throw new JsonResponseException("item.not.belong.to.user");
            }

            Response<Long> rCount = skuReadService.countByItemId(item.getId());
            if (!rCount.isSuccess()) {
                log.error("failed to count skus by itemId={}, error code: {}", item.getId(), rCount.getError());
                throw new JsonResponseException(rCount.getError());
            }
            if (itemDistributionPrice.getSkuPrices().size() < rCount.getResult()) {
                log.error("the num of skus is incomplete, itemDistribution({})", itemDistributionPrice);
                throw new JsonResponseException("sku.count.incomplete");
            } else if (itemDistributionPrice.getSkuPrices().size() > rCount.getResult()) {
                log.error("the num of skus is illegal, itemDistribution({})", itemDistributionPrice);
                throw new JsonResponseException("sku.count.illegal");
            }
            Response<Boolean> response = distributionPriceService.setItemDistributionPrice(itemDistributionPrice, item);
            if (!response.isSuccess()) {
                log.error("failed to set distribution price ({}), error code: {}", itemDistributionPrice, response.getError());
                throw new JsonResponseException(response.getError());
            }

            EventSender.sendApplicationEvent(new DistributionPriceSetEvent(item.getId()));

            return response.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to set distribution prices by itemDistributionPrice({}), cause:{}", itemDistributionPrice, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("distribution.price.set.fail");
        }
    }

    @RequestMapping(value = "/setSkuPrice", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean setSkuPrice(@RequestBody SkuDistributionPrice skuDistributionPrice) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            //检查商家是否开启微分销
            checkIsOpenWeShop(commonUser.getShopId());

            Response<Sku> rSku = skuReadService.findSkuById(skuDistributionPrice.getSkuId());
            if (!rSku.isSuccess()) {
                log.error("failed to find sku by id={}, error code: {}", skuDistributionPrice.getSkuId(), rSku.getError());
                throw new JsonResponseException(rSku.getError());
            }
            Sku existSku = rSku.getResult();
            if (!Objects.equal(existSku.getShopId(), commonUser.getShopId())) {
                log.error("sku(id={} is not belong to user(id={})", existSku.getId(), commonUser.getId());
                throw new JsonResponseException("sku.not.belong.to.user");
            }

            //extraJson中sellInWeShop不为true的不允许单独改单独一个sku分销价
            Map<String, String> extra = existSku.getExtraMap();
            if (extra == null || extra.get(DistributionConstants.SELL_IN_WE_SHOP) == null || !extra.get(DistributionConstants.SELL_IN_WE_SHOP).equals("true")) {
                log.error("can not set distribution price to sku(id={}) not sell in weShop", existSku.getId());
                throw new JsonResponseException("can.not.set.distribution.price.sku.not.weShop");
            }

            Map<String, Integer> extraPrice = existSku.getExtraPrice();
            extraPrice.put(DistributionConstants.SKU_DISTRIBUTION_PRICE, skuDistributionPrice.getDistributionPrice().intValue());
            // modify by liuchao 20190401
            extraPrice.put(DistributionConstants.SUPPLY_PRICE, skuDistributionPrice.getSupplyPrice().intValue());
            /* extraPrice.put(DistributionConstants.SKU_PROFIT, skuDistributionPrice.getProfit().intValue());*/
            Sku sku = new Sku();
            sku.setId(existSku.getId());
            sku.setExtraPrice(extraPrice);
            Response<Boolean> response = skuWriteService.updateSku(sku);
            if (!response.isSuccess()) {
                log.error("failed to update sku({}), error code: {}", sku, response.getError());
                throw new JsonResponseException(response.getError());
            }

            EventSender.sendApplicationEvent(new DistributionPriceSetEvent(sku.getItemId()));

            return response.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to set distribution price by skuDistributionPrice({}), cause:{}", skuDistributionPrice, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("distribution.price.set.fail");
        }
    }

    @RequestMapping(value = "/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<Sku> paging(@RequestParam(required = false) Long itemId,
                              @RequestParam(required = false) String name,
                              @RequestParam(required = false) Integer pageNo,
                              @RequestParam(required = false) Integer pageSize) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        try {
            Response<Paging<Sku>> rSkuPaging = skuReadService.pagingSellInWeShop(shopId, itemId, name, pageNo, pageSize);
            if (!rSkuPaging.isSuccess()) {
                log.error("failed to paging skus by shopId={}, itemId={}, name={}, pageNo={}, pageSize={}, error code: {}",
                        shopId, itemId, name, pageNo, pageSize, rSkuPaging.getError());
                throw new JsonResponseException(rSkuPaging.getError());
            }
            return rSkuPaging.getResult();
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to paging skus by shopId={}, itemId={}, name={}, pageNo={}, pageSize={}, cause:{}",
                    shopId, itemId, name, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("sku.paging.fail");
        }
    }

    private void checkIsOpenWeShop(Long shopId) {
        Response<Shop> rShop = shopReadService.findById(shopId);
        if (!rShop.isSuccess()) {
            log.error("failed to find shop by id={}, error code: {}", shopId, rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        Shop shop = rShop.getResult();
        Map<String, String> extra = shop.getExtra();
        if (extra == null || !Objects.equal("true", extra.get(DistributionConstants.SHOP_OPEN_WE_SHOP))) {
            throw new JsonResponseException("shop.not.open.weShop");
        }
    }

}
