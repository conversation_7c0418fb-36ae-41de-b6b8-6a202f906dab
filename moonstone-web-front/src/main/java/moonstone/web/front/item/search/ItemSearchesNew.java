package moonstone.web.front.item.search;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import io.vertx.core.json.JsonObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.cache.FrontCategoryCacher;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.ShopCategoryCacher;
import moonstone.category.model.FrontCategory;
import moonstone.category.model.ShopCategory;
import moonstone.common.api.Result;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.EmptyUtils;
import moonstone.common.utils.R;
import moonstone.common.utils.UserUtil;
import moonstone.common.utils.XCXUtil;
import moonstone.item.dto.ItemPromotionVO;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.search.dto.*;
import moonstone.search.item.ItemSearchReadService;
import moonstone.search.weShopItem.WeShopItemSearchReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.model.SubStore;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.user.model.IsFirstBuyer;
import moonstone.web.core.component.cache.StoreIntegralCache;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.front.component.item.SearchItemProfitViewHelper;
import moonstone.web.front.item.app.ItemSearchDecorateHelper;
import moonstone.web.front.item.search.vo.SearchItemProfitView;
import moonstone.web.front.item.search.vo.SetDefaultFrontPageItemIdsRequest;
import moonstone.web.front.logic.ItemsEsIndexFrontLogic;
import moonstone.web.front.shop.app.ShopExtraConfigureComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 商品搜索相关接口
 * @Author: wuxian-yjp
 * @Date: 2019/8/1 9:24
 */
@ConditionalOnProperty(value = "enable.item.search", havingValue = "true", matchIfMissing = true)
@RestController
@Slf4j
public class ItemSearchesNew {

	@RpcConsumer
	SkuReadService skuReadService;
	@RpcConsumer
	StoreIntegralCache storeIntegralCache;
	@Autowired
	SubStoreCache subStoreCache;
	@Autowired
	GuiderCache guiderCache;
	@Autowired
	ServiceProviderCache serviceProviderCache;
	@RpcConsumer
	private WeShopItemSearchReadService weShopItemSearchReadService;
	@RpcConsumer
	private ItemSearchReadService itemSearchReadService;
	private LoadingCache<Long, List<Sku>> skuCache;
	@Resource
	private ShopCategoryCacher shopCategoryCacher;
	@Resource
	private FrontCategoryCacher frontCategoryCacher;
	@Resource
	private ShopCacheHolder shopCacheHolder;
	@Autowired
	private SearchItemProfitViewHelper searchItemProfitViewHelper;
	@Resource
	private ItemCacheHolder itemCacheHolder;
	@Resource
	private ShopExtraConfigureComponent shopExtraConfigureComponent;
	@Resource
	private ItemSearchDecorateHelper itemSearchDecorateHelper;
	@Resource
	private ItemsEsIndexFrontLogic itemsEsIndexFrontLogic;

	@PostConstruct
	public void init() {
		skuCache = Caffeine.newBuilder()
				.expireAfterWrite(30, TimeUnit.SECONDS)
				.maximumSize(10000)
				.build(this::infoCache);
	}

	/**
	 * 店铺内按类目分类
	 */
	@RequestMapping(value = "/api/news/search-in-shop/category", produces = MediaType.APPLICATION_JSON_VALUE)
	public R searchItemInShopWithAggsByCategory(@RequestParam(required = false) Integer pageNo,
	                                            @RequestParam(required = false) Integer pageSize,
	                                            @RequestParam Map<String, String> params) {
		// n1:  小程序商品搜索列表（普通商城）
		String shopId = params.get("shopId");
		if (!StringUtils.hasText(shopId)) {
			log.error("shop id is required when search in shop");
			return R.error(-1, "shopId empty");
		}
		//此参数不为空即查询全部商品
		String typeAll = params.get("typeAll");
		if (ObjectUtils.isEmpty(typeAll)) {
			// 默认不显示积分商品
			String type = params.get("type");
			if (ObjectUtils.isEmpty(type)) {
				params.put("type", "1");
			}
		}
		Map<String, String> map = new HashMap<>(params);
		//供销和分销小程序商品来源区分
		if (XCXUtil.getCurrentFlag()) {
			map.put("weShopId", map.get("shopId"));
			map.remove("shopId");
			map.put("status", "1");
			return buildWeShopCategory(shopId, map);
		} else {
			return buildCategory(shopId, map);
		}
	}

	/**
	 * 供销小程序
	 * 供销用的全局分类 表we_shops
	 */
	private R buildWeShopCategory(String weShopId, Map<String, String> params) {
		SearchedItemByCategory searchedItemByCategory = new SearchedItemByCategory();
		List<FrontCategory> frontCategories = frontCategoryCacher.findByPid(0L);
		List<Categorys> categoryList = new ArrayList<>();
		Categorys allItemCategoryRoot = new Categorys();
		allItemCategoryRoot.setCategoryId("-1");
		allItemCategoryRoot.setCategoryName("全部宝贝");
		allItemCategoryRoot.setList(assembleItemsWeShop(-1, params, weShopId));
		categoryList.add(allItemCategoryRoot);
		if (EmptyUtils.isNotEmpty(frontCategories)) {
			for (FrontCategory entity : frontCategories) {
				Categorys categorys = new Categorys();
				categorys.setCategoryId(entity.getId() + "");
				categorys.setCategoryName(entity.getName());
				categorys.setList(assembleItemsWeShop(entity.getId().intValue(), params, weShopId));
				categoryList.add(categorys);
			}
		}
		categoryList = categoryList.stream().filter(entity -> EmptyUtils.isNotEmpty(entity.getList())).collect(Collectors.toList());

		searchedItemByCategory.setItemList(categoryList);
		return R.ok().add("data", searchedItemByCategory);
	}

	/**
	 * 组装供销小程序
	 */
	private List<ItemsNew> assembleItemsWeShop(int key, Map<String, String> params, String weShopId) {
		String templateName = "search.mustache";
		Map<String, String> mapParams = new HashMap<>(params);
		if (EmptyUtils.isNotEmpty(key) && !Objects.equals(-1, key)) {
			mapParams.put("fcid", key + "");
		}
		Response<? extends SearchedWeShopItemInWeShopWithAggs<SearchedWeShopItem>> stm = weShopItemSearchReadService.searchInWeShopWithAggs(1, 200, templateName, mapParams, SearchedWeShopItem.class);
		if (!stm.isSuccess() || stm.getResult().getEntities() == null || stm.getResult().getEntities().getData().isEmpty()) {
			return new ArrayList<>();
		}
		List<ItemsNew> inl = new ArrayList<>();
		for (SearchedWeShopItem sim : stm.getResult().getEntities().getData()) {
			ItemsNew inw = new ItemsNew();
			BeanUtils.copyProperties(sim, inw);
			inw.setMainPic(sim.getMainImage());
			inw.setTitle(infoNames(sim.getName()));
			if (sim.getOriginId() != null) {
				JSONObject o = new JSONObject();
				o.put("id", sim.getOriginId());
				o.put("icon", sim.getOriginUrl());
				o.put("name", sim.getOrigin());
				inw.setOrigin(o);
			}
			inl.add(inw);
		}
		return inl;
	}

	/**
	 * 按类目分组
	 */
	private R buildCategory(String shopId, Map<String, String> params) {
		SearchedItemByCategory searchedItemByCategory = new SearchedItemByCategory();
		List<ShopCategory> shopCategories = shopCategoryCacher.findByShopIdAndSort(Long.valueOf(shopId));

		List<Categorys> categorysList = new ArrayList<>();
		Categorys categorys1 = new Categorys();
		categorys1.setCategoryId("-1");
		categorys1.setCategoryName("全部宝贝");
		categorys1.setList(assembleItems(-1, params, shopId));
		categorysList.add(categorys1);
		if (EmptyUtils.isNotEmpty(shopCategories)) {
			for (ShopCategory entity : shopCategories) {
				Categorys categorys = new Categorys();
				categorys.setCategoryId(entity.getId() + "");
				categorys.setCategoryName(entity.getName());
				categorys.setList(assembleItems(entity.getId(), params, shopId));
				categorysList.add(categorys);
			}
		}
		categorysList = categorysList.stream().filter(entity -> EmptyUtils.isNotEmpty(entity.getList())).collect(Collectors.toList());

		searchedItemByCategory.setItemList(categorysList);
		return R.ok().add("data", searchedItemByCategory);
	}

	/**
	 * 组装商品明细
	 */
	private List<ItemsNew> assembleItems(Object key, Map<String, String> params, String shopId) {
		String templateName = "search.mustache";
		if (EmptyUtils.isNotEmpty(key) && !Objects.equals(-1, key)) {
			params.put("shopCatId", key + "");
		}
		Response<? extends SearchedItemInShopWithAggs<SearchedItem>> stm = itemSearchReadService.searchInShopWithAggs(1, 200, templateName, params, SearchedItem.class);
		if (!stm.isSuccess() || stm.getResult().getEntities() == null || stm.getResult().getEntities().getData().isEmpty()) {
			return new ArrayList<>();
		}
		Shop shop = shopCacheHolder.findShopById(Long.parseLong(shopId));
		val isCommonShop = Optional.ofNullable(shop.getExtra()).orElse(new HashMap<>()).getOrDefault(ShopExtra.SalesPattern.getCode(), ShopExtra.commonShop.getCode()).equals(ShopExtra.commonShop.getCode());
		List<ItemsNew> inl = new ArrayList<>();
		for (SearchedItem sim : stm.getResult().getEntities().getData()) {
			ItemsNew inw = new ItemsNew();
			BeanUtils.copyProperties(sim, inw);
			inw.setMainPic(sim.getMainImage());
			inw.setTitle(infoNames(sim.getName()));
			if (sim.getOriginId() != null) {
				JSONObject o = new JSONObject();
				o.put("id", sim.getOriginId());
				o.put("icon", sim.getOriginUrl());
				o.put("name", sim.getOrigin());
				inw.setOrigin(o);
			}
			//目前只有普通商品才会显示佣金率
			if (Objects.equals(sim.getType(), 1) && !isCommonShop) {
				inw.setRate(searchItemProfitViewHelper.packProfitView(sim.getId(), shopId, sim.getPrice(), UserUtil.getCurrentUser()));
			}
			inl.add(inw);
		}
		return inl;
	}


	/**
	 * 店铺内搜索
	 *
	 * @param pageNo   起始页码
	 * @param pageSize 每页记录条数
	 * @param params   搜索上下文
	 * @return 搜索结果, 包括属性导航, 面包屑等
	 */
//	@RequestMapping(value = "/api/news/search-in-shop", produces = MediaType.APPLICATION_JSON_VALUE)
	public R searchItemInShopWithAggs(@RequestParam(required = false) Integer pageNo,
	                                  @RequestParam(required = false) Integer pageSize,
	                                  @RequestParam Map<String, String> params) {
		String shopId = params.get("shopId");
		if (!StringUtils.hasText(shopId)) {
			log.error("shop id is required when search in shop");
			return R.error(-1, "shopId empty");
		}

		pageNo = (pageNo == null || pageNo <= 0) ? 1 : pageNo;
		pageSize = (pageSize == null || pageSize <= 0) ? 20 : Math.min(pageSize, 20);

		String typeAll = params.get("typeAll");//此参数不为空即查询全部商品
		if (ObjectUtils.isEmpty(typeAll)) {
			// 默认不显示积分商品
			String type = params.get("type");
			if (ObjectUtils.isEmpty(type)) {
				params.put("type", "1");
			}
		}
		String templateName = "search.mustache";
		// add limit to the role
		limitItemByRoleForSubStore(shopId, params);
		Response<? extends SearchedItemInShopWithAggs<SearchedItem>> stm = itemSearchReadService.searchInShopWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);

		return decorateItems(stm, shopId);
	}

	/**
	 * 分页查询商品列表
	 * @param params 请求参数
	 * @return 商品列表
	 */
	@GetMapping("/api/news/search-in-shop")
	public Result<SearchedItemNew> miniPageItem(@RequestParam Map<String,String> params){
		log.info("小程序端 分页查询商品列表 请求参数 {}", JSONUtil.toJsonStr(params));
		return Result.data(itemsEsIndexFrontLogic.miniPageItem(params));
	}

	/**
	 * 设置指定shop的首页，在未登录的情况下，显示的商品id
	 *
	 * @param request
	 * @return
	 */
	@PostMapping("/api/news/search-in-shop/setDefaultFrontPageItemIds")
	public Result<Boolean> setDefaultFrontPageItemIds(@RequestBody SetDefaultFrontPageItemIdsRequest request) {
		CommonUser user = UserUtil.getCurrentUser();
		if (user == null || user.getShopId() == null) {
			return Result.fail("请先登录");
		}
		if (request == null || !StringUtils.hasText(request.getItemIds())) {
			return Result.fail("入参皆不能为空");
		}

		//更新
		try {
			return Result.data(shopExtraConfigureComponent.setDefaultFrontPageItemIds(user.getShopId(), request.getItemIds()));
		} catch (Exception ex) {
			log.error("setDefaultFrontPageItemIds error: ", ex);
			return Result.fail(ex.getMessage());
		}
	}

	private void limitItemByRoleForSubStore(String shopIdStr, Map<String, String> params) {
		try {
			Long userId = UserUtil.getUserId();
			if (userId == null) {
				params.put("ids", shopExtraConfigureComponent.findDefaultFrontPageItemIds(Long.parseLong(shopIdStr)));
				params.remove("ssid");
				return;
			}
			long shopId = Long.parseLong(shopIdStr);
			Map<String, String> shopModel = Optional.ofNullable(shopCacheHolder.findShopById(shopId).getExtra())
					.orElse(Collections.emptyMap());
			if (!SalePattern.SubStore.getCode().equals(shopModel.get(ShopExtra.SalesPattern.getCode()))) {
				return;
			}
			Optional<SubStore> subStore = subStoreCache.findByShopIdAndUserId(shopId, userId);
			if (subStore.isPresent()) {
				params.put("ssid", subStore.get().getId() + "");
				return;
			}
			Optional<SubStoreTStoreGuider> guider = guiderCache.findByShopIdAndUserId(shopId, userId);
			if (guider.isPresent()) {
				params.put("ssid", guider.get().getSubStoreId() + "");
				return;
			}
			serviceProviderCache.findServiceProviderByShopIdAndUserId(shopId, userId)
					.ifPresent(userLevelView ->
							params.put("ssid", serviceProviderCache.findServiceProviderByUserIdAndShopId(userId, shopId)
									.getId())
					);
		} catch (Exception e) {
			log.warn("Shop Not Exists");
		}
	}

	private R decorateItems(Response<? extends SearchedItemInShopWithAggs<SearchedItem>> stm, String shopId) {
		SearchedItemNew list = new SearchedItemNew();
		if (!stm.isSuccess() || stm.getResult().getEntities() == null || stm.getResult().getEntities().getData().isEmpty()) {
			// return R.ok().add("msg", "未查询到数据！！").add("data", null);
			return R.ok().add("data", new JsonObject().put("list", Collections.emptyList()).put("total", 0).getMap());
		}
		Shop shop = shopCacheHolder.findShopById(Long.parseLong(shopId));
		val isCommonShop = Optional.ofNullable(shop.getExtra()).orElse(new HashMap<>()).getOrDefault(ShopExtra.SalesPattern.getCode(), ShopExtra.commonShop.getCode()).equals(ShopExtra.commonShop.getCode());
		List<ItemsNew> inl = new ArrayList<>();
		for (SearchedItem sim : stm.getResult().getEntities().getData()) {
			ItemsNew inw = new ItemsNew();
			BeanUtils.copyProperties(sim, inw);
			inw.setMainPic(sim.getMainImage());
			inw.setTitle(infoNames(sim.getName()));
			if (sim.getOriginId() != null) {
				JSONObject o = new JSONObject();
				o.put("id", sim.getOriginId());
				o.put("icon", sim.getOriginUrl());
				o.put("name", sim.getOrigin());
				inw.setOrigin(o);
			}
			//目前只有普通商品才会显示佣金率
			if (Objects.equals(sim.getType(), 1) && !isCommonShop) {
				inw.setRate(searchItemProfitViewHelper.packProfitView(sim.getId(), shopId, sim.getPrice(), UserUtil.getCurrentUser()));
			}

			inl.add(inw);
		}

		//填充额外的商品和sku信息
		itemSearchDecorateHelper.appendAdditionalItemInfo(inl);
		itemSearchDecorateHelper.appendAdditionalSkuInfo(inl);

		list.setList(inl);
		list.setTotal(stm.getResult().getEntities().getTotal());

		//设置关键词2
		if (stm.getResult().getShopCategories() == null || stm.getResult().getShopCategories().isEmpty()) {
			if (stm.getResult().getBreadCrumbs() == null || stm.getResult().getBreadCrumbs().isEmpty()) {
				List<JSONObject> keyWords = new ArrayList<>();
				JSONObject jsonKeyWords = new JSONObject();
				for (IdAndName idAndName : stm.getResult().getBreadCrumbs()) {
					if (!ObjectUtils.isEmpty(idAndName.getName()) && !StringUtils.isEmpty(idAndName.getId())
							&& !Objects.equals(idAndName.getId(), 0L)) {
						jsonKeyWords.put(idAndName.getId() + "", idAndName.getName());
						keyWords.add(jsonKeyWords);
					}
				}
				list.setKeyWords(keyWords);
			}
			return R.ok().add("data", list);
		}
		//设置关键词2
		List<JSONObject> keyWords = new ArrayList<>();
		JSONObject jsonKeyWords = new JSONObject();
		for (AggNav agv : stm.getResult().getShopCategories()) {
			if (!ObjectUtils.isEmpty(agv.getName()) && !StringUtils.isEmpty(agv.getKey())
					&& !Objects.equals(String.valueOf(agv.getKey()), "-1")) {
				jsonKeyWords.put(agv.getKey() + "", agv.getName());
				keyWords.add(jsonKeyWords);
			}
		}
		list.setKeyWords(keyWords);

		return R.ok().add("data", list);
	}

	//过滤所有的搜索<em></em>
	private String infoNames(String name) {
		return name.replaceAll("<em>", "").replaceAll("</em>", "");
	}

	/**
	 * 礼品类商品搜索
	 * type  3  积分商品  4 新客礼  5 累计礼
	 *
	 * @param pageNo   起始页码
	 * @param pageSize 每页记录条数
	 * @param params   搜索上下文
	 * @return 搜索结果, 包括属性导航, 面包屑等
	 */
	@RequestMapping(value = "/api/search-in-shop-gift", produces = MediaType.APPLICATION_JSON_VALUE)
	public R searchItemInShopWithAggsGift(@RequestParam(required = false) Integer pageNo,
	                                      @RequestParam(required = false) Integer pageSize,
	                                      @RequestParam Map<String, String> params) {
		String shopId = params.get("shopId");
		if (!StringUtils.hasText(shopId)) {
			log.error("shop id is required when search in shop");
			return R.error(-1, "shopId empty");
		}
		// 默认不显示积分商品
		String type = params.get("type");
		String types = params.get("types");
		if (ObjectUtils.isEmpty(type) && StringUtils.isEmpty(types)) {
			log.error("type id is required when search in shop");
			return R.error(-1, "type empty");
		}

		//TODO 检查店铺是否存在和是否冻结?
		String templateName = "search.mustache";
		Response<? extends SearchedItemInShopWithAggs<SearchedItem>> stm = itemSearchReadService.searchInShopWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);

		return infoGift(stm, Long.valueOf(shopId));
	}

	private R infoGift(Response<? extends SearchedItemInShopWithAggs<SearchedItem>> stm, Long shopId) {
		SearchedItemGiftNew list = new SearchedItemGiftNew();
		if (!stm.isSuccess() || stm.getResult().getEntities() == null || stm.getResult().getEntities().getData().isEmpty()) {
			return R.ok().add("msg", "未查询到数据！！").add("data", null);
		}
		List<ItemsGiftNew> inl = new ArrayList<>();
		for (SearchedItem sim : stm.getResult().getEntities().getData()) {
			ItemsGiftNew inw = new ItemsGiftNew();
			BeanUtils.copyProperties(sim, inw);
			inw.setMainPic(sim.getMainImage());
			inw.setTitle(sim.getName());
			//目前只考虑单规格的商品
			//拓展属性
			if (sim.getType().equals(4)) {
				inw.setExtra(infoJson(shopId));
			}
			if (sim.getType().equals(3)) {
				inw.setOriginPrice(infoJsonByType(sim.getId()));
			}
			if (sim.getOriginId() != null) {
				JSONObject o = new JSONObject();
				o.put("id", sim.getOriginId());
				o.put("icon", sim.getOriginUrl());
				o.put("name", sim.getOrigin());
				inw.setOrigin(o);
			}

			inl.add(inw);
		}
		//过滤皇家的新客礼首次机会
		List<ItemsGiftNew> inls = inl.stream().sorted(Comparator.comparing(ItemsGiftNew::getType)).filter(ign -> (ign.getExtra() == null || (ign.getExtra() != null && Objects.equals(ign.getExtra().get("isFlag"), "false")))).collect(Collectors.toList());

		list.setList(inls);
		list.setTotal(inls.size());


		if (stm.getResult().getShopCategories() == null || stm.getResult().getShopCategories().isEmpty()) {

			return R.ok().add("data", list);
		}
		List<String> keyWords = new ArrayList<>();
		for (AggNav agv : stm.getResult().getShopCategories()) {
			if (agv.getName() != null) {
				keyWords.add(agv.getName());
			}
		}
		keyWords.remove("未分类");
		list.setKeyWords(keyWords);

		return R.ok().add("data", list);
	}

	private Integer infoJsonByType(Long id) {
		Integer originPrice = 0;
		List<Sku> list = skuCache.get(id);
		if (list != null && !list.isEmpty()) {
			if (list.get(0).getExtraPrice() != null && list.get(0).getExtraPrice().containsKey("originPrice")) {
				originPrice = list.get(0).getExtraPrice().get("originPrice");
			}
		}
		return originPrice;
	}

	/**
	 * 拓展属性
	 */
	private JSONObject infoJson(Long shopId) {
		JSONObject json = new JSONObject();
		json.put("isFlag", "false");
		if (!ObjectUtils.isEmpty(UserUtil.getUserId())) {
			Long userId = UserUtil.getUserId();
			List<IsFirstBuyer> list = storeIntegralCache.getList(shopId);//storeIntegralCache.get(shopId);
			if (list == null || list.isEmpty()) {
				return json;
			}
			for (IsFirstBuyer li : list) {
				if (li.getUserId().equals(userId) && li.getIsFlag()) {
					json.put("isFlag", "true");
				}
			}
			return json;
		}
		return json;
	}

	private List<Sku> infoCache(Long id) {
		//目前只考虑单规格的商品
		Response<List<Sku>> skuList = skuReadService.findSkusByItemId(id);
		if (!skuList.isSuccess() || skuList.getResult().isEmpty()) {
			return new ArrayList<>();
		}
		return skuList.getResult();
	}

	@Data
	public static class SearchedItemNew {
		List<ItemsNew> list = Collections.emptyList();
		Long total = 0L;//总数量
		List<JSONObject> keyWords;//关键词
	}

	@Data
	public static class SearchedItemByCategory {
		List<Categorys> itemList;
		Long total;//总数量
	}

	@Data
	public static class Categorys {
		String categoryId;
		String categoryName;
		List<ItemsNew> list;
	}

	@Data
	public static class ItemsNew {
		Long weShopId;
		Long id;//商品id
		String mainPic;//商品主图
		String title;//商品名
		Integer price;//商品价格
		Integer saleQuantity;//销售数量
		Integer isBonded;//是否保税和完税
		Long shopId;
		String shopName;
		/**
		 * 来源国信息
		 * id 来源国id
		 * icon  来源国图片
		 * name 来源国名
		 */
		JSONObject origin;//json
		/**
		 * 放入前端需要显示的佣金比率 firstRate 一级佣金率 fee=price*firstRate(万分制) secondRate 二级佣金率
		 * firstFee  --一级固定佣金 secondFee  --二级固定佣金  fee  （固定金额=firstFee）
		 */
		SearchItemProfitView rate;

		/**
		 * 划线价（单位: 分）
		 */
		private Integer crossedPrice;

		/**
		 * 售罄状态
		 *
		 * @see moonstone.item.emu.ItemSellOutStatusEnum
		 */
		private Integer sellOutStatus;

        /**
         * 该商品可用的营销活动
         */
        private List<ItemPromotionVO> promotionList;

		/**
		 * 商品上的标记列表，如 活动商品，限定商品之类的
		 */
		private List<String> flagList;
	}

	@Data
	public static class SearchedItemGiftNew {
		List<ItemsGiftNew> list;
		Integer total;//总数量
		List<String> keyWords;//关键词
	}

	@Data
	public static class ItemsGiftNew implements Comparable<ItemsGiftNew> {
		Long id;//商品id
		String mainPic;//商品主图
		String title;//商品名
		Integer price;//商品积分
		Integer saleQuantity;//销售数量
		Integer isBonded;//是否保税和完税
		Integer Type;//1普通商品 2-组合商品 3-积分商品 4-新客礼品 5-累计礼品
		Long shopId;
		String shopName;
		JSONObject origin;//json
		JSONObject extra;//拓展属性
		Integer originPrice;//原销售价
		String promotionName;
		Integer previewPrice;

		@Override
		public int compareTo(ItemsGiftNew ob) {
			return this.getType().compareTo(ob.getType());
		}
	}


}
