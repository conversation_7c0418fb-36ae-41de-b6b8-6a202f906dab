package moonstone.web.front.item;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.model.CommonUser;
import moonstone.common.model.vo.ComboBoxVo;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.common.model.vo.PageVo;
import moonstone.common.utils.UserUtil;
import moonstone.web.core.fileNew.dto.ItemPageDto;
import moonstone.web.core.fileNew.dto.ItemUpdateReqDto;
import moonstone.web.core.fileNew.enums.ItemCustomsEum;
import moonstone.web.core.fileNew.enums.SourceTypeEnum;
import moonstone.web.core.fileNew.logic.ItemsLogic;
import moonstone.web.core.fileNew.vo.ItemSortRequestVo;
import moonstone.web.core.fileNew.vo.ItemSortedVo;
import moonstone.web.core.fileNew.vo.ItemsVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 商品相关接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/items")
@Slf4j
public class ItemsV2Controller {

    @Resource
    private ItemsLogic itemsLogic;


    /**
     * 分页查询商品列表
     *
     * @param req 请求参数
     * @return 商品列表
     */
    @PostMapping("paging/v2")
    public Result<PageVo<ItemsVo>> pages(@RequestBody @Valid ItemPageDto req) {
        log.info("分页查询商品列表 请求参数 {}", JSONUtil.toJsonStr(req));
        return Result.data(itemsLogic.pages(req));
    }

    /**
     * 修改商品价格
     *
     * @param req 请求参数
     * @return 是否修改成功
     */
    @PostMapping("update/price")
    public Result<Boolean> updatePrice(@RequestBody ItemUpdateReqDto req) {
        log.info("修改商品价格 请求参数 {}", JSONUtil.toJsonStr(req));
        return Result.data(itemsLogic.updatePrice(req));
    }

    /**
     * 修改商品平台可售库存
     *
     * @param req 请求参数
     * @return 是否修改成功
     */
    @PostMapping("update/stockQuantity")
    public Result<Boolean> updateStockQuantity(@RequestBody ItemUpdateReqDto req) {
        log.info("修改商品平台可售库存 请求参数 {}", JSONUtil.toJsonStr(req));
        return Result.data(itemsLogic.updateStockQuantity(req));
    }

    /**
     * 删除商品类目
     * @param req 请求参数
     * @return 是否删除成功
     */
    @PostMapping("delete/shopCategory")
    public Result<Boolean> deleteShopCategory(@RequestBody ItemUpdateReqDto req) {
        log.info("删除商品类目 请求参数 {}", JSONUtil.toJsonStr(req));
        return Result.data(itemsLogic.deleteShopCategory(req));
    }

    /**
     * 批量修改商品状态
     * @param req 请求参数
     * @return 是否修改成功
     */
    @PostMapping("update/status")
    public Result<Boolean> updateStatus(@RequestBody ItemUpdateReqDto req) {
        log.info("批量修改商品状态 {}", JSONUtil.toJsonStr(req));
        return Result.data(itemsLogic.updateStatus(req));
    }

    /**
     * 批量修改商品是否售罄标识
     * @param req 请求参数
     * @return 是否修改成功
     */
    @PostMapping("update/selloutStatus")
    public Result<Boolean> updateSelloutStatus(@RequestBody ItemUpdateReqDto req) {
        log.info("批量修改商品是否售罄标识 请求参数 {}", JSONUtil.toJsonStr(req));
        return Result.data(itemsLogic.updateSelloutStatus(req));
    }

    /**
     * 批量修改商品店铺分类
     * @param req 请求参数
     * @return 是否修改成功
     */
    @PostMapping("update/shopCategory")
    public Result<Boolean> updateShopCategory(@RequestBody ItemUpdateReqDto req) {
        log.info("批量修改商品店铺分类 请求参数 {}", JSONUtil.toJsonStr(req));
        return Result.data(itemsLogic.updateShopCategory(req));
    }

    /**
     * 批量修改商品可售地区模板
     * @param req 请求参数
     * @return 是否修改成功
     */
    @PostMapping("update/restricted/sales/area")
    public Result<Boolean> updateRestrictedSales(@RequestBody ItemUpdateReqDto req) {
        log.info("批量修改商品可售地区模板 请求参数 {}", JSONUtil.toJsonStr(req));
        return Result.data(itemsLogic.updateRestrictedSales(req));
    }

    /**
     * 获取固定海关信息下拉框
     * @return 固定海关信息下拉框
     */
    @GetMapping("/customs/drop/down/box")
    public Result<List<DropDownBoxVo>> getCustomsDropDownBox() {
        log.info("获取固定海关信息下拉框");
        return Result.data(ItemCustomsEum.getDefaultCustoms());
    }

    @PostMapping("/cashGift/drop/down/box")
    public Result<List<ComboBoxVo>> getCashGiftDropDownBox() {
        List<ComboBoxVo> comboBoxes = itemsLogic.getCashGiftDropDownBox();
        return Result.data(comboBoxes);
    }

    @PostMapping("/source/drop/down/box")
    public Result<List<DropDownBoxVo>> getSourceDropDownBox() {
        return Result.data(SourceTypeEnum.getDefaultCustoms());
    }

    @PostMapping("/sorted/list")
    public Result<List<ItemSortedVo>> sortedList(@RequestBody ItemSortRequestVo itemSortRequestVo) {
        log.info("商品排序集合 {}", JSONUtil.toJsonStr(itemSortRequestVo));
        List<ItemSortedVo> itemSortedVos = itemsLogic.sortedList(itemSortRequestVo);
        return Result.data(itemSortedVos);
    }


    @PostMapping("/sorted/list/update")
    public Result<Boolean> sortedListUpdate(@RequestBody ItemSortRequestVo itemSortRequestVo) {
        log.info("商品排序 {}", JSONUtil.toJsonStr(itemSortRequestVo));
        itemsLogic.sortedListUpdate(itemSortRequestVo);
        return Result.data(true);
    }

}
