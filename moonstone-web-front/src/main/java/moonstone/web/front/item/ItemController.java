/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.item;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.ThirdPartyCacheHolder;
import moonstone.common.api.APIResp;
import moonstone.common.api.Result;
import moonstone.common.api.ResultCode;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.*;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.component.dto.item.EditItem;
import moonstone.component.dto.item.ItemCommissionConfigDTO;
import moonstone.component.item.component.ItemReader;
import moonstone.component.item.component.ItemWriter;
import moonstone.component.item.component.TaxChecker;
import moonstone.component.membership.component.MembershipPriceChecker;
import moonstone.delivery.model.DeliveryFeeTemplate;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.delivery.service.DeliveryFeeReadService;
import moonstone.item.api.InitialItemInfoFiller;
import moonstone.item.domain.ItemDomain;
import moonstone.item.dto.*;
import moonstone.item.dto.paging.ItemCriteria;
import moonstone.item.emu.*;
import moonstone.item.model.*;
import moonstone.item.service.*;
import moonstone.itemBrand.dto.ItemBrandDto;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.showcase.mq.config.anno.MQEventConsumerMethod;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import moonstone.user.model.StoreProxy;
import moonstone.web.core.component.intermediate.IntermediateInfoComponent;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.config.FunctionSwitch;
import moonstone.web.core.constants.ParanaConfig;
import moonstone.web.core.events.item.ItemCreatedEvent;
import moonstone.web.core.events.item.ItemUpdateEvent;
import moonstone.web.core.events.shop.ShopUpdateEvent;
import moonstone.web.core.exports.common.DefaultExporter;
import moonstone.web.core.fileNew.dto.ItemUpdateReqDto;
import moonstone.web.core.fileNew.logic.ItemBrandLogic;
import moonstone.web.core.shop.application.ShopManager;
import moonstone.web.core.third.ThirdIntermediateInfo;
import moonstone.web.core.user.StoreProxyManager;
import moonstone.web.front.component.item.ItemDetailMemberPriceHelper;
import moonstone.web.front.component.item.ItemReadLogic;
import moonstone.web.front.component.item.ItemStockNameQueryHelper;
import moonstone.web.front.component.item.SearchItemProfitViewHelper;
import moonstone.web.front.item.app.ItemConvertor;
import moonstone.web.front.item.app.ItemSortApp;
import moonstone.web.front.item.vo.SellOutStateUpdateVO;
import moonstone.web.front.item.vo.ViewedItemForLevelDistribution;
import moonstone.web.front.util.ShareImageUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.lang.Integer.parseInt;
import static java.lang.Integer.valueOf;

/**
 * 商品相关接口
 */
@RestController
@Slf4j
public class ItemController {

    @Resource
    protected ItemCacheHolder itemCacheHolder;
    @Autowired
    protected ItemStockNameQueryHelper itemStockNameQueryHelper;
    @Autowired
    protected StoreProxyManager storeProxyManager;
    @Resource
    ThirdPartyCacheHolder thirdPartyCacheHolder;
    @Autowired
    ThirdIntermediateInfo thirdIntermediateInfo;
    @Resource
    private ItemWriter itemWriter;
    @Autowired
    private ItemReadLogic itemReadLogic;
    @Resource
    private ShopReadService shopReadService;
    @Resource
    private ShopCacheHolder shopCacheHolder;
    @Resource
    private ItemReadService itemReadService;
    @Resource
    private ItemWriteService itemWriteService;
    @Resource
    private SkuReadService skuReadService;
    @Resource
    private SkuWriteService skuWriteService;
    @Resource
    private ThirdPartySkuStockReadService thirdPartySkuStockReadService;
    @Resource
    private DeliveryFeeReadService deliveryFeeReadService;
    @Resource
    private MembershipPriceChecker membershipPriceChecker;
    @Resource
    private ItemReader itemReader;
    @Resource
    private InitialItemInfoFiller initialItemInfoFiller;
    @Autowired
    private ParanaConfig paranaConfig;
    @Autowired
    private FunctionSwitch functionSwitch;
    @Resource
    private TaxChecker taxChecker;
    // This为自我代理 用于独立事务处理 以避免eventBus幻读
    @Autowired
    private ItemController self;
    @Autowired
    private ItemTagController itemTagController;
    @Autowired
    private ItemDetailMemberPriceHelper itemDetailMemberPriceHelper;
    @Autowired
    private SearchItemProfitViewHelper searchItemProfitViewHelper;
    @Autowired
    private IntermediateInfoComponent intermediateInfoComponent;
    @Autowired
    private ItemConvertor itemConvertor;

    @Resource
    private RestrictedSalesAreaTemplateReadService restrictedSalesAreaTemplateReadService;

    @Resource
    private ItemSortApp itemSortApp;

    @Resource
    private ShopManager shopManager;

    @Resource
    private ItemBrandLogic itemBrandLogic;

    @PutMapping("/api/seller/item/{id}/addAuth")
    @Transactional
    public boolean addAuthForSku(@PathVariable(name = "id") Long id, boolean needAuth, @RequestParam(value = "isSkuId", defaultValue = "true") boolean isSkuId, @RequestParam(value = "ignoreAuth", defaultValue = "false") boolean ignoreAuth) {
        List<Sku> skuList;
        if (!isSkuId) {
            skuList = skuReadService.findSkusByItemId(id).getResult();
        } else {
            skuList = Collections.singletonList(skuReadService.findSkuById(id).getResult());
            if (skuList.get(0) == null) {
                throw new JsonResponseException(new Translate("单品信息不存在").toString());
            }
        }
        if (!ignoreAuth && java.util.Objects.equals(skuList.get(0).getShopId(), ((CommonUser) UserUtil.getCurrentUser()).getShopId())) {
            throw new JsonResponseException(new Translate("授权失败,请登录商家或者有权限的帐号").toString());
        }
        for (Sku sku : skuList) {
            Sku update = new Sku();
            update.setId(sku.getId());
            Map<String, String> extra = sku.getExtraMap();
            extra.put(SkuExtraIndex.NeedAuth.getCode(), needAuth + "");
            update.setExtraMap(extra);
            skuWriteService.updateSku(update);
        }
        return true;
    }

    @GetMapping("/api/hs/{hsCode}/taxSplit")
    public Object taxSplit(@PathVariable(name = "hsCode") String hsCode, Long price) {
        SkuCustom skuCustom = new SkuCustom();
        skuCustom.setHsCode(hsCode);
        java.util.function.Function<BigDecimal, BigDecimal> getTax = (fee) ->
                BigDecimal.valueOf(taxChecker.getRate(ThirdPartySystem.Y800_V2.Id(), fee.longValue(), skuCustom)).multiply(fee);
        return taxChecker.splitTaxAtSelf(getTax, new BigDecimal(price))
                .orElseThrow(() -> Translate.exceptionOf("HsCode -> %s", hsCode));
    }

    @MyLog(title = "商品管理->创建商品", value = "创建商品", opBusinessType = OpBusinessType.INSERT)
    @RequestMapping(value = "/api/seller/items", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long createItem(@RequestBody FullItem fullItem) {
        //开关判断是否运行创建商品
        if (!functionSwitch.getAllowCreateItem()) {
            throw new JsonResponseException(new Translate("not.allow.create.item").toString());
        }
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        Response<Shop> rShop = shopReadService.findById(shopId);
        if (!rShop.isSuccess()) {
            log.error("failed to find shop(id={}), error code:{}", shopId, rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        final Shop shop = rShop.getResult();
        boolean blockV2 = shopManager.isBlockV2Item(shop);
        if (!Objects.equals(shop.getStatus(), 1)) {
            log.error("shop(id={})'s status is {}, create item failed", shopId, shop.getStatus());
            throw new JsonResponseException("shop.status.abnormal");
        }

        Item item = fullItem.getItem();

        if (java.util.Objects.nonNull(item.getIsThirdPartyItem())) {
            Stream.of(ThirdPartyItemType.values())
                    .filter(v -> v.getType() == item.getIsThirdPartyItem())
                    .findFirst().orElseThrow(() -> new JsonResponseException("item.isThirdPartyItem.illegal"));
        }
        if (item.getIsThirdPartyItem() == 1) {
            //判断第三方平台商品是否传了推单平台
            for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                Sku sku = skuWithCustom.getSku();
                if (sku.getTags() == null || sku.getTags().get(SkuTagIndex.pushSystem.name()) == null) {
                    log.error("bonded sku({}) has no pushSystem", sku.getId());
                    throw new JsonResponseException("sku.bonded.no.pushSystem");
                }

                var tags = sku.getTags().get(SkuTagIndex.pushSystem.name());
                if (blockV2 && Arrays.stream(tags.split(SkuTagIndex.pushSystem.getSplitter())).anyMatch(system ->
                        ThirdPartySystem.Y800_V2.Id().toString().equals(system))) {
                    throw new JsonResponseException("当前不能创建v2接口标准的商品");
                }
            }
            //检查该第三方商品库存是否充足
            if (functionSwitch.getThirdPartyStockLimit()) {
                checkThirdPartyStock(shopId, fullItem.getSkuWithCustoms());
            }
        }

        checkDeliveryFeeTemplate(fullItem.getItemDeliveryFee());
        checkRestrictedSalesAreaTemplate(fullItem.getItem());

        //关键信息防止被伪造
        item.setShopId(shopId);
        item.setShopName(shop.getName());
        item.setTags(null);

        //完善商品的默认信息
        initialItemInfoFiller.fill(item);

        // 设置默认的ShopId
        fullItem.getSkuWithCustoms().forEach(skuWithCustom -> skuWithCustom.getSku().setShopId(shopId));

        //计算商品价格区间
        try {
            extractInfoFromSkus(item, fullItem.getSkuWithCustoms());
        } catch (Exception e) {
            log.error("bad sku info", e);
            throw new JsonResponseException(e.getMessage());
        }
        checkItemVolumeLimit(shopId, fullItem);
        Response<Long> rItemId = self.create(fullItem);
        if (!rItemId.isSuccess()) {
            log.error("failed to create {}, error code:{}", fullItem, rItemId.getError());
            throw new JsonResponseException(rItemId.getError());
        }
        //发出商品创建事件
        EventSender.publish(new ItemCreatedEvent(rItemId.getResult()));

        //中间字段处理
        IntermediateInfo intermediateInfo = new IntermediateInfo();
        intermediateInfo.setThirdId(rItemId.getResult());
        intermediateInfo.setFirstRate(fullItem.getFirstRate());
        intermediateInfo.setFirstFee(fullItem.getFirstFee());
        intermediateInfo.setSecondRate(fullItem.getSecondRate());
        intermediateInfo.setSecondFee(fullItem.getSecondFee());
        intermediateInfo.setCommission(fullItem.getCommission());
        intermediateInfo.setFirstCommission(fullItem.getFirstCommission());
        intermediateInfo.setIsCommission(fullItem.getIsCommission());
        intermediateInfo.setServiceProviderRate(fullItem.getServiceProviderRate());
        intermediateInfo.setServiceProviderFee(fullItem.getServiceProviderFee());
        // 目前的利润不版本
        intermediateInfo.setVersion(3);
        intermediateInfo.setType(ThirdIntermediateType.SKU.value());
        intermediateInfo.trimUpdateFee();
        intermediateInfo.setMatchingType(IntermediateInfoMatchingTypeEnum.NORMAL.getCode());
        intermediateInfoComponent.createAndUpdate(intermediateInfo, shopId);
        return rItemId.getResult();
    }

    /**
     * 创建商品
     *
     * @param fullItem 商品相关信息
     * @return 商品id
     */
    @MyLog(title = "商品管理->创建商品", value = "创建商品", opBusinessType = OpBusinessType.INSERT)
    @PostMapping(value = "/api/seller/items/v2")
    public Result<Long> createItemV2(@RequestBody FullItem fullItem) {
        log.info("创建商品 请求参数 {}", JSONUtil.toJsonStr(fullItem));
        //开关判断是否运行创建商品
        if (!functionSwitch.getAllowCreateItem()) {
            throw new JsonResponseException(new Translate("not.allow.create.item").toString());
        }
        if (Objects.equals(fullItem.getIsCashGift(), 1)) {
            if (CashGiftUnitEnum.PERCENT.getCode().equals(fullItem.getUnit()) && fullItem.getMaxDeduction() != null
                    && fullItem.getMaxDeduction().compareTo(new BigDecimal("100")) >= 0) {
                throw new ApiException("最高可抵扣金额已超过上限");
            }
        }
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        Response<Shop> rShop = shopReadService.findById(shopId);
        if (!rShop.isSuccess()) {
            log.error("failed to find shop(id={}), error code:{}", shopId, rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        final Shop shop = rShop.getResult();
        boolean blockV2 = shopManager.isBlockV2Item(shop);
        if (!Objects.equals(shop.getStatus(), 1)) {
            log.error("shop(id={})'s status is {}, create item failed", shopId, shop.getStatus());
            throw new JsonResponseException("shop.status.abnormal");
        }

        Item item = fullItem.getItem();

        if (java.util.Objects.nonNull(item.getIsThirdPartyItem())) {
            Stream.of(ThirdPartyItemType.values())
                    .filter(v -> v.getType() == item.getIsThirdPartyItem())
                    .findFirst().orElseThrow(() -> new JsonResponseException("item.isThirdPartyItem.illegal"));
        }
        if (item.getIsThirdPartyItem() == 1) {
            //判断第三方平台商品是否传了推单平台
            for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                Sku sku = skuWithCustom.getSku();
                if (sku.getTags() == null || sku.getTags().get(SkuTagIndex.pushSystem.name()) == null) {
                    log.error("bonded sku({}) has no pushSystem", sku.getId());
                    throw new JsonResponseException("sku.bonded.no.pushSystem");
                }

                var tags = sku.getTags().get(SkuTagIndex.pushSystem.name());
                if (blockV2 && Arrays.stream(tags.split(SkuTagIndex.pushSystem.getSplitter())).anyMatch(system ->
                        ThirdPartySystem.Y800_V2.Id().toString().equals(system))) {
                    throw new JsonResponseException("当前不能创建v2接口标准的商品");
                }
            }
            //检查该第三方商品库存是否充足
            if (functionSwitch.getThirdPartyStockLimit()) {
                checkThirdPartyStock(shopId, fullItem.getSkuWithCustoms());
            }
        }

        checkDeliveryFeeTemplate(fullItem.getItemDeliveryFee());
        checkRestrictedSalesAreaTemplate(fullItem.getItem());

        //关键信息防止被伪造
        item.setShopId(shopId);
        item.setShopName(shop.getName());
        item.setTags(null);

        // itemName填充, 社群模式必填校验
        validateAndSetCommunityItemBrand(shop, item);

        //完善商品的默认信息
        initialItemInfoFiller.fill(item);

        // 设置默认的ShopId
        fullItem.getSkuWithCustoms().forEach(skuWithCustom -> skuWithCustom.getSku().setShopId(shopId));

        //计算商品价格区间
        try {
            extractInfoFromSkus(item, fullItem.getSkuWithCustoms());
        } catch (Exception e) {
            log.error("bad sku info", e);
            throw new JsonResponseException(e.getMessage());
        }
        checkItemVolumeLimit(shopId, fullItem);
        Response<Long> rItemId = self.create(fullItem);
        if (!rItemId.isSuccess()) {
            log.error("failed to create {}, error code:{}", fullItem, rItemId.getError());
            throw new JsonResponseException(rItemId.getError());
        }
        if (Objects.equals(fullItem.getIsCashGift(), 1)) {
            if (CashGiftUnitEnum.YUAN.getCode().equals(fullItem.getUnit()) && fullItem.getMaxDeduction() != null
                    && fullItem.getMaxDeduction().compareTo(new BigDecimal(String.valueOf(item.getLowPrice())).divide(new BigDecimal("100"))) >= 0) {
                throw new ApiException("最高可抵扣金额错误");
            }
        }
        //发出商品创建事件
        EventSender.publish(new ItemCreatedEvent(rItemId.getResult()));

        //中间字段处理
        IntermediateInfo intermediateInfo = new IntermediateInfo();
        intermediateInfo.setThirdId(rItemId.getResult());
        intermediateInfo.setFirstRate(fullItem.getFirstRate());
        intermediateInfo.setFirstFee(fullItem.getFirstFee());
        intermediateInfo.setSecondRate(fullItem.getSecondRate());
        intermediateInfo.setSecondFee(fullItem.getSecondFee());
        intermediateInfo.setCommission(fullItem.getCommission());
        intermediateInfo.setFirstCommission(fullItem.getFirstCommission());
        intermediateInfo.setIsCommission(fullItem.getIsCommission());
        intermediateInfo.setServiceProviderRate(fullItem.getServiceProviderRate());
        intermediateInfo.setServiceProviderFee(fullItem.getServiceProviderFee());
        intermediateInfo.setIsCashGift(fullItem.getIsCashGift() == null ? 0 : fullItem.getIsCashGift());
        intermediateInfo.setMaxDeduction(fullItem.getMaxDeduction());
        intermediateInfo.setUnit(fullItem.getUnit());
        Long timeStart = getTimeStartOrEnd(fullItem.getCashGiftUseTimeStart(), 1);
        Long timeEnd = getTimeStartOrEnd(fullItem.getCashGiftUseTimeEnd(), 2);
        intermediateInfo.setCashGiftUseTimeStart(timeStart);
        intermediateInfo.setCashGiftUseTimeEnd(timeEnd);
        // 目前的利润不版本
        intermediateInfo.setVersion(3);
        intermediateInfo.setType(ThirdIntermediateType.SKU.value());
        intermediateInfo.trimUpdateFee();
        intermediateInfo.setMatchingType(IntermediateInfoMatchingTypeEnum.NORMAL.getCode());
        intermediateInfoComponent.createAndUpdate(intermediateInfo, shopId);
        return Result.data(rItemId.getResult());
    }

    private void validateAndSetCommunityItemBrand(Shop shop, Item item) {
        if (Objects.equals(Optional.ofNullable(shop.getExtra())
                .orElseGet(HashMap::new)
                .get(ShopExtra.SalesPattern.getCode()), ShopExtra.communityOperation.getCode()) && item.getBrandId() == null) {
            throw new JsonResponseException("商品品牌不能为空");
        }
        if (item.getBrandId() != null) {
            ItemBrandDto itemBrand = itemBrandLogic.findById(item.getBrandId());
            if (itemBrand != null) {
                item.setBrandName(itemBrand.getName());
            }
        }
    }

    /**
     * 从单品中解析出商品价格数据
     * 应当重构, 将其使用fullItemDomain封装
     *
     * @param item           商品
     * @param skuWithCustoms 单品信息
     */
    private void extractInfoFromSkus(Item item, List<SkuWithCustom> skuWithCustoms) {

        //计算商品库存, 只考虑不分仓存储
        if (Objects.equals(item.getStockType(), 0)) {
            int stockQuantity = 0;
            for (SkuWithCustom skuWithCustom : skuWithCustoms) {
                Sku sku = skuWithCustom.getSku();
                if (sku.getStockQuantity() == null) {
                    throw new JsonResponseException("stock.empty");
                }
                if (sku.getStockQuantity() < 0) {
                    throw new IllegalArgumentException("sku.stock.negative");
                }
                stockQuantity += sku.getStockQuantity();
            }
            item.setStockQuantity(stockQuantity);
        }

        int highPrice = -1;
        int lowPrice = -1;

        for (SkuWithCustom skuWithCustom : skuWithCustoms) {
            Sku sku = skuWithCustom.getSku();
            if (sku.getShopId() == null) {
                sku.setShopId(item.getShopId());
            }
            if (sku.getPrice() != null) {
                if (sku.getPrice() <= 0) {
                    throw new IllegalArgumentException("sku.price.need.positive");
                }
                if (sku.getPrice() > highPrice) {
                    highPrice = sku.getPrice();
                }
                if (sku.getPrice() < lowPrice || lowPrice < 0) {
                    lowPrice = sku.getPrice();
                }
            }

            if (!BondedType.fromInt(item.getIsBonded()).isBonded()) {
                continue;
            }

            //判断税费是否为null
            if (!shapeMap(sku.getTags()).getOrDefault("pushSystem", "").isEmpty()) {
                if (taxChecker.getRate(parseInt(sku.getTags().get("pushSystem").split(",")[0]), sku, skuWithCustom.getSkuCustom()) == null) {
                    log.error("tax can't be null of Sku(id:{})", sku.getId());
                    throw new JsonResponseException("tax.find.fail");
                }
            }
        }
        Map<String, String> itemExtra = Optional.ofNullable(item.getExtra()).orElseGet(HashMap::new);
        // warn this part is not suitable to modify easily, if not set, so mean delete
        if (Objects.nonNull(item.getHighPrice())) {
            itemExtra.put(ItemDomain.ExtraPriceIndex.maxPrice.name(), item.getHighPrice().toString());
        }
        if (Objects.nonNull(item.getLowPrice())) {
            itemExtra.put(ItemDomain.ExtraPriceIndex.minPrice.name(), item.getLowPrice().toString());
        }
        item.setExtra(itemExtra);
        if (highPrice > 0) {
            item.setHighPrice(highPrice);
        }
        if (lowPrice > 0) {
            item.setLowPrice(lowPrice);
        }
    }

    @Transactional(rollbackForClassName = "RuntimeException")
    public Boolean updateQuantityAndPrice(FullItem fullItem, CommonUser commonUser) {
        final Long shopId = commonUser.getShopId();
        final Long itemId = fullItem.getItem().getId();
        Response<Item> itemRes = itemReadService.findById(itemId);
        if (!itemRes.isSuccess()) {
            log.error("failed to find item(id={}), error code:{}", itemId, itemRes.getError());
            throw new JsonResponseException(itemRes.getError());
        }
        if (!Objects.equals(itemRes.getResult().getShopId(), shopId)) {
            log.error("the item(id={}) is not belong to seller(shop id={})", itemId, shopId);
            throw new JsonResponseException("item.not.belong.to.seller");
        }
        Response<List<Sku>> skuListRes = skuReadService.findSkusByItemId(fullItem.getItem().getId());
        HashMap<Long, Sku> skuHashMap = new HashMap<>();
        skuListRes.getResult().forEach(sku -> skuHashMap.put(sku.getId(), sku));
        int minSkuPrice = -1;
        int maxSkuPrice = -1;
        for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
            Sku sku = skuHashMap.get(skuWithCustom.getSku().getId());
            sku.setStockQuantity(skuWithCustom.getSku().getStockQuantity());
            sku.setPrice(skuWithCustom.getSku().getPrice());
            if (sku.getPrice() > maxSkuPrice || maxSkuPrice == -1) {
                maxSkuPrice = sku.getPrice();
            }
            if (sku.getPrice() < minSkuPrice || minSkuPrice == -1) {
                minSkuPrice = sku.getPrice();
            }
            try {
                sku.setExtraPriceJson(skuWithCustom.getSku().getExtraPriceJson());
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("parse price failed,cause:{}", ex.getMessage());
                return false;
            }
            if (!skuWriteService.updateSku(sku).isSuccess()) {
                throw new RuntimeException("quantity change failed(sku:" + skuWithCustom.getSku().getId() + ")+quantity:" + skuWithCustom.getSku().getStockQuantity());
            }
            checkItemVolumeLimit(shopId, fullItem);
        }
        Integer stockQuantity = fullItem.getSkuWithCustoms().stream().map(SkuWithCustom::getSku)
                .map(Sku::getStockQuantity).filter(Objects::nonNull).reduce(Integer::sum).orElse(null);
        itemRes.getResult().setStockQuantity(stockQuantity);
        itemRes.getResult().setHighPrice(maxSkuPrice);
        itemRes.getResult().setLowPrice(minSkuPrice);
        if (!self.update(itemRes.getResult()).isSuccess()) {
            throw new JsonResponseException("item.update.fail");
        }
        EventSender.publish(new ItemUpdateEvent(itemId));
        return true;
    }

    @MyLog(title = "商品管理->上/下架商品", value = "修改商品信息", opBusinessType = OpBusinessType.UPDATE)
    @RequestMapping(value = "/api/seller/items", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean updateItem(@RequestBody FullItem fullItem) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        Response<Shop> rShop = shopReadService.findById(shopId);
        if (!rShop.isSuccess()) {
            log.error("failed to find shop(id={}), error code:{}", shopId, rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        final Shop shop = rShop.getResult();
        if (!Objects.equals(shop.getStatus(), 1)) {
            log.error("shop(id={})'s status is {}, create item failed", shopId, shop.getStatus());
            throw new JsonResponseException("shop.status.abnormal");
        }
        if (functionSwitch.getRevokeShopItemEditPrivilege() && "true".equals(shop.getExtra().getOrDefault(ShopExtra.editLimit.name(), "false"))) {
            return self.updateQuantityAndPrice(fullItem, commonUser);
        }
        Item item = fullItem.getItem();

        if (java.util.Objects.nonNull(item.getIsThirdPartyItem())) {
            Stream.of(ThirdPartyItemType.values()).filter(type -> item.getIsThirdPartyItem() == type.getType())
                    .findFirst().orElseThrow(() -> new JsonResponseException("item.isThirdPartyItem.illegal"));
        }

        final Long itemId = item.getId();
        Response<Item> rItem = itemReadService.findById(itemId);
        if (!rItem.isSuccess()) {
            log.error("failed to find item(id={}), error code:{}", itemId, rItem.getError());
            throw new JsonResponseException(rItem.getError());
        }

        boolean isThirdPartyItem = java.util.Objects.equals(ThirdPartyItemType.THIRD_PARTY_ITEM.getType(), Optional.ofNullable(item.getIsThirdPartyItem()).orElseGet(rItem.getResult()::getIsThirdPartyItem));
        if (java.util.Objects.nonNull(item.getIsBonded()) && BondedType.fromInt(item.getIsBonded()).isBonded() && isThirdPartyItem) {
            //判断保税商品是否传了报关平台
            for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                Sku sku = skuWithCustom.getSku();
                if (sku.getTags() == null || sku.getTags().get("pushSystem") == null) {
                    log.error("bonded sku has no pushSystem");
                    throw new JsonResponseException("sku.bonded.no.pushSystem");
                }
            }
            //检查该第三方商品库存是否充足
            if (functionSwitch.getThirdPartyStockLimit()) {
                checkThirdPartyStock(shopId, fullItem.getSkuWithCustoms());
            }
        }

        checkDeliveryFeeTemplate(fullItem.getItemDeliveryFee());
        checkItemActivityConfig(fullItem.getItem());
        checkSkuActivityConfig(fullItem.getSkuWithCustoms());
        checkSkuActivityCommissionConfig(fullItem.getActivityCommissionConfig());
        checkRestrictedSalesAreaTemplate(fullItem.getItem());

        //关键信息防止被伪造
        item.setShopId(shopId);
        item.setShopName(shop.getName());
        item.setTags(null);


        if (!Objects.equals(rItem.getResult().getShopId(), shopId)) {
            log.error("the item(id={}) is not belong to seller(shop id={})", itemId, shopId);
            throw new JsonResponseException("item.not.belong.to.seller");
        }

        BiFunction<Map<String, String>, Map<String, String>, Consumer<String>> copyExtraIfExists = (oldExtra, newExtra) -> (index) -> {
            if (oldExtra == null || newExtra == null) {
                return;
            }
            if (!StringUtils.hasText(oldExtra.get(index))) {
                return;
            }
            newExtra.put(index, oldExtra.get(index));
        };
        //澳新商品、微分销商品标志
        try {
            Map<String, String> existExtra = rItem.getResult().getExtra();
            Map<String, String> toUpdateExtra = item.getExtra();
            Consumer<String> copyIntoUpdateItemExtra = copyExtraIfExists.apply(existExtra, toUpdateExtra);
            // copy Item Extra
            // 复制Item额外配置数据
            Collections.singletonList(DistributionConstants.SELL_IN_WE_SHOP).forEach(copyIntoUpdateItemExtra);
            String sellInWeShop = existExtra.get(DistributionConstants.SELL_IN_WE_SHOP);
            if (!ObjectUtils.isEmpty(sellInWeShop)) {
                Response<List<Sku>> rExistSkus = skuReadService.findSkusByItemId(itemId);
                if (!rExistSkus.isSuccess()) {
                    log.error("failed to find skus by itemId={}, error code: {}", itemId, rExistSkus.getError());
                    throw new JsonResponseException(rExistSkus.getError());
                }
                Map<Long, Sku> skuById = Maps.uniqueIndex(rExistSkus.getResult(), Sku::getId);
                for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                    Sku sku = skuWithCustom.getSku();
                    Map<String, String> extra = sku.getExtraMap();
                    Map<String, Integer> extraPrice = sku.getExtraPrice();
                    Sku existSku = skuById.get(sku.getId());
                    String skuSellInWeShop = existSku.getExtraMap().get(DistributionConstants.SELL_IN_WE_SHOP);
                    Integer skuDistributionPrice = existSku.getExtraPrice().get(DistributionConstants.SKU_DISTRIBUTION_PRICE);
                    Integer skuProfit = existSku.getExtraPrice().get(DistributionConstants.SKU_PROFIT);
                    // copy sku extra
                    // 复制额外的Sku配置数据
                    if (!ObjectUtils.isEmpty(skuSellInWeShop)) {
                        extra.put(DistributionConstants.SELL_IN_WE_SHOP, skuSellInWeShop);
                    }
                    if (!ObjectUtils.isEmpty(skuDistributionPrice)) {
                        extraPrice.put(DistributionConstants.SKU_DISTRIBUTION_PRICE, skuDistributionPrice);
                    }
                    if (!ObjectUtils.isEmpty(skuProfit)) {
                        extraPrice.put(DistributionConstants.SKU_PROFIT, skuProfit);
                    }
                    sku.setExtraMap(extra);
                    sku.setExtraPrice(extraPrice);
                }
            }
            item.setExtra(toUpdateExtra);
        } catch (Exception e) {
            log.error("fail to handle exist extra or extraPrice when update item(fullItem: {}), cause: {}", fullItem, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("item.update.handle.exist.fail");
        }

        item.setStockType(rItem.getResult().getStockType());
        //避免重复计算默认值啥的
        extractInfoFromSkus(item, fullItem.getSkuWithCustoms());
        checkItemVolumeLimit(shopId, fullItem);
        Response<Boolean> rUpdate = self.update(fullItem);
        if (!rUpdate.isSuccess()) {
            log.error("failed to update {}, error code:{}", fullItem, rUpdate.getError());
            throw new JsonResponseException(rUpdate.getError());
        }

        EventSender.publish(new ItemUpdateEvent(itemId));

        //中间字段处理
        IntermediateInfo intermediateInfo = new IntermediateInfo();
        intermediateInfo.setFirstRate(fullItem.getFirstRate());
        intermediateInfo.setFirstFee(fullItem.getFirstFee());
        intermediateInfo.setSecondRate(fullItem.getSecondRate());
        intermediateInfo.setSecondFee(fullItem.getSecondFee());
        intermediateInfo.setCommission(fullItem.getCommission());
        intermediateInfo.setFirstCommission(fullItem.getFirstCommission());
        intermediateInfo.setIsCommission(fullItem.getIsCommission());
        intermediateInfo.setServiceProviderFee(fullItem.getServiceProviderFee());
        intermediateInfo.setServiceProviderRate(fullItem.getServiceProviderRate());
        // 目前的利润不版本
        intermediateInfo.setVersion(3);
        intermediateInfo.trimUpdateFee();
        intermediateInfo.setType(ThirdIntermediateType.SKU.value());
        intermediateInfo.setThirdId(itemId);
        intermediateInfo.setMatchingType(IntermediateInfoMatchingTypeEnum.NORMAL.getCode());
        intermediateInfoComponent.createAndUpdate(intermediateInfo, shopId);

        //保存活动佣金配置
        saveActivityCommissionConfig(fullItem.getDeleteActivityCommissionConfig(), fullItem.getActivityCommissionConfig(), itemId, shopId);

        return rUpdate.getResult();
    }

    /**
     * 修改商品信息
     * @param fullItem 商品信息
     * @return 是否修改成功
     */
    @PostMapping(value = "/api/seller/items/update")
    public Result<Boolean> updateItemV2(@RequestBody FullItem fullItem) {
        log.info("修改商品信息 请求参数 {}", JSONUtil.toJsonStr(fullItem));
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        Response<Shop> rShop = shopReadService.findById(shopId);
        if (!rShop.isSuccess()) {
            log.error("failed to find shop(id={}), error code:{}", shopId, rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        final Shop shop = rShop.getResult();
        if (!Objects.equals(shop.getStatus(), 1)) {
            log.error("shop(id={})'s status is {}, create item failed", shopId, shop.getStatus());
            throw new JsonResponseException("shop.status.abnormal");
        }
        if (Objects.equals(fullItem.getIsCashGift(), 1)) {
            if (CashGiftUnitEnum.PERCENT.getCode().equals(fullItem.getUnit()) && fullItem.getMaxDeduction() != null
                    && fullItem.getMaxDeduction().compareTo(new BigDecimal("100")) >= 0) {
                throw new ApiException("最高可抵扣金额已超过上限");
            }
        }
        if (functionSwitch.getRevokeShopItemEditPrivilege() && "true".equals(shop.getExtra().getOrDefault(ShopExtra.editLimit.name(), "false"))) {
            return Result.data(self.updateQuantityAndPrice(fullItem, commonUser));
        }
        Item item = fullItem.getItem();
        // itemName填充, 社群模式必填校验
        validateAndSetCommunityItemBrand(shop, item);

        if (java.util.Objects.nonNull(item.getIsThirdPartyItem())) {
            Stream.of(ThirdPartyItemType.values()).filter(type -> item.getIsThirdPartyItem() == type.getType())
                    .findFirst().orElseThrow(() -> new JsonResponseException("item.isThirdPartyItem.illegal"));
        }

        final Long itemId = item.getId();
        Response<Item> rItem = itemReadService.findById(itemId);
        if (!rItem.isSuccess()) {
            log.error("failed to find item(id={}), error code:{}", itemId, rItem.getError());
            throw new JsonResponseException(rItem.getError());
        }

        boolean isThirdPartyItem = java.util.Objects.equals(ThirdPartyItemType.THIRD_PARTY_ITEM.getType(), Optional.ofNullable(item.getIsThirdPartyItem()).orElseGet(rItem.getResult()::getIsThirdPartyItem));
        if (java.util.Objects.nonNull(item.getIsBonded()) && BondedType.fromInt(item.getIsBonded()).isBonded() && isThirdPartyItem) {
            //判断保税商品是否传了报关平台
            for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                Sku sku = skuWithCustom.getSku();
                if (sku.getTags() == null || sku.getTags().get("pushSystem") == null) {
                    log.error("bonded sku has no pushSystem");
                    throw new JsonResponseException("sku.bonded.no.pushSystem");
                }
            }
            //检查该第三方商品库存是否充足
            if (functionSwitch.getThirdPartyStockLimit()) {
                checkThirdPartyStock(shopId, fullItem.getSkuWithCustoms());
            }
        }

        checkDeliveryFeeTemplate(fullItem.getItemDeliveryFee());
        checkItemActivityConfig(fullItem.getItem());
        checkSkuActivityConfig(fullItem.getSkuWithCustoms());
        checkSkuActivityCommissionConfig(fullItem.getActivityCommissionConfig());
        checkRestrictedSalesAreaTemplate(fullItem.getItem());

        //关键信息防止被伪造
        item.setShopId(shopId);
        item.setShopName(shop.getName());
        item.setTags(null);


        if (!Objects.equals(rItem.getResult().getShopId(), shopId)) {
            log.error("the item(id={}) is not belong to seller(shop id={})", itemId, shopId);
            throw new JsonResponseException("item.not.belong.to.seller");
        }

        BiFunction<Map<String, String>, Map<String, String>, Consumer<String>> copyExtraIfExists = (oldExtra, newExtra) -> (index) -> {
            if (oldExtra == null || newExtra == null) {
                return;
            }
            if (!StringUtils.hasText(oldExtra.get(index))) {
                return;
            }
            newExtra.put(index, oldExtra.get(index));
        };
        //澳新商品、微分销商品标志
        try {
            Map<String, String> existExtra = rItem.getResult().getExtra();
            Map<String, String> toUpdateExtra = item.getExtra();
            Consumer<String> copyIntoUpdateItemExtra = copyExtraIfExists.apply(existExtra, toUpdateExtra);
            // copy Item Extra
            // 复制Item额外配置数据
            Collections.singletonList(DistributionConstants.SELL_IN_WE_SHOP).forEach(copyIntoUpdateItemExtra);
            String sellInWeShop = existExtra.get(DistributionConstants.SELL_IN_WE_SHOP);
            if (!ObjectUtils.isEmpty(sellInWeShop)) {
                Response<List<Sku>> rExistSkus = skuReadService.findSkusByItemId(itemId);
                if (!rExistSkus.isSuccess()) {
                    log.error("failed to find skus by itemId={}, error code: {}", itemId, rExistSkus.getError());
                    throw new JsonResponseException(rExistSkus.getError());
                }
                Map<Long, Sku> skuById = Maps.uniqueIndex(rExistSkus.getResult(), Sku::getId);
                for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
                    Sku sku = skuWithCustom.getSku();
                    Map<String, String> extra = sku.getExtraMap();
                    Map<String, Integer> extraPrice = sku.getExtraPrice();
                    Sku existSku = skuById.get(sku.getId());
                    String skuSellInWeShop = existSku.getExtraMap().get(DistributionConstants.SELL_IN_WE_SHOP);
                    Integer skuDistributionPrice = existSku.getExtraPrice().get(DistributionConstants.SKU_DISTRIBUTION_PRICE);
                    Integer skuProfit = existSku.getExtraPrice().get(DistributionConstants.SKU_PROFIT);
                    // copy sku extra
                    // 复制额外的Sku配置数据
                    if (!ObjectUtils.isEmpty(skuSellInWeShop)) {
                        extra.put(DistributionConstants.SELL_IN_WE_SHOP, skuSellInWeShop);
                    }
                    if (!ObjectUtils.isEmpty(skuDistributionPrice)) {
                        extraPrice.put(DistributionConstants.SKU_DISTRIBUTION_PRICE, skuDistributionPrice);
                    }
                    if (!ObjectUtils.isEmpty(skuProfit)) {
                        extraPrice.put(DistributionConstants.SKU_PROFIT, skuProfit);
                    }
                    sku.setExtraMap(extra);
                    sku.setExtraPrice(extraPrice);
                }
            }
            item.setExtra(toUpdateExtra);
        } catch (Exception e) {
            log.error("fail to handle exist extra or extraPrice when update item(fullItem: {}), cause: {}", fullItem, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("item.update.handle.exist.fail");
        }

        item.setStockType(rItem.getResult().getStockType());
        //避免重复计算默认值啥的
        extractInfoFromSkus(item, fullItem.getSkuWithCustoms());
        checkItemVolumeLimit(shopId, fullItem);
        Response<Boolean> rUpdate = self.update(fullItem);
        if (!rUpdate.isSuccess()) {
            log.error("failed to update {}, error code:{}", fullItem, rUpdate.getError());
            throw new JsonResponseException(rUpdate.getError());
        }

        if (Objects.equals(fullItem.getIsCashGift(), 1)) {
            if (CashGiftUnitEnum.YUAN.getCode().equals(fullItem.getUnit()) && fullItem.getMaxDeduction() != null
                    && fullItem.getMaxDeduction().compareTo(new BigDecimal(String.valueOf(item.getLowPrice())).divide(new BigDecimal("100"))) >= 0) {
                throw new ApiException("最高可抵扣金额错误");
            }
        }

        EventSender.publish(new ItemUpdateEvent(itemId));

        //中间字段处理
        IntermediateInfo intermediateInfo = new IntermediateInfo();
        intermediateInfo.setFirstRate(fullItem.getFirstRate());
        intermediateInfo.setFirstFee(fullItem.getFirstFee());
        intermediateInfo.setSecondRate(fullItem.getSecondRate());
        intermediateInfo.setSecondFee(fullItem.getSecondFee());
        intermediateInfo.setCommission(fullItem.getCommission());
        intermediateInfo.setFirstCommission(fullItem.getFirstCommission());
        intermediateInfo.setIsCommission(fullItem.getIsCommission());
        intermediateInfo.setServiceProviderFee(fullItem.getServiceProviderFee());
        intermediateInfo.setServiceProviderRate(fullItem.getServiceProviderRate());
        intermediateInfo.setIsCashGift(fullItem.getIsCashGift() == null ? 0 : fullItem.getIsCashGift());
        intermediateInfo.setMaxDeduction(fullItem.getMaxDeduction());
        intermediateInfo.setUnit(fullItem.getUnit());

        Long timeStart = getTimeStartOrEnd(fullItem.getCashGiftUseTimeStart(), 1);
        Long timeEnd = getTimeStartOrEnd(fullItem.getCashGiftUseTimeEnd(), 2);
        intermediateInfo.setCashGiftUseTimeStart(timeStart);
        intermediateInfo.setCashGiftUseTimeEnd(timeEnd);
        // 目前的利润不版本
        intermediateInfo.setVersion(3);
        intermediateInfo.trimUpdateFee();
        intermediateInfo.setType(ThirdIntermediateType.SKU.value());
        intermediateInfo.setThirdId(itemId);
        intermediateInfo.setMatchingType(IntermediateInfoMatchingTypeEnum.NORMAL.getCode());
        intermediateInfoComponent.createAndUpdate(intermediateInfo, shopId);

        //保存活动佣金配置
        saveActivityCommissionConfig(fullItem.getDeleteActivityCommissionConfig(), fullItem.getActivityCommissionConfig(), itemId, shopId);

        return Result.data(rUpdate.getResult());
    }

    private Long getTimeStartOrEnd(Long timestamp, Integer type) {
        if (Objects.isNull(timestamp)) {
            return null;
        }
        // 将时间戳转换为 Instant 对象
        Instant instant = Instant.ofEpochMilli(timestamp);
        // 获取系统默认时区
        ZoneId zoneId = ZoneId.systemDefault();
        // 将 Instant 对象转换为指定时区的 ZonedDateTime 对象
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);
        // 提取日期部分
        LocalDate localDate = zonedDateTime.toLocalDate();
        if (type == 1) {
            // 获取该日期的开始时间（00:00:00:000）
            LocalDateTime startOfDay = localDate.atStartOfDay();
            // 将开始时间转换为指定时区的 ZonedDateTime 对象
            ZonedDateTime startZonedDateTime = startOfDay.atZone(zoneId);
            return startZonedDateTime.toInstant().toEpochMilli();
        } else if (type == 2) {
            // 获取该日期的结束时间（23:59:59:999）
            LocalDateTime endOfDay = localDate.atTime(23, 59, 59, 999_000_000);
            // 将结束时间转换为指定时区的 ZonedDateTime 对象
            ZonedDateTime endZonedDateTime = endOfDay.atZone(zoneId);
            // 获取结束时间的时间戳
            return endZonedDateTime.toInstant().toEpochMilli();

        }
        return timestamp;

    }

    private void checkRestrictedSalesAreaTemplate(Item item) {
        if (item.getRestrictedSalesAreaTemplateId() == null) {
            throw new JsonResponseException("可售地区模板不能为空");
        }

        var template = restrictedSalesAreaTemplateReadService.findById(
                item.getRestrictedSalesAreaTemplateId()).getResult();
        if (template == null) {
            throw new JsonResponseException("选择的可售地区模板已不存在");
        }
        if (RestrictedSalesAreaTemplateStatusEnum.INACTIVE.getCode().equals(template.getStatus())) {
            throw new JsonResponseException("选择的可售地区模板已停用");
        }
    }

    /**
     * 保存活动佣金配置
     *
     * @param activityCommissionConfig
     * @param itemId
     * @param shopId
     */
    private void saveActivityCommissionConfig(String deleteFlag, ItemCommissionConfigDTO activityCommissionConfig, Long itemId, Long shopId) {
        if (Boolean.TRUE.toString().equals(deleteFlag)) {
            intermediateInfoComponent.deleteActivityForSkuByItem(itemId);
            return;
        }
        if (activityCommissionConfig == null) {
            return;
        }

        IntermediateInfo intermediateInfo = new IntermediateInfo();
        intermediateInfo.setFirstRate(activityCommissionConfig.getFirstRate());
        intermediateInfo.setFirstFee(activityCommissionConfig.getFirstFee());
        intermediateInfo.setSecondRate(activityCommissionConfig.getSecondRate());
        intermediateInfo.setSecondFee(activityCommissionConfig.getSecondFee());
        intermediateInfo.setCommission(activityCommissionConfig.getCommission());
        intermediateInfo.setFirstCommission(activityCommissionConfig.getFirstCommission());
        intermediateInfo.setIsCommission(activityCommissionConfig.getIsCommission());
        intermediateInfo.setServiceProviderFee(activityCommissionConfig.getServiceProviderFee());
        intermediateInfo.setServiceProviderRate(activityCommissionConfig.getServiceProviderRate());
        // 目前的利润不版本
        intermediateInfo.setVersion(3);
        intermediateInfo.trimUpdateFee();
        intermediateInfo.setType(ThirdIntermediateType.SKU.value());
        intermediateInfo.setThirdId(itemId);
        intermediateInfo.setMatchingType(IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode());
        intermediateInfo.setMatchingStartTime(DateUtil.parseDate(activityCommissionConfig.getMatchingStartTimeString()));
        intermediateInfo.setMatchingEndTime(DateUtil.parseDate(activityCommissionConfig.getMatchingEndTimeString()));

        intermediateInfoComponent.createAndUpdate(intermediateInfo, shopId);
    }

    /**
     * 活动佣金配置校验
     *
     * @param activityCommissionConfig
     */
    private void checkSkuActivityCommissionConfig(ItemCommissionConfigDTO activityCommissionConfig) {
        if (activityCommissionConfig == null) {
            return;
        }

        if (!StringUtils.hasText(activityCommissionConfig.getMatchingStartTimeString()) ||
                !StringUtils.hasText(activityCommissionConfig.getMatchingEndTimeString())) {
            throw new JsonResponseException("活动佣金生效的开始时间和结束时间皆不能为空");
        }

        try {
            var start = DateUtils.parseDate(activityCommissionConfig.getMatchingStartTimeString(), DateUtil.YMDHMS_FORMAT);
            var end = DateUtils.parseDate(activityCommissionConfig.getMatchingEndTimeString(), DateUtil.YMDHMS_FORMAT);
            if (!start.before(end)) {
                throw new JsonResponseException("活动佣金生效的开始时间必须小于结束时间");
            }
        } catch (Exception ex) {
            log.error("checkSkuActivityCommissionConfig error, startTimeString={}, endTimeString={}",
                    activityCommissionConfig.getMatchingStartTimeString(), activityCommissionConfig.getMatchingEndTimeString(), ex);
            throw new JsonResponseException("活动佣金生效时间的格式不正确");
        }
    }

    /**
     * 校验用户输入是否合法（定时改变sku价格的配置）
     *
     * @param skuWithCustoms
     */
    private void checkSkuActivityConfig(List<SkuWithCustom> skuWithCustoms) {
        if (CollectionUtils.isEmpty(skuWithCustoms)) {
            return;
        }

        skuWithCustoms.stream()
                .map(SkuWithCustom::getSku)
                .filter(Objects::nonNull)
                .filter(e -> !CollectionUtils.isEmpty(e.getExtraMap()))
                .forEach(sku -> {
                    Map<String, String> extra = sku.getExtraMap();
                    if (sku.getActivitySalesPriceSwitch() == 1) {
                        if (StringUtils.hasText(extra.get(SkuExtraIndex.activitySalesPrice.name()))) {
                            //配置了活动价
                            try {
                                var price = new BigDecimal(extra.get(SkuExtraIndex.activitySalesPrice.name()));
                                if (price.compareTo(new BigDecimal("0.00")) <= 0) {
                                    throw new JsonResponseException("sku.schedule.change.config.price.under.zero");
                                }
                            } catch (Exception ex) {
                                throw new JsonResponseException("sku.schedule.change.config.wrong.price");
                            }

                            if (!StringUtils.hasText(extra.get(SkuExtraIndex.activityStartTime.name())) ||
                                    !StringUtils.hasText(extra.get(SkuExtraIndex.activityEndTime.name()))) {
                                throw new JsonResponseException("sku.schedule.change.config.empty.date");
                            }

                            if (!checkStartEnd(extra.get(SkuExtraIndex.activityStartTime.name()), extra.get(SkuExtraIndex.activityEndTime.name()))) {
                                throw new JsonResponseException("sku.schedule.change.config.invalid.date");
                            }
                        } else {
                            //没有配置活动价，就不接受对应的时间配置
                            if (StringUtils.hasText(extra.get(SkuExtraIndex.activityStartTime.name())) ||
                                    StringUtils.hasText(extra.get(SkuExtraIndex.activityEndTime.name()))) {
                                throw new JsonResponseException("sku.schedule.change.config.no.data");
                            }
                        }
                    }
                });
    }

    /**
     * 校验用户输入是否合法（定时改变商品图片的配置）
     *
     * @param item
     */
    private void checkItemActivityConfig(Item item) {
        if (item.getActivityImageSwitch() != 1) {
            return;
        }
        Map<String, String> extra = item.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            return;
        }

        if (StringUtils.hasText(extra.get(ItemExtraIndex.activityMainImage.name())) ||
                StringUtils.hasText(extra.get(ItemExtraIndex.activityDetailImages.name()))) {
            //配置了图片，就要配置合法的开始和结束时间
            if (!StringUtils.hasText(extra.get(ItemExtraIndex.activityStartTime.name())) ||
                    !StringUtils.hasText(extra.get(ItemExtraIndex.activityEndTime.name()))) {
                throw new ApiException("活动图片已配置，请检查活动时间是否配置");
            }

            //时间格式是否合法
            if (!checkStartEnd(extra.get(ItemExtraIndex.activityStartTime.name()), extra.get(ItemExtraIndex.activityEndTime.name()))) {
                throw new ApiException("活动时间格式不合法");
            }
        } else {
            //没有配置图片, 就不接受对应的时间配置
            if (StringUtils.hasText(extra.get(ItemExtraIndex.activityStartTime.name())) ||
                    StringUtils.hasText(extra.get(ItemExtraIndex.activityEndTime.name()))) {
                throw new ApiException("活动图片未配置，活动时间配置无效");
            }
        }
    }

    /**
     * 校验开始时间与结束时间是否合法
     *
     * @param startDateString
     * @param endDateString
     * @return
     */
    private boolean checkStartEnd(String startDateString, String endDateString) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.YMDHMS_FORMAT);
            simpleDateFormat.setLenient(false);

            Date start = simpleDateFormat.parse(startDateString);
            Date end = simpleDateFormat.parse(endDateString);

            return start.before(end);
        } catch (Exception ex) {
            log.error("checkStartEnd exception: startDateString={}, endDateString={}", startDateString, endDateString, ex);
            throw new JsonResponseException("schedule.change.config.wrong.date.format");
        }
    }

    private void checkItemVolumeLimit(Long shopId, FullItem fullItem) {
        if (fullItem == null) {
            return;
        }
        for (SkuWithCustom skuWithCustom : fullItem.getSkuWithCustoms()) {
            Sku sku = skuWithCustom.getSku();
            if (sku.getExtraMap() == null || !sku.getExtraMap().containsKey(SkuExtraIndex.unitQuantity.getCode()) || sku.getExtraMap().get(SkuExtraIndex.unitQuantity.getCode()).isEmpty()) {
                continue;
            }
            if (Objects.equals(fullItem.getItem().getIsThirdPartyItem(), 1) || !ObjectUtils.isEmpty(sku.getOuterSkuId())) {
                if (sku.getTags() == null || ObjectUtils.isEmpty(sku.getTags().get("pushSystem"))) {
                    log.error("{} wrong push System for sku:{}", LogUtil.getClassMethodName(), sku);
                    return;
                }
                Integer thirdPartyId = valueOf(sku.getTags().get("pushSystem"));
                if (BondedType.fromInt(fullItem.getItem().getIsBonded()).isBonded()) {
                    List<ThirdPartySkuStock> thirdPartySkuStockList = thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(shopId, thirdPartyId, sku.getOuterSkuId()).getResult();
                    log.debug("{} sku[{}] match => {}", LogUtil.getClassMethodName(), sku.getOuterSkuId(), Json.toJson(thirdPartySkuStockList));
                    BigDecimal volume = thirdPartySkuStockList.stream().map(ThirdPartySkuStock::getVolume)
                            .filter(java.util.Objects::nonNull).sorted()
                            .findFirst().orElse(BigDecimal.valueOf(360));
                    String manualSetVolume = Optional.ofNullable(fullItem.getItem().getExtra()).orElseGet(HashMap::new)
                            .get(SkuExtraIndex.skuOrderSplitLine.getCode());
                    log.info("Item Extra => {}", fullItem.getItem().getExtra());
                    if (!ObjectUtils.isEmpty(manualSetVolume)) {
                        volume = BigDecimal.valueOf(360 / Long.parseLong(manualSetVolume));
                    }
                    if (volume.longValue() == 0) {
                        continue;
                    }

                    if (parseInt(sku.getExtraMap().get(SkuExtraIndex.unitQuantity.getCode())) * volume.intValue() > 360) {
                        throw new RuntimeException("超过体积上限 请增大拆单数量或者减小计量单位");
                    }
                }
            }
        }
    }

    private void checkThirdPartyStock(Long shopId, List<SkuWithCustom> skuWithCustoms) {
        for (SkuWithCustom skuWithCustom : skuWithCustoms) {
            boolean flag = false;
            String pushSystemStr = skuWithCustom.getSku().getTags().get("pushSystem");
            //拆成列表
            if (pushSystemStr.endsWith(",")) {
                pushSystemStr = pushSystemStr.substring(0, pushSystemStr.length() - 1);
            }
            List<String> stringList = Splitter.on(",").splitToList(pushSystemStr);
            List<Integer> pushSystems = stringList.stream().map(Integer::parseInt).collect(Collectors.toList());

            Integer totalAuthenticStock = 0;
            for (Integer thirdPartyId : pushSystems) {
                if (ObjectUtils.isEmpty(skuWithCustom.getSku().getOuterSkuId())) {
                    flag = true;
                    break;
                }
                Response<List<ThirdPartySkuStock>> response =
                        thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(shopId, thirdPartyId, skuWithCustom.getSku().getOuterSkuId());
                if (!response.isSuccess()) {
                    log.error("fail to find thirdPartySkuStock by thirdPartyId={}, outerSkuId={}",
                            thirdPartyId, skuWithCustom.getSku().getOuterSkuId());
                    throw new JsonResponseException("thirdParty.sku.stock.find.fail");
                }
                for (ThirdPartySkuStock thirdPartySkuStock : response.getResult()) {
                    totalAuthenticStock += thirdPartySkuStock.getAuthenticStock();
                }
                if (skuWithCustom.getSku().getStockQuantity() <= totalAuthenticStock) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                log.error("outSku(outerSkuId={}) inventory shortage", skuWithCustom.getSku().getOuterSkuId());
                throw new JsonResponseException(513, "thirdParty.sku.stock.shortage");
            }
        }
    }

    private void checkDeliveryFeeTemplate(ItemDeliveryFee itemDeliveryFee) {
        if (itemDeliveryFee == null) {
            return;
        }
        final Long deliveryFeeTemplateId = itemDeliveryFee.getDeliveryFeeTemplateId();
        if (deliveryFeeTemplateId == null) {
            return;
        }

        Response<DeliveryFeeTemplate> findResp = deliveryFeeReadService.findDeliveryFeeTemplateById(deliveryFeeTemplateId);
        if (!findResp.isSuccess()) {
            log.error("fail to find delivery fee template by id:{},cause:{}", deliveryFeeTemplateId, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        DeliveryFeeTemplate deliveryFeeTemplate = findResp.getResult();

        CommonUser commonUser = UserUtil.getCurrentUser();
        if (!Objects.equals(deliveryFeeTemplate.getShopId(), commonUser.getShopId())) {
            log.error("the delivery fee template(id={}) not belong to seller(shop id={})",
                    deliveryFeeTemplateId, commonUser.getShopId());
            throw new JsonResponseException("delivery.fee.template.not.belong.to.seller");
        }
    }

    @RequestMapping(value = "/api/seller/items/{id}/status", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean updateItemStatus(@PathVariable("id") Long itemId, @RequestParam("status") Integer status) {
        log.info("ItemController.updateItemStatus, 更新上下架状态, itemId={}, status={}", itemId, status);
        CommonUser commonUser = UserUtil.getCurrentUser();

        Response<Boolean> r = self.updateStatusByShopIdAndItemId(commonUser.getShopId(),
                itemId, status);
        if (!r.isSuccess()) {
            log.error("failed to update status to {} for item(id={}), error code:{}",
                    status, itemId, r.getError());
            throw new JsonResponseException(r.getError());
        }

        log.info("ItemController.updateItemStatus, fire ItemUpdateEvent, itemId={}, status={}", itemId, status);
        EventSender.publish(new ItemUpdateEvent(itemId), true);
        return r.getResult();
    }

    @RequestMapping(value = "/api/seller/items/status", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean batchUpdateStatus(@RequestParam("ids[]") Long[] ids, @RequestParam Integer status) {
        log.info("ItemController.batchUpdateStatus, 更新上下架状态, itemId={}, status={}", JSON.toJSONString(ids), status);
        CommonUser commonUser = UserUtil.getCurrentUser();
        List<Long> itemIds = Arrays.asList(ids);
        Response<Boolean> r = self.batchUpdateStatusByShopIdAndItemIds(commonUser.getShopId(),
                itemIds, status);
        if (!r.isSuccess()) {
            log.error("failed to update status to {} for item(id={}), error code:{}",
                    status, itemIds, r.getError());
            throw new JsonResponseException(r.getError());
        }
        for (Long id : ids) {
            log.info("ItemController.batchUpdateStatus, fire ItemUpdateEvent, itemId={}, status={}", id, status);
            EventSender.publish(new ItemUpdateEvent(id), true);
        }
        return r.getResult();
    }

    /**
     * 更新上下架状态
     *
     * @param req 请求参数
     * @return 是否修改成功
     */
    @RequestMapping(value = "/api/seller/items/status/v2", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Boolean> batchUpdateStatus(@RequestBody @Valid ItemUpdateReqDto req) {
        log.info("更新上下架状态 请求参数 {}", JSONUtil.toJsonStr(req));
        List<Long> itemIds = req.getIds();
        Integer status = req.getStatus();
        CommonUser commonUser = UserUtil.getCurrentUser();
        Response<Boolean> r = self.batchUpdateStatusByShopIdAndItemIds(commonUser.getShopId(),
                itemIds, status);
        if (!r.isSuccess()) {
            log.error("failed to update status to {} for item(id={}), error code:{}",
                    status, itemIds, r.getError());
            throw new JsonResponseException(r.getError());
        }
        for (Long id : itemIds) {
            log.info("ItemController.batchUpdateStatus, fire ItemUpdateEvent, itemId={}, status={}", id, status);
            EventSender.publish(new ItemUpdateEvent(id), true);
        }
        return Result.data(r.getResult());
    }

    @RequestMapping(value = "/api/mobile/item/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public FullItem findById(@PathVariable("id") Long id) {
        Response<FullItem> rFullItem = itemReadService.findFullInfoByItemId(id);
        if (!rFullItem.isSuccess()) {
            log.error("failed to find full item(id={}), error code:{}", id, rFullItem.getError());
            throw new JsonResponseException(rFullItem.getError());
        }
        return rFullItem.getResult();
    }

    // these are delegating to services

    @RequestMapping(value = "/api/items", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Item> findItemsByIds(@RequestParam Long[] ids) {
        if (ids == null || ids.length == 0) {
            return Collections.emptyList();
        }
        var resp = itemReadService.findByIds(Lists.newArrayList(ids));
        if (!resp.isSuccess()) {
            log.error("find items by ids failed, ids={}, error={}",
                    ids, resp.getError());
            throw new JsonResponseException(resp.getError());
        }
        return resp.getResult();
    }

    @RequestMapping(value = "/api/item/{itemId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Item> findItemById(@PathVariable Long itemId) {
        return itemReadService.findById(itemId);
    }

    /**
     * 商品分享图片生成
     */
    @RequestMapping(value = "/api/item/{itemId}/share", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public void itemShare(HttpServletResponse response, @PathVariable Long itemId, @RequestParam String qrCodeRef, @RequestParam String price) {
        Response<Item> itemResult = itemReadService.findById(itemId);
        if (itemResult.isSuccess()) {
            OutputStream out = null;
            try {
                String mainImage = itemResult.getResult().getMainImage_();
                String item = itemResult.getResult().getName();
                String note = "扫描或长按识别二维码";
                BufferedImage image = ShareImageUtil.createItemShareImage(mainImage, qrCodeRef, item, new BigDecimal(price), note);
                out = response.getOutputStream();
                response.setContentType("image/jpg");
                ImageIO.write(image, ShareImageUtil.IMAGE_FORMAT_JPG, out);
                out.flush();
            } catch (Exception e) {
                log.error("fail to create itemShareImage, cause: {}", Throwables.getStackTraceAsString(e));
            } finally {
                if (out != null) {
                    try {
                        out.close();
                    } catch (Exception e) {
                        log.error("fail to close image OutputStream, cause: {}", Throwables.getStackTraceAsString(e));
                    }
                }
            }
        }
    }

    @EventListener(ItemUpdateEvent.class)
    @VertxEventBusListener(ItemUpdateEvent.class)
    @MQEventConsumerMethod(ItemUpdateEvent.class)
    public void updateCache(ItemUpdateEvent event) {
        itemCacheHolder.invalid(event.getItemId());
    }

    @RequestMapping(value = "/api/item/{itemId}/for-view", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<ViewedItem> findForView(@PathVariable Long itemId, @RequestParam(required = false) Long refererId) {
        CommonUser user = UserUtil.getCurrentUser();
        ViewedItemForLevelDistribution viewedItem = loadViewItemCache(itemId, refererId);
        if (Objects.nonNull(user)) {
            itemDetailMemberPriceHelper.setMemberPriceForViewedItem(itemId, viewedItem, user);
            searchItemProfitViewHelper.setViewedItemProfit(viewedItem, user);
        }

        // 优惠卷信息
        itemConvertor.appendPromotionInfo(viewedItem);
        // 标记
        itemConvertor.appendFlag(viewedItem);

        return Response.ok(viewedItem);
    }

    public ViewedItemForLevelDistribution loadViewItemCache(Long itemId, Long refererId) {
        var view = itemCacheHolder.findViewedItemById(itemId);
        if (refererId == null) {
            return ViewedItemForLevelDistribution.from(view, List.of(), null);
        }
        Long shopId = view.getItem().getShopId();
        Shop shop = shopCacheHolder.findShopById(shopId);

        String mode = Optional.ofNullable(shop.getExtra()).map(m -> m.get(ShopExtra.SalesPattern.getCode()))
                .orElse(SalePattern.Common.getCode());
        String name = mode.equals(SalePattern.StoreProxy.getCode()) ? Optional.of(refererId).flatMap(userId -> storeProxyManager.findValidStoreProxyFromRefererIdAndShopId(shopId, userId))
                .map(StoreProxy::getProxyShopName).orElse(null) : null;

        return ViewedItemForLevelDistribution.from(view
                , itemStockNameQueryHelper.queryStockNameByItemId(itemId)
                , name);
    }

    /**
     * 根据传入的UserId作为查询会员价的依据 获取商品详情
     *
     * @param itemId 商品id
     * @param pid    查询会员价依据的用户Id
     * @return 用于商品详情页显示的商品
     */
    @RequestMapping(value = "/api/item/{itemId}/membershipByPid/for-view", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<ViewedItem> findMembershipByPUserIdForView(@PathVariable Long itemId,
                                                               @RequestParam(required = false) Long pid) {
        Response<ViewedItem> res = itemReadService.findForView(itemId);
        ViewedItem viewedItem = res.getResult();
        if (pid != null) {
            Integer lowPrice = null;
            Integer highPrice = null;
            for (Sku sku : viewedItem.getSkus()) {
                membershipPriceChecker.check(sku, pid);
                if (lowPrice == null) {
                    lowPrice = sku.getPrice();
                    highPrice = lowPrice;
                } else {
                    Integer price = sku.getPrice();
                    if (lowPrice > price) {
                        lowPrice = price;
                    } else if (highPrice < price) {
                        highPrice = price;
                    }
                }
            }
            viewedItem.getItem().setLowPrice(lowPrice);
            viewedItem.getItem().setHighPrice(highPrice);
        }
        return res;
    }

    private Map<String, String> shapeMap(Map<String, String> map) {
        return map == null ? new TreeMap<>() : map;
    }

    @RequestMapping(value = "/api/item/{itemId}/for-edit", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<EditItem> findForEdit(@PathVariable Long itemId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null) {
            log.error("user not login when find item for edit.");
            throw new JsonResponseException(401, "user.not.login");
        }

        Response<EditItem> editItemResponse = itemReader.findForEdit(commonUser, itemId);
        if (editItemResponse.isSuccess()) {
            List<SkuWithCustom> skuWithCustomList = editItemResponse.getResult().getSkuWithCustoms();
            skuWithCustomList.forEach(skuWithCustom ->
            {
                Sku sku = skuWithCustom.getSku();
                if (!shapeMap(sku.getTags()).getOrDefault("pushSystem", "").isEmpty()) {
                    Map<String, String> extra = sku.getExtraMap();
                    int quantityMax = 0;
                    try {
                        for (String pushSystem : sku.getTags().get(SkuTagIndex.pushSystem.name()).split(",")) {
                            ThirdPartySystem thirdPartySystem = ThirdPartySystem.fromInt(Integer.parseInt(pushSystem));
                            if (thirdPartySystem == null || thirdPartySystem == ThirdPartySystem.Unknown) {
                                continue;
                            }
                            int quantity = thirdPartyCacheHolder.getStock(sku.getShopId(), thirdPartySystem, sku.getOuterSkuId()).stream().map(ThirdPartySkuStock::getAuthenticStock)
                                    .reduce(Integer::sum).orElse(0);
                            quantityMax = Math.max(quantityMax, quantity);
                        }
                    } catch (Exception ex) {
                        log.error("{} fail to load the stock tags[{}] sku[{}]", LogUtil.getClassMethodName(), sku.getTags(), sku.getOuterShopId(), ex);
                    }
                    extra.put("thirdPartyStockQuantity", quantityMax + "");
                    log.debug("{} query sku[{}] item[{}] outerSkuId[{}] quantity[{}]", LogUtil.getClassMethodName(), sku.getId(), itemId, sku.getOuterSkuId(), quantityMax);
                    sku.setExtraMap(extra);
                }
            });
        }
        //中间字段处理
        List<IntermediateInfo> rIntermediateInfos = intermediateInfoComponent.findSkuByItemId(itemId);
        if (!CollectionUtils.isEmpty(rIntermediateInfos)) {
            var normal = rIntermediateInfos.stream()
                    .filter(entity -> !IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode().equals(entity.getMatchingType()))
                    .findFirst()
                    .orElse(null);
            appendNormalCommissionConfig(normal, editItemResponse);

            var activity = rIntermediateInfos.stream()
                    .filter(entity -> IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode().equals(entity.getMatchingType()))
                    .findFirst()
                    .orElse(null);
            appendActivityCommissionConfig(activity, editItemResponse);
        }

        return editItemResponse;
    }

    /**
     * 获取编辑商品数据
     *
     * @param itemId 商品id
     * @return 商品数据
     */
    @GetMapping(value = "/api/item/for-edit/v2")
    public Result<EditItem> findForEditV2(@RequestParam("itemId") Long itemId) {
        log.info("获取编辑商品数据 请求参数 {}", itemId);
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null) {
            throw new ApiException(ResultCode.UN_AUTHORIZED);
        }
        Response<EditItem> editItemResponse = itemReader.findForEdit(commonUser, itemId);
        if (editItemResponse.isSuccess()) {
            List<SkuWithCustom> skuWithCustomList = editItemResponse.getResult().getSkuWithCustoms();
            skuWithCustomList.forEach(skuWithCustom ->
            {
                Sku sku = skuWithCustom.getSku();
                if (!shapeMap(sku.getTags()).getOrDefault("pushSystem", "").isEmpty()) {
                    Map<String, String> extra = sku.getExtraMap();
                    int quantityMax = 0;
                    try {
                        for (String pushSystem : sku.getTags().get(SkuTagIndex.pushSystem.name()).split(",")) {
                            ThirdPartySystem thirdPartySystem = ThirdPartySystem.fromInt(Integer.parseInt(pushSystem));
                            if (thirdPartySystem == null || thirdPartySystem == ThirdPartySystem.Unknown) {
                                continue;
                            }
                            int quantity = thirdPartyCacheHolder.getStock(sku.getShopId(), thirdPartySystem, sku.getOuterSkuId()).stream().map(ThirdPartySkuStock::getAuthenticStock)
                                    .reduce(Integer::sum).orElse(0);
                            quantityMax = Math.max(quantityMax, quantity);
                        }
                    } catch (Exception ex) {
                        log.error("{} fail to load the stock tags[{}] sku[{}]", LogUtil.getClassMethodName(), sku.getTags(), sku.getOuterShopId(), ex);
                    }
                    extra.put("thirdPartyStockQuantity", quantityMax + "");
                    log.debug("{} query sku[{}] item[{}] outerSkuId[{}] quantity[{}]", LogUtil.getClassMethodName(), sku.getId(), itemId, sku.getOuterSkuId(), quantityMax);
                    sku.setExtraMap(extra);
                }
            });
        }
        //中间字段处理
        List<IntermediateInfo> rIntermediateInfos = intermediateInfoComponent.findSkuByItemId(itemId);
        if (!CollectionUtils.isEmpty(rIntermediateInfos)) {
            var normal = rIntermediateInfos.stream()
                    .filter(entity -> !IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode().equals(entity.getMatchingType()))
                    .findFirst()
                    .orElse(null);
            appendNormalCommissionConfig(normal, editItemResponse);

            var activity = rIntermediateInfos.stream()
                    .filter(entity -> IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode().equals(entity.getMatchingType()))
                    .findFirst()
                    .orElse(null);
            appendActivityCommissionConfig(activity, editItemResponse);
        }

        return Result.data(editItemResponse.getResult());
    }

    /**
     * 填充活动佣金配置
     *
     * @param activity
     * @param editItemResponse
     */
    private void appendActivityCommissionConfig(IntermediateInfo activity, Response<EditItem> editItemResponse) {
        if (activity == null) {
            return;
        }
        ItemCommissionConfigDTO activityConfig = new ItemCommissionConfigDTO();

        activityConfig.setCommission(activity.getCommission());
        activityConfig.setFirstRate(activity.getFirstRate());
        activityConfig.setFirstFee(activity.getFirstFee());
        activityConfig.setSecondRate(activity.getSecondRate());
        activityConfig.setSecondFee(activity.getSecondFee());
        activityConfig.setIsCommission(activity.getIsCommission());
        activityConfig.setFirstCommission(activity.getFirstCommission());
        activityConfig.setFlag(activity.getFlag());
        activityConfig.setServiceProviderFee(activity.getServiceProviderFee());
        activityConfig.setServiceProviderRate(activity.getServiceProviderRate());

        activityConfig.setMatchingStartTimeString(DateUtil.toString(activity.getMatchingStartTime()));
        activityConfig.setMatchingEndTimeString(DateUtil.toString(activity.getMatchingEndTime()));
        activityConfig.setMatchingType(activity.getMatchingType());

        editItemResponse.getResult().setActivityCommissionConfig(activityConfig);
    }

    /**
     * 填充通常佣金配置
     *
     * @param rIntermediateInfo
     * @param editItemResponse
     */
    private void appendNormalCommissionConfig(IntermediateInfo rIntermediateInfo, Response<EditItem> editItemResponse) {
        if (rIntermediateInfo == null) {
            return;
        }

        editItemResponse.getResult().setCommission(rIntermediateInfo.getCommission());
        editItemResponse.getResult().setFirstRate(rIntermediateInfo.getFirstRate());
        editItemResponse.getResult().setFirstFee(rIntermediateInfo.getFirstFee());
        editItemResponse.getResult().setSecondRate(rIntermediateInfo.getSecondRate());
        editItemResponse.getResult().setSecondFee(rIntermediateInfo.getSecondFee());
        editItemResponse.getResult().setIsCommission(rIntermediateInfo.getIsCommission());
        editItemResponse.getResult().setFirstCommission(rIntermediateInfo.getFirstCommission());
        editItemResponse.getResult().setFlag(rIntermediateInfo.getFlag());
        editItemResponse.getResult().setServiceProviderFee(rIntermediateInfo.getServiceProviderFee());
        editItemResponse.getResult().setServiceProviderRate(rIntermediateInfo.getServiceProviderRate());
        editItemResponse.getResult().setIsCashGift(rIntermediateInfo.getIsCashGift());
        editItemResponse.getResult().setMaxDeduction(rIntermediateInfo.getMaxDeduction());
        editItemResponse.getResult().setUnit(rIntermediateInfo.getUnit());
        editItemResponse.getResult().setCashGiftUseTimeStart(rIntermediateInfo.getCashGiftUseTimeStart());
        editItemResponse.getResult().setCashGiftUseTimeEnd(rIntermediateInfo.getCashGiftUseTimeEnd());
    }

    @RequestMapping(value = "/api/item/{itemId}/detail-info", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<ViewedItemDetailInfo> findItemDetailInfoByItemId(@PathVariable Long itemId) {
        return Response.ok(itemCacheHolder.findViewDetail(itemId));
    }
    /**
     * 获取商品详情
     * @param itemId 商品id
     * @return 获取商品详情
     */
    @GetMapping(value = "/{itemId}/detail-info/v2")
    public Result<ViewedItemDetailInfo> findItemDetailInfoByItemIdV2(@PathVariable Long itemId) {
        ViewedItemDetailInfo viewDetail = itemCacheHolder.findViewDetail(itemId);
        return Result.data(viewDetail);
    }

    @RequestMapping(value = "/api/items/find-by-ids", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<List<Item>> findByIds(@RequestParam String ids) {
        if (!StringUtils.hasText(ids)) {
            return Response.ok(Collections.emptyList());
        }

        List<Long> idList = Lists.newArrayList();
        for (String id : Splitters.COMMA.splitToList(ids)) {
            try {
                idList.add(Long.parseLong(id));
            } catch (Exception e) {
                log.error("fail to parse id:{} to long", id);
            }
        }

        if (idList.isEmpty()) {
            return Response.ok(Collections.emptyList());
        }
        return itemReadService.findByIds(idList);
    }

    @RequestMapping(value = "/api/seller/items/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Paging<ItemForList>> pagingItems(@RequestParam(required = false) Long shopId,
                                                     @RequestParam(required = false) String itemCode,
                                                     @RequestParam(required = false) Long itemId,
                                                     @RequestParam(required = false) String itemName,
                                                     @RequestParam(required = false) Integer status,
                                                     @RequestParam(required = false) String statuses,
                                                     @RequestParam(required = false) Integer type,
                                                     @RequestParam(required = false) String typees,
                                                     @RequestParam(required = false) Integer sellOutStatus,
                                                     @RequestParam(required = false) Long shopCategoryId,
                                                     @RequestParam(required = false) Integer pageNo,
                                                     @RequestParam(required = false) Integer pageSize,
                                                     @RequestParam(required = false) Long restrictedSalesAreaTemplateId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (shopId != null) {
            commonUser.setShopId(shopId);
        }
        Response<Paging<Item>> rpItem = itemReadService.findBy(commonUser, null, itemCode, itemId, itemName, statuses, status, type, typees,
                sellOutStatus, shopCategoryId, restrictedSalesAreaTemplateId, pageNo, pageSize);
        if (!rpItem.isSuccess()) {
            log.error("failed to paging items by shopId={}, itemCode={}, itemId={}, itemName={}, status={}, statuses={}, type={}, pageNo={}, pageSize={}, error code:{}",
                    shopId, itemCode, itemId, itemName, status, statuses, type, pageNo, pageSize, rpItem.getError());
            throw new JsonResponseException(rpItem.getError());
        }
        Paging<Item> pItem = rpItem.getResult();
        List<ItemForList> itemForLists = new ArrayList<>();
        for (Item item : pItem.getData()) {
            ItemForList itemForList = CopyUtil.copy(item, ItemForList.class);
            String itemWxaUrl = Optional.ofNullable(shopCacheHolder.findShopById(item.getShopId()))
                    .map(Shop::getExtra)
                    .map(extra -> extra.get(ShopExtra.shopUrl.getCode()))
                    .orElse(paranaConfig.getParanaWxaUrl());
            if (itemWxaUrl.endsWith("/")) {
                itemWxaUrl = itemWxaUrl.substring(0, itemWxaUrl.length() - 1);
            }
            itemWxaUrl = itemWxaUrl + "/goods/detail?storeId=" + shopId + "&skuSn=" + item.getId();
            itemForList.setWxaUrl(itemWxaUrl);
            //itemForList.setTagList(Optional.ofNullable(itemTagController.queryTagByItem(item.getId()).getResult()).orElseGet(ArrayList::new).stream().map(Tag::getName).collect(Collectors.toList()));
            itemForLists.add(itemForList);
        }

        // 商品于店铺内的类目路径
        itemConvertor.appendFullShopCategory(itemForLists);
        // 佣金信息
        itemConvertor.appendCommissionInfo(itemForLists);
        // 售罄状态
        itemConvertor.appendSellOutStatus(itemForLists);
        // 填充sku相关的信息
        itemConvertor.appendSkuInfo(itemForLists);
        // 限售模板相关信息
        itemConvertor.appendRestrictedSalesAreaTemplateInfo(itemForLists);

        return Response.ok(new Paging<>(pItem.getTotal(), itemForLists));
    }


    @RequestMapping(value = "/api/seller/items/paging/new", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<DataPage<ItemForList>> pagingItemsNew(@RequestParam Long shopId,
                                                        @RequestParam(required = false) String itemCode,
                                                        @RequestParam(required = false) Long itemId,
                                                        @RequestParam(required = false) String itemName,
                                                        @RequestParam(required = false) Integer status,
                                                        @RequestParam(required = false) String statuses,
                                                        @RequestParam(required = false) Integer type,
                                                        @RequestParam(required = false) String typees,
                                                        @RequestParam(required = false) Integer pageNo,
                                                        @RequestParam(required = false) Integer pageSize) {
        CommonUser commonUser = new CommonUser();
        commonUser.setShopId(shopId);
        Response<Paging<Item>> rpItem = itemReadService.findBy(commonUser, null, itemCode, itemId, itemName, statuses, status, type, typees,
                null, null, null, pageNo, pageSize);
        if (!rpItem.isSuccess()) {
            log.error("failed to paging items by shopId={}, itemCode={}, itemId={}, itemName={}, status={}, statuses={}, type={}, pageNo={}, pageSize={}, error code:{}",
                    shopId, itemCode, itemId, itemName, status, statuses, type, pageNo, pageSize, rpItem.getError());
            throw new JsonResponseException(rpItem.getError());
        }
        Paging<Item> pItem = rpItem.getResult();
        //获取分页信息
        long currentPage;
        //获取当页条数
        if (java.util.Objects.isNull(pageSize)) {
            pageSize = 20;
        }
        //获取当前页数
        if (java.util.Objects.nonNull(pageNo)) {
            currentPage = pageNo;
        } else {
            currentPage = 1L;
        }
        //构建分页信息类
        TotalCalculation totalCalculation = new TotalCalculation(pageSize, currentPage, pItem.getTotal(), 0L);
        totalCalculation.setTotalPage(totalCalculation.getPageSize(), totalCalculation.getTotalCount());
        List<ItemForList> itemForLists = new ArrayList<>();
        for (Item item : pItem.getData()) {
            ItemForList itemForList = CopyUtil.copy(item, ItemForList.class);
            String itemWxaUrl = Optional.ofNullable(shopCacheHolder.findShopById(item.getShopId()))
                    .map(Shop::getExtra)
                    .map(extra -> extra.get(ShopExtra.shopUrl.getCode()))
                    .orElse(paranaConfig.getParanaWxaUrl());
            if (itemWxaUrl.endsWith("/")) {
                itemWxaUrl = itemWxaUrl.substring(0, itemWxaUrl.length() - 1);
            }
            itemWxaUrl = itemWxaUrl + "/goods/detail?storeId=" + shopId + "&skuSn=" + item.getId();
            itemForList.setWxaUrl(itemWxaUrl);
            itemForList.setTagList(Optional.ofNullable(itemTagController.queryTagByItem(item.getId()).getResult()).orElseGet(ArrayList::new).stream().map(Tag::getName).collect(Collectors.toList()));
            itemForLists.add(itemForList);
        }
        return Result.data(DataPage.build(totalCalculation, itemForLists));
    }

    @RequestMapping(value = "/api/items/{id}/cache", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Item> findByIdWithCache(@PathVariable Long id) {
        Item item = itemCacheHolder.findItemById(id);
        return Response.ok(item);
    }

    @RequestMapping(value = "/api/seller/find-item-with-skus", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<ItemWithSkus> findItemWithSkus(@RequestParam(value = "itemCode", required = false) String itemCode,
                                                 @RequestParam(value = "itemName", required = false) String itemName,
                                                 @RequestParam(value = "shopCategoryId", required = false) Long shopCategoryId,
                                                 @RequestParam(value = "typees", required = false) String typees,
                                                 @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                 @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        final Long shopId = commonUser.getShopId();

        Response<Paging<ItemWithSkus>> findResp = itemReadService.findItemWithSkus(shopId, itemCode, itemName, shopCategoryId,
                typees, pageNo, pageSize);
        if (!findResp.isSuccess()) {
            log.error("fail to find item with skus,params(shopId={},itemCode={},itemName={},pageSize={},pageNo={}),cause:{},",
                    shopId, itemCode, itemName, pageNo, pageSize, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }

        if (findResp.getResult() != null && !findResp.getResult().isEmpty()) {
            // 商品于店铺内的类目路径
            itemConvertor.appendShopCategory(findResp.getResult().getData());
        }

        return findResp.getResult();
    }

    @RequestMapping(value = "/api/seller/items/item-export", method = RequestMethod.GET)
    public void exportItems(ItemCriteria criteria, HttpServletRequest request, HttpServletResponse response) throws IOException {
        checkShopIdNotNull();
        CommonUser user = UserUtil.getCurrentUser();
        criteria.setShopId(user.getShopId());
        criteria.setParanaWxaUrl(paranaConfig.getParanaWxaUrl());
        DefaultExporter.setHttpServletResponse(request, response, "商家商品列表" + new DateTime(new Date()).toString("yyyyMMddHHmmss"));
        itemReadLogic.exportExcel(criteria, response.getOutputStream());
    }

    private void checkShopIdNotNull() {
        CommonUser user = UserUtil.getCurrentUser();
        if (user.getShopId() == null) {
            log.warn("permission deny for user={}", user);
            throw new JsonResponseException("permission.deny");
        }
    }

    @RequestMapping(value = "/api/seller/items/query")
    public List<String> queryStockName(Long itemId) {
        return itemStockNameQueryHelper.queryStockNameByItemId(itemId);
    }

    // 以下为事务独立处理方法，避免eventBus触发事件幻读

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Boolean> update(Item item) {
        return itemWriteService.update(item);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Boolean> updateStatusByShopIdAndItemId(Long shopId, Long itemId, Integer status) {
        return itemWriteService.updateStatusByShopIdAndItemId(shopId, itemId, status);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Boolean> batchUpdateStatusByShopIdAndItemIds(Long shopId, List<Long> ids, Integer status) {
        return itemWriteService.batchUpdateStatusByShopIdAndItemIds(shopId, ids, status);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Long> copy(Item item, Shop shop) {
        return itemWriter.copy(item, shop);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Long> create(FullItem fullItem) {
        return itemWriter.create(fullItem);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Response<Boolean> update(FullItem fullItem) {
        return itemWriter.update(fullItem);
    }

    @GetMapping("/api/post")
    public void postTest(Long itemId) {
        EventSender.publish(new ItemUpdateEvent(itemId));
    }

    @GetMapping("/api/postShop")
    public void postShopTest(Long shopId) {
        EventSender.publish(new ShopUpdateEvent(shopId));
    }

    /**
     * 变更商品的"售罄状态"
     *
     * @param updateVO
     * @return
     */
    @PostMapping("api/seller/item/updateSellOutStatus")
    public APIResp<Boolean> updateSellOutStatus(@RequestBody SellOutStateUpdateVO updateVO) {
        CommonUser currentUser = UserUtil.getCurrentUser();
        if (currentUser == null || currentUser.getShopId() == null) {
            return APIResp.error("请重新登录");
        }
        if (updateVO == null || updateVO.getItemId() == null || updateVO.getSellOutStatus() == null) {
            return APIResp.error("入参皆不能为空");
        }

        try {
            return APIResp.ok(itemWriter.updateSellOutStatus(
                    currentUser.getShopId(), updateVO.getItemId(), updateVO.getSellOutStatus()));
        } catch (Exception ex) {
            log.error("ItemController.updateSellOutStatus error, parameter={}", JSON.toJSONString(updateVO), ex);
            return APIResp.error(ex.getMessage());
        }
    }

    /**
     * 迁移自 sort_manage.clj 里的 manage-sort 方法
     *
     * @param itemId
     * @param action
     * @return
     */
    @PostMapping("/api/seller/item/{itemId}/sort")
    public APIResp<Map<String, Object>> updateSort(@PathVariable("itemId") Long itemId, @RequestParam("action") String action) {
        CommonUser currentUser = UserUtil.getCurrentUser();
        if (currentUser == null || currentUser.getShopId() == null) {
            return APIResp.error("请重新登录");
        }
        if (itemId == null || !StringUtils.hasText(action)) {
            return APIResp.error("入参皆不能为空");
        }

        try {
            itemSortApp.sort(itemId, action);
            return APIResp.ok(Map.of("success", true));
        } catch (Exception ex) {
            log.error("ItemController.updateSort error, itemId={}, action={}", itemId, action, ex);
            return APIResp.ok(Map.of("error", ex.getMessage()));
        }
    }
}
