package moonstone.web.front.item;

import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Splitter;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.api.Result;
import moonstone.common.api.ResultCode;
import moonstone.common.api.Y800PlatformAPI;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.model.rpcAPI.y800dto.ListDTO;
import moonstone.common.model.rpcAPI.y800dto.Y800SkuCodeDTO;
import moonstone.common.model.rpcAPI.y800dto.Y800SkuQueryResponse;
import moonstone.common.utils.*;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopWriteService;
import moonstone.thirdParty.dto.BatchStock;
import moonstone.thirdParty.dto.DepotStock;
import moonstone.thirdParty.dto.RichThirdPartySkuStock;
import moonstone.thirdParty.dto.ThirdPartySkuInfo;
import moonstone.thirdParty.model.*;
import moonstone.thirdParty.service.*;
import moonstone.user.area.model.Area;
import moonstone.user.area.service.AreaReadService;
import moonstone.web.core.component.cache.ThirdPartyUserShopCache;
import moonstone.web.core.events.thirdParty.ThirdPartySynchronizeEvent;
import moonstone.web.core.events.thirdParty.ThirdPartyUserShopUpdateEvent;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.item.app.ItemSourceFromSystemVersion;
import moonstone.web.core.shop.application.ShopManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/api/item/thirdParty")
public class ThirdPartyController {
	@Value("${Y800.wd.support}")
	String y800WdSupport;

	@Resource
	private ThirdPartyUserShopReadService thirdPartyUserShopReadService;

	@Resource
	private ThirdPartyUserShopWriteService thirdPartyUserShopWriteService;

	@Resource
	private ThirdPartySkuShopReadService thirdPartySkuShopReadService;

	@Autowired
	private ThirdPartyUserShopCache thirdPartyUserShopCache;

	@Resource
	private ThirdPartySkuReadService thirdPartySkuReadService;

	@Resource
	private AreaReadService areaReadService;

	@Resource
	private ThirdPartySkuStockReadService thirdPartySkuStockReadService;
	@Resource
	private ThirdPartySkuStockWriteService thirdPartySkuStockWriteService;
	@Resource
	private SkuReadService skuReadService;

	@RemoteAPI
	Y800PlatformAPI y800PlatformApi;

	@Resource
	private ShopCacheHolder shopCacheHolder;

	@Resource
	private ShopWriteService shopWriteService;

	@Autowired
	private ItemSourceFromSystemVersion itemSourceFromSystemVersion;

	@Resource
	private ShopManager shopManager;

	@Resource
	private RocketMQProducer rocketMQProducer;

	/**
	 * 自动分割文本
	 * 将由 `,` 组合的列表字符串分割且取第一个
	 *
	 * @param groupedString 用`,`组合的列表
	 * @return 列表第一个数据
	 */
	private String getSplitOne(String groupedString) {
		if (groupedString == null) {
			return null;
		}
		return groupedString.split(",")[0];
	}

	/**
	 * 获取单品信息
	 *
	 * @param thirdPartyId 系统名称
	 * @param skuCode      商品代号
	 * @param shopId       可有可无的店铺Id
	 * @return 商品信息
	 */
	@RequestMapping(value = "/sku", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ThirdPartySkuInfo getSkuInfo(@RequestParam Integer thirdPartyId, @RequestParam String skuCode, @RequestParam(required = false) Long shopId) {
		if (itemSourceFromSystemVersion.isYang800(shopId)) {
			return getSkuInfoOld(thirdPartyId, skuCode);
		}
		CommonUser user = UserUtil.getCurrentUser();
		if (user != null && itemSourceFromSystemVersion.isYang800(user.getShopId())) {
			return getSkuInfoOld(thirdPartyId, skuCode);
		}
		try {
			if (shopId == null && user == null) {
				throw new RuntimeException("请登录");
			}
			assert user != null;
			shopId = Optional.ofNullable(shopId).orElseGet(user::getShopId);

			ThirdPartyUserShop thirdPartyUserShop = thirdPartyUserShopCache.findBy(new ThirdSystemAID(thirdPartyId, shopId))
					.orElseThrow(() -> Translate.exceptionOf("第三方店铺查询失败"));
			ThirdPartySkuInfo thirdPartySkuInfo = new ThirdPartySkuInfo();
			switch (ThirdPartySystem.fromInt(thirdPartyId)) {
				case Y800_V2: {
					try {
						y800PlatformApi.setAccessCode(thirdPartyUserShop.getThirdPartyCode());
						Either<List<Y800SkuQueryResponse>> result = y800PlatformApi.goodsQuery(new ListDTO<>(Collections.singletonList(new Y800SkuCodeDTO(skuCode))));
						if (result.isSuccess()) {
							Y800SkuQueryResponse response = result.take().get(0);
							BeanUtils.copyProperties(response, thirdPartySkuInfo);
							thirdPartySkuInfo.setOuterSkuId(response.getCode());
							thirdPartySkuInfo.setMainImage(getSplitOne(response.getImage()));
							thirdPartySkuInfo.setUnit(getSplitOne(response.getUnit()));
							thirdPartySkuInfo.setSpecification(getSplitOne(response.getSpecification()));
							thirdPartySkuInfo.setOrigin(getSplitOne(response.getOrigin()));
							thirdPartySkuInfo.setBrand(getSplitOne(response.getBrand()));
							if (response.getTradeType() != null) {
								thirdPartySkuInfo.setType(response.getTradeType());
							}
							NumberUtil.parseNumber(response.getWeight(), Float.TYPE).ifSuccess(thirdPartySkuInfo::setWeight);
							// 对方的垃圾代码 导致我这边必须兼容
							if (response.getDepotCode() != null || response.getDepotName() != null) {
								thirdPartySkuStockWriteService.updateDeportInfoByThirdPartyIdAndOuterSkuIdAndVolume(shopId, ThirdPartySystem.Y800_V2.Id(), response.getCode(), response.getDepotName(), response.getDepotCode(), response.getSupplierName(), new BigDecimal(response.getVolume()));
							}
						}
					} catch (Exception ex) {
						log.error("{} msg:{}", LogUtil.getClassMethodName(), ex.getMessage(), ex);
						return getSkuInfoOld(thirdPartyId, skuCode);
					}
					break;
				}
				case Y800_V3: {
					ThirdPartySku thirdPartySku = thirdPartySkuReadService.findByThirdPartyAndSkuId(thirdPartyId, skuCode).getResult();

					thirdPartySkuInfo.setName(thirdPartySku.getOuterSkuName());
					thirdPartySkuInfo.setType(0);
					break;
				}
				default:
					break;
			}

			/*  匹配地区id    */
			SkuCustom skuCustom = thirdPartySkuInfo.getSkuCustom();
			if (skuCustom != null) {
				if (skuCustom.getCustomOrigin() != null) {
					Response<Area> rOrigin = areaReadService.findByName(skuCustom.getCustomOrigin());
					if (rOrigin.isSuccess()) {
						skuCustom.setCustomOriginId(rOrigin.getResult().getId());
						Response<Area> rProvince = areaReadService.findByName(skuCustom.getProvince());
						if (rProvince.isSuccess()) {
							skuCustom.setProvinceId(rProvince.getResult().getId());
							Response<Area> rCity = areaReadService.findByName(skuCustom.getCity());
							if (rCity.isSuccess()) {
								skuCustom.setCityId(rCity.getResult().getId());
							}
						}
					}
				}
				thirdPartySkuInfo.setSkuCustom(skuCustom);
			}

			return thirdPartySkuInfo;
		} catch (Exception e) {
			log.error("failed to get outer sku info, thirdPartyId={}, skuCode={}, cause:", thirdPartyId, skuCode, e);
			ThirdPartySku thirdPartySku = thirdPartySkuReadService.findByThirdPartyAndSkuId(thirdPartyId, skuCode).getResult();
			if (thirdPartySku == null) {
				throw new JsonResponseException("thirdParty.sku.info.get.fail");
			}
			ThirdPartySkuInfo skuInfo = new ThirdPartySkuInfo();
			BeanUtils.copyProperties(thirdPartySku, skuInfo);
			skuInfo.setOuterSkuId(thirdPartySku.getOuterSkuId());
			skuInfo.setName(thirdPartySku.getOuterSkuName());
			if (ThirdPartySystem.fromInt(thirdPartyId) == ThirdPartySystem.Y800_V2) {
				skuInfo.setType(1);
			}
			return skuInfo;
		}
	}

	@RequestMapping(value = "/sku-old", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ThirdPartySkuInfo getSkuInfoOld(@RequestParam Integer thirdPartyId, @RequestParam String skuCode) {
		try {
			ThirdPartySkuInfo thirdPartySkuInfo = new ThirdPartySkuInfo();
			if (ThirdPartySystem.fromInt(thirdPartyId) == ThirdPartySystem.Y800_V2) {
				HttpRequest request = HttpRequest.get(y800WdSupport + "/api/sku/" + skuCode, true);
				if (request.ok()) {
					Map<String, Object> map = JSONObject.parseObject(request.body());
					if (!(boolean) map.get("success")) {
						log.error("[" + y800WdSupport + "/api/sku/" + skuCode + "]Request failed, cause:{}", map.get("result"));
						throw new JsonResponseException("skuInfo.get.fail");
					}
					thirdPartySkuInfo = JSONObject.parseObject(JSONObject.toJSONString(map.get("result")), ThirdPartySkuInfo.class);
				} else {
					log.error("[" + y800WdSupport + "/api/sku/" + skuCode + "]HttpRequest failed");
					throw new JsonResponseException(500, "skuInfo.http.request.fail");
				}
			}

			/*匹配地区id*/
			SkuCustom skuCustom = thirdPartySkuInfo.getSkuCustom();
			if (skuCustom != null) {
				if (skuCustom.getCustomOrigin() != null) {
					Response<Area> rOrigin = areaReadService.findByName(skuCustom.getCustomOrigin());
					if (rOrigin.isSuccess()) {
						skuCustom.setCustomOriginId(rOrigin.getResult().getId());
						Response<Area> rProvince = areaReadService.findByName(skuCustom.getProvince());
						if (rProvince.isSuccess()) {
							skuCustom.setProvinceId(rProvince.getResult().getId());
							Response<Area> rCity = areaReadService.findByName(skuCustom.getCity());
							if (rCity.isSuccess()) {
								skuCustom.setCityId(rCity.getResult().getId());
							}
						}
					}
				}
				thirdPartySkuInfo.setSkuCustom(skuCustom);
			}

			return thirdPartySkuInfo;
		} catch (Exception e) {
			log.error("failed to get outer sku info, thirdPartyId={}, skuCode={}, cause:", thirdPartyId, skuCode, e);
			ThirdPartySku sku = thirdPartySkuReadService.findByThirdPartyAndSkuId(thirdPartyId, skuCode).getResult();
			if (sku == null) {
				throw new JsonResponseException("thirdParty.sku.info.get.fail");
			}
			ThirdPartySkuInfo skuInfo = new ThirdPartySkuInfo();
			BeanUtils.copyProperties(sku, skuInfo);
			skuInfo.setName(sku.getOuterSkuName());
			skuInfo.setOuterSkuId(sku.getOuterSkuId());
			return skuInfo;
		}
	}

	@RequestMapping(value = "/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public Paging<Map<String, Object>> paging(@RequestParam(required = false, defaultValue = "0") Integer thirdPartyId,
	                                          @RequestParam(required = false, defaultValue = "") String text,
	                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
	                                          @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
		CommonUser commonUser = UserUtil.getCurrentUser();

		Response<Paging<ThirdPartySku>> rThirdPartySkus;
		boolean blockV2 = shopManager.isBlockV2Item(commonUser.getShopId());

		Response<List<String>> rOuterSkuIds = thirdPartySkuShopReadService.findOuterSkuIdByThirdPartyIdAndShopId(
				thirdPartyId, commonUser.getShopId(), blockV2);
		if (!rOuterSkuIds.isSuccess()) {
			log.error("fail to find outerSkuIds by thirdPartyId={}, shopId={}, cause:{}",
					thirdPartyId, commonUser.getShopId(), rOuterSkuIds.getError());
			throw new JsonResponseException(rOuterSkuIds.getError());
		}
		List<String> outerSkuIds = rOuterSkuIds.getResult();
		if (outerSkuIds.isEmpty()) {
			log.warn("no thirdPartySkuShop was found by thirdPartyId={}, shopId={}", thirdPartyId, commonUser.getShopId());
			return new Paging<>();
		}

		rThirdPartySkus = thirdPartySkuReadService.pagingInOuterSkuIdsWithText(thirdPartyId, outerSkuIds, text, pageNo, pageSize);

		if (!rThirdPartySkus.isSuccess()) {
			log.error("fail to paging thirdPartySku, cause:{}", rThirdPartySkus.getError());
			throw new JsonResponseException(rThirdPartySkus.getError());
		}

		Paging<ThirdPartySku> paging = rThirdPartySkus.getResult();

		List<ThirdPartySku> thirdPartySkus = paging.getData();

		List<Map<String, Object>> data = new ArrayList<>();
		for (ThirdPartySku thirdPartySku : thirdPartySkus) {
			Map<String, Object> map = new HashMap<>(2);
			map.put("skuCode", thirdPartySku.getOuterSkuId());
			map.put("name", thirdPartySku.getOuterSkuName());
			data.add(map);
		}

		Paging<Map<String, Object>> result = new Paging<>();
		result.setTotal(paging.getTotal());
		result.setData(data);

		return result;
	}

	@RequestMapping(value = "/bind", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public boolean bind(@RequestBody ThirdPartyUserShop thirdPartyUserShop) {
		CommonUser commonUser = UserUtil.getCurrentUser();

		thirdPartyUserShop.setShopId(commonUser.getShopId());

		Response<Boolean> resp = thirdPartyUserShopWriteService.bind(thirdPartyUserShop);
		if (!resp.isSuccess()) {
			log.error("failed to bind shop with thirdParty user, error code:{}", resp.getError());
			throw new JsonResponseException(resp.getError());
		}

		EventSender.publish(new ThirdPartyUserShopUpdateEvent(thirdPartyUserShop.getThirdPartyId(), thirdPartyUserShop.getShopId()));
		EventSender.send(new ThirdPartySynchronizeEvent(ThirdPartySystem.fromInt(thirdPartyUserShop.getThirdPartyId()), thirdPartyUserShop));

		return resp.getResult();
	}

//	@RequestMapping(value = "/synchronize", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
//	public Result<Boolean> synchronize(@RequestParam(value = "thirdPartyId",required = false) Integer thirdPartyId) {
//		if (thirdPartyId == null) {
//			thirdPartyId = 3;
//		}
//		CommonUser commonUser = UserUtil.getCurrentUser();
//		Long shopId = commonUser.getShopId();
//		try {
//			Response<ThirdPartyUserShop> rThirdPartyUserShop = thirdPartyUserShopReadService.findByThirdPartyIdAndShopId(thirdPartyId, shopId);
//			if (!rThirdPartyUserShop.isSuccess()) {
//				log.error("failed to find thirdPartyUserShop, error code:{}", rThirdPartyUserShop.getError());
//				throw new JsonResponseException(rThirdPartyUserShop.getError());
//			}
//			ThirdPartyUserShop thirdPartyUserShop = rThirdPartyUserShop.getResult();
//			if (ObjectUtils.isEmpty(thirdPartyUserShop)) {
//				throw new JsonResponseException("thirdParty.user.shop.not.find");
//			}
//			if (thirdPartyUserShop.getStatus() < 0) {
//				throw new JsonResponseException("thirdParty.user.shop.status.abnormal");
//			}
//
//			EventSender.send(new ThirdPartySynchronizeEvent(ThirdPartySystem.fromInt(thirdPartyUserShop.getThirdPartyId()), thirdPartyUserShop));
//			return Result.data(true);
//		} catch (Exception e) {
//			log.error("fail to synchronize thirdPartyItems by thirdPartyId={}, shopId={}, cause:{}", thirdPartyId, shopId, Throwables.getStackTraceAsString(e));
//			throw new JsonResponseException("third.party.sku.synchronize.fail");
//		}
//	}

	@RequestMapping(value = "/synchronize", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public Result<Boolean> synchronize(@RequestParam(value = "thirdPartyId",required = false) Integer thirdPartyId) {
		if (thirdPartyId == null) {
			thirdPartyId = 3;
		}
		CommonUser commonUser = UserUtil.getCurrentUser();
		if (ObjUtil.isEmpty(commonUser)) {
			throw new ApiException(ResultCode.UN_AUTHORIZED);
		}
		Long shopId = commonUser.getShopId();
		JSONObject msg = new JSONObject();
		msg.put("thirdPartyId", thirdPartyId);
		msg.put("shopId", shopId);
		rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.PRODUCT_THIRD_PARTY_STOCK_SYNCHRONIZATION_TAG, msg.toString());
		return Result.data(true);
	}

	@RequestMapping(value = "/list", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public List<Map<String, Object>> list(HttpServletRequest request) {
		CommonUser commonUser = UserUtil.getCurrentUser();

		Map<String, String> shopExtra = new HashMap<>(2);
		int shopType = 0;
		if (commonUser != null && commonUser.getShopId() != null) {
			shopType = Optional.ofNullable(shopCacheHolder.findShopById(commonUser.getShopId()).getType()).orElse(9);
			shopExtra = Optional.ofNullable(shopCacheHolder.findShopById(commonUser.getShopId()).getExtra()).orElse(shopExtra);
		}

		List<Map<String, Object>> result = new ArrayList<>();

		// 设置限制的第三方推送
		String[] pushSystemIdStrArray = shopExtra.getOrDefault(ShopExtra.PushSystem.getCode(), "").split(",");
		Set<ThirdPartySystem> thirdPartySystemSet = Stream.of(pushSystemIdStrArray).filter(StringUtils::hasText).
				map(Integer::parseInt).map(ThirdPartySystem::fromInt).collect(Collectors.toSet());

		if (thirdPartySystemSet.isEmpty()) {
			thirdPartySystemSet.addAll(Arrays.asList(ThirdPartySystem.values()));
		}

		thirdPartySystemSet.remove(ThirdPartySystem.Unknown);
		for (ThirdPartySystem thirdPartySystem : thirdPartySystemSet.stream().sorted(Comparator.comparing(ThirdPartySystem::Id)).collect(Collectors.toList())) {
			Map<String, Object> map = new HashMap<>(thirdPartySystemSet.size());
			map.put("thirdPartyId", thirdPartySystem.Id());
			map.put("thirdPartyName", thirdPartySystem.Name());
			if (thirdPartySystem == ThirdPartySystem.Y800_V2) {
				// 对邮顺同进行改名
				if (Optional.ofNullable(request.getHeader("host")).orElse("").endsWith("youshuntong.cn")) {
					map.put("thirdPartyName", "邮顺通");
				}
			}
			result.add(map);
		}

		// 进行店铺对第三方限制   (兼容老版本用)
		if (pushSystemIdStrArray.length == 0 && shopType != 3) {
			result.removeIf(map -> map.getOrDefault("thirdPartyId", "").equals(ThirdPartySystem.Y800_V3.Id()));
			thirdPartySystemSet.remove(ThirdPartySystem.Y800_V3);
			StringBuilder builder = new StringBuilder();
			thirdPartySystemSet.stream().map(ThirdPartySystem::Id).forEach(str -> {
				builder.append(str);
				builder.append(",");
			});
			//  更新老版本数据,进行兼容
			if (commonUser != null) {
				Shop shop = shopCacheHolder.findShopById(commonUser.getShopId());
				Map<String, String> updateExtra = shop.getExtra();
				updateExtra.put(ShopExtra.PushSystem.getCode(), builder.toString());
				Shop update = new Shop();
				update.setId(shop.getId());
				update.setExtra(updateExtra);
				shopWriteService.update(update);
			}
		}

		return result;
	}

	@RequestMapping(value = "/stock/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public Response<Paging<RichThirdPartySkuStock>> pagingThirdPartySku(@RequestHeader(value = "host", required = false) String host, @RequestParam(defaultValue = "0") Integer thirdPartyId,
	                                                                    @RequestParam(required = false) String depotName,
	                                                                    @RequestParam(required = false) String outerSkuId,
	                                                                    @RequestParam(required = false) String outerSkuName,
	                                                                    @RequestParam(required = false) Integer pageNo,
	                                                                    @RequestParam(required = false) Integer pageSize,
	                                                                    @RequestParam(required = false) Long shopId,
																		@RequestParam(required = false) Integer matchStatus) {
		CommonUser commonUser = UserUtil.getCurrentUser();
		if (shopId == null) {
			if (commonUser == null || commonUser.getShopId() == null) {
				return Response.fail("user.not.login");
			}
		}
		boolean blockV2 = shopManager.isBlockV2Item(commonUser.getShopId());

		try {
			Paging<RichThirdPartySkuStock> result = new Paging<>();
			Response<Paging<ThirdPartySkuShop>> rThirdPartySkuShops = thirdPartySkuShopReadService.pagingByThirdPartyIdAndShopId(
					thirdPartyId, commonUser.getShopId(), outerSkuId, outerSkuName, matchStatus, depotName, pageNo, pageSize, blockV2);
			if (!rThirdPartySkuShops.isSuccess()) {
				log.error("fail to paging thirdPartySkuShop by thirdPartyId={}, shopId={}, outerSkuId={}, outerSkuName={}, cause:{}",
						thirdPartyId, commonUser.getShopId(), outerSkuId, outerSkuName, rThirdPartySkuShops.getError());
				throw new JsonResponseException(rThirdPartySkuShops.getError());
			}

			result.setTotal(rThirdPartySkuShops.getResult().getTotal());

			List<RichThirdPartySkuStock> richThirdPartySkuStocks = new ArrayList<>();
			for (ThirdPartySkuShop thirdPartySkuShop : rThirdPartySkuShops.getResult().getData()) {
				Response<List<ThirdPartySkuStock>> rThirdPartySkuStocks = thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(commonUser.getShopId(),
						thirdPartySkuShop.getThirdPartyId(), thirdPartySkuShop.getOuterSkuId());
				if (!rThirdPartySkuStocks.isSuccess()) {
					log.error("fail to find thirdPartySkuStock, cause:{}", rThirdPartySkuStocks.getError());
					throw new JsonResponseException(rThirdPartySkuStocks.getError());
				}
				RichThirdPartySkuStock richThirdPartySkuStock = new RichThirdPartySkuStock();

				richThirdPartySkuStock.setThirdPartyId(thirdPartySkuShop.getThirdPartyId());
				richThirdPartySkuStock.setThirdPartyName(ThirdPartySystem.fromInt(thirdPartySkuShop.getThirdPartyId()).Name());
				richThirdPartySkuStock.setType(thirdPartySkuShop.getType());
				if (ThirdPartySystem.fromInt(thirdPartySkuShop.getThirdPartyId()) == ThirdPartySystem.Y800_V2) {
					// 对邮顺同进行改名
					if (Optional.ofNullable(host).orElse("").endsWith("youshuntong.cn")) {
						richThirdPartySkuStock.setThirdPartyName("邮顺通");
					}
				}

				richThirdPartySkuStock.setOuterSkuId(thirdPartySkuShop.getOuterSkuId());
				richThirdPartySkuStock.setOuterSkuName(thirdPartySkuShop.getOuterSkuName());
				richThirdPartySkuStock.setId(thirdPartySkuShop.getThirdPartyId() + thirdPartySkuShop.getOuterSkuId());

				int day = calculateDate(rThirdPartySkuStocks.getResult().isEmpty() ? new Date() : rThirdPartySkuStocks.getResult().get(0).getEffectPeriod(), new Date());
				int minDate = day;
				int maxDate = day;
				Integer totalAuthenticStock = 0;
				Integer totalDefectiveStock = 0;

				Map<String, Integer> flag = new HashMap<>(8);
				List<DepotStock> depotStocks = new ArrayList<>();
				for (ThirdPartySkuStock thirdPartySkuStock : rThirdPartySkuStocks.getResult()) {
					Integer n = flag.get(thirdPartySkuStock.getDepotCode());
					if (n == null) {
						DepotStock depotStock = new DepotStock();
						depotStock.setDepotCode(thirdPartySkuStock.getDepotCode());
						depotStock.setId(depotStock.getDepotCode());
						depotStock.setDepotName(thirdPartySkuStock.getDepotName());

						List<BatchStock> batchStocks = new ArrayList<>();
						BatchStock batchStock = new BatchStock();
						BeanUtils.copyProperties(thirdPartySkuShop, batchStock);
						Optional.of(thirdPartySkuStock).map(ThirdPartySkuStock::getEffectPeriod).map(Date::getTime)
								.ifPresent(batchStock::setEffectPeriod);
						day = calculateDate(thirdPartySkuStock.getEffectPeriod(), new Date());
						if (day < minDate) {
							minDate = day;
						}
						if (day > maxDate) {
							maxDate = day;
						}
						totalAuthenticStock += thirdPartySkuStock.getAuthenticStock();
						totalDefectiveStock += thirdPartySkuStock.getDefectiveStock();
						batchStocks.add(batchStock);

						depotStock.setBatchStocks(batchStocks);
						depotStocks.add(depotStock);
						flag.put(depotStock.getDepotCode(), depotStocks.size() - 1);
					} else {
						DepotStock depotStock = depotStocks.get(n);

						List<BatchStock> batchStocks = depotStock.getBatchStocks();
						BatchStock batchStock = new BatchStock();
						BeanUtils.copyProperties(thirdPartySkuShop, batchStock);
						Optional.of(thirdPartySkuStock).map(ThirdPartySkuStock::getEffectPeriod).map(Date::getTime)
								.ifPresent(batchStock::setEffectPeriod);
						day = calculateDate(thirdPartySkuStock.getEffectPeriod(), new Date());
						if (day < minDate) {
							minDate = day;
						}
						if (day > maxDate) {
							maxDate = day;
						}
						totalAuthenticStock += thirdPartySkuStock.getAuthenticStock();
						totalDefectiveStock += thirdPartySkuStock.getDefectiveStock();
						batchStocks.add(batchStock);

						depotStock.setBatchStocks(batchStocks);
					}
				}
				richThirdPartySkuStock.setHasChildren(!depotStocks.isEmpty());
				richThirdPartySkuStock.setDepotStocks(depotStocks);

				if (maxDate < 0) {
					richThirdPartySkuStock.setEffectPeriodWarning("已过期");
				} else {
					richThirdPartySkuStock.setEffectPeriodWarning(minDate + "天-" + maxDate + "天");
				}
				richThirdPartySkuStock.setTotalAuthenticStock(totalAuthenticStock);
				richThirdPartySkuStock.setTotalDefectiveStock(totalDefectiveStock);

				richThirdPartySkuStock.setStatus(false);
				StringBuilder itemIds = new StringBuilder();
				Response<List<Sku>> rSkuList = skuReadService.findSkuByOuterSkuId(commonUser.getShopId(), thirdPartySkuShop.getOuterSkuId());
				if (!rSkuList.isSuccess()) {
					log.error("fail to find sku, cause:{}", rSkuList.getError());
					throw new JsonResponseException(rSkuList.getError());
				}
				for (Sku sku : rSkuList.getResult()) {
					Map<String, String> tags = sku.getTags();
					if (tags == null) {
						continue;
					}
					String pushSystemStr = tags.get("pushSystem");
					if (pushSystemStr == null) {
						continue;
					}
					List<String> pushSystem = Splitter.on(",").splitToList(pushSystemStr);
					if (!pushSystem.isEmpty()) {
						richThirdPartySkuStock.setStatus(true);
						itemIds.append(sku.getItemId()).append(",");
					}
				}
				if (itemIds.length() > 0) {
					itemIds = new StringBuilder(itemIds.substring(0, itemIds.length() - 1));
				}
				richThirdPartySkuStock.setItemIds(itemIds.toString());

				richThirdPartySkuStocks.add(richThirdPartySkuStock);
			}
			result.setData(richThirdPartySkuStocks);
			return Response.ok(result);
		} catch (Exception e) {
			log.error("failed to paging stock, shopId={}, thirdPartyId={}, cause:", commonUser.getShopId(), thirdPartyId, e);
			throw new JsonResponseException("thirdParty.sku.stock.paging.fail");
		}
	}

	/**
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/stock/paging/new", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public Result<DataPage<RichThirdPartySkuStock>> pagingThirdPartySkuNew(@RequestHeader(value = "host", required = false) String host, @RequestParam(defaultValue = "0") Integer thirdPartyId,
	                                                                       @RequestParam(required = false) String depotName,
	                                                                       @RequestParam(required = false) String outerSkuId,
	                                                                       @RequestParam(required = false) String outerSkuName,
	                                                                       @RequestParam(required = false) Integer pageNo,
	                                                                       @RequestParam(required = false) Integer pageSize,
	                                                                       @RequestParam(required = false) Long shopId) {

		Paging<RichThirdPartySkuStock> result = pagingThirdPartySku(host, thirdPartyId, depotName, outerSkuId, outerSkuName,
				pageNo, pageSize, shopId, null).getResult();

		//获取分页信息
		TotalCalculation totalCalculation = TotalCalculation.build(pageSize, pageNo, result.getTotal());
		return Result.data(DataPage.build(totalCalculation, result.getData()));
	}

	@RequestMapping("/item-query")
	public Response<List<ThirdPartySkuStock>> itemQuery(Integer thirdPartyId, String outerSkuId) {
		return thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(UserUtil.getCurrentShopId(), thirdPartyId, outerSkuId);
	}

	/**
	 * 计算两天间的天数（date1-date2）
	 */
	private Integer calculateDate(Date date1, Date date2) {
		if (Objects.isNull(date1) || Objects.isNull(date2)) {
			return 0;
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		Date date3 = calendar.getTime();

		calendar.setTime(date2);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		Date date4 = calendar.getTime();

		long day = (date3.getTime() - date4.getTime()) / (1000 * 3600 * 24);
		return (int) day;
	}

}
