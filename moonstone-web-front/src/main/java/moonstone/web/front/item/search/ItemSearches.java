/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.item.search;

import io.terminus.common.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.api.APIResp;
import moonstone.common.api.Result;
import moonstone.common.utils.APIRespWrapper;
import moonstone.common.utils.EmptyUtils;
import moonstone.search.dto.*;
import moonstone.search.item.ItemSearchReadService;
import moonstone.shop.model.Shop;
import moonstone.web.core.item.app.ItemSearchLimitByShopIdApp;
import moonstone.web.front.item.search.vo.ItemSearchInShopParam;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@ConditionalOnProperty(value = "enable.item.search", havingValue = "true", matchIfMissing = true)
@RestController
@Slf4j
public class ItemSearches {

    @Autowired
    private ItemSearchReadService itemSearchReadService;
    @Autowired
    private ItemSearchLimitByShopIdApp itemSearchLimitByShopIdApp;

    @Autowired
    private ShopCacheHolder shopCacheHolder;

    /**
     * 搜索商品, 并且包括属性导航, 面包屑等(主搜)
     *
     * @param pageNo   起始页码
     * @param pageSize 每页记录条数
     * @param params   搜索上下文
     * @return 搜索结果, 包括属性导航, 面包屑等
     */
    @RequestMapping(value = "/api/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends SearchedItemWithAggs<SearchedItem>> searchItemWithAggs(@RequestParam(required = false) Integer pageNo,
                                                                                     @RequestParam(required = false) Integer pageSize,
                                                                                     @RequestParam Map<String, String> params,
                                                                                     @RequestHeader(required = false) String host,
                                                                                     @CookieValue(required = false, name = "origin-domain") String cookieHost,
                                                                                     HttpServletRequest request) {
        if (host != null) {
            params.put("host", host);
        }
        if (cookieHost != null) {
            params.put("host", cookieHost);
        }
        params = itemSearchLimitByShopIdApp.limitSearchParam(params);
        String templateName = "search.mustache";
        return itemSearchReadService.searchWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);
    }

    @RequestMapping(value = "/api/search-in-shop", produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends SearchedItemInShopWithAggs<SearchedItem>> searchItemInShopWithAggs(@RequestParam(required = false) Integer pageNo,
                                                                                                 @RequestParam(required = false) Integer pageSize,
                                                                                                 @RequestParam Map<String, String> params,
                                                                                                 @RequestHeader(required = false) String host) {
        if (host != null) {
            params.put("host", host);
        }
        params = itemSearchLimitByShopIdApp.limitSearchParam(params);
        String shopId = params.get("shopId");
        if (!StringUtils.hasText(shopId)) {
            log.error("shop id is required when search in shop");
            return Response.fail("shop.id.not.found");
        }
        // 默认不显示积分商品
        String type = params.get("type");
        if (ObjectUtils.isEmpty(type)) {
            params.put("type", "1");
        }

        //TODO 检查店铺是否存在和是否冻结?
        String templateName = "search.mustache";
        return itemSearchReadService.searchInShopWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);
    }

    /**
     * 店铺内搜索
     *
     * @param searchInShopParam   搜索上
     * @return 搜索结果, 包括属性导航, 面包屑等
     */
    @PostMapping(value = "/api/search-in-shop/v2", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<SearchedItemInShopWithAggs<SearchedItem>> searchItemInShopWithAggs(@RequestBody ItemSearchInShopParam searchInShopParam) {
        String host = searchInShopParam.getHost();
        if (searchInShopParam.getParams() == null) {
            searchInShopParam.setParams(new HashMap<>());
        }
        Map<String, String> params = searchInShopParam.getParams();
        Integer pageNo = searchInShopParam.getPageNo();
        Integer pageSize = searchInShopParam.getPageSize();
        if (host != null) {
            params.put("host", host);
        }
        params = itemSearchLimitByShopIdApp.limitSearchParam(params);
        String shopId = params.get("shopId");
        if (!StringUtils.hasText(shopId)) {
            log.error("shop id is required when search in shop");
            return Result.fail("shop.id.not.found");
        }
        // 默认不显示积分商品
        String type = params.get("type");
        if (ObjectUtils.isEmpty(type)) {
            params.put("type", "1");
        }

        //TODO 检查店铺是否存在和是否冻结?
        String templateName = "search.mustache";
        Response<? extends SearchedItemInShopWithAggs<SearchedItem>> response = itemSearchReadService.searchInShopWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);
        return Result.data(response.getResult());
    }

    /**
     * 店铺内搜索积分商品
     *
     * @param pageNo   起始页码
     * @param pageSize 每页记录条数
     * @param params   搜索上下文
     * @return 搜索结果, 包括属性导航, 面包屑等
     */
    @RequestMapping(value = "/api/search-gift-in-shop", produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends SearchedItemInShopWithAggs<SearchedItem>> searchGiftItemInShopWithAggs(@RequestParam(required = false) Integer pageNo,
                                                                                                     @RequestParam(required = false) Integer pageSize,
                                                                                                     @RequestParam Map<String, String> params) {
        String shopId = params.get("shopId");
        if (!StringUtils.hasText(shopId)) {
            log.error("shop id is required when search in shop");
            return Response.fail("shop.id.not.found");
        }
        // 默认积分商品
        params.put("types", "2_3_4");

        String templateName = "search.mustache";
        return itemSearchReadService.searchInShopWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);
    }


    /**
     * 供销搜索商品
     *
     * @param pageNo   起始页码
     * @param pageSize 每页记录条数
     * @param params   搜索上下文
     * @return 搜索结果, 包括属性导航, 面包屑等
     */
    @RequestMapping(value = "/api/gx/pc/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public APIResp<SearchedItemWithAggsVO> gxSearchItemWithAggs(@RequestParam(defaultValue = "1") Integer pageNo,
                                                                @RequestParam(defaultValue = "20") Integer pageSize,
                                                                @RequestParam Map<String, String> params,
                                                                @RequestHeader(required = false) String host) {
        if (host != null) {
            params.put("host", host);
        }
        params = itemSearchLimitByShopIdApp.limitSearchParam(params);
        List<Shop> shopList = shopCacheHolder.findByIsSupply(1);
        if (EmptyUtils.isEmpty(shopList) || shopList.stream().noneMatch(entity -> Objects.equals(1, entity.getIsSupply()))) {
            return APIRespWrapper.wrap(Response.ok(new SearchedItemWithAggsVO()));
        }
        buildSort(params);
        // 默认不显示积分商品
        String type = params.get("type");
        if (ObjectUtils.isEmpty(type)) {
            params.put("type", "1");
        }
        params.put("shopIds", shopList.stream().filter(entity -> Objects.equals(1, entity.getIsSupply())).
                map(Shop::getId).map(String::valueOf).collect(Collectors.joining("_")));

        String templateName = "search.mustache";
        Response<? extends SearchedItemWithAggs<SearchedItem>> response = itemSearchReadService.searchWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);
        SearchedItemWithAggsVO searchedItemWithAggsVO = new SearchedItemWithAggsVO();
        if (response.isSuccess() && EmptyUtils.isNotEmpty(response.getResult())) {
            searchedItemWithAggsVO.setItems(response.getResult().getEntities().getData());
            searchedItemWithAggsVO.setBrands(response.getResult().getBrands());
            searchedItemWithAggsVO.setOrigin(response.getResult().getOrigin());
            searchedItemWithAggsVO.setChosen(response.getResult().getChosen());
            if (EmptyUtils.isNotEmpty(searchedItemWithAggsVO.getItems())) {
                searchedItemWithAggsVO.getItems().forEach(entity -> entity.setName(removeItemName(entity.getName())));
            }
            Pagination pagination = new Pagination();
            pagination.setTotalCount(response.getResult().getEntities().getTotal() == null ? 0 : response.getResult().getEntities().getTotal().intValue());
            pagination.setCurrPage(pageNo);
            pagination.setPageSize(pageSize);
            pagination.setTotalPage((int) Math.ceil((double) pagination.getTotalCount() / pageSize));
            searchedItemWithAggsVO.setPagination(pagination);
        }
        return APIRespWrapper.wrap(Response.ok(searchedItemWithAggsVO));
    }

    /**
     * build 排序
     *
     */
    private void buildSort(Map<String, String> params) {
        if (EmptyUtils.isEmpty(params)) {
            return;
        }
        String sort = params.getOrDefault("sort", "z");
        String desc = params.getOrDefault("desc", "u");
        params.remove("sort");
        params.remove("desc");
        desc = desc.toUpperCase();
        switch (sort) {
            case "p":
                if (Objects.equals(desc, Desc.UP.name())) {
                    params.put("sort", "1_0_0_0");
                } else {
                    params.put("sort", "2_0_0_0");
                }
                break;
            case "s":
                if (Objects.equals(desc, Desc.UP.name())) {
                    params.put("sort", "0_0_1_0");
                } else {
                    params.put("sort", "0_0_2_0");
                }
                break;
            case "z":
                if (Objects.equals(desc, Desc.UP.name())) {
                    params.put("sort", "0_0_0_0");
                } else {
                    params.put("sort", "0_0_0_0");
                }
                break;
            default:
                break;
        }
    }


    public enum Desc {
        UP("U"),
        DOWN("D");
        public String code;

        Desc(String code) {
        }

        public String getCode() {
            return this.code;
        }
    }

    //过滤所有的搜索<em></em>
    private String removeItemName(String name) {
        return name.replaceAll("<em>", "").replaceAll("</em>", "");
    }


    @Data
    class SearchedItemWithAggsVO {
        /**
         * 搜索结果
         */
        private List<SearchedItem> items;

        /**
         * 返回的品牌聚合
         */
        private List<AggNav> brands;

        /**
         * 返回所有来源国信息
         */
        private List<Origin> origin;

        /**
         * 返回所有已选择的品牌及属性
         */
        private List<Chosen> chosen;

        Pagination pagination;
    }

    @Data
    class Pagination {
        //总记录数
        private int totalCount;
        //每页记录数
        private int pageSize;
        //总页数
        private int totalPage;
        //当前页数
        private int currPage;
    }

}
