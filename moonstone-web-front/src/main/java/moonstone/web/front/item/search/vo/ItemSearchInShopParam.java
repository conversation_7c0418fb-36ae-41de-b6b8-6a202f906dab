package moonstone.web.front.item.search.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

import lombok.Data;

/**
 * @Author: yousx
 * @Date: 2025/02/24
 * @Description:
 */
@Data
public class ItemSearchInShopParam implements Serializable {

    @Serial
    private static final long serialVersionUID = -3645587784569759281L;

    private Integer pageNo;

    private Integer pageSize;

    private Map<String, String> params;

    private String host;
}
