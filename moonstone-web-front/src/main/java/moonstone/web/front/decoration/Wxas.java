package moonstone.web.front.decoration;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cart.dto.RichCart;
import moonstone.cart.dto.RichCartItem;
import moonstone.cart.service.CartReadService;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.component.item.component.TaxChecker;
import moonstone.component.membership.component.MembershipPriceChecker;
import moonstone.web.core.constants.ParanaConfig;
import moonstone.web.front.component.cart.CartReader;
import moonstone.web.front.component.promotion.CartPromotionComposer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 购物车相关接口
 */
@Slf4j
@RestController
public class Wxas {

    @Autowired
    private ParanaConfig paranaConfig;

    @Resource
    private CartReadService cartReadService;

    @Resource
    private CartReader cartReader;

    @Resource
    private MembershipPriceChecker membershipPriceChecker;

    @Autowired
    private TaxChecker taxChecker;

    @Autowired
    private CartPromotionComposer cartPromotionComposer;

    @RequestMapping(value = "/api/wxa/share/url", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String shareWxaUrl() {
        String url = paranaConfig.getParanaWxaUrl();
        return url + "/";
    }

    /**
     * 获取当前登录用户某店铺的永久购物车
     *
     * @param shopId 店铺id
     * @return 购物车信息
     */
    @RequestMapping(value = "/api/wxa/user/cart", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<RichCart> findByUserAndShop(Long shopId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null) {
            return Collections.emptyList();
        }
        Response<List<RichCart>> findResp = cartReader.findByUserAndShopId(commonUser, shopId);
        if (!findResp.isSuccess()) {
            log.error("fail to find cart by user:{},cause:{}",
                    commonUser, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        List<RichCart> richCarts = findResp.getResult();

        for (RichCart richCart : richCarts) {
            for (RichCartItem richCartItem : richCart.getCartItems()) {
                membershipPriceChecker.check(richCartItem.getSku(), commonUser.getId());
                try {
                    Long tax = taxChecker.getTax(richCartItem.getSku(), 1);
                    richCartItem.setTax(tax);
                } catch (Exception e) {
                    log.error("FAIL TO CALCULATE THE TAX by {}", richCartItem.getSku(), e);
                }
            }
        }

        cartPromotionComposer.composePromotions(commonUser, richCarts);
        cartPromotionComposer.appendFlag(richCarts, shopId);
        return richCarts;
    }

    /**
     * 返回当前登录用户购物车内某店铺的商品总数
     *
     * @param shopId 店铺id
     * @return 购物车内商品总数
     */
    @ResponseBody
    @RequestMapping(value = "/api/wxa/carts/count", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Integer count(Long shopId) {
        if (UserUtil.getCurrentUser() == null) {
            return 0;
        }

        Response<Integer> getCount = cartReadService.count(UserUtil.getUserId(), shopId);
        if (!getCount.isSuccess()) {
            log.error("fail to get cart item quantity count for user:{}, cause:{}",
                    UserUtil.getUserId(), getCount.getError());
            throw new JsonResponseException(getCount.getError());
        }
        return getCount.getResult();
    }
}
