package moonstone.web.front.itemBrand.dto;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.BaseQuery;
import moonstone.common.model.dto.PageDto;

/**
 * @Author: yousx
 * @Date: 2025/02/21
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ItemBrandReqDto extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 3318512935221339254L;

    private String name;

    private Long id;

    private Integer current;

    private Integer size;

    private Long shopId;
}
