package moonstone.web.front.sales;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.common.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.constants.JacksonType;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.utils.Json;
import moonstone.order.enu.OrderRoleSnapshotExtra;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.model.OrderRoleSnapshot;
import moonstone.order.service.OrderRoleSnapshotReadService;
import moonstone.order.service.OrderRoleSnapshotWriteService;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

//@Profile({"dev", "test","pre"})
@Slf4j
@RestController
public class OrderRoleSnapshotTestController {

    @Resource
    private OrderRoleSnapshotReadService orderRoleSnapshotReadService;

    @Resource
    private OrderRoleSnapshotWriteService orderRoleSnapshotWriteService;

    @Resource
    private SubStoreCache subStoreCache;

    @Resource
    private ServiceProviderCache serviceProviderCache;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    /**
     * 初始化快照表中新增的字段
     *
     * @return
     */
    @PostMapping("/api/OrderRoleSnapshot/initOldSnapshotNewFields")
    public APIResp<Boolean> initOldSnapshotField(@RequestBody InitRequest param) {
        try {
            for (int pageSize = 500, currentPage = 1; ; currentPage++) {
                var page = orderRoleSnapshotReadService.findAllByPage(param.maxId(), currentPage, pageSize);
                if (!page.isSuccess()) {
                    throw new RuntimeException(page.getError());
                }
                if (CollectionUtils.isEmpty(page.getResult())) {
                    break;
                }

                page.getResult().forEach(snapshot -> {
                    try {
                        initNewField(snapshot);
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }
                });
            }

            return APIResp.ok(true);
        } catch (Exception ex) {
            log.error("OrderRoleSnapshotTestController.initOldSnapshotField failed ", ex);
            return APIResp.error(ex.getMessage());
        }
    }

    private void initNewField(OrderRoleSnapshot snapshot) throws JsonProcessingException {
        if (snapshot == null) {
            return;
        }
        if (StringUtils.hasText(snapshot.getProvince()) && StringUtils.hasText(snapshot.getCity()) &&
                StringUtils.hasText(snapshot.getCounty())) {
            return;
        }

        var role = SubStoreUserIdentityEnum.parse(snapshot.getUserRole());
        if (role == null) {
            return;
        }
        switch (role) {
            case STORE_GUIDER -> {
            }
            case SUB_STORE -> {
                updateBySubStore(snapshot);
            }
            case SERVICE_PROVIDER -> {
                updateByServiceProvider(snapshot);
            }
        }
    }

    private void updateByServiceProvider(OrderRoleSnapshot snapshot) {
        var serviceProvider = serviceProviderCache.findServiceProviderByUserIdAndShopId(
                snapshot.getUserId(), snapshot.getShopId());
        if (serviceProvider == null) {
            return;
        }

        OrderRoleSnapshot updateObject = new OrderRoleSnapshot();
        updateObject.setId(snapshot.getId());
        updateObject.setProvince(serviceProvider.getProvince());
        updateObject.setCity(serviceProvider.getCity());
        updateObject.setCounty(serviceProvider.getCounty());

        orderRoleSnapshotWriteService.update(updateObject);
    }

    private void updateBySubStore(OrderRoleSnapshot snapshot) throws JsonProcessingException {
        var subStore = subStoreCache.findByShopIdAndUserId(snapshot.getShopId(), snapshot.getUserId()).orElse(null);
        if (subStore == null) {
            return;
        }

        OrderRoleSnapshot updateObject = new OrderRoleSnapshot();
        updateObject.setId(snapshot.getId());
        updateObject.setProvince(subStore.getProvince());
        updateObject.setCity(subStore.getCity());
        updateObject.setCounty(subStore.getCounty());

        var serviceProviderSnapshot = orderRoleSnapshotReadService.findByShopOrderIdAndRole(snapshot.getShopOrderId(),
                OrderRoleSnapshotOrderTypeEnum.parse(snapshot.getOrderType()), SubStoreUserIdentityEnum.SERVICE_PROVIDER).getResult();
        if (serviceProviderSnapshot != null) {
            Map<String, String> map = null;
            if (StringUtils.hasText(snapshot.getExtraJson())) {
                map = objectMapper.readValue(snapshot.getExtraJson(), JacksonType.MAP_OF_STRING);
            }
            if (map == null) {
                map = new HashMap<>();
            }

            map.put(OrderRoleSnapshotExtra.serviceProviderName.name(), serviceProviderSnapshot.getName());
            map.put(OrderRoleSnapshotExtra.serviceProviderProvince.name(), StringUtils.hasText(serviceProviderSnapshot.getProvince()) ?
                    serviceProviderSnapshot.getProvince() : findServiceProviderProvice(serviceProviderSnapshot));
            snapshot.setExtraJson(Json.toJson(map));
        }

        orderRoleSnapshotWriteService.update(updateObject);
    }

    private String findServiceProviderProvice(OrderRoleSnapshot serviceProviderSnapshot) {
        var serviceProvider = serviceProviderCache.findServiceProviderByUserIdAndShopId(
                serviceProviderSnapshot.getUserId(), serviceProviderSnapshot.getShopId());
        if (serviceProvider == null) {
            return "";
        }

        return serviceProvider.getProvince();
    }

    public record InitRequest(Long maxId) {
    }
}
