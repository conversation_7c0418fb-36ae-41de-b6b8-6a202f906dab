package moonstone.web.front.open.app;

import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import io.terminus.pampas.openplatform.annotations.OpenBean;
import io.terminus.pampas.openplatform.annotations.OpenMethod;
import io.terminus.pampas.openplatform.exceptions.OPClientException;
import io.terminus.pampas.openplatform.exceptions.OPServerException;
import lombok.extern.slf4j.Slf4j;
import moonstone.cart.dto.RichCart;
import moonstone.cart.service.CartWriteService;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.web.front.component.cart.CartReader;
import moonstone.web.front.component.promotion.CartPromotionComposer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Objects;

/**
 * Author:cp
 * Created on 8/14/16.
 */
@OpenBean
@Slf4j
public class CartApi {

    @Autowired
    private CartReader cartReader;

    @RpcConsumer
    private SkuReadService skuReadService;

    @RpcConsumer
    private CartWriteService cartWriteService;

    @Autowired
    private CartPromotionComposer cartPromotionComposer;

    @OpenMethod(key = "cart.list", httpMethods = RequestMethod.GET)
    public List<RichCart> findByUser() {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null) {
            throw new OPClientException(401,"user.not.login");
        }
        Response<List<RichCart>> findResp = cartReader.findByUser(commonUser);
        if (!findResp.isSuccess()) {
            log.error("fail to find cart by user:{},cause:{}",
                    commonUser, findResp.getError());
            throw new OPServerException(findResp.getError());
        }

        List<RichCart> richCarts = findResp.getResult();
        cartPromotionComposer.composePromotions(commonUser, richCarts);
        return richCarts;
    }

    @OpenMethod(key = "cart.change", paramNames = {"skuId", "quantity"}, httpMethods = RequestMethod.POST)
    public Integer changeCart(Long skuId, Integer quantity) {
        final Long buyerId = UserUtil.getUserId();
        if (buyerId == null) {
            throw new OPClientException(401,"user.not.login");
        }

        quantity = MoreObjects.firstNonNull(quantity, 1);

        Response<Sku> findSku = skuReadService.findSkuById(skuId);
        if (!findSku.isSuccess()) {
            log.error("fail to find sku by id={},cause:{}",
                    skuId, findSku.getError());
            throw new OPServerException(findSku.getError());
        }

        // 检查商品是否上架
        Sku sku = findSku.getResult();
        if (!Objects.equals(sku.getStatus(), 1)) {
            log.error("sku(id={}) not available", skuId);
            throw new OPServerException("sku.not.available");
        }

        // 检查库存
        if (MoreObjects.firstNonNull(sku.getStockQuantity(), 0) - quantity < 0) {
            log.error("sku(id={}) stock not enough,expected:{},but actual:{}",
                    quantity, sku.getStockQuantity());
            throw new OPServerException("sku.stock.not.enough");
        }

        // 更改购物车
        Response<Integer> tryChange = cartWriteService.changeCart(sku, quantity, buyerId,"");
        if (!tryChange.isSuccess()) {
            log.error("fail to change cart by skuId={}, quantity={}, userId={}, error code:{}",
                    skuId, quantity, buyerId, tryChange.getError());
            throw new OPServerException(tryChange.getError());
        }
        return tryChange.getResult();
    }

    @OpenMethod(key = "cart.batch.delete", paramNames = {"skuIds"}, httpMethods = RequestMethod.POST)
    public Boolean batchDelete(String skuIds) {
        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new OPClientException(401,"user.not.login");
        }

        List<Long> skuIdList;
        try {
            skuIdList = Splitters.splitToLong(skuIds, Splitters.COMMA);
        } catch (Exception e) {
            log.error("parse skuIds({}) fail,cause:{}",
                    skuIds, Throwables.getStackTraceAsString(e));
            throw new OPClientException(400, "sku.ids.illegal.format");
        }

        Response<Boolean> tryDelete = cartWriteService.batchDelete(skuIdList, userId);
        if (!tryDelete.isSuccess()) {
            log.error("fail to batch delete cart,skuIds={}, userId={},error code={}",
                    skuIds, userId, tryDelete.getError());
            throw new OPServerException(tryDelete.getError());
        }
        return tryDelete.getResult();
    }

}
