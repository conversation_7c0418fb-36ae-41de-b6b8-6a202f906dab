package moonstone.web.front.open.thirdParty.callback;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.OutSystemIdProvider;
import moonstone.order.dto.fsm.OrderExceptionEnum;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.enu.*;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.*;
import moonstone.showcase.mq.message.OrderShippedMessage;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.model.ThirdSystemAID;
import moonstone.web.core.component.cache.ThirdPartyUserShopCache;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.front.open.constant.ThirdPartyExpress;
import moonstone.web.front.open.dto.StorageShipmentCallBack;
import moonstone.web.front.order.component.ShipmentComponent;
import moonstone.web.front.order.service.impl.ShipmentConfirmService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 发货单处理服务
 */
@Component
public class Y800StorageDeliveryCallBackImpl extends Y800CallBackService<StorageShipmentCallBack> {

    @Autowired
    private OutSystemIdProvider outSystemIdProvider;
    @Resource
    private ShopOrderReadService shopOrderReadService;
    @Resource
    private OrderWriteService orderWriteService;
    @Autowired
    private ThirdPartyUserShopCache thirdPartyUserShopCache;
    @Autowired
    private ThirdPartyExpress thirdPartyExpress;
    @Autowired
    private ShipmentComponent shipmentComponent;
    @Resource
    private PaymentWriteService paymentWriteService;
    @Resource
    private PaymentReadService paymentReadService;
    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private SkuOrderWriteService skuOrderWriteService;

    @Resource
    private ShopOrderWriteService shopOrderWriteService;
    @Autowired
    private ShipmentConfirmService shipmentConfirmService;

    @Resource
    private RocketMQProducer rocketMQProducer;

    private String convert(String expName) {
        return thirdPartyExpress.getY800().get(expName);
    }

    @Override
    public String getServiceName() {
        return "delivery.confirm";
    }

    @Override
    public Object handle(StorageShipmentCallBack shipmentCallBackResponse) {
        log.info("[{}]Y800StorageDeliveryCallBackImpl.handle StorageShipmentCallBack={}", getServiceName(), JSON.toJSONString(shipmentCallBackResponse));

        Long shopOrderId = outSystemIdProvider.decode(shipmentCallBackResponse.getThirdNo()).getId();
        ShopOrder shopOrder = shopOrderReadService.findById(shopOrderId).getResult();
        thirdPartyUserShopCache.findBy(new ThirdSystemAID(ThirdPartySystem.Y800_V3.Id(), shopOrder.getShopId()))
                .map(ThirdPartyUserShop::getThirdPartyKey)
                .ifPresent(key -> checkTheSign(getRequest(), key));

        if (Y800V3DeliveryConfirmCallbackTypeEnum.DELIVERY.getCode().equals(shipmentCallBackResponse.getType())) {
            //正常发货
            if (shopOrder.getStatus() == OrderStatus.SHIPPED.getValue() || shopOrder.getStatus() == OrderStatus.CONFIRMED.getValue()) {
                log.warn("{} shopOrder [{}] already shipped", LogUtil.getClassMethodName(), shopOrderId);
                return "订单已经发货,请勿重复发货";
            }

            Objects.requireNonNull(shipmentComponent.createShipmentCallBack(shopOrder, convert(shipmentCallBackResponse.getDeliveryInfo().getLogisticCode()),
                    shipmentCallBackResponse.getDeliveryInfo().getExpNo(), shipmentCallBackResponse.getDeliveryInfo().getLogisticCode()));

            // 更新京东云交易单号
            String source = shipmentCallBackResponse.getDeliveryInfo().getSource();
            String depotOrderNo = shipmentCallBackResponse.getDeliveryInfo().getDepotOrderNo();
            this.thirdPartyProcess(source, depotOrderNo, shopOrderId, shopOrder);

            // 删除推送错误信息
            shopOrderWriteService.deletePushError(shopOrder);
            orderWriteService.updateOrderPushException(shopOrderId, OrderExceptionEnum.CLEAR_ERROR.getCode(), OrderExceptionEnum.CLEAR_ERROR.getMessage());
            //更新对应的支付单推送状态为已推送
            //updatePayment(shopOrder, PaymentPushStatus.PUSH_SUCCESS, null);

            OrderShippedMessage shippedMessage = new OrderShippedMessage();
            shippedMessage.setShopOrderId(shopOrderId);
            shippedMessage.setDeliveryTime(System.currentTimeMillis());//实际发货时间
            SendResult sendResult = rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_SHIPPED_TAG, com.alibaba.fastjson.JSONObject.toJSONString(shippedMessage));
            if(sendResult.getSendStatus() == SendStatus.SEND_OK){
                log.info("订单状态 发货消息 发送成功 {} {}", shopOrderId, sendResult.getMsgId());
            }else{
                log.error("订单状态 发货消息 发送失败");
            }
            return true;
        } else if (Y800V3DeliveryConfirmCallbackTypeEnum.LOGISTICS.getCode().equals(shipmentCallBackResponse.getType())){
            List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderId(shopOrderId).getResult();
            Set<Integer> shippingWarehouseType = skuOrderList.stream().map(SkuOrder::getShippingWarehouseType).collect(Collectors.toSet());
            if (shippingWarehouseType.contains(SkuOrderShippingWarehouseTypeEnum.JD_CLOUD_TRANSACTION.getValue())) {
                // 实际发货仓＝京东仓
                StorageShipmentCallBack.LogisticsInfo signInfo = shipmentCallBackResponse.getSignInfo();
                if (Objects.equals(shopOrder.getStatus() , OrderStatus.CONFIRMED.getValue())) {
                    log.warn("{} shopOrder [{}] already CONFIRMED", LogUtil.getClassMethodName(), shopOrderId);
                    return true;
                }
                if (signInfo != null && Objects.equals(signInfo.getNode(), "SING")) {
                    // 妥投接收
                    shipmentConfirmService.confirmOrder(shopOrderId);
                }
            }
            return true;
        } else {
            //发生错误
            //记录错误信息到shopOrder的extra字段中
            recordError(shopOrder, shipmentCallBackResponse);

            //更新payment的推送状态
            //updatePaymentPushStatus(shopOrder, shipmentCallBackResponse);

            return true;
        }
    }

    private void thirdPartyProcess(String source, String depotOrderNo, Long shopOrderId, ShopOrder shopOrder) {
        Integer skuOrderShippingWarehouseType = null;
        if (Objects.equals("1", source)) {
            skuOrderShippingWarehouseType = SkuOrderShippingWarehouseTypeEnum.DAITA_SELF.getValue();
        } else if (Objects.equals("2", source)) {
            skuOrderShippingWarehouseType = SkuOrderShippingWarehouseTypeEnum.JD_CLOUD_TRANSACTION.getValue();
            Payment payment = findPayment(shopOrder);
            if (Objects.nonNull(payment)) {
                Payment updateObject = new Payment();
                updateObject.setId(payment.getId());
                updateObject.setThirdPartyTransactionNo(depotOrderNo);
                updateObject.setThirdPartyTransactionNoSource(PaymentThirdPartyTransactionSourceEnum.JD_CLOUD_TRANSACTION.getCode());
                paymentWriteService.update(updateObject);
            }
        }
        if (skuOrderShippingWarehouseType != null) {
            skuOrderWriteService.updateShippingWarehouseTypeByOrderId(shopOrderId, skuOrderShippingWarehouseType, Boolean.TRUE);
        }
    }

    private void updatePaymentPushStatus(ShopOrder shopOrder, StorageShipmentCallBack shipmentCallBackResponse) {
        var errorCode = StringUtils.trim(shipmentCallBackResponse.getType());
        var errorMessage = Y800V3DeliveryConfirmCallbackTypeEnum.parseDescription(errorCode);

        if (Y800V3DeliveryConfirmCallbackTypeEnum.PAYER_WRONG.getCode().equals(errorCode)) {
            //如果是支付人信息错误，则可让用户自行修正
            updatePayment(shopOrder, PaymentPushStatus.DECLARED_FAIL_WAIT_PUSH_RETRY, errorMessage);
        } else {
            updatePayment(shopOrder, PaymentPushStatus.DECLARE_FAIL, errorMessage);
        }
    }

    /**
     * 更新支付单的pushStatus
     *
     * @param shopOrder
     */
    private void updatePayment(ShopOrder shopOrder, PaymentPushStatus pushStatus, String errorMessage) {
        if (shopOrder == null) {
            return;
        }

        Payment payment = findPayment(shopOrder);
        if (payment == null) {
            return;
        }

        Payment updateObject = new Payment();

        updateObject.setId(payment.getId());
        updateObject.setPushStatus(pushStatus.getValue());
        if (StringUtils.isNotBlank(errorMessage)) {
            var extra = payment.getExtra();
            if (CollectionUtils.isEmpty(extra)) {
                extra = new HashMap<>();
            }
            extra.put(PaymentExtraIndexEnum.customsDeclareErrorMsg.getCode(), errorMessage);

            updateObject.setExtra(extra);
        }

        paymentWriteService.update(updateObject);
    }

    /**
     * 找到对应的支付单 payment
     *
     * @param shopOrder
     * @return
     */
    private Payment findPayment(ShopOrder shopOrder) {
        var payments = paymentReadService.findByOrderIdAndOrderLevel(shopOrder.getId(), OrderLevel.SHOP).getResult();
        if (!CollectionUtils.isEmpty(payments)) {
            return payments.stream()
                    .filter(payment -> payment.getStatus() != null && OrderStatus.PAID.getValue() == payment.getStatus())
                    .findAny()
                    .orElse(null);
        }

        var skuOrders = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
        if (CollectionUtils.isEmpty(skuOrders)) {
            return null;
        }

        payments = paymentReadService.findByOrderIdAndOrderLevel(skuOrders.get(0).getId(), OrderLevel.SKU).getResult();
        if (CollectionUtils.isEmpty(payments)) {
            return null;
        }

        return payments.stream()
                .filter(payment -> payment.getStatus() != null && OrderStatus.PAID.getValue() == payment.getStatus())
                .findAny()
                .orElse(null);
    }

    /**
     * 记录错误信息到shopOrder的extra字段中
     *
     * @param shopOrder
     * @param shipmentCallBackResponse
     */
    private void recordError(ShopOrder shopOrder, StorageShipmentCallBack shipmentCallBackResponse) {
        //上游给下来错误码，带有后缀空格。。。坑
        String errorCode = StringUtils.trim(shipmentCallBackResponse.getType());

        Map<String, String> orderExtra = Optional.ofNullable(shopOrder.getExtra()).orElseGet(HashMap::new);
        orderExtra.put(ShopOrderExtra.orderPushErrorType.name(), errorCode);
        orderExtra.remove(ShopOrderExtra.identityError.name());

        var pushError = Y800V3DeliveryConfirmCallbackTypeEnum.parse(errorCode);
        if (pushError != null) {
            String errorMessage = pushError.getErrorMessage();
            Pair<String, String> errorPair = null;
            switch (pushError) {
                case PAYER_WRONG ->
                        orderExtra.put(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.TRUE.getCode());
                case CUSTOMS_FAIL -> errorPair = getCustomsFailMessage(shipmentCallBackResponse);
                case ORDER_EXCEPTION -> errorPair = getOrderExceptionMessage(shipmentCallBackResponse);
            }

            if (errorPair == null) {
                orderExtra.put(ShopOrderExtra.orderPushErrorMessage.name(), errorMessage);
            } else {
                orderExtra.put(ShopOrderExtra.orderPushErrorDetailType.name(), errorPair.getLeft());
                orderExtra.put(ShopOrderExtra.orderPushErrorMessage.name(), errorPair.getRight());
            }
            JSONObject msg = new JSONObject();
            msg.set("orderId", shopOrder.getId());
            switch (pushError) {
                case PAYER_WRONG -> {
                    msg.set("exceptionType", OrderExceptionEnum.PAYEE_INFO_ERROR.getCode());
                    msg.set("exceptionMessage", Y800V3DeliveryConfirmCallbackTypeEnum.PAYER_WRONG.getErrorMessage());
                }
                case CUSTOMS_FAIL ->{
                    msg.set("exceptionType", OrderExceptionEnum.CLEARANCE_FAILED.getCode());
                    msg.set("exceptionMessage", Y800V3DeliveryConfirmCallbackTypeEnum.CUSTOMS_FAIL.getErrorMessage());
                }
                case ORDER_EXPRESS_NOT_SEND ->{
                    msg.set("exceptionType", OrderExceptionEnum.EXPRESS_UNREACHABLE.getCode());
                    msg.set("exceptionMessage", Y800V3DeliveryConfirmCallbackTypeEnum.ORDER_EXPRESS_NOT_SEND.getErrorMessage());
                }
                case ORDER_EXCEPTION -> {
                    msg.set("exceptionType", OrderExceptionEnum.FEN_YIN_EXCEPTION.getCode());
                    String message = "";
                    if (errorPair != null) {
                        message = errorPair.getRight();
                    }
                    msg.set("exceptionMessage", message);
                }
                case PAYER_LIMIT -> {
                    msg.set("exceptionType", OrderExceptionEnum.OVER_YEAR.getCode());
                    msg.set("exceptionMessage", Y800V3DeliveryConfirmCallbackTypeEnum.PAYER_LIMIT.getErrorMessage());
                }
                default -> {
                    msg.set("exceptionType", OrderExceptionEnum.SYSTEM_ERROR.getCode());
                    msg.set("exceptionMessage", "发货单回调异常：" + errorMessage);
                    orderWriteService.updateOrderPushException(shopOrder.getId(), OrderExceptionEnum.SYSTEM_ERROR.getCode(), "系统异常");
                }
            }
            rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_EXCEPTION_RECORD_TAG, msg.toString());
            log.info("发送发货单回调，订单异常消息 {}", msg);
        }
        orderWriteService.updateOrderExtra(shopOrder.getId(), OrderLevel.SHOP, orderExtra);
        log.warn("Y800StorageDeliveryCallBackImpl.recordError, orderNo={}, originType={}, trimmedType={}",
                shipmentCallBackResponse.getThirdNo(), shipmentCallBackResponse.getType(), errorCode);
    }

    private Pair<String, String> getCustomsFailMessage(StorageShipmentCallBack callBack) {
        if (callBack.getCustomsInfo() == null) {
            return null;
        }

        String detailMessage = callBack.getCustomsInfo().getDetail() + "。请取消订单";

        return Pair.of(callBack.getCustomsInfo().getStatus(), detailMessage);
    }

    private Pair<String, String> getOrderExceptionMessage(StorageShipmentCallBack callBack) {
        if (callBack.getExceptionInfo() == null) {
            return null;
        }

        String message = callBack.getExceptionInfo().getExceptionDetail() + "。请耐心等待，商家将会在符合清关规则后重新推送";

        return Pair.of(callBack.getExceptionInfo().getExceptionCode(), message);
    }
}
