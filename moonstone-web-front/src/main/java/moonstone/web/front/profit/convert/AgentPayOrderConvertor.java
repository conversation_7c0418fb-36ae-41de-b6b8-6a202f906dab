package moonstone.web.front.profit.convert;

import moonstone.common.enums.DataValidEnum;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.utils.DateUtil;
import moonstone.order.enu.AgentPayOrderReceiverTypeEnum;
import moonstone.order.enu.AgentPayOrderStatusEnum;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.model.*;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.OrderRoleSnapshotReadService;
import moonstone.user.model.User;
import moonstone.user.service.UserBindAllinPayService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.component.pay.allinpayyst.AllInPayYSTToken;
import moonstone.web.core.component.pay.allinpayyst.AllInPayYSTTokenProvider;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
public class AgentPayOrderConvertor {

    @Resource
    private AllInPayYSTTokenProvider allInPayYSTTokenProvider;

    @Resource
    private OrderRoleSnapshotReadService orderRoleSnapshotReadService;

    @Resource
    private UserReadService<User> userReadService;

    @Resource
    private BalanceDetailReadService balanceDetailReadService;

    @Resource
    private UserBindAllinPayService userBindAllinPayService;

    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    public Pair<AgentPayOrder, List<AgentPayOrderDetail>> convert(ShopOrder shopOrder, Payment payment) {
        if (!PaymentChannelEnum.ALLINPAY_YST.getCode().equals(payment.getChannel())) {
            throw new RuntimeException("非通商云搞什么鸡脖");
        }

        // 获取支付token
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(shopOrder.getId()); //TODO 多支付,需要订单id
        var payToken = allInPayYSTTokenProvider.findToken(shopOrder.getShopId().toString());

        // 代付订单
        AgentPayOrder agentPayOrder = convertAgentPayOrder(shopOrder, payment);

        // 代付订单明细
        // 门店小c
        var subStore = convertSubStore(shopOrder);
        // 小B
        var shopUser = convertShopUser(shopOrder, payToken, subStore);
        // 大B
        var enterpriseUser = convertEnterpriseUser(shopOrder, payToken, subStore, shopUser);

        return Pair.of(agentPayOrder, List.of(subStore, shopUser, enterpriseUser));
    }

    private AgentPayOrder convertAgentPayOrder(ShopOrder shopOrder, Payment payment) {
        var target = new AgentPayOrder();

        target.setAmount(shopOrder.getFee());
        target.setCreatedBy(0L);
        target.setIsValid(DataValidEnum.VALID.getCode());
        target.setUpdatedBy(0L);
        target.setOrderNo(buildAgentPayOrderNo(shopOrder, AgentPayOrderReceiverTypeEnum.SUB_STORE));

        target.setPayChannel(payment.getChannel());
        target.setRelatedOrderId(shopOrder.getId());
        target.setRelatedPaymentId(payment.getId());
        target.setShopId(shopOrder.getShopId());
        target.setStatus(AgentPayOrderStatusEnum.NOT_PAID.getCode());

        return target;
    }

    private AgentPayOrderDetail convertEnterpriseUser(ShopOrder shopOrder, AllInPayYSTToken payToken,
                                                      AgentPayOrderDetail subStore, AgentPayOrderDetail shopUser) {
        var target = new AgentPayOrderDetail();

        target.setAmount(shopOrder.getFee() - subStore.getAmount() - shopUser.getAmount());
        target.setCreatedBy(0L);
        target.setIsValid(DataValidEnum.VALID.getCode());
        target.setUpdatedBy(0L);

        target.setReceiverId(payToken.getEnterpriseUserId());
        target.setReceiverType(AgentPayOrderReceiverTypeEnum.ENTERPRISE.getCode());
        target.setShopId(shopOrder.getShopId());

        return target;
    }

    private AgentPayOrderDetail convertShopUser(ShopOrder shopOrder, AllInPayYSTToken payToken, AgentPayOrderDetail subStore) {
        var target = new AgentPayOrderDetail();

        target.setAmount((shopOrder.getFee() - subStore.getAmount()) * payToken.getBizUserPercent() / 10000);
        target.setCreatedBy(0L);
        target.setIsValid(DataValidEnum.VALID.getCode());
        target.setUpdatedBy(0L);

        target.setReceiverId(payToken.getBizUserId());
        target.setReceiverType(AgentPayOrderReceiverTypeEnum.ENTERPRISE_USER.getCode());
        target.setShopId(shopOrder.getShopId());

        return target;
    }

    private AgentPayOrderDetail convertSubStore(ShopOrder shopOrder) {
        var target = new AgentPayOrderDetail();

        var user = findSubStoreUser(shopOrder.getId());
        var profit = findSubStoreProfit(shopOrder, user.getId());

        target.setAmount(profit.getChangeFee());
        target.setCreatedBy(0L);
        target.setIsValid(DataValidEnum.VALID.getCode());
        target.setUpdatedBy(0L);

        var userAllInPay = userBindAllinPayService.getActiveByShopIdAndUserId(shopOrder.getShopId(), user.getId());
        if (userAllInPay == null || StringUtils.isBlank(userAllInPay.getBizUserId())) {
            throw new RuntimeException("门店用户尚未注册云商通");
        }
        target.setReceiverId(userAllInPay.getBizUserId());
        target.setReceiverType(AgentPayOrderReceiverTypeEnum.SUB_STORE.getCode());
        target.setShopId(shopOrder.getShopId());

        return target;
    }

    private String buildAgentPayOrderNo(ShopOrder shopOrder, AgentPayOrderReceiverTypeEnum typeEnum) {
        return "APO" + shopOrder.getId() + DateUtil.toString(new Date(), DateUtil.YYYY_MM_DD_HH_MM_SS_SSS) + typeEnum.getCode();
    }

    private BalanceDetail findSubStoreProfit(ShopOrder shopOrder, Long subStoreUserId) {
        Map<Long, List<BalanceDetail>> balanceDetailMap = balanceDetailReadService.getBalanceDetailMap(
                List.of(shopOrder.getId()), shopOrder.getShopId());
        if (CollectionUtils.isEmpty(balanceDetailMap)) {
            throw new RuntimeException("利润列表查询为空");
        }

        return balanceDetailMap.values().stream()
                .flatMap(Collection::stream)
                .filter(entity -> subStoreUserId.equals(entity.getUserId()))
                .findAny()
                .orElseThrow();
    }

    private User findSubStoreUser(Long shopOrderId) {
        var snapshot = orderRoleSnapshotReadService.findByShopOrderIdAndRole(shopOrderId,
                OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER, SubStoreUserIdentityEnum.SUB_STORE).getResult();
        if (snapshot == null) {
            throw new RuntimeException("订单快照查询为空");
        }

        var user = userReadService.findById(snapshot.getUserId()).getResult();
        if (user == null) {
            throw new RuntimeException("门店用户数据查询为空");
        }

        return user;
    }
}
