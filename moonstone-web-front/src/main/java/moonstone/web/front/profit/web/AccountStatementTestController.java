package moonstone.web.front.profit.web;


import io.vertx.core.json.Json;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.utils.DateUtil;
import moonstone.shop.service.ShopReadService;
import moonstone.web.front.profit.application.AccountStatementAutoCreateApp;
import moonstone.web.front.profit.application.ProfitManualAdjustApp;
import moonstone.web.front.profit.job.AccountStatementAutoCreateJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试用 controller
 */
//@Profile({"dev", "test","pre"})  //上线时需要指定时间范围生成过往数据的账单，后续再加从线上拿掉这个功能
@Slf4j
@RestController
public class AccountStatementTestController {

    @Resource
    private AccountStatementAutoCreateJob accountStatementAutoCreateJob;

    @Resource
    private AccountStatementAutoCreateApp accountStatementAutoCreateApp;

    @Resource
    private ShopReadService shopReadService;

    @Resource
    private ProfitManualAdjustApp profitManualAdjustApp;

    /**
     * 触发定时任务
     *
     * @return
     */
    @PostMapping("/api/accountStatementTest/triggerAutoCreateJob")
    public APIResp<Void> triggerAutoCreateJob(@RequestBody(required = false) AccountStatementTriggerDTO request) {
        if (request == null || request.shopId() == null ||
                StringUtils.isBlank(request.startTime()) || StringUtils.isBlank(request.endTime())) {
            accountStatementAutoCreateJob.autoCreateAccountStatement();
        } else {
            accountStatementAutoCreateApp.create(shopReadService.findById(request.shopId()).getResult(),
                    DateUtil.parseDate(request.startTime()), DateUtil.parseDate(request.endTime()));
        }

        return APIResp.ok(null);
    }

    @PostMapping("/api/accountStatementTest/deleteDuplicateProfitData")
    public APIResp<List<Long>> deleteDuplicateProfitData(@RequestBody DeleteDuplicateProfitDataRequest request) {
        try {
            if (request == null || CollectionUtils.isEmpty(request.duplicateProfitIds())) {
                return APIResp.error("入参缺失");
            }

            List<Long> errorList = new ArrayList<>();
            for (Long profitId : request.duplicateProfitIds()) {
                try {
                    profitManualAdjustApp.deletePresentProfit(profitId);
                } catch (Exception ex) {
                    log.error("AccountStatementTestController.deleteDuplicateProfitData error, profitId={}", profitId, ex);
                    errorList.add(profitId);
                }
            }

            return APIResp.ok(errorList);
        } catch (Exception ex) {
            log.error("AccountStatementTestController.deleteDuplicateProfitData, reuqest={}", Json.encode(request), ex);
            return APIResp.error(ex.getMessage());
        }
    }

    /**
     * 重复的可提现利润已被提现，为后续订单利顺利流转，给预收入钱包补尝多提的金额
     *
     * @param request
     * @return
     */
    @PostMapping("/api/accountStatementTest/preIncomeCompensation")
    public APIResp<List<Long>> preIncomeCompensation(@RequestBody DeleteDuplicateProfitDataRequest request) {
        try {
            if (request == null || CollectionUtils.isEmpty(request.duplicateProfitIds())) {
                return APIResp.error("入参缺失");
            }

            List<Long> errorList = new ArrayList<>();
            for (Long profitId : request.duplicateProfitIds()) {
                try {
                    profitManualAdjustApp.preIncomeCompensation(profitId);
                } catch (Exception ex) {
                    log.error("AccountStatementTestController.deleteDuplicateProfitData error, profitId={}", profitId, ex);
                    errorList.add(profitId);
                }
            }

            return APIResp.ok(errorList);
        } catch (Exception ex) {
            log.error("AccountStatementTestController.preIncomeCompensation error, reuqest={}", Json.encode(request), ex);
            return APIResp.error(ex.getMessage());
        }
    }

    public record AccountStatementTriggerDTO(Long shopId, String startTime, String endTime) {
    }

    public record DeleteDuplicateProfitDataRequest(List<Long> duplicateProfitIds) {
    }
}
