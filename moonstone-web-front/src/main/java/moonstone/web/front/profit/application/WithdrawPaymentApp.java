package moonstone.web.front.profit.application;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.model.CommonUser;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.enu.WithdrawPaymentStatusEnum;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.shop.enums.ShopPayInfoUsageChannelEnum;
import moonstone.web.core.shop.application.ShopPayInfoComponent;
import moonstone.web.front.profit.application.bo.*;
import moonstone.web.front.profit.convert.WithdrawPaymentConvertor;
import moonstone.web.front.profit.domain.withdraw.WithdrawPaymentPayTool;
import moonstone.web.front.shop.substore.component.SubStoreUserIdentityComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * 提现申请-线上支付转账 相关服务
 */
@Slf4j
@Service
public class WithdrawPaymentApp {

    @Resource
    private SubStoreUserIdentityComponent subStoreUserIdentityComponent;

    @Resource
    private ShopWithdrawPayInfoWriteService shopWithdrawPayInfoWriteService;

    @Resource
    private ShopWithdrawPayInfoReadService shopWithdrawPayInfoReadService;

    @Resource
    private WithdrawPaymentReadService withdrawPaymentReadService;

    @Resource
    private WithdrawPaymentWriteService withdrawPaymentWriteService;

    @Resource
    private WithdrawPaymentReceiverReadService withdrawPaymentReceiverReadService;

    @Resource
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;

    @Resource
    private WithdrawAccountReadService withdrawAccountReadService;

    @Autowired
    private WithdrawPaymentPayTool withdrawPaymentPayTool;

    @Autowired
    private WithdrawSellerOperatorHelper withdrawSellerOperatorHelper;

    @Autowired
    private WithdrawPaymentApp self;

    @Resource
    private ShopPayInfoComponent shopPayInfoComponent;

    /**
     * 提现-线上支付，当前支持的支付渠道
     */
    private static List<String> SUPPORTED_PAY_CHANNEL = Lists.newArrayList(PaymentChannelEnum.UMF.getCode());


    /**
     * 更新支付证书文件路径
     *
     * @param shopId
     * @param filePath
     * @return
     */
    public Boolean updateCertFilePath(Long shopId, String filePath) {
        var shopWithdrawPayInfo = shopWithdrawPayInfoReadService.findByShopId(shopId).getResult();
        if (shopWithdrawPayInfo == null) {
            throw new RuntimeException("商家的支付信息查询失败");
        }

        return shopWithdrawPayInfoWriteService.updateCertFilePath(shopWithdrawPayInfo.getId(), filePath).getResult();
    }

    /**
     * 提现申请单，发起线上支付
     *
     * @param operatorUserId  操作人id
     * @param withdrawApplyId 提现申请单id
     * @return
     */
    public WithdrawPayOnlineResultBO payOnline(Long withdrawApplyId, Long operatorUserId) {
        // 查询提现申请相关的信息
        var applyInfo = findWithdrawApply(withdrawApplyId);

        // 生成提现支付单
        var withdrawPayment = createWithdrawPayment(applyInfo, operatorUserId);

        // 发起支付请求
        withdrawPaymentPayTool.pay(withdrawPayment);

        // 根据支付请求结果进行
        updateAfterPay(applyInfo.getApplyOrder(), withdrawPayment.getWithdrawPayment(), withdrawPayment.getPayErrorMessage());

        // 返回
        return buildResult(withdrawPayment);
    }

    /**
     * 线上支付结果回调处理
     *
     * @param withdrawPaymentId
     * @param httpServletRequest
     */
    public WithdrawPaymentCallbackResultBO payCallback(Long withdrawPaymentId, HttpServletRequest httpServletRequest) throws Exception {
        // 查询支付单信息
        var withdrawPayment = findWithdrawPayment(withdrawPaymentId);

        // 是否支付成功
        var callbackResultBO = withdrawPaymentPayTool.parsePayResult(
                new WithdrawPaymentCallbackBO(withdrawPayment, httpServletRequest));

        // 处理回调结果
        self.handleCallbackResult(withdrawPayment, callbackResultBO);

        return callbackResultBO;
    }

    /**
     * 处理完支付回调后，应答调用方
     *
     * @param response
     * @param result
     */
    public void answerCaller(HttpServletResponse response, WithdrawPaymentCallbackResultBO result) throws IOException {
        withdrawPaymentPayTool.answerCaller(response, result);
    }

    /**
     * 处理回调结果
     *
     * @param paymentBO
     * @param callbackResultBO
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleCallbackResult(WithdrawPaymentBO paymentBO, WithdrawPaymentCallbackResultBO callbackResultBO) {
        if (noNeedCallbackUpdate(paymentBO.getWithdrawPayment())) {
            return;
        }

        // 更新支付单的状态
        updatePayment(paymentBO.getWithdrawPayment(), callbackResultBO.getPaySuccess(), callbackResultBO.getParsedCallBackString());

        // 更新提现申请单
        updateWithdrawApply(paymentBO.getWithdrawPayment(), callbackResultBO.getPaySuccess(), callbackResultBO.getErrorMessage());
    }

    /**
     * 如果
     *
     * @param withdrawPayment
     * @return
     */
    private boolean noNeedCallbackUpdate(WithdrawPayment withdrawPayment) {
        var list = Lists.newArrayList(WithdrawPaymentStatusEnum.PAID.getCode(), WithdrawPaymentStatusEnum.FAILURE.getCode());
        if (list.contains(withdrawPayment.getStatus())) {
            log.warn("WithdrawPaymentApp.noNeedCallbackUpdate, withdrawApplyId={}, withdrawPayment={}, status={}, 支付单已经处理终结状态，不再更新相关数据",
                    withdrawPayment.getWithdrawApplyId(), withdrawPayment.getId(), withdrawPayment.getStatus());
            return true;
        }

        return false;
    }

    /**
     * 更新提现申请单
     *
     * @param withdrawPayment
     * @param paySuccess
     * @param errorMessage
     */
    private void updateWithdrawApply(WithdrawPayment withdrawPayment, Boolean paySuccess, String errorMessage) {
        // 提现申请单id
        Long applyId = withdrawPayment.getWithdrawApplyId();

        // 操作人
        CommonUser operator = new CommonUser();
        operator.setId(withdrawPayment.getCreatedBy());
        operator.setShopId(withdrawPayment.getShopId());

        // 提现完成 或 提现失败
        if (paySuccess) {
            withdrawSellerOperatorHelper.finishWithdraw(operator, applyId,
                    WithDrawProfitApply.WithdrawPaidType.OTHER.getType(), withdrawPayment.getTradeNo());
        } else {
            //如果失败，回写失败原因，不操作其他状态
            withdrawSellerOperatorHelper.writeReason(operator, applyId, errorMessage);
        }
    }

    /**
     * 更新支付单的状态
     *
     * @param withdrawPayment
     * @param paySuccess
     */
    private void updatePayment(WithdrawPayment withdrawPayment, Boolean paySuccess, String payCallBackString) {
        WithdrawPayment updatedObject = new WithdrawPayment();
        updatedObject.setId(withdrawPayment.getId());
        updatedObject.setPayCallback(payCallBackString);
        updatedObject.setStatus(paySuccess ?
                WithdrawPaymentStatusEnum.PAID.getCode() : WithdrawPaymentStatusEnum.FAILURE.getCode());

        var updated = withdrawPaymentWriteService.update(updatedObject);
        if (updated == null || !updated.isSuccess() || !updated.getResult()) {
            throw new RuntimeException("支付单更新失败");
        }
    }

    private WithdrawPaymentBO findWithdrawPayment(Long withdrawPaymentId) {
        var payment = withdrawPaymentReadService.findById(withdrawPaymentId).getResult();
        if (payment == null) {
            throw new RuntimeException(String.format("id=%s的支付付单信息不存在", withdrawPaymentId));
        }

        var shopPayInfo = shopWithdrawPayInfoReadService.findByShopId(payment.getShopId()).getResult();
        if (shopPayInfo == null) {
            log.warn(String.format("shopId=%s的提现支付配置信息不存在", payment.getShopId()));
        }

        return new WithdrawPaymentBO(payment, shopPayInfo);
    }

    /**
     * 根据支付请求结果进行
     *
     * @param applyOrder
     * @param withdrawPayment
     */
    private void updateAfterPay(WithDrawProfitApply applyOrder, WithdrawPayment withdrawPayment, String errorMessage) {
        // 待更新的支付单
        var paymentUpdate = buildUpdatePayment(withdrawPayment);

        // 待更新的提现申请单
        var applyUpdate = buildUpdateApply(applyOrder, withdrawPayment, errorMessage);

        // 更新
        var result = withdrawPaymentWriteService.update(applyUpdate, paymentUpdate);
        if (result == null || !result.getResult()) {
            throw new RuntimeException("提现申请与支付单更新失败");
        }
    }

    /**
     * 构造待更新的支付单
     *
     * @param withdrawPayment
     * @return
     */
    private WithdrawPayment buildUpdatePayment(WithdrawPayment withdrawPayment) {
        var paymentUpdate = new WithdrawPayment();

        paymentUpdate.setId(withdrawPayment.getId());
        paymentUpdate.setPayRequest(withdrawPayment.getPayRequest());
        paymentUpdate.setPayResponse(withdrawPayment.getPayResponse());
        paymentUpdate.setStatus(withdrawPayment.getStatus());
        paymentUpdate.setTradeNo(withdrawPayment.getTradeNo());

        return paymentUpdate;
    }

    /**
     * 构造待更新的提现申请单
     *
     * @param applyOrder
     * @param withdrawPayment
     * @param errorMessage
     * @return
     */
    private WithDrawProfitApply buildUpdateApply(WithDrawProfitApply applyOrder, WithdrawPayment withdrawPayment, String errorMessage) {
        var applyUpdate = new WithDrawProfitApply();
        applyUpdate.setId(applyOrder.getId());

        if (WithdrawPaymentStatusEnum.PAY_IN_PROGRESS.getCode().equals(withdrawPayment.getStatus())) {
            applyUpdate.setStatus((applyOrder.getStatus() | WithDrawProfitApply.WithdrawExtraStatus.PAYING.getMaskBit()));
        } else {
            var extra = applyOrder.getExtra();
            if (extra == null) {
                extra = new HashMap<>();
            }
            extra.put(WithDrawProfitApply.REASON, errorMessage);
            applyUpdate.setExtra(extra);
            applyUpdate.setStatus(null);
        }

        return applyUpdate;
    }

    /**
     * 查询提现申请相关的信息
     *
     * @param withdrawApplyId
     * @return
     */
    private WithdrawApplyBO findWithdrawApply(Long withdrawApplyId) {
        // 提现申请单
        var withDrawProfitApply = withDrawProfitApplyReadService.findById(withdrawApplyId).getResult();
        checkForPay(withDrawProfitApply);

        // 提现申请人的账户
        var receiverAccount = withdrawAccountReadService.findById(withDrawProfitApply.getWithdrawAccountId())
                .getResult().orElse(null);
        checkForPay(receiverAccount);

        // 对应商家的支付配置
        var shopPayInfo = shopWithdrawPayInfoReadService.findByShopId(withDrawProfitApply.getSourceId()).getResult();
        // checkForPay(shopPayInfo);

        // 提现申请人的角色
        SubStoreUserIdentityEnum userRole = determineUserRole(withDrawProfitApply);

        // 返回
        return new WithdrawApplyBO(withDrawProfitApply, receiverAccount, shopPayInfo, userRole);
    }

    /**
     * 判断提现用户所属角色
     *
     * @param withDrawProfitApply
     * @return
     */
    private SubStoreUserIdentityEnum determineUserRole(WithDrawProfitApply withDrawProfitApply) {
        var role = subStoreUserIdentityComponent.findUserIdentity(withDrawProfitApply.getUserId(),
                withDrawProfitApply.getSourceId(), withDrawProfitApply.getId(), OrderRoleSnapshotOrderTypeEnum.WITHDRAW_APPLY);
        if (role == null) {
            throw new RuntimeException("无法判断提现用户所属角色");
        }

        return role;
    }

    /**
     * 生成提现支付单
     *
     * @param applyBO
     * @param operatorUserId
     * @return
     */
    private WithdrawPaymentBO createWithdrawPayment(WithdrawApplyBO applyBO, Long operatorUserId) {
        // 查询出当前已存在的，非终结状态的支付单信息
        var existedPayment = findExisted(applyBO.getApplyOrder().getId());
        if (existedPayment != null) {
            existedPayment.setShopWithdrawPayInfo(applyBO.getPayInfo());
            existedPayment.setUserRole(applyBO.getUserRole());
            return existedPayment;
        }

        var channel = shopPayInfoComponent.getPaymentChannel(applyBO.getApplyOrder().getSourceId(),
                ShopPayInfoUsageChannelEnum.WECHAT_APP, applyBO.getApplyOrder().getUserId());
//        //TODO 是否这样修改
//        var channel = shopPayInfoComponent.getPaymentChannelNew(applyBO.getApplyOrder().getId(), applyBO.getApplyOrder().getUserId());

        //构造
        WithdrawPayment withdrawPayment = WithdrawPaymentConvertor.convert(channel, applyBO.getPayInfo(), applyBO.getApplyOrder(),
                operatorUserId);
        WithdrawPaymentReceiver receiver = WithdrawPaymentConvertor.convert(applyBO.getAccount(), operatorUserId);

        //插入
        var result = withdrawPaymentWriteService.create(withdrawPayment, receiver);
        if (result == null || !result.getResult()) {
            throw new RuntimeException("支付单信息保存失败");
        }

        return new WithdrawPaymentBO(withdrawPayment, receiver, applyBO.getPayInfo(), applyBO.getUserRole());
    }

    /**
     * 查询出当前已存在的，非终结状态的支付单信息
     *
     * @param withdrawApplyId 提现申请单id
     * @return
     */
    private WithdrawPaymentBO findExisted(Long withdrawApplyId) {
        var list = withdrawPaymentReadService.findByWithdrawApplyId(withdrawApplyId).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (list.stream().anyMatch(entity -> WithdrawPaymentStatusEnum.PAID.getCode().equals(entity.getStatus()))) {
            throw new RuntimeException("当前提现申请单已支付成功");
        }

        // 找出未支付/支付中的
        List<Integer> inProgress = Lists.newArrayList(WithdrawPaymentStatusEnum.NOT_PAID.getCode(),
                WithdrawPaymentStatusEnum.PAY_IN_PROGRESS.getCode());
        var payment = list.stream().filter(entity -> inProgress.contains(entity.getStatus())).findAny().orElse(null);
        if (payment == null) {
            return null;
        }

        // 收款方信息
        var receiver = withdrawPaymentReceiverReadService.findByWithdrawPaymentId(
                payment.getId()).getResult();
        if (receiver == null) {
            log.error("WithdrawPaymentId={}, 对应的收款方信息为空！", payment.getId());
            return null;
        }

        return new WithdrawPaymentBO(payment, receiver);
    }

    /**
     * 线上支付提现单的校验
     *
     * @param shopPayInfo
     */
    private void checkForPay(ShopWithdrawPayInfo shopPayInfo) {
        if (shopPayInfo == null) {
            throw new RuntimeException("商家的提现线上支付配置为空");
        }
        if (StringUtils.isBlank(shopPayInfo.getMerchantId())) {
            throw new RuntimeException("商家的提现线上支付配置的商户号为空");
        }
        if (StringUtils.isBlank(shopPayInfo.getCertFilePath())) {
            throw new RuntimeException("商家的提现线上支付配置的证书路径为空");
        }
        if (!SUPPORTED_PAY_CHANNEL.contains(shopPayInfo.getPayChannel())) {
            throw new RuntimeException(String.format("当前不支持该支付渠道(%s)", shopPayInfo.getPayChannel()));
        }
    }

    /**
     * 线上支付提现单的校验
     *
     * @param receiverAccount
     */
    private void checkForPay(WithdrawAccount receiverAccount) {
        if (receiverAccount == null) {
            throw new RuntimeException("对应的提现账户数据不存在");
        }
        if (StringUtils.isBlank(receiverAccount.getAccount())) {
            throw new RuntimeException("对应的提现账户账号为空");
        }
        if (StringUtils.isBlank(receiverAccount.getRealName())) {
            throw new RuntimeException("对应的提现账户户名为空");
        }
    }

    /**
     * 线上支付提现单的校验
     *
     * @param withDrawProfitApply
     */
    private void checkForPay(WithDrawProfitApply withDrawProfitApply) {
        if (withDrawProfitApply == null) {
            throw new RuntimeException("对应的提现申请单数据不存在");
        }
        if (!withDrawProfitApply.isAuthed()) {
            throw new RuntimeException("对应的提现申请单尚未审核通过");
        }
        if (withDrawProfitApply.isPaid()) {
            throw new RuntimeException("对应的提现申请单已经完成支付");
        }
        if (withDrawProfitApply.isPaying()) {
            throw new RuntimeException("对应的提现申请单正在支付中");
        }
        if (withDrawProfitApply.isClosed() || withDrawProfitApply.isError()) {
            throw new RuntimeException("对应的提现申请单已经关闭");
        }
        if (withDrawProfitApply.getWithdrawAccountId() == null) {
            throw new RuntimeException("对应的提现申请单的提现账户id为空");
        }
    }

    private WithdrawPayOnlineResultBO buildResult(WithdrawPaymentBO withdrawPayment) {
        return new WithdrawPayOnlineResultBO(
                WithdrawPaymentStatusEnum.PAY_IN_PROGRESS.getCode().equals(withdrawPayment.getWithdrawPayment().getStatus()),
                withdrawPayment.getPayErrorMessage());
    }

    /**
     * 错过回调的，通过状态查询来兜底回调
     *
     * @param withdrawPaymentId
     */
    public void callbackByQuery(Long withdrawPaymentId) {
        var withdrawPayment = withdrawPaymentReadService.findById(withdrawPaymentId).getResult();

        var callbackResultBO = withdrawPaymentPayTool.queryPayResult(withdrawPayment);
        var bo = new WithdrawPaymentBO(withdrawPayment, null, null, null);

        // 处理回调结果
        self.handleCallbackResult(bo, callbackResultBO);
    }
}
