/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.category;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCategoryCacher;
import moonstone.category.dto.ShopCategoryWithChildren;
import moonstone.category.model.ShopCategory;
import moonstone.category.model.ShopCategoryItem;
import moonstone.category.service.ShopCategoryItemReadService;
import moonstone.category.service.ShopCategoryItemWriteService;
import moonstone.category.service.ShopCategoryReadService;
import moonstone.category.service.ShopCategoryWriteService;
import moonstone.common.api.Result;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.vo.PageVo;
import moonstone.common.utils.DataPage;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.UserUtil;
import moonstone.item.dto.ItemWithShopCategory;
import moonstone.web.core.events.item.ItemUpdateEvent;
import moonstone.web.front.category.dto.ShopCategoryPageDto;
import moonstone.web.front.component.distribution.WeShopCategoryLogic;
import moonstone.web.front.component.item.ItemShopCategoryService;
import moonstone.web.front.util.ShopCategoryUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 店铺类目相关接口
 */
@RestController
@Slf4j
@RequestMapping("/api/seller/shopCategories")
public class ShopCategories {

    @Autowired
    private ItemShopCategoryService itemShopCategoryService;

    @Autowired
    private ShopCategoryReadService shopCategoryReadService;

    @Autowired
    private ShopCategoryWriteService shopCategoryWriteService;

    @Autowired
    private ShopCategoryItemWriteService shopCategoryItemWriteService;

    @Autowired
    private WeShopCategoryLogic weShopCategoryLogic;

    @Autowired
    ShopCategoryItemReadService shopCategoryItemReadService;

    @Autowired
    private ShopCategoryCacher shopCategoryCacher;


    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<ShopCategory> create(@RequestBody ShopCategory shopCategory) {
        Long shopId = getCurrentShopId();
        shopCategory.setShopId(shopId);
        int length = shopCategory.getName().length();
        if (length > 20) {
            throw new ApiException("类目名称不能超过20个字符");
        }
        Response<List<ShopCategory>> listResponse = shopCategoryReadService.findByShopIds(Collections.singletonList(shopId));
        if (listResponse.isSuccess() && !listResponse.getResult().isEmpty()
                && listResponse.getResult().size() > 15) {
            log.warn("ShopCategory more than 15 shopId(shopId={})", shopId);
            throw new JsonResponseException(500, "店铺类目最多15条");
        }
        Response<ShopCategory> r = shopCategoryWriteService.create(shopCategory);
        if (!r.isSuccess()) {
            log.warn("failed to create {}, error code:{}", shopCategory, r.getError());
        }
        shopCategoryCacher.invalidateByShopId(shopId);
        return r;

    }

    @RequestMapping(value = "/{id}/name", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> update(@PathVariable("id") long id,
                                    @RequestParam("name") String name) {
        final Long shopId = getCurrentShopId();
        Response<Boolean> r = shopCategoryWriteService.updateName(id, shopId, name);
        if (!r.isSuccess()) {
            log.warn("failed to update shop category(id={}) name to {} ,error code:{}", id, name, r.getError());
        }
        shopCategoryCacher.invalidateByShopId(shopId);
        return r;

    }

    private Long getCurrentShopId() {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            throw new JsonResponseException(401, "user.not.login");
        }
        Long shopId = user.getShopId();
        if (shopId == null) {
            log.warn("no shop found for user(id={})", user.getId());
            throw new JsonResponseException(403, "user.no.permission");
        }
        return shopId;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> delete(@PathVariable("id") Long id) {
        Long shopId = getCurrentShopId();

        Response<List<ShopCategoryItem>> listResponse = shopCategoryItemReadService.findByShopIdsAndShopCategoryId(Collections.singletonList(shopId), id);
        if (listResponse.isSuccess() && !listResponse.getResult().isEmpty()) {
            log.warn("remove item shopId:{} shopCategoryId:{}", shopId, id);
            throw new JsonResponseException(500, "分类下有商品,请先移除商品,再删除");
        }

        Response<Boolean> r = shopCategoryWriteService.delete(id, shopId);
        if (!r.isSuccess()) {
            log.warn("failed to delete shop category(id={}) of shop(id={}) ,error code:{}",
                    id, shopId, r.getError());
        }
        shopCategoryCacher.invalidateByShopId(shopId);
        return r;
    }

    /**
     * 根据父级id查询子级小程序类目
     * @param id 父级id
     * @return 子级小程序类目
     */
    @RequestMapping(value = "/{id}/children", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<List<ShopCategory>> findChildrenById(@PathVariable Long id) {
        Long shopId = getCurrentShopId();
        var resp = shopCategoryReadService.findChildrenByShopIdAndPid(shopId, id);
        if (!resp.isSuccess()) {
            log.warn("find children by pid failed, pid={}, shopId={}, error={}", id, shopId, resp.getError());
        }
        return resp;
    }

    @RequestMapping(value = "/tree", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ShopCategoryWithChildren> findEntireTree() {
        Long shopId = getCurrentShopId();
        return findEntireTree(shopId);
    }

    private List<ShopCategoryWithChildren> findEntireTree(Long shopId) {
        var findTreeResp = shopCategoryReadService.findEntireTreeByShopId(shopId);
        if (!findTreeResp.isSuccess()) {
            log.warn("find entire tree by shopId={} failed, error={}", shopId, findTreeResp.getError());
            throw new JsonResponseException(500, findTreeResp.getError());
        }
        return findTreeResp.getResult();
    }

    @RequestMapping(value = "/we/tree", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ShopCategoryWithChildren> buildTreeByWeShopId(Long weShopId, Long shopId) {
        return weShopCategoryLogic.findShopCategoryTreeByWeShopIdAndShopId(weShopId, shopId);
    }

    @RequestMapping(value = "/{shopCategoryId}/bind", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ShopCategory> bind(@PathVariable Long shopCategoryId, @RequestParam("itemIds") Long[] itemIdArray) {
        if (itemIdArray == null || itemIdArray.length == 0) {
            return Collections.emptyList();
        }
        Long shopId = getCurrentShopId();
        List<Long> itemIds = Lists.newArrayList(Sets.newHashSet(itemIdArray));

        var tree = findEntireTree(shopId);

        List<Long> shopCategoryIds = Lists.newArrayList();
        ShopCategoryUtils.findShopCategoryAncestors(tree, shopCategoryId, shopCategoryIds);

        if (shopCategoryIds.isEmpty()) {
            return Collections.emptyList();
        }

        var resp = shopCategoryItemWriteService.batchCreate(shopId, shopCategoryIds, itemIds);
        if (!resp.isSuccess()) {
            log.error("batch create shopCategoryItems failed, shopId={}, shopCategoryId={}, shopCategoryIds={}, itemIds={}, error={}",
                    shopId, shopCategoryId, shopCategoryIds, itemIds, resp.getError());
            throw new JsonResponseException(500, resp.getError());
        }
        for (Long entity : itemIds) {
            EventSender.publish(new ItemUpdateEvent(entity));
        }

        return buildShopCategoryWithFullName(shopCategoryIds, shopId, tree);
    }

    @RequestMapping(value = "/{shopCategoryId}/unbind", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ShopCategory> unbind(@PathVariable Long shopCategoryId, @RequestParam("itemIds") Long[] itemIdArray) {
        if (itemIdArray == null || itemIdArray.length == 0) {
            return Collections.emptyList();
        }
        Long shopId = getCurrentShopId();
        List<Long> itemIds = Lists.newArrayList(Sets.newHashSet(itemIdArray));

        var tree = findEntireTree(shopId);

        List<Long> shopCategoryIds = Lists.newArrayList();
        ShopCategoryUtils.findShopCategoryDescendants(tree, false, shopCategoryId, shopCategoryIds);

        if (shopCategoryIds.isEmpty()) {
            return Collections.emptyList();
        }

        var resp = shopCategoryItemWriteService.batchDelete(shopId, shopCategoryIds, itemIds);
        if (!resp.isSuccess()) {
            log.error("batch delete shopCategoryItems failed, shopId={}, shopCategoryId={}, shopCategoryIds={}, itemIds={}, error={}",
                    shopId, shopCategoryId, shopCategoryIds, itemIds, resp.getError());
            throw new JsonResponseException(500, resp.getError());
        }
        for (Long entity : itemIds) {
            EventSender.publish(new ItemUpdateEvent(entity));
        }

        return buildShopCategoryWithFullName(shopCategoryIds, shopId, tree);
    }

    @RequestMapping(value = "/{id}/logo", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> updateLogo(@PathVariable("id") long id,
                                        @RequestParam("logo") String logo) {
        Response<Boolean> r = shopCategoryWriteService.updateLogo(id, logo);
        if (!r.isSuccess()) {
            log.warn("failed to update front category(id={}) logo to {} ,error code:{}",
                    id, logo, r.getError());
            throw new JsonResponseException(500, r.getError());
        }
        return r;
    }

    private List<ShopCategory> buildShopCategoryWithFullName(List<Long> shopCategoryIds, Long shopId, List<ShopCategoryWithChildren> tree) {
        List<ShopCategory> rs = Lists.newArrayList();
        for (Long shopCategoryId : shopCategoryIds) {
            ShopCategory sc = ShopCategoryUtils.fixName(tree, shopCategoryId, shopId);
            if (sc != null) {
                rs.add(sc);
            }
        }
        return rs;
    }

    @RequestMapping(value = "/{shopCategoryId}/move-up", method = RequestMethod.PUT)
    public Response<Boolean> moveUp(@PathVariable Long shopCategoryId) {
        Long shopId = getCurrentShopId();
        Response<Boolean> booleanResponse = shopCategoryWriteService.move(shopCategoryId, -1);
        shopCategoryCacher.invalidateByShopId(shopId);
        return booleanResponse;
    }

    @RequestMapping(value = "/{shopCategoryId}/move-down", method = RequestMethod.PUT)
    public Response<Boolean> moveDown(@PathVariable Long shopCategoryId) {
        Long shopId = getCurrentShopId();
        Response<Boolean> booleanResponse = shopCategoryWriteService.move(shopCategoryId, 1);
        shopCategoryCacher.invalidateByShopId(shopId);
        return booleanResponse;
    }

    @RequestMapping(value = "/{shopCategoryId}/disclosed", method = RequestMethod.PUT)
    public Response<Boolean> changeDisclosed(@PathVariable Long shopCategoryId, @RequestParam("value") Boolean disclosed) {
        getCurrentShopId();
        return shopCategoryWriteService.updateDisclosed(shopCategoryId, disclosed);
    }

    @RequestMapping(value = "/{shopCategoryId}/changeVisibility", method = RequestMethod.PUT)
    public Response<Boolean> changeVisibility(@PathVariable Long shopCategoryId, @RequestParam("value") Integer visibility) {
        Long shopId = getCurrentShopId();

        var response = shopCategoryWriteService.changeVisibility(shopCategoryId, visibility);
        if (response != null && response.isSuccess() && response.getResult()) {
            shopCategoryCacher.invalidateByShopId(shopId);
        }

        return response;
    }

    @RequestMapping(value = "/fullTree")
    public Response<List<ShopCategoryWithChildren>> findEntireTreeByShopId(@RequestParam("shopId") Long shopId) {
        return shopCategoryReadService.findEntireTreeByShopId(shopId);
    }

    /**
     * 查询商品小程序类目树
     *
     * @param shopId 店铺id
     * @return 商品小程序类目树
     */
    @RequestMapping(value = "/fullTree/new")
    public Result<List<ShopCategoryWithChildren>> findEntireTreeByShopIdNew(@RequestParam("shopId") Long shopId) {
        return shopCategoryReadService.findEntireTreeByShopIdNew(shopId);
    }

    @RequestMapping(value = "/{shopCategoryId}/items")
    public Response<Paging<ItemWithShopCategory>> findByShopIdAndCategoryId(@PathVariable Long shopCategoryId,
                                                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                            @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) {
        return itemShopCategoryService.findByShopIdAndCategoryId(shopCategoryId, pageNo, pageSize);
    }

    @RequestMapping(value = "/items", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Paging<ItemWithShopCategory>> findByShopIdAndCategoryIdForEdit(@RequestParam(value = "shopCategoryId", required = false) Long shopCategoryId,
                                                                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                   @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) {
        return itemShopCategoryService.findByShopIdAndCategoryId(shopCategoryId, pageNo, pageSize);
    }

    /**
     * 分页查询店铺类目
     * @param req 请求参数
     * @return 类目列表
     */
    @PostMapping(value = "/items")
    public Result<PageVo<ItemWithShopCategory>> findByShopIdAndCategoryIdForEditV2(@RequestBody @Valid ShopCategoryPageDto req) {
        Response<Paging<ItemWithShopCategory>> response = itemShopCategoryService.findByShopIdAndCategoryId(req.getShopCategoryId(), (int) req.getSize(), (int) req.getCurrent());
        Long total = response.getResult().getTotal();
        long pages = total / req.getSize() + (total % req.getSize() > 0 ? 1 : 0);
        return Result.data(PageVo.build(total, req.getSize(), req.getCurrent(), pages, response.getResult().getData()));
    }

    @RequestMapping(value = "/items/new", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<DataPage<ItemWithShopCategory>> findByShopIdAndCategoryIdForEditNew(@RequestParam(value = "shopCategoryId", required = false) Long shopCategoryId,
                                                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                      @RequestParam(name = "pageSize", defaultValue = "20") Integer pageSize) {
        return itemShopCategoryService.findByShopIdAndCategoryIdNew(shopCategoryId, pageNo, pageSize);
    }
}
