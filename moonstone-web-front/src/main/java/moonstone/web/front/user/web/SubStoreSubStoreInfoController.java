package moonstone.web.front.user.web;

import io.terminus.common.exception.JsonResponseException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.model.CommonUser;
import moonstone.common.model.ShopIdAndUserId;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.item.emu.SubStoreExtraIndex;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreWriteService;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.cache.UserProfileCacheHolder;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.service.UserProfileWriteService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.events.item.ShopItemDumpEvent;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.event.SubStoreUpdateEvent;
import moonstone.web.front.shop.substore.component.ServiceProviderAddSubStoreApp;
import moonstone.web.front.shop.substore.dto.SubStoreAddRequest;
import moonstone.web.front.shop.substore.event.SubStoreAuthEvent;
import moonstone.web.front.user.application.UserV2App;
import moonstone.web.front.user.dto.InviteCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

@RestController
@Slf4j
public class SubStoreSubStoreInfoController {

    @Autowired
    GuiderCache guiderCache;
    @Autowired
    ServiceProviderCache serviceProviderCache;

    @Resource
    SubStoreReadService subStoreReadService;
    @Resource
    SubStoreWriteService subStoreWriteService;

    @Resource
    UserProfileCacheHolder userProfileCacheHolder;
    @Resource
    UserProfileWriteService userProfileWriteService;
    @Resource
    UserReadService<User> userReadService;
    @Resource
    UserCacheHolder userCacheHolder;
    @Autowired
    ServiceProviderAddSubStoreApp serviceProviderAddSubStoreApp;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    UserV2App userV2App;

    @GetMapping("/api/v2/subStore/view")
    public APIResp<SubStoreView> view(Long subStoreId) {
        SubStore subStore = subStoreReadService.findById(subStoreId).getResult();
        SubStoreView view = new SubStoreView();
        BeanUtils.copyProperties(subStore, view);
        Long userId = subStore.getUserId();
        view.setAvatar(Optional.ofNullable(userProfileCacheHolder.getUserProfileByUserId(userId))
                .map(UserProfile::getAvatar).orElse(subStore.getLogoUrl()));
        view.setMobile(userCacheHolder.findByUserId(userId).map(User::getMobile).orElse("ERROR"));
        view.setAuthStatus("待审核");
        if (subStore.isReject()) {
            view.setAuthStatus("审核已拒绝");
        }
        if (subStore.isAuthed()) {
            view.setAuthStatus("审核已通过");
        }
        if (subStore.isReject()) {
            view.setReason(subStore.getExtra().get("reason"));
        }
        return APIResp.ok(view);
    }

    /**
     * 填写申请动作
     * 如果门店存在则重新申请, 如果不存在则申请
     *
     * @param subStoreRegisterRequest 申请请求
     * @return 门店Id
     */
    @PostMapping("/api/v2/subStore/register")
    @Transactional(rollbackFor = Exception.class)
    public APIResp<Long> register(@RequestBody SubStoreRegisterRequest subStoreRegisterRequest) {
        // n1：门店注册
        Long operatorId = null;
        Long subStoreId = null;
        Long shopId;
        // login first
        UserV2App.LoginDTO dto = new UserV2App.LoginDTO();
        log.info("Register DTO -> {}", subStoreRegisterRequest);
        BeanUtils.copyProperties(subStoreRegisterRequest, dto);
        Long userId;
        if (subStoreRegisterRequest.code != null) {
            try {
                userId = userV2App.login(dto).getData().getId();
                subStoreRegisterRequest.setMobile(userReadService.findById(userId).getResult().getMobile());
                log.info("User -> {} Mobile -> {}, req -> {}", userId, subStoreRegisterRequest.getMobile(), subStoreRegisterRequest);
            } catch (JsonResponseException ex) {
                return APIResp.error(-3, ex.getMessage());
            } catch (Exception e) {
                log.error("login fail", e);
                return APIResp.error(-3, "微信平台错误, 请重试");
            }
            log.info("Login -> {}", subStoreRegisterRequest);
        } else {
            userId = UserUtil.getUserId();
        }
        if (subStoreRegisterRequest.inviteCode != null || subStoreRegisterRequest.getShopId() == null) {
            try {
                if (subStoreRegisterRequest.inviteCode.startsWith("c:")) {
                    subStoreRegisterRequest.inviteCode = subStoreRegisterRequest.inviteCode.substring(2);
                }
                operatorId = resolveCode(subStoreRegisterRequest.getInviteCode()).getUserId();
                shopId = resolveCode(subStoreRegisterRequest.getInviteCode()).getShopId();
            } catch (Exception e) {
                log.error("Error to resolve the Invitor Code", e);
                return APIResp.error(e.getMessage());
            }
        } else {
            shopId = subStoreRegisterRequest.getShopId();
        }
        if (userId == null) {
            userId = Optional.ofNullable(userReadService.findByMobile(subStoreRegisterRequest.mobile).getResult())
                    .map(User::getId).orElse(null);
        }
        if (userId == null) {
            return APIResp.error("缺少邀请码, 请重新尝试");
        }
        EventSender.publish(new ShopItemDumpEvent(shopId));
        try {
            validRole(userId, shopId);
            if (subStoreReadService.findUserIdAndShopId(userId, shopId).getResult() == null) {
                if (operatorId == null) {
                    log.error("code -> {}", subStoreRegisterRequest.getInviteCode());
                    return APIResp.error("邀请码无效");
                }
                SubStoreAddRequest request = new SubStoreAddRequest();
                BeanUtils.copyProperties(subStoreRegisterRequest, request);
                subStoreId = serviceProviderAddSubStoreApp.addSubStore(shopId, operatorId, request);
            } else {
                SubStore origin = subStoreReadService.findUserIdAndShopId(userId, shopId).getResult();
                if (!origin.isReject()) {
                    return APIResp.error("门店只有被拒绝后才能修改申请信息");
                }
                BeanUtils.copyProperties(subStoreRegisterRequest, origin);
                origin.initAuth();
                subStoreWriteService.update(origin);
                EventSender.publish(new SubStoreUpdateEvent(origin.getId()));
                subStoreId = origin.getId();
            }
            // update the avatar
            UserProfile profile = userProfileCacheHolder.getUserProfileByUserId(userId);
            profile.setAvatar(subStoreRegisterRequest.avatar);
            userProfileWriteService.updateProfile(profile);
            return APIResp.ok(subStoreId);
        } catch (Exception e) {
            log.error("fail to register -> {}", subStoreRegisterRequest, e);
            return APIResp.error("注册失败");
        } finally {
            if (subStoreId != null) {
                EventSender.publish(new SubStoreUpdateEvent(subStoreId));
            }
        }
    }

    private void validRole(Long userId, Long shopId) {
        if (guiderCache.findByShopIdAndUserId(shopId, userId).isPresent()) {
            throw Translate.exceptionOf("该用户已经是导购");
        }
        if (serviceProviderCache.findServiceProviderByShopIdAndUserId(shopId, userId).isPresent()) {
            throw Translate.exceptionOf("该用户已经是服务商");
        }
    }

    /**
     * 解析邀请码
     *
     * @param inviterCode 邀请码
     * @return 店铺和用户Id
     */
    private ShopIdAndUserId resolveCode(String inviterCode) {
        InviteCode code = mongoTemplate.findOne(Query.query(Criteria.where("code").is(inviterCode)), InviteCode.class);
        if (code == null) {
            throw Translate.exceptionOf("邀请码无效");
        }
        log.info("Invite Code -> {}", code);
        return new ShopIdAndUserId(code.getShopId(), code.getUserId());
    }

    /**
     * 审核门店
     *
     * @param action     动作
     * @param subStoreId 门店Id
     * @param reason     原因
     * @return 成功
     */
    @PutMapping("/api/v2/subStore/{subStoreId}/{action}")
    public APIResp<Boolean> authAction(@PathVariable String action, @PathVariable Long subStoreId,
                                       String reason) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.notLogin();
        }
        SubStore subStore = subStoreReadService.findById(subStoreId).getResult();
        if (subStore == null || !Objects.equals(subStore.getShopId(), user.getShopId())) {
            return APIResp.error(Translate.of("该门店不属于你"));
        }
        SubStoreWriteService.Action act;
        try {
            act = SubStoreWriteService.Action.valueOf(action);
        } catch (Exception e) {
            return APIResp.error("未知的动作");
        }
        try {
            return subStoreWriteService.auth(subStoreId, act, reason, user.getId());
        } finally {
            EventSender.publish(new SubStoreUpdateEvent(subStoreId));
            EventSender.sendApplicationEvent(new SubStoreAuthEvent(subStoreId, act == SubStoreWriteService.Action.auth));
        }
    }

    /**
     * 获取目前门店状态
     *
     * @param shopId 店铺Id
     * @return 状态
     */
    @GetMapping("/api/v2/subStore/current")
    public APIResp<SubStoreView> viewCurrentUserSubStore(Long shopId) {
        Long userId = UserUtil.getUserId();
        if (userId == null) {
            return APIResp.notLogin();
        }

        var subStore = subStoreReadService.findUserIdAndShopId(userId, shopId);
        if (StringUtils.isNotBlank(subStore.getError())) {
            return APIResp.error(subStore.getError());
        }
        if (subStore.getResult() == null) {
            return APIResp.error(Translate.of("你没有门店身份"));
        }

        SubStoreView view = new SubStoreView();
        BeanUtils.copyProperties(subStore.getResult(), view);
        view.setAvatar(Optional.ofNullable(userProfileCacheHolder.getUserProfileByUserId(userId))
                .map(UserProfile::getAvatar).orElse(subStore.getResult().getLogoUrl()));
        view.setMobile(userCacheHolder.findByUserId(userId).map(User::getMobile).orElse("ERROR"));
        view.setAuthStatus("待审核");
        if (subStore.getResult().isReject()) {
            view.setAuthStatus("审核已拒绝");
        }
        if (subStore.getResult().isAuthed()) {
            view.setAuthStatus("审核已通过");
        }
        if (subStore.getResult().isReject()) {
            view.setReason(subStore.getResult().getExtra().get(SubStoreExtraIndex.auditingRemark.name()));
        }
        return APIResp.ok(view);
    }

    @Data
    public static class SubStoreView {
        String mobile;
        String avatar;
        String name;
        String province;
        String city;
        String county;
        String address;
        String authStatus;
        String reason;
        // 银行名称
        String bankName;
        // 银行ID
        String bankNo;
        // 法人名称
        String nameInLaw;
        // 食品销售许可证
        String foodSellAllowImg;
        // 身份证图片
        String frontImg = "";
        String backImg = "";
        // 经营许可证
        String businessImg = "";
        Date authAt;
    }

    @Data
    public static class SubStoreRegisterRequest {
        Long shopId;
        Long projectId;
        String encryptedData;
        String iv;
        String code;
        String inviteCode;
        String avatar;
        String mobile;
        String wxCode;
        String name;
        String province;
        String city;
        String county;
        String address;
        String bankName;
        String bankNo;
        String nameInLaw;
        String foodSellAllowImg;
        // 身份证图片
        String frontImg = "";
        String backImg = "";
        // 经营许可证
        String businessImg = "";
    }
}
