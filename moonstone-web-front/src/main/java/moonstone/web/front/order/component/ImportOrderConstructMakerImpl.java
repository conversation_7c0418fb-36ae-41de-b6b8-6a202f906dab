package moonstone.web.front.order.component;

import com.google.common.collect.Lists;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.DistributionRateProvider;
import moonstone.order.dto.*;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.settle.enums.CommissionBusinessType;
import moonstone.settle.model.CommissionRule;
import moonstone.settle.service.CommissionRuleReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.web.front.order.api.ImportOrderConstructMaker;
import moonstone.web.front.order.dto.ImportExcelException;
import moonstone.web.front.order.dto.UnGroupedSubmittedOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ImportOrderConstructMakerImpl implements ImportOrderConstructMaker {

    @Autowired
    private SkuReadService skuReadService;
    @Autowired
    private ItemReadService itemReadService;
    @Autowired
    private ShopReadService shopReadService;
    @Autowired
    private CommissionRuleReadService commissionRuleReadService;
    @Autowired
    private ReceiverInfoReadService receiverInfoReadService;
    @Autowired
    private DistributionRateProvider distributionRateProvider;

    @Override
    public Either<RichOrder> full(UnGroupedSubmittedOrder submittedOrderWithReceiverDTO, CommonUser buyer) {
        // 19-1-21 复制来源于RichOrderMaker,进行稍微修改
        RichOrder richOrder = new RichOrder();
        Response<ReceiverInfo> result = receiverInfoReadService.findById(submittedOrderWithReceiverDTO.getReceiverInfoId());
        if (!result.isSuccess()) {
            log.error("{} receiver found error!cause: {}", LogUtil.getClassMethodName(), result.getError());
            return Either.error(new ImportExcelException(submittedOrderWithReceiverDTO.getLines().get(0), Translate.of("收货人信息获取失败")));
        }
        richOrder.setReceiverInfo(result.getResult());
        richOrder.setBuyer(buyer);
        BeanUtils.copyProperties(submittedOrderWithReceiverDTO, richOrder);
        richOrder.setPayerId(submittedOrderWithReceiverDTO.getPayerNo());
        richOrder.setPayerName(submittedOrderWithReceiverDTO.getPayerName());

        final List<SubmittedSkusByShop> submittedSkusByShops = submittedOrderWithReceiverDTO.getSubmittedSkusByShops();
        if (CollectionUtils.isEmpty(submittedSkusByShops)) {
            log.error("{} no shop orders specified", LogUtil.getClassMethodName());
            Either.error(new ImportExcelException(submittedOrderWithReceiverDTO.getLines().get(0), Translate.of("主订单信息获取失败")));
        }

        List<RichSkusByShop> richSkusByShops = Lists.newArrayListWithExpectedSize(submittedSkusByShops.size());
        for (SubmittedSkusByShop submittedSkusByShop : submittedSkusByShops) {
            RichSkusByShop richSkusByShop = new RichSkusByShop();
            Long shopId = submittedSkusByShop.getShopId();
            Shop shop = findShopById(shopId);
            BeanUtils.copyProperties(submittedSkusByShop, richSkusByShop);
            richSkusByShop.setShop(shop);
            richSkusByShop.setChannel(submittedOrderWithReceiverDTO.getChannel());
            richSkusByShop.setOrderStatus(OrderStatus.NOT_PAID.getValue());
            richSkusByShop.setOrderType(1);
            final Long receiverInfoId = submittedSkusByShop.getReceiverInfoId();
            if (receiverInfoId != null) {
                result = receiverInfoReadService.findById(receiverInfoId);
                if (result.isSuccess()) {
                    richSkusByShop.setReceiverInfo(result.getResult());
                } else {
                    log.error("find receiverInfo By id({}) failed,cause:{}", receiverInfoId, result.getError());
                    //几乎不可能报错 因为这边的信息是全部由excel导入的。一旦导入失败，那么这个流程也不会被触发
                    return Either.error(new ImportExcelException(submittedOrderWithReceiverDTO.getLines().get(0), "fail.find.receiverInfo"));
                }
            }

            Integer commissionRate = findCommissionRateByShopId(shopId);
            richSkusByShop.setCommissionRate(commissionRate);

            List<SubmittedSku> submittedSkus = submittedSkusByShop.getSubmittedSkus();
            if (CollectionUtils.isEmpty(submittedSkus)) {
                log.error("no sku specified for shop(id={})", shopId);
                return Either.error(new ImportExcelException(submittedOrderWithReceiverDTO.getLines().get(0), "shop.sku.empty"));
            }
            List<RichSku> richSkus = Lists.newArrayListWithExpectedSize(submittedSkus.size());
            for (SubmittedSku submittedSku : submittedSkus) {
                RichSku richSku = new RichSku();
                BeanUtils.copyProperties(submittedSku, richSku);

                richSku.setChannel(submittedOrderWithReceiverDTO.getChannel());
                richSku.setOrderStatus(OrderStatus.NOT_PAID.getValue());
                if (submittedSku.getReceiverInfoId() != null) {
                    submittedSku.setReceiverInfoId(submittedSku.getReceiverInfoId());
                }
                final Long skuId = submittedSku.getSkuId();
                Sku sku = findSkuById(skuId);
                if (!Objects.equals(sku.getShopId(), shopId)) {
                    log.error("sku(id={}) not belong to shop(id={})", skuId, shopId);
                    return Either.error(new ImportExcelException(submittedOrderWithReceiverDTO.getLines().get(submittedSkus.indexOf(submittedSku)), "shop.sku.mismatch"));
                }
                richSku.setSku(sku);
                Item item = findItemById(sku.getItemId());
                richSku.setItem(item);
                Integer distributionRateForSkuOrder = distributionRateProvider.retrieveRateForSkuOrder(shop, richSku);
                richSku.setDistributionRate(distributionRateForSkuOrder);
                richSku.setExtra(sku.getExtraMap());
                richSku.setTags(sku.getTags());
                richSkus.add(richSku);
            }
            richSkusByShop.setRichSkus(richSkus);
            Integer distributeRateForShopOrder = distributionRateProvider.retrieveRateForShopOrder(shop, richSkus);
            richSkusByShop.setDistributionRate(distributeRateForShopOrder);
            richSkusByShops.add(richSkusByShop);
        }
        richOrder.setRichSkusByShops(richSkusByShops);
        return Either.ok(richOrder);
    }

    private Sku findSkuById(Long skuId) {
        Response<Sku> rSku = skuReadService.findSkuById(skuId);
        if (!rSku.isSuccess()) {
            log.error("failed to find sku(id={}), error code:{}", skuId, rSku.getError());
            throw new ServiceException(rSku.getError());
        }
        return rSku.getResult();
    }

    private Shop findShopById(Long shopId) {
        Response<Shop> rShop = shopReadService.findById(shopId);
        if (!rShop.isSuccess()) {
            log.error("failed to find shop(id={}), error code:{}", shopId, rShop.getError());
            throw new ServiceException(rShop.getError());
        }
        return rShop.getResult();
    }

    private Item findItemById(Long itemId) {
        Response<Item> rItem = itemReadService.findById(itemId);
        if (!rItem.isSuccess()) {
            log.error("failed to find item(id={}), error code:{}", itemId, rItem.getError());
            throw new ServiceException(rItem.getError());
        }
        return rItem.getResult();
    }

    private Integer findCommissionRateByShopId(Long shopId) {
        if (commissionRuleReadService == null) {
            return 0;
        }
        Response<CommissionRule> rCommissionRule = commissionRuleReadService.findMatchCommissionRule(shopId, CommissionBusinessType.SHOP.value());
        if (!rCommissionRule.isSuccess()) {
            log.error("findMatchCommissionRule for shop fail, shopId={}, cause={}", shopId, rCommissionRule.getError());
            throw new ServiceException(rCommissionRule.getError());
        }
        CommissionRule commissionRule = rCommissionRule.getResult();
        if (commissionRule == null) {
            return 0;
        } else {
            return commissionRule.getRate();
        }
    }


}
