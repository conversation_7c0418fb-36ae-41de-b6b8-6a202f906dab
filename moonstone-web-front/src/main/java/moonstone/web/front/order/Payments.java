/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.order;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.mercury.pay.sdk.MercuryPayClient;
import com.danding.mercury.pay.sdk.MercuryPayResult;
import com.danding.mercury.pay.sdk.trade.create.MercuryPayTradeCreateResult;
import com.danding.mercury.pay.sdk.trade.notify.MercuryPayTradeNotifyResult;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.constants.RequestParams;
import io.terminus.pay.exception.PayException;
import io.terminus.pay.model.Redirect;
import io.terminus.pay.model.TradeRequest;
import io.terminus.pay.model.TradeResult;
import io.terminus.pay.service.PayChannel;
import io.vertx.core.Vertx;
import io.vertx.core.json.Json;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserOpenIdUtil;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.GbGroupInfo;
import moonstone.item.model.GbGroupMember;
import moonstone.item.service.GbGroupMemberReadService;
import moonstone.item.service.MerchantCommodityInventoryReadService;
import moonstone.order.component.PayInfoDigestor;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.model.*;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.enums.ShopPayInfoUsageChannelEnum;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.ShopPayInfoReadService;
import moonstone.web.core.component.order.PayFactory;
import moonstone.web.core.component.order.PaymentLogic;
import moonstone.web.core.component.pay.allinpay.user.AllinPayAccountBindService;
import moonstone.web.core.component.pay.allinpay.user.message.UserPayRegisterMessage;
import moonstone.web.core.component.qrCode.QrCodeGenWrapper;
import moonstone.web.core.config.FunctionSwitch;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.constants.ParanaConfig;
import moonstone.web.core.order.OrderReadLogic;
import moonstone.web.core.order.dto.JsapiWechatpayParams;
import moonstone.web.core.order.dto.PayParams;
import moonstone.common.enums.OrderSourceEnum;
import moonstone.web.core.order.enums.GbGroupMemberRoleEnum;
import moonstone.web.core.order.enums.GbGroupStatusEnum;
import moonstone.web.core.shop.application.ShopPayInfoComponent;
import moonstone.web.core.util.HttpServletUtils;
import moonstone.web.front.component.WechatUserAuthorizeLogic;
import moonstone.web.op.allinpay.lock.RedissonDistributedLock;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.joda.time.DateTime;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.MediaType;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.locks.Lock;

/**
 * 支付相关操作
 * todo: 修改AppId获取方案
 * <p>
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
public class Payments {

    @Autowired
    Vertx vertx;
    @Value("${mercury.pay.host}")
    private String mercuryPayHost;
    @Value("${mercury.pay.appCode}")
    private String mercuryPayAppCode;
    @Value("${mercury.pay.merchantCode}")
    private String mercuryPayMerchantCode;
    @Resource
    private ChannelRegistry channelRegistry;
    @Autowired
    private PaymentLogic paymentLogic;
    @Autowired
    private OrderReadLogic orderReadLogic;
    @Resource
    private PaymentReadService paymentReadService;
    @Resource
    private ShopPayInfoReadService shopPayInfoReadService;
    @Autowired
    private PayFactory payFactory;
    @Autowired
    private ParanaConfig paranaConfig;
    @Autowired
    private WechatUserAuthorizeLogic wechatUserAuthorizeLogic;
    @Autowired
    private FunctionSwitch functionSwitch;
    @Resource
    private ShopOrderReadService shopOrderReadService;
    @Resource
    private SkuOrderReadService skuOrderReadService;
    @Value("${wechat.mp.appId}")
    private String weChatMpAppId;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private EnvironmentConfig environmentConfig;

    @Resource
    private ShopPayInfoComponent shopPayInfoComponent;

    @Resource
    private AllinPayAccountBindService allinPayAccountBindService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private RedissonDistributedLock distributedLock;

    @Value("${order.auto.cancel.in.minutes}")
    private Integer expireMinutes;

    /**
     * 首先根据传入的参数, 创建支付单, 并做必要的费用计算以及冻结相应的营销
     * <p>
     * 然后根据选中的支付渠道, 生成支付请求url
     *
     * @param payParams 支付参数的dto
     * @return 是否成功
     */
    @RequestMapping(value = "/api/order/pay", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Object pay(@RequestBody PayParams payParams) {
        log.info("Payments.pay, payParams={}, 预支付开始.", payParams);

        CommonUser user = UserUtil.getCurrentUser();
        String index = Json.encode(payParams);

        // 加锁
        Lock iLock = redissonClient.getLock(getClass().getName() + "#" + index);
        if (!iLock.tryLock()) {
            log.info("Payments.pay, payParams={}, 正在支付中,直接返回.", payParams);
            return TradeRequest.fail(Translate.of("正在支付中..."));
        }
        try {
            OrderLevel orderLevel = OrderLevel.fromInt(payParams.getOrderType());
            var order = orderReadLogic.findOrder(payParams.getOrderIds().get(0), orderLevel);

            // 订单状态校验
            checkOrderStatus(order);

            // 设置支付渠道
            setPaymentChannel(payParams, order);

            log.info("Payments.pay, payParams={}, payFactory.prePay开始.", JSON.toJSONString(payParams));
            var payResult = payFactory.prePay(payParams);

            log.info("Payments.pay,payParams={}, payFactory.prePay结束, 预支付完成. payResult={}",
                    JSON.toJSONString(payParams), JSON.toJSONString(payResult));
            return payResult;
        } catch (Exception e) {
            log.error("Payments.pay, failed to pay for order(params={}), userId={}", payParams, user.getId(), e);
            return TradeRequest.fail(e.getMessage());
        } finally {
            iLock.unlock();
        }
    }

    @Resource
    private GbGroupMemberReadService gbGroupMemberReadService;

    @Resource
    private MerchantCommodityInventoryReadService merchantCommodityInventoryReadService;

    private void checkOrderStatus(OrderBase order) {
        if (order == null) {
            throw new RuntimeException("订单数据已不存在");
        }
        if (OrderStatus.NOT_PAID.getValue() != order.getStatus()) {
            throw new RuntimeException("订单已不处于待支付状态");
        }
        if (new DateTime(order.getCreatedAt()).isBefore(DateTime.now().minusMinutes(expireMinutes))) {
            throw new RuntimeException("订单已超时");
        }

        // 拼团购  检查当前是否存在已经开团过的订单
        if(order instanceof ShopOrder){
            ShopOrder shopOrder = (ShopOrder) order;
            Long userId = order.getBuyerId();
            Long shopOrderId = shopOrder.getId();

            Integer orderSource = shopOrder.getOrderSource();
            OrderSourceEnum orderSourceEnum = OrderSourceEnum.findByCode(orderSource);
            if(orderSourceEnum != null
                    && (
                            OrderSourceEnum.SHARE_PURCHASE.equals(orderSourceEnum)
                                    || OrderSourceEnum.GROUP_BUYING.equals(orderSourceEnum)
                    )){

                Integer activityId = shopOrder.getActivityId();

                // 校验团状态
                GbGroupMember gbGroupMember = validGroupStatus(shopOrderId, userId);

                // 检验库存（创建订单时扣减库存 无需校验）
//                List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
//                validateSkuInventory(skuOrderList, activityId);

                // 校验活动-团成员信息
                Long groupId = shopOrder.getGroupId();
                validateGroupMemberInfo(activityId, userId, gbGroupMember);
            }
        }
    }

    private void validateGroupMemberInfo(Integer activityId, Long userId, GbGroupMember gbGroupMember) {
        if(activityId == null){
            return;
        }
        Long orderGroupId = gbGroupMember.getGroupId();

        if(GbGroupMemberRoleEnum.MEMBER_ROLE_LEADER.getCode().equals(gbGroupMember)){
            log.info("团长支付开团校验 {} {}", gbGroupMember.getId(), gbGroupMember.getGroupId());

            List<GbGroupMember> list = gbGroupMemberReadService.findLeaderByActivityId(activityId, userId);
            if(CollUtil.isNotEmpty(list)){
                throw new RuntimeException("存在已开团订单 不再允许发起支付");
            }
        }else{
            log.info("团员参团无需校验是否开团过");
        }
    }

    private void validateSkuInventory(List<SkuOrder> skuOrderList, Integer activityId) {
        if(!CollectionUtils.isEmpty(skuOrderList)){
            for (SkuOrder skuOrder : skuOrderList) {
                Long skuId = skuOrder.getSkuId();
                merchantCommodityInventoryReadService.validateSkuInventory(activityId, skuId, skuOrder.getQuantity());
            }
        }
    }

    private GbGroupMember validGroupStatus(Long shopOrderId, Long userId) {
        GbGroupMember gbGroupMember = gbGroupMemberReadService.findByShopOrderId(shopOrderId, userId);

        GbGroupInfo gbGroupInfo = gbGroupMemberReadService.findGroupInfo(gbGroupMember);
        if(gbGroupInfo == null){
            log.error("找不到团信息 {} {}", shopOrderId, userId);
            throw new RuntimeException("内部错误(group_id)");
        }
        if(GbGroupMemberRoleEnum.MEMBER_ROLE_NORMAL.getCode().equals(gbGroupMember.getMemberRole())){
            if(GbGroupStatusEnum.IN_PROGRESS.getCode().equals(gbGroupInfo.getGroupStatus())
                    // 分享购
                    || GbGroupStatusEnum.GROUP_FULL.getCode().equals(gbGroupInfo.getGroupStatus())){
            }else{
                log.error("团已结束 团员支付 {} {}", gbGroupInfo.getId(), gbGroupInfo.getGroupStatus());
                throw new RuntimeException("团已结束");
            }
        }
        return gbGroupMember;
    }

    @RequestMapping(value = "/api/order/new/pay", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Object newPay(@RequestBody PayParams payParams) {
        try {
            Object obj;
            payParams.setAppId(null);
            if (PayChannelName.mockpay.name().equals(payParams.getChannel())) {
                obj = payFactory.prePay(payParams);
            } else {
                MercuryPayTradeCreateResult mercuryPayResult = payFactory.preMercuryPay(payParams);
                obj = mercuryPayResult;
                if (PayChannelName.wechatpay_qr.getName().equals(payParams.getChannel())) {
                    String codeUrl = mercuryPayResult.getResult().getRedirectUrl();
                    obj = TradeRequest.ok(new Redirect(payParams.getChannel(), true, null, Collections
                            .singletonMap("codeUrl", codeUrl), mercuryPayResult.getResult().getRequestMessage()));
                }
            }
            return obj;

        } catch (Exception e) {
            log.error("failed to pay for order(params={}), cause:{}", payParams, e);
            throw new JsonResponseException("order.pay.fail");
        }
    }

    @RequestMapping(value = "/api/order/we/pay", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Object distributionPay(@RequestBody PayParams payParams) {
        try {
            payParams.setAppId(paranaConfig.getParanaWeBuyerAppId());
            log.info("[prePay](WFX) payParama:{}", payParams);
            return payFactory.prePay(payParams);

        } catch (Exception e) {
            log.error("failed to pay for order(params={}), cause:{}", payParams, e);
            throw new JsonResponseException("order.pay.fail");
        }
    }

    @RequestMapping(value = "/api/order/paid/{channel}/account/{accountNo}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public String paid(@PathVariable("channel") String channel, @PathVariable("accountNo") String accountNo, HttpServletRequest request) {
        try {
            log.info("[Payments](callBack) out paymentSystem call back channel:{} accountNo:{}", channel, accountNo);
            PayChannel paymentChannel = channelRegistry.findChannel(channel);
            request.setAttribute(RequestParams.ACCOUNT, accountNo);
            request.setAttribute("channel", channel);
            Optional<String> skip = skipPaymentThatIsAlreadyFinish(channel, request);
            if (skip.isPresent()) {
                return skip.get();
            }
            paymentChannel.verify(request);

            TradeResult tradeResult = paymentChannel.paymentCallback(request);
            log.info("[Payments](callBack) decode tradeResult:{}", JSON.toJSONString(tradeResult));
            //如果通知不是代表支付成功事件，则直接返回
            if (!tradeResult.isPaySuccess()) {
                log.warn("trade result:{} is not a pay success event,skip to update payment",
                        tradeResult);
                return tradeResult.getCallbackResponse();
            }
            if (tradeResult.isFail()) {
                log.warn("payment callback result={}", tradeResult);
                return "fail";
            }

            if(isPayRegisterCallback(tradeResult.getMerchantSerialNo())){
                log.info("付费注册 发送消息");
                sendPayRegisterCallBackMessage(tradeResult, accountNo);
                return tradeResult.getCallbackResponse();
            }

            Payment payment = new Payment();
            payment.setPaidAt(tradeResult.getTradeAt());
            payment.setOutId(tradeResult.getMerchantSerialNo());
            payment.setPaySerialNo(tradeResult.getGatewaySerialNo());

            String responseOriginalBody = "";
            if (readParameterMap(channel)) {
                responseOriginalBody = JSON.toJSONString(request.getParameterMap());
            } else if (Objects.nonNull(request.getAttribute("requestBody"))) {
                responseOriginalBody = Objects.toString(request.getAttribute("requestBody"));
            }
            payment.setPayResponse(responseOriginalBody);
            log.info("{} construct payment:{}", LogUtil.getClassMethodName(), JSON.toJSONString(payment));
            paymentLogic.postPay(payment);
            return tradeResult.getCallbackResponse();
        } catch (PayException payException) {
            log.error("{} fail to handle payment [channel => {}] callback, cause:", channel, LogUtil.getClassMethodName()
                    , new RuntimeException(payException.getError(), payException));
            return "fail";
        } catch (Exception e) {
            log.error("failed to handle payment callback, request params is:{}, cause is:",
                    HttpServletUtils.convertRequestToMap(request), e);
            return "fail";
        }
    }

    private void sendPayRegisterCallBackMessage(TradeResult tradeResult, String accountNo) {
        UserPayRegisterMessage userPayRegisterMessage = new UserPayRegisterMessage();
        userPayRegisterMessage.setOrderNo(tradeResult.getMerchantSerialNo());
        try {
            // 201_pay_register
            Long shopId = Long.valueOf(accountNo);
            userPayRegisterMessage.setShopId(shopId);
        }catch (Exception e){
            log.error("处理失败", e);
        }
        Message<UserPayRegisterMessage> message = MessageBuilder.withPayload(userPayRegisterMessage).build();
        SendResult sendResult = rocketMQTemplate.syncSend("mall_user_pay_register_callback_topic", message);
        if(SendStatus.SEND_OK.equals(sendResult.getSendStatus())){
            log.info("付费支付回调 消息发送成功 {} {}", accountNo, sendResult.getMsgId());
        }else{
            log.info("付费支付回调 消息发送失败 {}", accountNo);
        }
    }

    private boolean isPayRegisterCallback(String merchantSerialNo) {
        if(merchantSerialNo.startsWith("FAMILY")){
            return true;
        }
        return false;
    }

    private boolean readParameterMap(String channel) {
        return channel.contains(PayChannelName.alipay.name()) ||
                channel.contains(PayChannelName.mockpay.name()) ||
                PaymentChannelEnum.ALLINPAY.getCode().equals(channel) ||
                PaymentChannelEnum.ALLINPAY_YST.getCode().equals(channel);
    }

    private Optional<String> skipPaymentThatIsAlreadyFinish(String channel, HttpServletRequest request) {
        // 关闭超时的支付单回馈, 这个必然是失败的
        if (channel.contains("alipay")) {
            String outId = request.getParameter("out_trade_no");
            Payment payment = paymentReadService.findByOutId(outId).getResult();
            if (Objects.isNull(payment)) {
                log.error("{} fail to find Payment[OutId => {}, Channel => {}]", LogUtil.getClassMethodName(), outId, channel);
                return Optional.of("fail");
            }
            Date lastYear = Date.from(LocalDateTime.now().minusYears(1).atZone(ZoneId.systemDefault()).toInstant());
            if (payment.getCreatedAt().before(lastYear)) {
                log.error("{} Payment[Id => {}, OutId => {}, Channel => {}, CreatedAt => {}] is expired",
                        LogUtil.getClassMethodName(), payment.getId(), outId, channel, payment.getCreatedAt());
                return Optional.of("fail");
            }
            // 如果支付单已经完成了, 则不需要回馈
            if (OrderStatus.fromInt(payment.getStatus()) == OrderStatus.PAID) {
                return Optional.of("success");
            }
        }
        return Optional.empty();
    }

    /**
     * 新支付系统回调
     */
    @RequestMapping(value = "/api/order/paid/{channel}/account/{accountNo}/new", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public String tradePayNotify(HttpServletRequest request) {
        log.info("[op: TradePayNotify-request] payNotify={}", JSONObject.toJSONString(request.getParameterMap()));
        MercuryPayClient client = new MercuryPayClient(mercuryPayHost + "/api",
                mercuryPayAppCode, mercuryPayMerchantCode);
        com.danding.common.net.Response<? extends MercuryPayResult>
                response = client.receive(request, MercuryPayTradeNotifyResult.class);
        log.info("[op: TradePayNotify-response] response={}", JSONObject.toJSONString(response));
        if (response.isSuccess()) {
            MercuryPayTradeNotifyResult result = (MercuryPayTradeNotifyResult) response.getResult();
            log.info("[op: TradePayNotify-result] result={}", JSONObject.toJSONString(result));

            Payment payment = new Payment();
            // todo:应由支付系统传入
            payment.setPaidAt(DateTime.now().toDate());
            payment.setOutId(result.getOutOrderNo());
            payment.setPaySerialNo(result.getBankNo());

            payment.setPayResponse(result.getResponseMessage());
            log.info("[op: TradePayNotify-payment] payment={}", JSONObject.toJSONString(payment));
            paymentLogic.postPay(payment);
            log.info("payNotify={}", JSONObject.toJSONString(result));
            return "SUCCESS";
        } else {
            log.error("payNotifyError={}", response.getError());
            return "FAIL";
        }
    }

    @RequestMapping(value = "/api/order/payment", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Payment findPayment(@RequestParam("orderIds") List<Long> orderIds,
                               @RequestParam(value = "orderType", defaultValue = "1") Integer orderType,
                               @RequestParam("channel") String channel) {
        OrderLevel orderLevel = OrderLevel.fromInt(orderType);
        String payInfoMd5 = PayInfoDigestor.digest(orderIds, orderLevel, channel, 0L);
        Response<Optional<Payment>> findPayment = paymentReadService.findPaymentByPayInfoMd5(payInfoMd5);
        if (!findPayment.isSuccess()) {
            log.error("fail to find payment by payInfoMd5:{},cause:{}",
                    payInfoMd5, findPayment.getError());
            throw new JsonResponseException(findPayment.getError());
        }
        java.util.Optional<Payment> paymentOptional = findPayment.getResult();
        if (paymentOptional.isEmpty()) {
            log.error("payment not found where paymentInfoMd5={}", payInfoMd5);
            throw new JsonResponseException("payment.not.found");
        }
        Payment payment = paymentOptional.get();

        checkAuth(payment.getId());
        return payment;
    }

    private void checkAuth(Long paymentId) {
        Response<List<OrderPayment>> findOrderPayments = paymentReadService.findOrderIdsByPaymentId(paymentId);
        if (!findOrderPayments.isSuccess()) {
            log.error("fail to find order payments by paymentId={},cause:{}",
                    paymentId, findOrderPayments.getError());
            throw new JsonResponseException(findOrderPayments.getError());
        }
        List<OrderPayment> orderPayments = findOrderPayments.getResult();
        if (CollectionUtils.isEmpty(orderPayments)) {
            log.error("order payments not found where paymentId={}", paymentId);
            throw new JsonResponseException("order.payment.not.found");
        }
        OrderPayment orderPayment = orderPayments.get(0);

        OrderBase orderBase = orderReadLogic.findOrder(orderPayment.getOrderId(), orderPayment.getOrderLevel());

        CommonUser commonUser = UserUtil.getCurrentUser();
        if (!Objects.equals(commonUser.getId(), orderBase.getBuyerId())) {
            log.error("payment(id={}) not belong to buyer(id={})",
                    paymentId, commonUser.getId());
            throw new JsonResponseException("payment.not.belong.to.buyer");
        }
    }

    @RequestMapping(value = "/api/order/isUnifiedPayment", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean isUnifiedPayment() {
        return functionSwitch.getUnifiedPayment();
    }

    @RequestMapping(value = "/api/order/getOrderQrCode")
    public String getOrderQrCodeUrl(@RequestBody PayParams payParams, HttpServletResponse response) {
        try {
            if (functionSwitch.getUnifiedPayment().equals(Boolean.FALSE)) {
                log.error("illegal unified pay request, payParams: {}", payParams);
                throw new JsonResponseException("application.not.provide.function");
            }
            String promotionIdStr = payParams.getPromotionId() == null ? "" : payParams.getPromotionId().toString();
            Long orderAmount = 0L;
            Long shopId = null;
            switch (OrderLevel.fromInt(payParams.getOrderType())) {
                case SKU -> {
                    var param = new OrderCriteria();
                    param.setOrderIds(payParams.getOrderIds());
                    var rSkuOrders = skuOrderReadService.pagingSkuOrder(param).getData();
                    if (rSkuOrders == null) {
                        log.error("failed to find sku orders by ids={}", payParams.getOrderIds());
                        throw new JsonResponseException();
                    }
                    for (SkuOrder skuOrder : rSkuOrders) {
                        if (shopId == null) {
                            shopId = skuOrder.getShopId();
                        } else if (!Objects.equals(shopId, skuOrder.getShopId())) {
                            log.error("can not pay for orders with different shopId({}, {})", shopId, skuOrder.getShopId());
                            throw new JsonResponseException("order.pay.only.same.shopId");
                        }
                        orderAmount += skuOrder.getFee();
                    }
                }
                case SHOP -> {
                    var criteria = new OrderCriteria();
                    criteria.setOrderIds(payParams.getOrderIds());
                    List<ShopOrder> rShopOrders = shopOrderReadService.pagingShopOrder(criteria).getData();
                    if (null == rShopOrders) {
                        log.error("failed to find shop orders by ids={}", payParams.getOrderIds());
                        throw new JsonResponseException();
                    }
                    for (ShopOrder shopOrder : rShopOrders) {
                        if (shopId == null) {
                            shopId = shopOrder.getShopId();
                        } else if (!Objects.equals(shopId, shopOrder.getShopId())) {
                            log.error("can not pay for orders with different shopId({}, {})", shopId, shopOrder.getShopId());
                            throw new JsonResponseException("order.pay.only.same.shopId");
                        }
                        orderAmount += shopOrder.getFee();
                    }
                }
                default -> {
                    log.warn("[op:getOrderQrCodeUrl] orderType({}) is illegal", payParams.getOrderType());
                    throw new JsonResponseException("order.level.illegal");
                }
            }
            Response<ShopPayInfo> rShopPayInfo = shopPayInfoReadService.findByShopIdAndPayChannel(shopId, PayChannelName.wechatpay.name());
            if (!rShopPayInfo.isSuccess()) {
                log.error("failed to find shop pay info by shopId={}, payChannel={}, error code: {}", shopId, PayChannelName.wechatpay.name(), rShopPayInfo.getError());
                throw new JsonResponseException(rShopPayInfo.getError());
            }
            WechatMQData mqData = getWechatMqData(shopId).orElse(null);
            String wxAppId = mqData == null ? weChatMpAppId : mqData.getAppId();
            if (ObjectUtils.isEmpty(wxAppId)) {
                wxAppId = weChatMpAppId;
            }
            String url = paranaConfig.getParanaH5Url() + "/order/pay?orderIds=" + JSON.toJSONString(payParams.getOrderIds())
                    + "&orderType=" + payParams.getOrderType() + "&orderAmount=" + orderAmount + "&promotionId=" + promotionIdStr + "&wxAppId=" + wxAppId;
            return QrCodeGenWrapper.of(url).asString();
        } catch (Exception e) {
            log.error("fail to get OrderQrCode by params={}, cause: {}", payParams, e);
            throw new JsonResponseException("order.qrCode.get.fail");
        }
    }

    public Either<WechatMQData> getWechatMqData(String appId) {
        try {
            return Either.ok(mongoTemplate.findOne(Query.query(Criteria.where("appId").is(appId)), WechatMQData.class));
        } catch (Exception ex) {
            log.error("{} find wechat-MQ failed by appId:{}", LogUtil.getClassMethodName(), appId, ex);
            return Either.error(ex);
        }
    }

    public Either<WechatMQData> getWechatMqData(Long shopId) {
        try {
            return Either.ok(mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopId)), WechatMQData.class));
        } catch (Exception ex) {
            log.error("{} find wechat-MQ failed by shopId:{}", LogUtil.getClassMethodName(), shopId, ex);
            return Either.error(ex);
        }
    }

    @RequestMapping("/api/wechatMQ/add")
    public Response<Boolean> addWeChatMqData(Long shopId, String appId, String secret, String auth) throws Exception {
        if (!java.util.Objects.equals("add-wechat-mq-fuck-you", auth)) {
            throw new HttpRequestMethodNotSupportedException("please use SUPER-PUT-WITH-ASSHOLE METHOD");
        }
        try {
            if (mongoTemplate.exists(Query.query(Criteria.where("shopId").is(shopId)), WechatMQData.class)) {
                mongoTemplate.updateFirst(Query.query(Criteria.where("shopId").is(shopId)), Update.update("appId", appId).set("secret", secret), WechatMQData.class);
            } else {
                WechatMQData mqData = new WechatMQData();
                mqData.setAppId(appId);
                mqData.setSecret(secret);
                mqData.setShopId(shopId);
                mongoTemplate.save(mqData);
            }
        } catch (Exception ex) {
            log.error("{} failed to add wechatMQ:[{}]", LogUtil.getClassMethodName(), Arrays.asList(appId, secret, shopId), ex);
            return Response.fail(ex.getMessage());
        }
        return Response.ok(true);
    }

    /**
     * 微信公众号支付，先用微信授权code换取openId，在进行支付
     *
     * @param params 支付参数（包含微信授权code）
     */
    @RequestMapping(value = "/api/order/jsapi/wechatpay", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Object jsapiWechatpay(@RequestBody JsapiWechatpayParams params, HttpServletRequest request) {
        try {
            params.setChannel("wechatpay-jsapi");
            String userAgent = request.getHeader("User-Agent").toLowerCase();
            if (userAgent.indexOf("micromessenger") <= 0) {
                log.warn("want to wechatpay for order without wechat browser, by params={}", params);
                throw new JsonResponseException("wechatpay.jsapi.request.illegal");
            }
            WechatMQData mqData = (
                    params.getAppId() == null ?
                            java.util.Optional.ofNullable(shopOrderReadService.findById(params.getOrderIds().get(0)).getResult())
                                    .map(ShopOrder::getShopId)
                                    .map(this::getWechatMqData) :
                            java.util.Optional.ofNullable(getWechatMqData(params.getAppId()))
            ).orElse(Either.error("not-found-wechatMQ"))
                    .orElse(null);
            log.debug("{} wechatMQData:{} params:{}", LogUtil.getClassMethodName(), mqData, params);
            String openId = mqData == null ? wechatUserAuthorizeLogic.authorizeForOpenId(params.getWxCode())
                    : wechatUserAuthorizeLogic.authorizeForOpenId(mqData.getAppId(), mqData.getSecret(), params.getWxCode());
            UserOpenIdUtil.putOpenId(openId);

            if (params.getAppId() == null) {
                params.setAppId(mqData == null ? weChatMpAppId : mqData.getAppId());
            }

            return payFactory.prePay(params);
        } catch (Exception e) {
            log.error("failed to jsapi wechatpay for order(params={}), cause:{}", params, e);
            throw new JsonResponseException("order.pay.fail");
        }
    }

    /**
     * 设置支付渠道
     *
     * @param payParams
     */
    private void setPaymentChannel(PayParams payParams, OrderBase order) {
        if (payParams.getUsageChannel() == null || PaymentChannelEnum.INTEGRAL_PAY.getCode().equals(payParams.getChannel())) {
            return;
        }
        if (!environmentConfig.isOnline() && PaymentChannelEnum.MOCK_PAY.getCode().equals(payParams.getChannel())) {
            return;
        }

//        var shopId = order.getShopId();
//        var channel = shopPayInfoComponent.getPaymentChannel(order.getShopId(),
//                ShopPayInfoUsageChannelEnum.parse(payParams.getUsageChannel()), UserUtil.getUserId());

        // 获取可用的支付配置
        var channel = shopPayInfoComponent.getPaymentChannelNew(order.getId(), UserUtil.getUserId());
        if (channel != null) {
            payParams.setChannel(channel.getCode());
        }

        // 检查通联支付时 订单所属门店 是否完善了银行卡等信息（防止影响门店的交易行为）
        String outShopId = order.getOutShopId();
        if(StringUtils.isNotEmpty(outShopId)){
            allinPayAccountBindService.checkAllinPayAccountInfo(outShopId, channel, UserUtil.getUserId());
        }

        if (StringUtils.isBlank(payParams.getChannel())) {
            throw new RuntimeException("没有可用的支付配置");
        }
    }

    private enum PayChannelName {
        /**
         * 用于匹配支付渠道
         */
        wechat, alipay, mockpay, wechatpay_qr, wechatpay;

        String getName() {
            return name().replace("_", "-");
        }
    }

    /**
     * 禁止修改类名
     *
     * @see MongoTemplate#query 查询对这个有要求
     */
    @Data
    static class WechatMQData {
        String appId;
        String secret;
        Long shopId;
        Integer status = 1;
    }
}
