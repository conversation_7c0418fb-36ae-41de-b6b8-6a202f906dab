package moonstone.web.front.order.web;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableBiMap;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.api.Result;
import moonstone.common.constants.ParanaConstants;
import moonstone.common.constants.RocketMQConstant;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.UserStatus;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.event.OrderCreatedEvent;
import moonstone.event.PaymentDeclareInlineEvent;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.RichOrderMaker;
import moonstone.order.dto.*;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.enu.OrderCreateChannelEnum;
import moonstone.order.enu.OrderFlagEnum;
import moonstone.order.model.*;
import moonstone.order.rule.OrderRuleEngine;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.promotion.api.CouponCheck;
import moonstone.promotion.component.OrderCharger;
import moonstone.promotion.component.PromotionOngoingValidator;
import moonstone.shop.model.Shop;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.user.model.DataExportTask;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.UserCertification;
import moonstone.user.service.UserCertificationReadService;
import moonstone.web.core.constants.RedisConstants;
import moonstone.web.core.fileNew.logic.ShopOrderLogic;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.order.OrderReadLogic;
import moonstone.web.core.order.OrderWriteLogic;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.user.RealNameCertificationManager;
import moonstone.web.core.user.StoreProxyManager;
import moonstone.web.core.user.UserRelationManager;
import moonstone.web.core.util.LockKeyUtils;
import moonstone.web.front.component.order.OrderExporter;
import moonstone.web.front.component.order.OrderReader;
import moonstone.web.front.component.order.RefundExporter;
import moonstone.web.front.component.order.dto.EnhanceOrderCriteria;
import moonstone.web.front.component.order.dto.RefundExportParameter;
import moonstone.web.front.order.convert.RefundConvertor;
import moonstone.web.front.order.dto.OrderCancelReqDto;
import org.joda.time.DateTime;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class Orders {

    private static final String PAYER_ID_PATTERN = "(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{4}\\*{9}\\d{4}$)";
    private static final int CANCEL_DELAY = 5;

    @Resource
    RichOrderMaker richOrderMaker;
    @Resource
    OrderWriteService orderWriteService;
    @Resource
    PromotionOngoingValidator promotionOngoingValidator;
    @Autowired
    OrderWriteLogic orderWriteLogic;
    @Autowired
    OrderReadLogic orderReadLogic;
    @Resource
    OrderRuleEngine orderRuleEngine;
    @Resource
    OrderCharger charger;
    @Autowired
    List<CouponCheck> couponChecks;
    @Resource
    SkuReadService skuReadService;
    @Resource
    ShopReadService shopReadService;
    @Resource
    UserCertificationReadService userCertificationReadService;
    @Resource
    ShopOrderReadService shopOrderReadService;
    @Resource
    SkuOrderReadService skuOrderReadService;
    @Resource
    PaymentReadService paymentReadService;
    @Resource
    SubStoreReadService subStoreReadService;
    @Resource
    SubStoreTStoreGuiderReadService guiderReadService;
    @Autowired
    UserRelationManager userRelationManager;
    @Autowired
    StoreProxyManager storeProxyManager;
    @Resource
    ShopCacheHolder shopCacheHolder;
    @Autowired
    Orders self;
    @Autowired(required = false)
    JedisPool jedisPool;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    OrderExporter orderExporter;
    @Autowired
    private RealNameCertificationManager realNameCertificationManager;
    @Resource
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private UserCacheHolder userCacheHolder;

    @Resource
    private OrderReader orderReader;

    @Resource
    private RefundExporter refundExporter;

    @Resource
    private RefundConvertor refundConvertor;

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private RocketMQProducer rocketMQProducer;

    @Resource
    private ShopOrderLogic shopOrderLogic;

    private void addMemberShip(long userId, RichOrder richOrder) {
        if (richOrder != null && !richOrder.getRichSkusByShops().isEmpty()) {
            RichSkusByShop richSkusByShop = richOrder.getRichSkusByShops().get(0);
            Long shopId = richSkusByShop.getShop().getId();
            if (shopId != null) {
                try {
                    userRelationManager.addSubStoreMember(shopId, userId);
                } catch (Exception ex) {
                    log.warn("{} userId:{} shopId:{}", LogUtil.getClassMethodName("fail-add-memberShip"), userId, shopId);
                }
            }
        }
    }

    public List<Long> create(SubmittedOrder submittedOrder, CommonUser user) {
        var lock = redissonClient.getLock(LockKeyUtils.orderCreate(user.getId()));
        if (!lock.tryLock()) {
            log.error("Orders.create error, userId={}, submittedOrder={}, 获取锁失败", user.getId(), JSON.toJSONString(submittedOrder));
            throw new RuntimeException("正在处理下单流程，请稍后重试");
        }

        try {
            var shopOrderIds = realCreate(submittedOrder, user);
            log.info("Orders.create, userId={}, submittedOrder={}, result shopOrderIds={}",
                    user.getId(), JSON.toJSONString(submittedOrder), JSON.toJSONString(shopOrderIds));

            return shopOrderIds;
        } catch (Exception ex) {
            log.error("Orders.create error, userId={}, submittedOrder={}", user.getId(), JSON.toJSONString(submittedOrder), ex);
            throw ex;
        } finally {
            lock.unlock();
        }
    }

    @RequestMapping(value = "/api/order", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Long> create(@RequestBody SubmittedOrder submittedOrder) {
        // n1：老程序 下单入口
        log.info("创建订单 {}", JSONObject.toJSONString(submittedOrder));
        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException("user.not.login");
        }

        log.info("Orders.create, userId={}, submittedOrder={}", userId, JSON.toJSONString(submittedOrder));
        createParamsCheck(submittedOrder);
        CommonUser user = UserUtil.getCurrentUser();
        List<Long> blackList = new ArrayList<>();
        blackList.add(49262L);
        blackList.add(67817L);
        blackList.add(75280L);
        blackList.add(67429L);
        blackList.add(71491L);
        blackList.add(69810L);
        blackList.add(71994L);
        blackList.add(60013L);
        blackList.add(60471L);
        blackList.add(60506L);
        blackList.add(60512L);
        blackList.add(60524L);
        blackList.add(60539L);
        blackList.add(60632L);
        blackList.add(60655L);
        blackList.add(60779L);
        blackList.add(60934L);
        blackList.add(60990L);
        blackList.add(61152L);
        blackList.add(61237L);
        blackList.add(62022L);
        blackList.add(64513L);
        blackList.add(64740L);
        blackList.add(64869L);
        blackList.add(64921L);
        blackList.add(64995L);
        blackList.add(65692L);
        blackList.add(67521L);
        blackList.add(69562L);
        blackList.add(69665L);
        blackList.add(69698L);
        blackList.add(70719L);
        blackList.add(70796L);
        blackList.add(71087L);
        blackList.add(65905L);
        blackList.add(60702L);
        blackList.add(71473L);
        blackList.add(71562L);
        blackList.add(71600L);
        blackList.add(72607L);
        blackList.add(72749L);
        blackList.add(73684L);
        blackList.add(73685L);
        blackList.add(73747L);
        blackList.add(73843L);
        blackList.add(73885L);
        blackList.add(62849L);
        blackList.add(75736L);
        blackList.add(76070L);
        blackList.add(76080L);
        blackList.add(76199L);
        blackList.add(76277L);
        blackList.add(76424L);
        blackList.add(76726L);
        blackList.add(76876L);
        blackList.add(77559L);
        blackList.add(78041L);
        blackList.add(67584L);

        blackList.add(60502L);
        blackList.add(61841L);
        blackList.add(62692L);
        blackList.add(60608L);
        blackList.add(60609L);
        blackList.add(60611L);
        blackList.add(60612L);
        blackList.add(60613L);
        blackList.add(61934L);
        blackList.add(60661L);
        blackList.add(60783L);
        blackList.add(60993L);
        blackList.add(61156L);
        blackList.add(61157L);
        blackList.add(61159L);
        blackList.add(61155L);
        blackList.add(61165L);
        blackList.add(61166L);
        blackList.add(61173L);
        blackList.add(61177L);
        blackList.add(61161L);
        blackList.add(61182L);
        blackList.add(61163L);
        blackList.add(61185L);
        blackList.add(61186L);
        blackList.add(61191L);
        blackList.add(61160L);
        blackList.add(61194L);
        blackList.add(61196L);
        blackList.add(61200L);
        blackList.add(61199L);
        blackList.add(61198L);
        blackList.add(61195L);
        blackList.add(65316L);
        blackList.add(70947L);
        blackList.add(62205L);
        blackList.add(72091L);
        blackList.add(64870L);
        blackList.add(72748L);
        if (blackList.contains(userId)) {
            throw new ApiException("该账户暂不支持下单，详情请联系商家");
        }
        return create(submittedOrder, user);
    }


    private void createParamsCheck(SubmittedOrder submittedOrder) {
        if (submittedOrder == null || submittedOrder.getSubmittedSkusByShops() == null) {
            throw new JsonResponseException("订单校验失败");
        }
        List<Long> skuIdList = submittedOrder.getSubmittedSkusByShops().stream()
                .flatMap(richSkusByShop -> richSkusByShop.getSubmittedSkus().stream())
                .map(SubmittedSku::getSkuId).collect(Collectors.toList());
        List<Sku> skuList = skuReadService.findSkusByIds(skuIdList).getResult();
        Map<Long, String> outSkuIdMap = skuList.stream().collect(Collectors.toMap(Sku::getId, Sku::getOuterSkuId));
        List<Long> itemIdList = skuList.stream().map(Sku::getItemId).collect(Collectors.toList());
        List<Item> itemList = itemReadService.findByIds(itemIdList).getResult();
        // 检查来源是否一致
        Set<Integer> goodsSourceTypeSet = itemList.stream()
                .map(Item::getSourceType)
                .collect(Collectors.toSet());
        if (goodsSourceTypeSet.size() != 1) {
            throw new ApiException("所选商品发货仓来源不一致，请联系商家后重新下单");
        }
        // jd_cloud: 订单 检查商品是否有库存 不校验库存了
//        if (goodsSourceTypeSet.contains(SourceTypeEnum.JD.getCode())) {
//            List<SubmittedSkusByShop> submittedSkusByShops = submittedOrder.getSubmittedSkusByShops();
//            for (SubmittedSkusByShop submittedSkusByShop : submittedSkusByShops) {
//                ItemCheckStockDto checkStockDto = new ItemCheckStockDto();
//                checkStockDto.setShopId(submittedSkusByShop.getShopId());
//                Response<ReceiverInfo> receiverInfoResponse = receiverInfoReadService.findById(submittedOrder.getReceiverInfoId());
//                if (receiverInfoResponse.isSuccess()) {
//                    ReceiverInfo receiverInfo = receiverInfoResponse.getResult();
//                    ItemCheckStockDto.Address address = new ItemCheckStockDto.Address();
//                    address.setProvince(receiverInfo.getProvince());
//                    address.setCity(receiverInfo.getCity());
//                    address.setRegion(receiverInfo.getRegion());
//                    address.setStreet(receiverInfo.getStreet());
//                    address.setDetail(receiverInfo.getDetail());
//                    checkStockDto.setAddress(address);
//                }
//                Map<String, ItemCheckStockDto.SkuQuantity> skuQuantityMap = new HashMap<>();
//                for (SubmittedSku submittedSku : submittedSkusByShop.getSubmittedSkus()) {
//                    if (skuQuantityMap.containsKey(submittedSku.getOuterSkuId())) {
//                        ItemCheckStockDto.SkuQuantity skuQuantity = skuQuantityMap.get(submittedSku.getOuterSkuId());
//                        skuQuantity.setQuantity(skuQuantity.getQuantity() + submittedSku.getQuantity());
//                    } else {
//                        ItemCheckStockDto.SkuQuantity skuQuantity = new ItemCheckStockDto.SkuQuantity();
//                        skuQuantity.setQuantity(submittedSku.getQuantity());
//                        skuQuantity.setOuterSkuId(outSkuIdMap.get(submittedSku.getSkuId()));
//                        skuQuantityMap.put(submittedSku.getOuterSkuId(), skuQuantity);
//                    }
//                }
//                List<ItemCheckStockDto.SkuQuantity> skuQuantityList = new ArrayList<>(skuQuantityMap.values());
//                checkStockDto.setSkuQuantityList(skuQuantityList);
//                if (!thirdPartySkuShopLogic.checkStockInfo(checkStockDto)) {
//                     throw new ApiException("库存校验失败");
//                }
//            }
//        }
    }

    public List<Long> realCreate(@RequestBody SubmittedOrder submittedOrder, CommonUser user) {
        // 手动控制事务保证eventBus触发前事务写入完毕
        try {
            log.info("[order-realCreate] userId={}, submittedOrder={}", user.getId(), JSON.toJSONString(submittedOrder));
            RichOrder richOrder = richOrderMaker.full(submittedOrder, user);


            //检查用户是否具有购买资格
            orderRuleEngine.canBuy(richOrder);
            //提前增加注册用户
            addMemberShip(user.getId(), richOrder);

            Predicate<RichSkusByShop> isBondedCheck = richSkusByShop -> richSkusByShop.getRichSkus().stream()
                    .map(RichSku::getSku).map(Sku::getType).anyMatch(Predicate.isEqual(1));
            boolean hasBonded = richOrder.getRichSkusByShops().stream().anyMatch(isBondedCheck);
            //含有保税商品，检查订购人实名认证信息
            checkPayerInfo(submittedOrder.getChannel(), richOrder, user, hasBonded);

            //检查商家营销活动的有效性
            promotionOngoingValidator.validate(richOrder);

            //检查优惠券是否符合使用规则
            for (CouponCheck couponCheck : couponChecks) {
                couponCheck.check(richOrder);
            }

            //检查用户是否可以享受对应的营销, 如果可以, 则计算营销, 需要实付的金额, 以及运费等
            charger.charge(richOrder, null);

            validTheGuider(richOrder, user, submittedOrder);

            // 通过AOP层进行独立事务处理，以使eventBus的线程在persist事务后进行，避免幻读
            Response<List<Long>> rOrder = self.persistRichOrder(richOrder);

            if (!rOrder.isSuccess()) {
                log.error("failed to create {}, error code:{}", submittedOrder, rOrder.getError());
                throw new JsonResponseException(rOrder.getError());
            }

            final List<Long> shopOrderIds = rOrder.getResult();

            orderCreatedEventSend(shopOrderIds);
            return shopOrderIds;
        } catch (Exception e) {
            log.error("Orders.realCreate, failed to create, userId={}, submittedOrder={}, cause:",
                    user.getId(), JSON.toJSONString(submittedOrder), e);
            throw e;
        }
    }


    /**
     * 判断库存是否可用，然后持久化订单
     *
     * @param richOrder 订单内容
     * @return 是否持久化成功
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Response<List<Long>> persistRichOrder(RichOrder richOrder) {
        // 如果没有配置redis 那么就直接走吧
        if (jedisPool == null) {
            log.debug("{} redis not enable so may cause over sell", LogUtil.getClassMethodName());
            return orderWriteService.create(richOrder);
        }
        // 配置了redis 那么就从redis里拉缓存
        try (Jedis jedis = jedisPool.getResource()) {
            HashMap<String, Long> skuStockQuantityModifyHistory = new HashMap<>(8);
            //modify redis and record its action
            BiFunction<Long, Long, Long> modifySkuQuantity = (skuId, modQuantity) -> {
                String skuStockQuantityCacheInRedis = RedisConstants.SKU_STOCK_PREFIX + skuId;
                Lock lock = redissonClient.getLock(RedisConstants.SKU_STOCK_LOCK_PREFIX + skuId);
                lock.lock();
                try {
                    if (!jedis.exists(skuStockQuantityCacheInRedis)) {
                        Integer stockQuantity = skuReadService.findSkuById(skuId).getResult().getStockQuantity();
                        log.debug("{} update skuStock[{}] at redis", LogUtil.getClassMethodName(), stockQuantity);
                        jedis.setnx(skuStockQuantityCacheInRedis, stockQuantity.toString());
                    }
                    log.debug("{} " + RedisConstants.SKU_STOCK_PREFIX + " sku[{}] exists[{}]", LogUtil.getClassMethodName(), skuId, jedis.exists(skuStockQuantityCacheInRedis));
                    skuStockQuantityModifyHistory.put(skuStockQuantityCacheInRedis, modQuantity);
                    jedis.expire(skuStockQuantityCacheInRedis, 2 * 60);
                    return jedis.decrBy(skuStockQuantityCacheInRedis, modQuantity);
                } finally {
                    lock.unlock();
                }
            };
            //revoke all redis action
            Consumer<Map<String, Long>> revokeRedisAction = (map) -> map.forEach((skuStockQuantityCacheInRedis, modQuantity) -> {
                log.debug("{} redis stock[{}] add {} result [{}]", LogUtil.getClassMethodName(), skuStockQuantityCacheInRedis, modQuantity,
                        jedis.decrBy(skuStockQuantityCacheInRedis, -modQuantity));
                jedis.expire(skuStockQuantityCacheInRedis, 2 * 60);
            });
            boolean failed = false;
            // 遍历处理所有的单品去处理库存
            StockLoop:
            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                for (RichSku richSkus : richSkusByShop.getRichSkus()) {
                    Long afterQuantity = modifySkuQuantity.apply(richSkus.getSku().getId(), richSkus.getQuantity().longValue());
                    log.debug("{} skuId:{} after-stock-quantity:{} in redis", LogUtil.getClassMethodName(), richSkus.getSku().getId(), afterQuantity);
                    if (afterQuantity < 0) {
                        log.error("{} buyer:{} skuId:{} buy-quantity:{} quantity:{}", LogUtil.getClassMethodName()
                                , richOrder.getBuyer()
                                , richSkus.getSku()
                                , richSkus.getQuantity()
                                , afterQuantity);
                        failed = true;
                        break StockLoop;
                    }
                }
            }
            // 出现库存不足的情况了 回退所有吃掉的库存
            if (failed) {
                revokeRedisAction.accept(skuStockQuantityModifyHistory);
                return Response.fail(new Translate("扣减库存失败，请重新下单").toString());
            }
            Response<List<Long>> rShopOrderIds = orderWriteService.create(richOrder);
            if (!rShopOrderIds.isSuccess()) {
                revokeRedisAction.accept(skuStockQuantityModifyHistory);
            }
            return rShopOrderIds;
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} sku-stock-quantity modify error:{}", LogUtil.getClassMethodName(), ex.getMessage());
            return Response.fail(new Translate("扣减库存失败，请重新下单").toString());
        }
    }

    /**
     * check the `outFrom` guider data,fix it if we can
     *
     * @param richOrder      order
     * @param user
     * @param submittedOrder
     */
    private void validTheGuider(RichOrder richOrder, CommonUser user, SubmittedOrder submittedOrder) {
        for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {

            if (ObjectUtils.isEmpty(richSkusByShop.getOutFrom())) {
                continue;
            }
			if (richSkusByShop.getShop().getId().equals(202L)) {
                // 预发环境下  202 回归店铺不做对应校验
				continue;
			}
            OrderOutFrom outFrom = OrderOutFrom.fromCode(richSkusByShop.getOutFrom());
            switch (outFrom) {
                case SUB_STORE -> {
                    // never trust a frontend given data
                    // not from subStore mean skip this test
                    if (ObjectUtils.isEmpty(richSkusByShop.getOutShopId())) {
                        throw new RuntimeException("门店id丢失，请重新扫描二维码");
                    }
                    Long subStoreId = Long.valueOf(richSkusByShop.getOutShopId());
                    var rSubStore = subStoreReadService.findById(subStoreId);

                    if (rSubStore.isSuccess()) {
                        // wrong subStore?
                        if (rSubStore.getResult() == null) {
                            log.error("{} outShopId:{} outFrom:{} richOrder:{} subShopOrder:{}", LogUtil.getClassMethodName("wong-subStore"), richSkusByShop.getOutShopId(), richSkusByShop.getOutFrom(), richOrder, richSkusByShop);
                            richSkusByShop.setRefererId(null);
                            throw new RuntimeException("门店信息查询失败，请重新扫描二维码");
                        }

                        //门店对应的用户的状态校验
                        checkSubStoreUser(rSubStore.getResult());
                        // no  guider skip too
                        if (richSkusByShop.getRefererId() == null) {
                            continue;
                        }

                        //导购对应的用户状态校验
                        checkGuiderUser(richSkusByShop.getRefererId());

                        @SuppressWarnings("deprecation") var rGuiderList = guiderReadService.findByStoreGuiderId(richSkusByShop.getRefererId());
                        // not a guider?
                        if (!rGuiderList.isSuccess() || CollectionUtils.isEmpty(rGuiderList.getResult())) {
                            log.error("{} refererId:{} richOrder:{} subOrder:{}", LogUtil.getClassMethodName("not-guider"), richSkusByShop.getRefererId(), richOrder, richSkusByShop);
                            richSkusByShop.setRefererId(null);
                            continue;
                        }
                        // not guider from this subStore?
                        if (rGuiderList.getResult().stream().noneMatch(guider -> guider.getSubStoreId().equals(subStoreId))) {
                            log.error("{} refererId:{} richOrder:{} subOrder:{}", LogUtil.getClassMethodName("wrong-guider"), richSkusByShop.getRefererId(), richOrder, richSkusByShop);
                            richSkusByShop.setRefererId(null);
                            continue;
                        }
                    } else {
                        throw new RuntimeException("门店信息查询失败，请重新扫描二维码");
                    }
                }
                case LEVEL_Distribution -> {
                    if (richSkusByShop.getRefererId() == null) {
                        break;
                    }
                    if (richSkusByShop.getExtra() == null) {
                        richSkusByShop.setExtra(new HashMap<>(8));
                    }
                    Optional<StoreProxy> storeProxyOpt = storeProxyManager.findValidStoreProxyFromRefererIdAndShopId(richSkusByShop.getShop().getId(), richSkusByShop.getRefererId());
                    Optional<Long> realProxyId = storeProxyOpt.map(StoreProxy::getUserId);
                    if (realProxyId.isPresent() && !Objects.equals(realProxyId.get().toString(), richSkusByShop.getExtra().get("refererId"))) {
                        richSkusByShop.getExtra().put("from", richSkusByShop.getRefererId().toString());
                    }
                    realProxyId.ifPresent(richSkusByShop::setRefererId);
                    realProxyId.map(Object::toString).ifPresent(idStr -> richSkusByShop.getExtra().put("refererId", idStr));
                }
                default ->
                        log.debug("{} outFrom:{} userId:{}", LogUtil.getClassMethodName("no-check-for"), outFrom, richOrder.getBuyer());
            }

        }
    }

    /**
     * 门店用户状态不符合条件的，不允许下单
     *
     * @param subStore
     */
    private void checkSubStoreUser(SubStore subStore) {
        var subStoreUser = userCacheHolder.findByUserId(subStore.getUserId()).orElse(null);
        if (subStoreUser == null) {
            throw new RuntimeException("门店对应的用户不存在，请更换门店进行下单");
        }

        if (subStoreUser.getStatus() == null) {
            throw new RuntimeException("门店对应的用户状态无效，请更换门店进行下单");
        }
        if (UserStatus.FROZEN.value() == subStoreUser.getStatus()) {
            throw new RuntimeException("门店对应的用户已被冻结，请更换门店进行下单");
        }

        var serviceProvider = serviceProviderCache.findBySubStoreUserIdAndShopId(
                subStore.getUserId(), subStore.getShopId());
        if (serviceProvider == null) {
            throw new RuntimeException("门店对应的服务商不存在，请更换门店进行下单");
        }

        var serviceProviderUser = userCacheHolder.findByUserId(serviceProvider.getUserId()).orElse(null);
        if (serviceProviderUser == null) {
            throw new RuntimeException("门店对应的服务商用户不存在，请更换门店进行下单");
        }
        if (serviceProviderUser.getStatus() == null) {
            throw new RuntimeException("门店对应的服务商用户状态无效，请更换门店进行下单");
        }
        if (UserStatus.FROZEN.value() == serviceProviderUser.getStatus()) {
            throw new RuntimeException("门店对应的服务商用户已被冻结，请更换门店进行下单");
        }
    }

    private void checkGuiderUser(Long guiderUserId) {
        var user = userCacheHolder.findByUserId(guiderUserId).orElse(null);
        if (user == null) {
            throw new RuntimeException("导购对应的用户不存在，请更换导购进行下单");
        }

        if (user.getStatus() == null) {
            throw new RuntimeException("导购对应的用户状态无效，请更换导购进行下单");
        }
        if (UserStatus.FROZEN.value() == user.getStatus()) {
            throw new RuntimeException("导购对应的用户已被冻结，请更换导购进行下单");
        }
    }

    /**
     * 用户下单后的事件发送
     *
     * @param shopOrderIds 订单创建结果（订单主体id)
     */
    private void orderCreatedEventSend(List<Long> shopOrderIds) {
        for (Long shopOrderId : shopOrderIds.stream().filter(Objects::nonNull).toList()) {
            EventSender.sendApplicationEvent(new OrderCreatedEvent(shopOrderId));
        }
    }

    /**
     * 根据传入的UserId作为查询会员价的依据 创建订单
     *
     * @param submittedOrder 订单信息
     * @param pid            查询会员价依据的用户Id
     * @return 订单号列表
     */
    @RequestMapping(value = "/api/membershipByPid/order", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Long> createMembershipByPid(@RequestBody SubmittedOrder
                                                    submittedOrder, @RequestParam(required = false) Long pid) {
        try {
            CommonUser user = UserUtil.requireLoginUser();
            RichOrder richOrder = richOrderMaker.full(submittedOrder, UserUtil.getCurrentUser());
            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                for (RichSku richSku : richSkusByShop.getRichSkus()) {
                    richSku.setPid(pid);
                }
            }

            //检查用户是否具有购买资格
            orderRuleEngine.canBuy(richOrder);

            //检查用户是否享有会员价并计算税费
            Predicate<RichSkusByShop> isBondedCheck = richSkusByShop -> richSkusByShop.getRichSkus().stream()
                    .map(RichSku::getSku).map(Sku::getType).anyMatch(Predicate.isEqual(1));
            boolean hasBonded = richOrder.getRichSkusByShops().stream().anyMatch(isBondedCheck);
            //含有保税商品，检查订购人实名认证信息
            checkPayerInfo(submittedOrder.getChannel(), richOrder, user, hasBonded);

            //检查商家营销活动的有效性
            promotionOngoingValidator.validate(richOrder);

            //检查优惠券是否符合使用规则
            for (CouponCheck couponCheck : couponChecks) {
                couponCheck.check(richOrder);
            }

            //检查用户是否可以享受对应的营销, 如果可以, 则计算营销, 需要实付的金额, 以及运费等
            charger.charge(richOrder, null);

            Response<List<Long>> rOrder = self.persistRichOrder(richOrder);

            if (!rOrder.isSuccess()) {
                log.error("failed to create {}, error code:{}", submittedOrder, rOrder.getError());
                throw new JsonResponseException(rOrder.getError());
            }

            final List<Long> shopOrderIds = rOrder.getResult();
            orderCreatedEventSend(shopOrderIds);
            return shopOrderIds;
        } catch (Exception e) {
            log.error("failed to create {}, cause:{}", submittedOrder, e);
            throw new JsonResponseException("order.create.fail");
        }
    }

    @RequestMapping(value = "/api/we/order", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Long> distributionCreate(@RequestBody DistributionSubmittedOrder
                                                 distributionSubmittedOrder, HttpServletRequest request) {

        try {
            CommonUser user = UserUtil.requireLoginUser();

            RichOrder richOrder = richOrderMaker.full(distributionSubmittedOrder, user);

            //检查用户是否具有购买资格
            orderRuleEngine.canBuy(richOrder);

            //检查用户是否享有会员价并计算税费
            Predicate<RichSkusByShop> isBondedCheck = richSkusByShop -> richSkusByShop.getRichSkus().stream()
                    .map(RichSku::getSku).map(Sku::getType).anyMatch(Predicate.isEqual(1));
            boolean hasBonded = richOrder.getRichSkusByShops().stream().anyMatch(isBondedCheck);

            //含有保税商品，检查订购人实名认证信息
            checkPayerInfo(distributionSubmittedOrder.getChannel(), richOrder, user, hasBonded);

            richOrder.setPromotionId(null);
            richOrder.setPromotions(null);
            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                richSkusByShop.setPromotionId(null);
                richSkusByShop.setShopPromotions(null);
                richSkusByShop.setShipmentPromotionId(null);
                richSkusByShop.setShipmentPromotions(null);
                for (RichSku richSku : richSkusByShop.getRichSkus()) {
                    richSku.setPromotionId(null);
                    richSku.setSkuPromotions(null);
                }
            }

            /*
             *检查用户是否可以享受对应的营销, 如果可以, 则计算营销, 需要实付的金额, 以及运费等
             */
            charger.charge(richOrder, null);

            Response<List<Long>> rOrder = self.persistRichOrder(richOrder);

            if (!rOrder.isSuccess()) {
                log.error("failed to create {}, error code:{}", distributionSubmittedOrder, rOrder.getError());
                throw new JsonResponseException(rOrder.getError());
            }

            final List<Long> shopOrderIds = rOrder.getResult();
            orderCreatedEventSend(shopOrderIds);
            return shopOrderIds;
        } catch (Exception e) {
            log.error("failed to create {}", distributionSubmittedOrder, e);
            throw new JsonResponseException(e.getMessage());
        }
    }

    @RequestMapping(value = "/api/buyer/order/cancel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean buyerCancelOrder(@RequestParam("orderId") Long orderId,
                                    @RequestParam(value = "orderType", defaultValue = "1") Integer orderType) {
        log.info("用户取消订单 {}", orderId);
        CommonUser commonUser = UserUtil.getCurrentUser();

        OrderLevel orderLevel = OrderLevel.fromInt(orderType);
        OrderBase orderBase = orderReadLogic.findOrder(orderId, orderLevel);
        if (!orderBase.getCreatedAt().before(new DateTime(Date.from(Instant.now())).minusMinutes(CANCEL_DELAY).toDate())) {
            throw new JsonResponseException(new Translate("订单需要５分钟后才能关闭").toString());
        }
        if (!Objects.equals(commonUser.getId(), orderBase.getBuyerId())) {
            log.error("the order(id={},type={}) not belong to buyer(id={})",
                    orderId, orderType, commonUser.getId());
            throw new JsonResponseException("order.not.belong.to.buyer");
        }
        JSONObject msg = new JSONObject();
        msg.put("orderId", orderId);
        rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_NOT_NEED_PUSH_TAG, msg.toString());

        if (orderBase.getOutFrom().equals(ShopExtra.communityOperation.getCode())) {
            // 社群运营模式下
            Map<String, Object> query = new HashMap<>();
            query.put("id", orderId);
            ShopOrder shopOrder = shopOrderLogic.getOne(query);
            log.info("社群运营模式 当前订单信息 {}", JSONUtil.toJsonStr(orderBase));
            msg.put("shopId", shopOrder.getShopId());
            msg.put("userId", shopOrder.getBuyerId());
            String feeDetailJson = shopOrder.getFeeDetailJson();
            ShopOrderFeeDetailDTO shopOrderFeeDetail = JSONUtil.toBean(feeDetailJson, ShopOrderFeeDetailDTO.class);
            msg.put("whetherUsedCoupon", !shopOrderFeeDetail.getCouponPrice().equals(BigDecimal.ZERO));
            msg.put("whetherUsedGiftMoney", !shopOrderFeeDetail.getGiftPrice().equals(BigDecimal.ZERO));
            msg.put("whetherUsedCash", !shopOrderFeeDetail.getCashPrice().equals(BigDecimal.ZERO));
            log.info("发送买家取消订单消息 {}", msg);
            rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_CANCEL_TAG, msg.toString());
            log.info("已发送买家取消订单消息");
        }


        return orderWriteLogic.updateOrderStatusByOrderEvent(orderBase, orderLevel, OrderEvent.BUYER_CANCEL);
    }

    @RequestMapping(value = "/api/seller/order/cancel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean sellerCancelOrder(@RequestParam("orderId") Long orderId,
                                     @RequestParam(value = "orderType", defaultValue = "1") Integer orderType) {
        log.info("商家取消订单 {}", orderId);
        CommonUser commonUser = UserUtil.getCurrentUser();

        OrderLevel orderLevel = OrderLevel.fromInt(orderType);
        OrderBase orderBase = orderReadLogic.findOrder(orderId, orderLevel);
        if (!orderBase.getCreatedAt().before(new DateTime(Date.from(Instant.now())).minusMinutes(CANCEL_DELAY).toDate())) {
            throw new JsonResponseException(new Translate("订单需要５分钟后才能关闭").toString());
        }
        if (!Objects.equals(commonUser.getShopId(), orderBase.getShopId())) {
            log.error("the order(id={},type={}) not belong to seller(shop id={})",
                    orderId, orderType, commonUser.getShopId());
            throw new JsonResponseException("order.not.belong.to.seller");
        }
        JSONObject msg = new JSONObject();
        msg.put("orderId", orderId);
        rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_NOT_NEED_PUSH_TAG, msg.toString());
        if (Objects.equals(orderBase.getOutFrom(), ShopExtra.communityOperation.getCode())) {
            // 社群运营模式下
            Map<String, Object> query = new HashMap<>();
            query.put("id", orderId);
            ShopOrder shopOrder = shopOrderLogic.getOne(query);
            log.info("社群运营模式 当前订单信息 {}", JSONUtil.toJsonStr(orderBase));

            msg.put("shopId", shopOrder.getShopId());
            msg.put("userId", shopOrder.getBuyerId());
            String feeDetailJson = shopOrder.getFeeDetailJson();
            ShopOrderFeeDetailDTO shopOrderFeeDetail = JSONUtil.toBean(feeDetailJson, ShopOrderFeeDetailDTO.class);
            msg.put("whetherUsedCoupon", !shopOrderFeeDetail.getCouponPrice().equals(BigDecimal.ZERO));
            msg.put("whetherUsedGiftMoney", !shopOrderFeeDetail.getGiftPrice().equals(BigDecimal.ZERO));
            msg.put("whetherUsedCash", !shopOrderFeeDetail.getCashPrice().equals(BigDecimal.ZERO));
            log.info("发送商家取消订单消息 {}", msg);
            rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_CANCEL_TAG, msg.toString());
            log.info("已发送商家取消订单消息");
        }
        return orderWriteLogic.updateOrderStatusByOrderEvent(orderBase, orderLevel, OrderEvent.SELLER_CANCEL);
    }

    /**
     * 商家端取消订单
     *
     * @param req 请求参数
     * @return
     */
    @PostMapping("api/seller/order/v2/cancel")
    public Result<Boolean> sellerV2CancelOrder(@RequestBody OrderCancelReqDto req) {
        if (req.getOrderType() == null) {
            req.setOrderType(1);
        }
        return Result.data(sellerCancelOrder(req.getOrderId(), req.getOrderType()));
    }


    @RequestMapping(value = "/api/order/delete", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean deleteShopOrder(@RequestParam Long orderId) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        OrderBase orderBase = orderReadLogic.findOrder(orderId, OrderLevel.SHOP);

        if (!Objects.equals(commonUser.getId(), orderBase.getBuyerId())) {
            log.error("the shopOrder(id={}) not belong to buyer(id={})",
                    orderId, commonUser.getId());
            throw new JsonResponseException("order.not.belong.to.buyer");
        }

        return orderWriteLogic.updateOrderStatusByOrderEvent(orderBase, OrderLevel.SHOP, OrderEvent.DELETE);
    }

    @RequestMapping(value = "/api/seller/order/authBatch", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean sellerAuthOrder(@RequestParam Long[] shopOrderIds, @RequestParam String
            sellerNote, @RequestParam(defaultValue = "false") Boolean skip) {
        return Stream.of(shopOrderIds).map(shopOrderId -> sellerAuthOrder(shopOrderId, sellerNote, skip)).reduce(true, Boolean::equals);
    }

    @RequestMapping(value = "/api/seller/order/auth", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean sellerAuthOrder(@RequestParam Long shopOrderId, @RequestParam String
            sellerNote, @RequestParam(defaultValue = "false") Boolean skip) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        log.info("Orders.sellerAuthOrder, 商家审核订单, currentUserId={}, shopOrderId={}, sellerNote={}",
                commonUser.getId(), shopOrderId, sellerNote);

        //检查商家是否开启商家审核
        Response<Shop> rShop = shopReadService.findById(commonUser.getShopId());
        if (!rShop.isSuccess()) {
            log.error("failed to find shop by id={}, error code: {}", commonUser.getShopId(), rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        Shop shop = rShop.getResult();
        Map<String, String> extra = shop.getExtra();
        if (extra == null || !Objects.equals(Boolean.TRUE.toString(), extra.get(ParanaConstants.SHOP_OPEN_ORDER_AUTH))) {
            log.warn("shop(id={}) want to auth order without opening orderAuth", shop.getId());
            //throw new JsonResponseException("shop.not.open.order.auth");
        }
        //检查操作权限
        OrderBase orderBase = orderReadLogic.findOrder(shopOrderId, OrderLevel.SHOP);
        if (!Objects.equals(orderBase.getShopId(), commonUser.getShopId())) {
            log.error("the shop order(id={}) not belong to seller(shop id={})", shopOrderId, commonUser.getShopId());
            throw new JsonResponseException("order.not.belong.to.seller");
        }
        var rShopOrder = shopOrderReadService.findById(shopOrderId);
        if (!rShopOrder.isSuccess() || rShopOrder.getResult() == null) {
            throw new JsonResponseException("order.find.fail");
        }
        Response<List<SkuOrder>> rSkuOrders = skuOrderReadService.findByShopOrderId(shopOrderId);
        if (!rSkuOrders.isSuccess() || CollectionUtils.isEmpty(rSkuOrders.getResult())) {
            log.error("find skuOrders from shopOrder fail (order id:{})", shopOrderId);
            throw new JsonResponseException("order.find.fail");
        }
        //审核
        if (orderWriteLogic.sellerAuthOrder(shopOrderId, rSkuOrders.getResult(), sellerNote)) {
            return false;
        }
        Optional.ofNullable(paymentReadService.findByOrderIdAndOrderLevel(orderBase.getId(), OrderLevel.SHOP).getResult())
                .orElseGet(ArrayList::new)
                .stream()
                .filter(paidPayment -> paidPayment.getStatus() > 0)
                .map(Payment::getId)
                .forEach(needPushPaymentId -> EventSender.publish(new PaymentDeclareInlineEvent(needPushPaymentId)));
        return true;
    }

    @RequestMapping(value = "/api/seller/order/auth/v2", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Boolean> sellerAuthOrderV2(@RequestParam Long shopOrderId, @RequestParam String
            sellerNote, @RequestParam(defaultValue = "false") Boolean skip) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        log.info("Orders.sellerAuthOrder, 商家审核订单, currentUserId={}, shopOrderId={}, sellerNote={}",
                commonUser.getId(), shopOrderId, sellerNote);

        //检查商家是否开启商家审核
        Response<Shop> rShop = shopReadService.findById(commonUser.getShopId());
        if (!rShop.isSuccess()) {
            log.error("failed to find shop by id={}, error code: {}", commonUser.getShopId(), rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        Shop shop = rShop.getResult();
        Map<String, String> extra = shop.getExtra();
        if (extra == null || !Objects.equals(Boolean.TRUE.toString(), extra.get(ParanaConstants.SHOP_OPEN_ORDER_AUTH))) {
            log.warn("shop(id={}) want to auth order without opening orderAuth", shop.getId());
            //throw new JsonResponseException("shop.not.open.order.auth");
        }
        //检查操作权限
        OrderBase orderBase = orderReadLogic.findOrder(shopOrderId, OrderLevel.SHOP);
        if (!Objects.equals(orderBase.getShopId(), commonUser.getShopId())) {
            log.error("the shop order(id={}) not belong to seller(shop id={})", shopOrderId, commonUser.getShopId());
            throw new JsonResponseException("order.not.belong.to.seller");
        }
        var rShopOrder = shopOrderReadService.findById(shopOrderId);
        if (!rShopOrder.isSuccess() || rShopOrder.getResult() == null) {
            throw new JsonResponseException("order.find.fail");
        }
        Response<List<SkuOrder>> rSkuOrders = skuOrderReadService.findByShopOrderId(shopOrderId);
        if (!rSkuOrders.isSuccess() || CollectionUtils.isEmpty(rSkuOrders.getResult())) {
            log.error("find skuOrders from shopOrder fail (order id:{})", shopOrderId);
            throw new JsonResponseException("order.find.fail");
        }
        //审核
        if (orderWriteLogic.sellerAuthOrder(shopOrderId, rSkuOrders.getResult(), sellerNote)) {
            return Result.data(false);
        }
        Optional.ofNullable(paymentReadService.findByOrderIdAndOrderLevel(orderBase.getId(), OrderLevel.SHOP).getResult())
                .orElseGet(ArrayList::new)
                .stream()
                .filter(paidPayment -> paidPayment.getStatus() > 0)
                .map(Payment::getId)
                .forEach(needPushPaymentId -> EventSender.publish(new PaymentDeclareInlineEvent(needPushPaymentId)));
        return Result.data(true);
    }

    private void judgePaperNo(String paperNo) {
        if (!paperNo.matches(PAYER_ID_PATTERN)) {
            throw new JsonResponseException(400, "user.certification.paperNo.invalid");
        }
    }

    private void checkPayerInfo(Integer channel, RichOrder richOrder, CommonUser user, boolean hasBonded) {
        var channelEnum = OrderCreateChannelEnum.parse(channel);
        if (channelEnum == null) {
            return;
        }

        switch (channelEnum) {
            case PC_WEB -> {
                if (ObjectUtils.isEmpty(richOrder.getPayerName()) || ObjectUtils.isEmpty(richOrder.getPayerId())) {
                    log.warn("submitOrder has no payer info[{}], when submit order with bonded item", user.getId());
                    // 尝试使用默认的验证信息
                    if (ObjectUtils.isEmpty(richOrder.getReceiverInfo())) {
                        if (hasBonded) {
                            throw new JsonResponseException("请填写收货人的身份证信息, 因为是跨境商品");
                        } else {
                            return;
                        }
                    }
                    richOrder.setPayerId(richOrder.getReceiverInfo().getPaperNo());
                    richOrder.setPayerName(richOrder.getReceiverInfo().getReceiveUserName());
                    if (ObjectUtils.isEmpty(richOrder.getPayerId())) {
                        if (hasBonded) {
                            throw new JsonResponseException("请填写收货人的身份证信息, 因为是跨境商品");
                        } else {
                            return;
                        }
                    }
                }
                judgePaperNo(richOrder.getPayerId());

                //当前只针对pc网页端提交的订单做实名验证并保存
                realNameCertificationManager.saveUserCertification(richOrder.getBuyer().getId(), richOrder.getPayerName(), richOrder.getPayerId());
            }
            case WX_APP -> {
                Response<Optional<UserCertification>> userCertificationResponse = userCertificationReadService.findDefaultByUserId(user.getId());
                if (!userCertificationResponse.isSuccess()) {
                    log.error("failed to find default user certification by userId={}, error code: {}", user.getId(), userCertificationResponse.getError());
                    if (hasBonded) {
                        throw new JsonResponseException(userCertificationResponse.getError());
                    } else {
                        return;
                    }
                }
                Optional<UserCertification> userCertificationOptional = userCertificationResponse.getResult();
                if (userCertificationOptional.isPresent()) {
                    UserCertification userCertification = userCertificationOptional.get();
                    richOrder.setPayerName(userCertification.getPaperName());
                    richOrder.setPayerId(userCertification.getPaperNo());
                } else {
                    log.warn("user(id={}) has no default certification, when submit order with bonded item on wxa", user.getId());
                    if (hasBonded) {
                        throw new JsonResponseException("default.user.certification.not.exist");
                    }
                }
            }
        }
    }

    @PostMapping("/api/order/refundOrderExcelExport/v2")
    public Result<Long> refundOrderExcelExport(@RequestBody RefundCriteria req) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Result.fail("请重新登录");
        }
        log.debug("Orders.refundOrderExcelExport, refundCriteria={}", JSON.toJSONString(req));

        try {
            //导出文件名
            String fileName = DataExportTaskTypeEnum.REFUND_CENTER.getDescription() + DateUtil.getDateString() + ".xlsx";

            //查询参数
            RefundExportParameter parameter = refundConvertor.covertV2(user.getShopId(), req);

            //执行导出
            DataExportTask task = refundExporter.asyncExport(user, fileName, parameter);

            return Result.data(task.getId());
        } catch (Exception ex) {
            log.error("Orders.refundOrderExcelExport failed, shopId={}, userId={}, refundCriteria={}",
                    user.getShopId(), user.getId(), JSON.toJSONString(req), ex);
            return Result.fail(ex.getMessage());
        }
    }

    @RequestMapping("/api/order/orderExcelExport")
    public Result<Long> orderExcelExport(@RequestParam(required = false) Map<String, Object> param) throws IOException {
        //清洗并构造查询参数
        var orderCriteria = convert(param);

        //查询要导出的订单id
        List<Long> orderIds = shopOrderReadService.listIdsBy(orderCriteria).getResult();
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new JsonResponseException(new Translate("没有可以导出的数据").toString());
        }

        try {
            //导出文件名
            String fileName = DataExportTaskTypeEnum.ORDER_MANAGEMENT.getDescription() + DateUtil.getDateString() + ".xlsx";
            //查询参数
            OrderExporter.OrderExportParameter parameter = new OrderExporter.OrderExportParameter(orderIds);

            //执行导出
            DataExportTask task = orderExporter.asyncExport(UserUtil.getCurrentUser(), fileName, parameter);

            return Result.data(task.getId());
        } catch (Exception ex) {
            log.error("Orders.orderExcelExport error ", ex);
            return Result.fail(ex.getMessage());
        }
    }

    private EnhanceOrderCriteria convert(Map<String, Object> param) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            throw new JsonResponseException(new Translate("请以管理者身份登录").toString());
        }

        var orderCriteria = JsonObject.mapFrom(param).mapTo(EnhanceOrderCriteria.class);
        if (orderCriteria.getShopId() == null) {
            orderCriteria.setShopId(user.getShopId());
        }

        //解析前端传递的发货日期
        orderCriteria.handleShipmentStartEnd();

        //最大导出数量
        orderCriteria.setSize(10000);

        orderReader.limitRefererIdByRefererMobile(orderCriteria);
        orderReader.limitUserIdBySubStoreName(orderCriteria);
        orderReader.limitOutShopIdByServiceProviderName(orderCriteria, user.getShopId());
        orderReader.limitShopOrderIdByPaymentOutId(orderCriteria);

        if (orderCriteria.getStartAt() == null && orderCriteria.getEndAt() == null) {
            orderCriteria.setStartAt(Date.from(ZonedDateTime.now().minusMonths(3).toInstant()));
        }

        if (orderCriteria.getStartAt() != null) {
            orderCriteria.setStartAt(DateUtil.withTimeAtStartOfDay(orderCriteria.getStartAt()));
        }
        if (orderCriteria.getEndAt() != null) {
            orderCriteria.setEndAt(DateUtil.withTimeAtEndOfDay(orderCriteria.getEndAt()));
        }
        if (orderCriteria.getOrderFlag() != null && orderCriteria.getOrderFlag() == 1) {
            orderCriteria.initOrderFlags().getOrderFlags().add(OrderFlagEnum.PROMOTION_LIMITED_ITEM.getValue());
        }

        return orderCriteria;
    }

    @GetMapping("/api/order/outFromList")
    public List<Map<String, String>> orderOutFromList() {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            throw new JsonResponseException("user.not.login");
        }
        Shop shop = shopCacheHolder.findShopById(commonUser.getShopId());
        Map<String, String> shopExtra = Optional.ofNullable(shop.getExtra()).orElseGet(HashMap::new);

        List<Map<String, String>> result = new ArrayList<>(8);

        BiFunction<String, String, Map<String, String>> getEntity = (name, value) -> ImmutableBiMap.of("name", name, "value", value);

        if (shop.getUserId().equals(commonUser.getId())) {
            result.add(getEntity.apply(new Translate("全部订单").toString(), ""));
            if (shop.getType() != 1) {
                result.add(getEntity.apply(new Translate("导入订单").toString(), OrderOutFrom.EXCEL_IMPORT.Code()));
            }
        }

        switch (shopExtra.getOrDefault(ShopExtra.SalesPattern.getCode(), "commonShop")) {
            case "subStore" ->
                    result.add(getEntity.apply(new Translate("门店订单").toString(), OrderOutFrom.SUB_STORE.Code()));
            case "ladderDistribution" ->
                    result.add(getEntity.apply(new Translate("分销订单").toString(), OrderOutFrom.LEVEL_Distribution.Code()));
            case "weShop" -> result.add(getEntity.apply(Translate.of("供销订单"), OrderOutFrom.WE_SHOP.Code()));
        }

        return result;
    }
}
