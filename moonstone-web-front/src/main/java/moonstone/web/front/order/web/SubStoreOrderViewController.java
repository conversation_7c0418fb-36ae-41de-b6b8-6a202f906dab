package moonstone.web.front.order.web;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.CommonUser;
import moonstone.common.model.EntityBase;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.OrderGroup;
import moonstone.order.dto.RefundCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.RefundStatusView;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.web.core.order.OrderReadLogic;
import moonstone.web.core.order.RefundReadLogic;
import moonstone.web.core.order.dto.OrderGroupViewObject;
import moonstone.web.core.order.dto.RefundDetailVO;
import moonstone.web.core.order.dto.RefundPageVO;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.front.component.order.OrderAuthStatusDescribeHelper;
import moonstone.web.front.component.order.OrderExtraInfoDecorateHelper;
import moonstone.web.front.component.order.dto.EnhanceOrderCriteria;
import moonstone.web.front.component.order.dto.RefundQueryCriteria;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@AllArgsConstructor
public class SubStoreOrderViewController {
    OrderReadLogic orderReadLogic;
    ServiceProviderCache serviceProviderCache;
    SubStoreCache subStoreCache;
    GuiderCache guiderCache;
    OrderExtraInfoDecorateHelper orderExtraInfoDecorateHelper;
    OrderAuthStatusDescribeHelper orderAuthStatusDescribeHelper;
    SubStoreReadService subStoreReadService;
    RefundReadLogic refundReadLogic;

    @GetMapping("/api/order/substore/paging")
    public Paging<OrderGroupViewObject> paging(EnhanceOrderCriteria criteria) {
        Long user = UserUtil.getUserId();
        Long shopId = criteria.getShopId();
        if (user == null || shopId == null) {
            throw Translate.exceptionOf("参数异常");
        }
        log.debug("SubStoreOrderViewController.paging, shopId={}, userId={}  criteria={}", shopId, user, JSON.toJSONString(criteria));
        criteria.setOutFrom(OrderOutFrom.SUB_STORE.Code());
        criteria.fulfillOrderStatusBySettleType();
        limitOrderSearchInfoByIdentify(user, shopId, criteria);
        DateUtil.washTheDate(criteria, EnhanceOrderCriteria.class);
        if (criteria.getStartAt() != null) {
            if (criteria.getStartAt().after(Date.from(ZonedDateTime.now().minusMonths(1).toInstant()))) {
                criteria.setStartAt(Date.from(ZonedDateTime.now().minusMonths(3).toInstant()));
            }
        }
        // query the order
        criteria.setOrCreatedBy(true);
        criteria.setCreatedBy(user);
        Paging<OrderGroup> originPaging = orderReadLogic.pagingOrder(criteria).getResult();
        // 打包额外的数据, 门店的数据
        return orderExtraInfoDecorateHelper.decorateOrderWithExtraInfo(
                orderAuthStatusDescribeHelper.describeAuthStatus(originPaging));
    }

    private void limitOrderSearchInfoByIdentify(Long user, Long shopId, EnhanceOrderCriteria orderCriteria) {
        // set the order limit 3 month
        // Date orderAfter = Date.from(java.time.ZonedDateTime.now().minusMonths(3).toInstant());
        // orderCriteria.setStartAt(orderAfter);
        // check identify
        if (serviceProviderCache.findServiceProviderByShopIdAndUserId(shopId, user).isPresent()) {
            serviceProviderCache.findBelongSubStoreUserId(shopId, user)
                    .map(list -> list.stream()
                            .map(userId -> subStoreCache.findByShopIdAndUserId(shopId, userId).map(EntityBase::getId).orElse(null))
                            .filter(Objects::nonNull)
                            .map(Objects::toString).collect(Collectors.toList()))
                    .ifPresent(orderCriteria::setOutShopIds);
        }
        // set the belong information
        subStoreCache.findByShopIdAndUserId(shopId, user).ifPresent(subStore -> orderCriteria.setOutShopId(subStore.getId() + ""));
        // set the guider information
        guiderCache.findByShopIdAndUserId(shopId, user).ifPresent(guider -> {
            orderCriteria.setRefererId(guider.getStoreGuiderId());
            orderCriteria.setOutShopId(guider.getSubStoreId() + "");
        });
    }

    @GetMapping(value = "/api/order/substore/count-by-status")
    public Map<String, Long> countByStatusAll(EnhanceOrderCriteria orderCriteria, @RequestParam(defaultValue = "-1") Integer way, @RequestParam(required = false) Long weShopId) {
        log.info("分销商城 查询各个订单状态下的订单数量 请求路径 orderCriteria {} way {} weShopId {}", JSONUtil.toJsonStr(orderCriteria),way,weShopId);
        Long toBeShipped;//待付款 status=0
        List<Integer> status = new ArrayList<>();
        status.add(0);
        orderCriteria.setStatus(status);
        toBeShipped = countByStatus(orderCriteria, way);

        Long shipped;//待发货 status=1
        status = new ArrayList<>();
        status.add(1);
        orderCriteria.setStatus(status);
        shipped = countByStatus(orderCriteria, way);

        Long toBeReceived;//待收货 status=2
        status = new ArrayList<>();
        status.add(2);
        orderCriteria.setStatus(status);
        toBeReceived = countByStatus(orderCriteria, way);

        Map<String, Long> map = new HashMap<>(8);
        map.put(CountIndex.tobeshipped.name(), toBeShipped);
        map.put(CountIndex.shipped.name(), shipped);
        map.put(CountIndex.toBeReceived.name(), toBeReceived);


        return map;
    }

    enum CountIndex {
        /**
         * index of display
         */
        tobeshipped,
        shipped,
        toBeReceived
    }

    @GetMapping(value = "/api/order/substore/count-by-status-one")
    public Long countByStatus(EnhanceOrderCriteria orderCriteria, @RequestParam(defaultValue = "0") Integer way) {
        CommonUser user = java.util.Optional.ofNullable((CommonUser) UserUtil.getCurrentUser()).orElseThrow(() -> new JsonResponseException("user.not.login"));
        DateUtil.washTheDate(orderCriteria, OrderCriteria.class);
        switch (way) {
            case 0 -> /// 购买端
                    orderCriteria.setBuyerId(user.getId());
            case 1 -> /// 商户端
                    orderCriteria.setShopId(user.getShopId());
            case 2 -> {
                /// 门店端
                List<SubStore> subStores = subStoreReadService.findByUserIdAndStatus(user.getId(), null).getResult();
                if (subStores == null || subStores.isEmpty()) {
                    return 0L;
                }
                orderCriteria.setOutShopId(subStores.get(0).getId() + "");
                orderCriteria.setOutFrom(OrderOutFrom.SUB_STORE.Code());
            }
            case 3 -> {
                /// 导购员端
                orderCriteria.setRefererId(user.getId());
                orderCriteria.setOutFrom(OrderOutFrom.SUB_STORE.Code());
            }
            default -> {

            }
        }
        limitOrderSearchInfoByIdentify(user.getId(), orderCriteria.getShopId(), orderCriteria);
        //log.debug("{} order criteria:{} user:{}", LogUtil.getClassMethodName(), orderCriteria, user);
        Response<Long> countResult = orderReadLogic.countShopOrder(orderCriteria);

        if (countResult.isSuccess()) {
            return countResult.getResult();
        }
        throw new JsonResponseException(countResult.getError());
    }

    /**
     * 退款单列表
     */
    @GetMapping("/api/order/substore/findRefundList")
    public Paging<RefundPageVO> findRefundList(RefundQueryCriteria request) {
        Long userId = UserUtil.getUserId();
        if (userId == null || request == null || request.getShopId() == null || StringUtils.isBlank(request.getRefundStatus())) {
            return Paging.empty();
        }
        List<Integer> statusList = OrderStatus.getRefundStatusByRefundView(Objects.requireNonNull(RefundStatusView.from(request.getRefundStatus())));
        if (CollectionUtils.isEmpty(statusList)) {
            return Paging.empty();
        }

        RefundCriteria criteria = refundReadLogic.getCriteriaByCurrentUser(userId, request.getShopId());
        if (criteria == null) {
            return Paging.empty();
        }

        criteria.setShopId(request.getShopId());
        criteria.setStatus(statusList);
        criteria.setPageNo(request.getPageNum());
        criteria.setSize(request.getPageSize());
        return refundReadLogic.find(criteria);
    }

    /**
     * 退款单详情
     */
    @GetMapping("/api/order/substore/findRefundDetail")
    public RefundDetailVO findRefundDetail(@RequestParam Long refundId) {
        return refundReadLogic.findById(refundId);
    }
}
