package moonstone.web.front.order;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.constants.RequestParams;
import io.terminus.pay.model.RefundParams;
import io.terminus.pay.model.RefundQueryParams;
import io.terminus.pay.model.TradeResult;
import io.terminus.pay.service.PayChannel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.UUID;
import moonstone.order.dto.RefundCriteria;
import moonstone.order.dto.RefundList;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.Refund;
import moonstone.order.service.RefundReadService;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.order.component.DefaultRefundParamsMaker;
import moonstone.web.core.refund.event.RefundSuccessCallbackEvent;
import moonstone.web.core.refund.service.RefundCallBackService;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Date;

/**
 * Created by yangzefeng on 16/9/28
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@AllArgsConstructor
public class RefundNotifies {

    private final ChannelRegistry channelRegistry;
    private final RefundCallBackService refundCallBackService;


    private final RefundReadService refundReadService;
    private final DefaultRefundParamsMaker defaultRefundParamsMaker;

    private final RocketMQProducer rocketMQProducer;

    /**
     * 退款回调函数,所有真实发生退款的接口都应该回调到这里
     */
    @RequestMapping(value = "/api/refund/notify/{channel}/account/{accountNo}", produces = MediaType.TEXT_PLAIN_VALUE)
    @ResponseBody
    public String postRefund(@PathVariable("channel") String channel, @PathVariable("accountNo") String accountNo, HttpServletRequest request) {
        try {
            log.info("RefundNotifies.postRefund, channel={}, accountNo={}", channel, accountNo);
            PayChannel paymentChannel = channelRegistry.findChannel(channel);
            request.setAttribute(RequestParams.ACCOUNT, accountNo);

            if (!channel.contains("wechatpay")) {
                paymentChannel.verify(request);
            }

            TradeResult refundedInfo = paymentChannel.refundCallback(request);
            log.info("实际退款回调结果 {}", JSONUtil.toJsonStr(refundedInfo));
            if (refundedInfo.isFail()) {
                return "fail";
            }
            JSONObject msg = new JSONObject();
            msg.set("outId", refundedInfo.getMerchantSerialNo());
            msg.set("tradeAt", refundedInfo.getTradeAt());
            rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.REFUND_CALLBACK_SUCCESS_TAG, msg.toString());
            return refundedInfo.getCallbackResponse();
        } catch (Exception e) {
            log.error("fail to call back refund by request params ({}), cause:{}",
                    request.getParameterMap(), Throwables.getStackTraceAsString(e));
            return "fail";
        }
    }

    @RequestMapping(value = "/api/refund/{channel}/account/{accountNo}/queryRefund", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean queryRefund(@PathVariable("channel") String channel, @PathVariable("accountNo") String accountNo,
                               @RequestParam(required = false) String tradeNo, @RequestParam(required = false) String gatewayTradeNo,
                               @RequestParam(required = false) String refundNo, @RequestParam(required = false) String gatewayRefundNo) {
        if (ObjectUtils.isEmpty(tradeNo) && ObjectUtils.isEmpty(gatewayTradeNo) && ObjectUtils.isEmpty(refundNo) && ObjectUtils.isEmpty(gatewayRefundNo)) {
            throw new JsonResponseException("Need at least one piece of No");
        }
        PayChannel paymentChannel = channelRegistry.findChannel(channel);
        RefundQueryParams refundQueryParams = new RefundQueryParams();
        refundQueryParams.setChannel(channel);
        refundQueryParams.setSellerNo(accountNo);
        refundQueryParams.setTradeNo(tradeNo);
        refundQueryParams.setGatewayTradeNo(gatewayTradeNo);
        refundQueryParams.setRefundNo(refundNo);
        refundQueryParams.setGatewayRefundNo(gatewayRefundNo);
        TradeResult tradeResult = paymentChannel.refundStats(refundQueryParams);
        log.debug("tradeResult: {}", tradeResult);
        if (tradeResult.getStatus() == 2) {
            refundCallBackService.refundCallBack(tradeResult.getMerchantSerialNo(), tradeResult.getTradeAt());
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    @Scheduled(cron = "0 0/3 * * * ?")
    public void scanRefund() {
        RefundCriteria refundCriteria = new RefundCriteria();
        refundCriteria.setStatus(Collections.singletonList(OrderStatus.REFUND_PROCESSING.getValue()));
        refundCriteria.setSize(100);
        Date last = Date.from(ZonedDateTime.now().minusMonths(6).toInstant());
        refundCriteria.setStartAt(last);
        int i = 1;
        while (i > 0) {
            refundCriteria.setPageNo(i);
            Paging<RefundList> refundPaging = refundReadService.findBy(refundCriteria).getResult();
            for (RefundList refundList : refundPaging.getData()) {
                Refund refund = refundList.getRefund();
                // skip no payment require refund
                if (refund.getPaymentId() == null || refund.getPaymentId() <= 0L) {
                    continue;
                }
                try {
                    RefundParams refundParams = defaultRefundParamsMaker.makeParams(refund);
                    queryRefund(refundParams.getChannel(), refundParams.getSellerNo(), refundParams.getTradeNo(), refundParams.getPaymentCode()
                            , refundParams.getRefundNo(), refund.getRefundSerialNo());
                } catch (Exception refundQueryError) {
                    log.error("{} fail to scan refund[Id => {}, PaymentId => {}, PaySerial => {}, RefundAccountNo => {}]", LogUtil.getClassMethodName(), refund.getId(), refund.getPaymentId(), refund.getPaySerialNo(), refund.getRefundAccountNo(), refundQueryError);
                }
            }
            if (refundPaging.getData().size() != refundCriteria.getSize()) {
                i = -1;
            } else {
                i++;
            }
        }
    }

    @PostMapping("/api/refund/callback/manual/trigger")
    public boolean refundCallback(Long[] refundIds) {
        log.info("RefundNotifies.refundCallback, 接收到入参={}", JSON.toJSONString(refundIds));
        for (Long refundId : refundIds) {
            Refund refund = refundReadService.findById(refundId).getResult();
            if (refund.getPaymentId() != null && refund.getPaymentId() >= 0L) {
                if (refund.getStatus() != OrderStatus.REFUND_PROCESSING.getValue()) {
                    continue;
                }
                EventSender.sendApplicationEvent(new RefundSuccessCallbackEvent(refundId, UUID.randomUUID().toString(), LocalDateTime.now()));
            }
        }
        return true;
    }
}
