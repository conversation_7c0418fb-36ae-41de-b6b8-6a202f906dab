package moonstone.web.front.order.service.impl;


import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.event.OrderConfirmCompletedEvent;
import moonstone.event.OrderConfirmEvent;
import moonstone.order.api.FlowPicker;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderRelation;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.ShipmentWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.showcase.mq.message.OrderConfirmedMessage;
import moonstone.web.core.events.trade.listener.OrderStatusUpdater;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.front.profit.application.AgentPayOrderCreateRecordApp;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Service
public class ShipmentConfirmService {
    @Autowired
    ShopOrderReadService shopOrderReadService;
    @Autowired
    OrderWriteService orderWriteService;
    @Autowired
    ShipmentWriteService shipmentWriteService;
    @Autowired
    FlowPicker flowPicker;

    @Resource
    private OrderStatusUpdater orderStatusUpdater;

    @Resource
    private AgentPayOrderCreateRecordApp agentPayOrderCreateRecordApp;

    @Resource
    private RocketMQProducer rocketMQProducer;

    /**
     * 确定收货 这个事务必须为最后启动的 如果外部失败不会导致这个回滚
     *
     * @param orderId ShopOrder 订单Id
     */
    @Transactional(rollbackFor = Exception.class)
    public void confirmOrder(Long orderId) {
        var order = shopOrderReadService.findById(orderId).getResult();
        if (order == null) {
            throw Translate.exceptionOf("该订单不存在");
        }
        if (!flowPicker.pick(order, OrderLevel.SHOP).operationAllowed(order.getStatus(), OrderEvent.CONFIRM.toOrderOperation())) {
            throw Translate.exceptionOf("订单已经退款或者已收货，无法确定收货");
        }
        // fix the error Order Status
        if (order.getCreatedAt().before(Date.from(LocalDateTime.now().minusYears(1).atZone(ZoneId.systemDefault()).toInstant()))) {
            // auto fix the status, stop the error stage
            Map<String, String> extra = Optional.ofNullable(order.getExtra())
                    .map(HashMap::new).orElseGet(HashMap::new);
            if (extra.containsKey(ShopOrderExtra.status.name())) {
                log.error("{} ShopOrder[Id => {}, Status => {}, Extra => {}] meet unexpected status", LogUtil.getClassMethodName(),
                        orderId, order.getStatus(), extra);
            } else {
                extra.put(ShopOrderExtra.status.name(), order.getStatus().toString());
                orderWriteService.updateOrderExtra(orderId, OrderLevel.SHOP, extra);
                orderWriteService.updateOrderStatus(orderId, OrderLevel.SHOP, OrderStatus.ERROR.getValue());
                return;
            }
        }
        shipmentWriteService.updateStatusByOrderIdAndOrderLevel(orderId, OrderLevel.SHOP, OrderStatus.SHIPPED.getValue());

        // 更新订单状态
        OrderRelation orderRelation = new OrderRelation() {
        };
        orderRelation.setOrderId(orderId);
        orderRelation.setOrderType(OrderLevel.SHOP.getValue());
        orderStatusUpdater.update(Collections.singletonList(orderRelation), OrderEvent.CONFIRM.toOrderOperation());

        agentPayOrderCreateRecordApp.createRecord(orderId);

        OrderConfirmedMessage confirmedMessage = new OrderConfirmedMessage();
        confirmedMessage.setShopOrderId(orderId);
        SendResult sendResult = rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC,
                RocketMQConstant.ORDER_CONFIRMED_TAG,
                JSONObject.toJSONString(confirmedMessage));
        log.info("订单发货消息发送成功 {} {}", orderId, sendResult.getMsgId());

        EventSender.sendApplicationEvent(new OrderConfirmEvent(orderId, OrderLevel.SHOP.getValue()));
        EventSender.sendApplicationEvent(new OrderConfirmCompletedEvent(orderId, OrderLevel.SHOP.getValue()));
    }

}
