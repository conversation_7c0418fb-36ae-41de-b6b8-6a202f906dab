package moonstone.web.front.order;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.common.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.PromotionCacher;
import moonstone.cache.PromotionToolCacher;
import moonstone.common.api.APIResp;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.model.Sku;
import moonstone.item.service.GbActivityConfigSkuReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.RichOrderMaker;
import moonstone.order.dto.*;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.rule.OrderRuleEngine;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.promotion.api.Behavior;
import moonstone.promotion.api.CouponCheck;
import moonstone.promotion.api.PromotionTool;
import moonstone.promotion.component.PreviewPromotions;
import moonstone.promotion.enums.PromotionStatus;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.service.PromotionReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.user.enums.UserCertificationVerifyStatusEnum;
import moonstone.user.model.StoreProxy;
import moonstone.user.service.UserCertificationReadService;
import moonstone.web.core.user.StoreProxyManager;
import moonstone.web.front.item.app.ItemConvertor;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public record PreviewOrders(
        RichOrderMaker richOrderMaker,
        OrderRuleEngine orderRuleEngine,
        PreviewPromotions previewPromotions,
        PromotionCacher promotionCacher,
        PromotionToolCacher promotionToolCacher,
        List<CouponCheck> couponCheckers,
        ReceiverInfoReadService receiverInfoReadService,
        StoreProxyManager storeProxyManager,
        UserCertificationReadService userCertificationReadService,
        PromotionReadService promotionReadService,
        ShopReadService shopReadService,
        ItemConvertor itemConvertor,
        SkuReadService skuReadService,
        GbActivityConfigSkuReadService gbActivityConfigSkuReadService ) {

    private final static TypeReference<List<SubmittedSku>> LIST_OF_SUBMITTED_SKU =
            new TypeReference<>() {
            };

    private final static TypeReference<List<DistributionPreviewSubmittedSku>> LIST_OF_DISTRIBUTION_PREVIEW_SUBMITTED_SKU =
            new TypeReference<>() {
            };

    /**
     * 订单预览
     *
     * @param skuIdAndQuantity skuId及数量, 是List<SubmittedSku>的json表示形式
     * @param channel          下单渠道, 1-pc, 2-手机
     *                         type 是否为积分商品 1积分类型商品 0 非积分商品
     * @return 订单信息
     */
    @RequestMapping(value = "/api/order/preview", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<RichOrder> preview(@RequestParam("data") String skuIdAndQuantity,
                                       @RequestParam(name = "promotionId", required = false) Long promotionId,
                                       @RequestParam(value = "channel", required = false) Integer channel,
                                       @RequestParam(value = "type", required = false) String integralType,
                                       @RequestParam(value = "shopId") Long shopId) throws Exception {
        final CommonUser buyer = UserUtil.getCurrentUser();
        if (buyer == null) {
            throw new JsonResponseException(new Translate("未登录, 请登录").toString());
        }
        List<SubmittedSku> submittedSkus = JsonMapper.JSON_NON_EMPTY_MAPPER.getMapper()
                .readValue(skuIdAndQuantity, LIST_OF_SUBMITTED_SKU);
        if (channel == null) {
            channel = 2;
        }

        var response = previewOrder(submittedSkus, promotionId, buyer.getId(), channel.longValue(), shopId);

        if (!response.ok()) {
            return Response.fail(response.getErrorMsg());
        }
        return Response.ok(response.getData());
    }

    @RequestMapping(value = "/api/order/preview/check", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public APIResp<Boolean> preCheck(@RequestParam("data") String skuIdAndQuantity,
                                     @RequestParam(name = "promotionId", required = false) Long promotionId,
                                     @RequestParam(value = "channel", required = false) Integer channel,
                                     @RequestParam(value = "type", required = false) String integralType,
                                     @RequestParam(value = "shopId") Long shopId,
                                     @RequestParam(value = "marketingToolId",required = false) Integer marketingToolId,
                                     @RequestParam(value = "activityId", required = false) Integer activityId) throws Exception {
        List<SubmittedSku> submittedSkus = JsonMapper.JSON_NON_EMPTY_MAPPER.getMapper()
                .readValue(skuIdAndQuantity, LIST_OF_SUBMITTED_SKU);

        log.info("预下单校验 {}", skuIdAndQuantity);

        for (SubmittedSku skus : submittedSkus) {
            if (ObjUtil.isEmpty(skus.getSkuId())) {
                throw new ApiException("当前商品已售罄，请重新选择");
            }
            if (ObjUtil.isEmpty(skus.getMarketingToolId())) {
                Sku sku = skuReadService.findSkuById(skus.getSkuId()).getResult();
                // 1. 购买数不能超过限购数
                String orderQuantityLimit = sku.getExtraMap().get(SkuExtraIndex.orderQuantityLimit.getCode());
                if (StrUtil.isNotBlank(orderQuantityLimit)) {
                    if (Integer.parseInt(orderQuantityLimit) < skus.getQuantity()) {
                        throw new ApiException("购买数不能超过限购数");
                    }
                }
                // 2. 传入数量不能超过当下库存
                if (sku.getStockQuantity() < skus.getQuantity()) {
                    throw new ApiException("传入数量不能超过当下库存");
                }
            } else {
                // 1：活动库存还有 限购数 的校验
               gbActivityConfigSkuReadService.validateSkuQuantity(skus.getSkuId(), skus.getMarketingToolId(), skus.getActivityId(), skus.getQuantity());
            }
        }
        // 检查来源是否一致
        Set<Integer> goodsSourceTypeSet = submittedSkus.stream()
                .map(SubmittedSku::getSourceType)
                .collect(Collectors.toSet());
        if (goodsSourceTypeSet.size() != 1) {
            throw new ApiException("所选商品发货仓来源不一致，请联系商家后重新下单");
        }
        return APIResp.ok(true);
    }

    @PostMapping(value = "/api/v1/order/preview")
    public APIResp<RichOrder> previewOrder(@RequestBody List<SubmittedSku> submittedSkus
            , @RequestParam(required = false) Long promotionId
            , Long userId
            , @RequestParam(defaultValue = "2") Long channel
            , @RequestParam(value = "shopId", required = false) Long shopId) {
        try {
            if (ObjectUtils.isEmpty(submittedSkus)) {
                log.warn("no sku specified, skip");
                throw new JsonResponseException("no sku selected");
            }
            PreviewPromotions.Suggest.One.SUGGEST_PROMOTION.set(false);
            CommonUser buyer = new CommonUser();
            buyer.setId(userId);
            RichOrder richOrder = richOrderMaker.partial(submittedSkus, buyer, channel.intValue());
            if (shopId != null) {
                Response<Shop> shopResponse = shopReadService.findById(shopId);
                if (shopResponse.isSuccess()) {
                    Shop shop = shopResponse.getResult();
                    if (shop != null) {
                        if (Boolean.FALSE.equals(shop.getJdStockCheck())) {
                            richOrder.setCheckStock(false);
                        }
                    }
                }
            }
            // find the promotion cache to judge if the promotion style
            if (promotionId != null) {
                Promotion promotion = promotionCacher.findByPromotionId(promotionId);
                if (promotion != null) {
                    PromotionTool<? extends Behavior> tool = promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());
                    switch (tool.level()) {
                        case SHOP -> {
                            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                                richSkusByShop.setPromotionId(promotionId);
                            }
                        }
                        case SKU -> {
                            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                                for (RichSku richSkus : richSkusByShop.getRichSkus()) {
                                    richSkus.setPromotionId(promotionId);
                                }
                            }
                        }
                        case GLOBAL -> richOrder.setPromotionId(promotionId);
                    }
                }
            }
            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                if (richSkusByShop.getRefererId() == null) {
                    continue;
                }
                if (richSkusByShop.getShop().getExtra().getOrDefault(ShopExtra.SalesPattern.getCode(), "").equals(SalePattern.StoreProxy.getCode())) {
                    String subStoreName = storeProxyManager.getStoreProxyByShopIdAndUserId(richSkusByShop.getShop().getId(), richSkusByShop.getRefererId())
                            .map(StoreProxy::getProxyShopName).orElse(null);
                    richSkusByShop.setRefererName(subStoreName);
                }
            }

            Response<Optional<ReceiverInfo>> findResp = receiverInfoReadService.findDefaultByUserId(buyer.getId());
            if (!findResp.isSuccess()) {
                log.error("fail to find default receiverInfo by userId={},cause:{}",
                        buyer.getId(), findResp.getError());
                throw new JsonResponseException(findResp.getError());
            }
            richOrder.setReceiverInfo(findResp.getResult().orElse(null));
            for (CouponCheck couponChecker : couponCheckers) {
                couponChecker.check(richOrder);
            }
            orderRuleEngine.canPreView(richOrder);
            previewPromotions.composePromotions(richOrder);

            //填充当前用户的实名信息
            appendUserCertification(richOrder);
            appendItemFlag(richOrder);

            return APIResp.ok(richOrder);
        } catch (JsonResponseException e) {
            log.error("PreviewOrders.previewOrder error, userId={}, submittedSkus={}, promotionId={}, channel={}",
                    userId, JSON.toJSONString(submittedSkus), promotionId, channel, e);
            return APIResp.error(e.getMessage());
        } catch (Exception e) {
            log.error("failed to preview order for  userId={}, submittedSkus={}", userId, JSON.toJSONString(submittedSkus), e);
            throw new JsonResponseException("order.preview.fail");
        } finally {
            PreviewPromotions.Suggest.One.SUGGEST_PROMOTION.remove();
        }
    }

    private void appendItemFlag(RichOrder richOrder) {
        if (richOrder == null || CollectionUtils.isEmpty(richOrder.getRichSkusByShops())) {
            return;
        }

        for (var richSkusByShop : richOrder.getRichSkusByShops()) {
            if (richSkusByShop.getShop() == null || CollectionUtils.isEmpty(richSkusByShop.getRichSkus())) {
                continue;
            }

            // 门店限定卷
            List<Promotion> subStoreOnly = promotionReadService.find(richSkusByShop.getShop().getId(), PromotionType.SUB_STORE_ONLY,
                    List.of(PromotionStatus.PUBLISHED, PromotionStatus.ONGOING)).getResult();

            richSkusByShop.getRichSkus().forEach(richSku -> itemConvertor.appendFlagBySubStoreOnly(richSku::setFlagList,
                    subStoreOnly, richSku.getItem().getId()));
        }
    }

    /**
     * 填充当前用户的实名信息
     *
     * @param richOrder
     */
    private void appendUserCertification(RichOrder richOrder) {
        //是否需要验证实名信息
        boolean hasBonded = richOrder.getRichSkusByShops().stream()
                .anyMatch(richSkusByShop -> richSkusByShop.getRichSkus().stream()
                        .map(RichSku::getSku).map(Sku::getType).anyMatch(Predicate.isEqual(1)));
        richOrder.setNeedCheckRealName(hasBonded);

        //当前用户的实名信息
        var certification = userCertificationReadService.findDefaultByUserId(
                richOrder.getBuyer().getId()).getResult();
        if (certification.isEmpty()) {
            return;
        }

        richOrder.setPayerName(certification.get().getPaperName());
        richOrder.setPayerId(certification.get().getPaperNo());
        richOrder.setRealNameCheckSuccess(UserCertificationVerifyStatusEnum.isPassed(certification.get().getVerifyStatus()));
    }

    /**
     * 微分销订单预览
     *
     * @param data    skuId及数量及微分销店铺id, 是List<DistributionPreviewSubmittedSku>的json表示形式
     * @param channel 下单渠道, 1-pc, 2-手机
     * @return 订单信息
     */
    @RequestMapping(value = "/api/we/order/preview", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<RichOrder> distributionPreview(@RequestParam("data") String data,
                                                   @RequestParam(value = "channel", required = false) Integer channel) {
        final CommonUser buyer = UserUtil.getCurrentUser();
        try {

            List<DistributionPreviewSubmittedSku> submittedSkus = JsonMapper.JSON_NON_EMPTY_MAPPER.getMapper()
                    .readValue(data, LIST_OF_DISTRIBUTION_PREVIEW_SUBMITTED_SKU);

            if (CollectionUtils.isEmpty(submittedSkus)) {
                log.warn("no sku specified, skip");
                return null;
            }
            RichOrder richOrder = richOrderMaker.distributionPartial(submittedSkus, buyer, channel);

            Response<Optional<ReceiverInfo>> findResp = receiverInfoReadService.findDefaultByUserId(buyer.getId());
            if (!findResp.isSuccess()) {
                log.error("fail to find default receiverInfo by userId={},cause:{}",
                        buyer.getId(), findResp.getError());
                throw new JsonResponseException(findResp.getError());
            }
            richOrder.setReceiverInfo(findResp.getResult().orElse(null));
            for (CouponCheck couponChecker : couponCheckers) {
                couponChecker.check(richOrder);
            }

            orderRuleEngine.canPreView(richOrder);
            previewPromotions.composeDistributionPromotions(richOrder);
            return Response.ok(richOrder);
        } catch (Exception e) {
            log.error("{} failed to preview order for {}, cause:{}", LogUtil.getClassMethodName(), data, e);
            throw new JsonResponseException("order.preview.fail");
        }
    }

    /**
     * 根据传入的UserId作为查询会员价的依据 预览订单
     *
     * @param skuIdAndQuantity skuId及数量, 是List<SubmittedSku>的json表示形式
     * @param channel          下单渠道, 1-pc, 2-手机
     * @param pid              查询会员价依据的用户Id
     * @return 订单信息
     */
    @RequestMapping(value = "/api/membershipByPid/order/preview", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<RichOrder> previewMembershipByPidUserId(@RequestParam("data") String skuIdAndQuantity,
                                                            @RequestParam(value = "channel", required = false) Integer channel,
                                                            @RequestParam(value = "pid", required = false) Long pid) {
        final CommonUser buyer = UserUtil.getCurrentUser();
        try {
            List<SubmittedSku> submittedSkus = JsonMapper.JSON_NON_EMPTY_MAPPER.getMapper()
                    .readValue(skuIdAndQuantity, LIST_OF_SUBMITTED_SKU);

            if (CollectionUtils.isEmpty(submittedSkus)) {
                log.warn("no sku specified, skip");
                return null;
            }
            RichOrder richOrder = richOrderMaker.partial(submittedSkus, buyer, channel);

            //检查用户是否享有会员价并计算税费
            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                for (RichSku richSku : richSkusByShop.getRichSkus()) {
                    if (Objects.nonNull(pid)) {
                        richSku.setPid(pid);
                    }
                }
            }

            Response<Optional<ReceiverInfo>> findResp = receiverInfoReadService.findDefaultByUserId(buyer.getId());
            if (!findResp.isSuccess()) {
                log.error("fail to find default receiverInfo by userId={},cause:{}",
                        buyer.getId(), findResp.getError());
                throw new JsonResponseException(findResp.getError());
            }
            richOrder.setReceiverInfo(findResp.getResult().orElse(null));

            orderRuleEngine.canPreView(richOrder);
            previewPromotions.composePromotions(richOrder);
            previewPromotions.tryGetMaxDiscountOrder(richOrder);
            return Response.ok(richOrder);
        } catch (Exception e) {
            log.error("failed to preview order for {}, pid={}",
                    skuIdAndQuantity, pid, e);
            throw new JsonResponseException("order.preview.fail");
        }
    }
}
