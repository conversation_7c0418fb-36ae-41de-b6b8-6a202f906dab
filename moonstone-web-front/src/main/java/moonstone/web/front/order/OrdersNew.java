package moonstone.web.front.order;

import cn.hutool.json.JSONUtil;
import com.google.common.base.Objects;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.EmptyUtils;
import moonstone.common.utils.R;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.FlowPicker;
import moonstone.order.dto.*;
import moonstone.order.dto.fsm.Flow;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.model.OrderBase;
import moonstone.order.model.OrderLevel;
import moonstone.order.service.ShopOrderReadService;
import moonstone.web.core.order.OrderReadLogic;
import moonstone.web.core.order.OrderWriteLogic;
import moonstone.web.front.component.order.dto.IdentityChange;
import moonstone.web.front.order.web.OrderIdentityChangeController;
import moonstone.web.front.order.web.Orders;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.*;

/**
 * 小程序订单下单
 *
 * @Author: wuxian-yjp
 * @Date: 2019/8/2 10:13
 */
@SuppressWarnings("UnstableApiUsage")
@Slf4j
@RestController
public class OrdersNew {
    @Autowired
    private OrderWriteLogic orderWriteLogic;
    @Autowired
    private OrderReadLogic orderReadLogic;
    @Autowired
    private SkuReadService skuReadService;
    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private FlowPicker flowPicker;

    @Autowired
    Orders orders;

    /**
     * 小程序下单
     */
    @RequestMapping(value = "/api/new/order", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public R create(@RequestBody SimpleSubmitOrder simpleSubmitOrder) {
        log.info("[SubmitOrderBody:] SubmitOrderBody:{}", simpleSubmitOrder);
        if (ObjectUtils.isEmpty(simpleSubmitOrder.getAddressId())) {
            return R.error(-1, "收货地址为空");
        }
        SubmittedOrder submittedOrder = buildSubmit(simpleSubmitOrder);

        if (submittedOrder == null) {
            log.error("submittedOrder:{}", submittedOrder);
            return R.error(-1, "下单失败");
        }
        log.info("new Order submittedOrder:{}", JSONUtil.toJsonStr(submittedOrder));
        List<Long> list = orders.create(submittedOrder, UserUtil.getCurrentUser());
        if (list == null || list.isEmpty()) {
            log.error("list:{}", list);
            return R.error(-1, "下单失败");
        }
        Map<String, Object> mapId = new HashMap<>();
        mapId.put("orderId", list);
        return R.ok().add("data", mapId);

    }


    /**
     * 小程序下单（社区运营）
     */
    @RequestMapping(value = "/api/new/community/order", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public R createCommunity(@RequestBody CommunitySubmittedOrder submittedOrder) {
        log.info("[submittedOrder:] submittedOrder {}", submittedOrder);
        if (submittedOrder == null) {
            return R.error(-1, "下单失败");
        }
        if (ObjectUtils.isEmpty(submittedOrder.getReceiverInfoId())) {
            return R.error(-1, "收货地址为空");
        }
        List<Long> list = orders.create(submittedOrder, UserUtil.getCurrentUser());
        if (list == null || list.isEmpty()) {
            log.error("list:{}", list);
            return R.error(-1, "下单失败");
        }
        Map<String, Object> mapId = new HashMap<>();
        mapId.put("idList", list);
        return R.ok().add("data", mapId);

    }

    /**
     * 小程序下单组装下单参数
     */
    private SubmittedOrder buildSubmit(SimpleSubmitOrder simpleSubmitOrder) {
        SubmittedOrder submittedOrder = new SubmittedOrder();
        submittedOrder.setReceiverInfoId(simpleSubmitOrder.getAddressId());//设置收货地址
        submittedOrder.setChannel(2);//设置来源小程序
        submittedOrder.setPayType(1);//支付方式在线支付
        submittedOrder.setPromotionId(simpleSubmitOrder.getPromotionId());

        List<SubmittedSkusByShop> submittedSkusByShops = new ArrayList<>();

        for (SubmitOrderPackBody entity : simpleSubmitOrder.getPackageBody()) {
            SubmittedSkusByShop submittedSkusByShop = new SubmittedSkusByShop();
            submittedSkusByShop.setPromotionId(entity.getPromotionId());
            //买家备注
            submittedSkusByShop.setBuyerNote(entity.getBuyerNote());
            //设置收货地址
            submittedSkusByShop.setReceiverInfoId(simpleSubmitOrder.getAddressId());
            List<SubmittedSku> submittedSkus = new ArrayList<>();
            for (SubmitOrderSkuBody sto : entity.getSkus()) {
                Response<Sku> skuResponse = skuReadService.findSkuById(sto.getSkuId());
                if (!skuResponse.isSuccess() || skuResponse.getResult() == null) {
                    log.error("skuResponse:{}", skuResponse.isSuccess());
                    throw new JsonResponseException(555, new Translate("商品不存在").toString());
                }
                if (!Objects.equal(skuResponse.getResult().getStatus(), 1)) {
                    throw new JsonResponseException(555, new Translate("商品已下架").toString());
                }
                submittedSkusByShop.setShopId(skuResponse.getResult().getShopId());
                SubmittedSku submittedSku = new SubmittedSku();
                submittedSku.setPromotionId(sto.getPromotionId());
                submittedSku.setSkuId(sto.getSkuId());
                submittedSku.setQuantity(sto.getQuantity());
                submittedSkus.add(submittedSku);
            }
            submittedSkusByShop.setSubmittedSkus(submittedSkus);
            submittedSkusByShops.add(submittedSkusByShop);
        }
        submittedOrder.setSubmittedSkusByShops(submittedSkusByShops);

        //针对购物车下单增加extra参数，用来移除购物车的信息
        if (EmptyUtils.isNotEmpty(simpleSubmitOrder.getIsCarts()) && simpleSubmitOrder.getIsCarts()) {
            Map<String, String> mapCarts = new HashMap<>();
            mapCarts.put("CartsDelete", "true");
            submittedOrder.setExtra(mapCarts);
        }
        return submittedOrder;
    }


    @Autowired
    OrderIdentityChangeController orderIdentityChangeController;
    /**
     * 修改实名信息
     */
    @RequestMapping(value = "/api/new/order/identity/change", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public R changeIdentity(@RequestBody IdentityChange identityChange) {
        if (EmptyUtils.isEmpty(identityChange) || EmptyUtils.isEmpty(identityChange.getOrderId())
                || EmptyUtils.isEmpty(identityChange.getIdentityCode()) || EmptyUtils.isEmpty(identityChange.getIdentityName())) {
            return R.error(-1, "参数不能为空！");
        }
        Boolean reslut = orderIdentityChangeController.changeIdentity(identityChange.getOrderId(), identityChange.getIdentityName(), identityChange.getIdentityCode());
        if (reslut) {
            return R.ok().add("data", Optional.ofNullable(shopOrderReadService.findById(identityChange.getOrderId()).getResult().getStatus()).orElse(1));
        } else {
            return R.error(-1, "修改失败！！");
        }
    }


    /**
     * 删除订单
     */
    @RequestMapping(value = "/api/new/order/delete/{orderId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public R deleteShopOrder(@PathVariable("orderId") Long orderId) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        OrderBase orderBase = orderReadLogic.findOrder(orderId, OrderLevel.SHOP);

        if (!Objects.equal(commonUser.getId(), orderBase.getBuyerId())) {
            log.error("the shopOrder(id={}) not belong to buyer(id={})",
                    orderId, commonUser.getId());
            throw new JsonResponseException("order.not.belong.to.buyer");
        }

        boolean b = orderWriteLogic.updateOrderStatusByOrderEvent(orderBase, OrderLevel.SHOP, OrderEvent.DELETE);
        if (b) {
            Flow flow = flowPicker.pick(orderBase, OrderLevel.SHOP);
            Integer targetStatus = flow.target(orderBase.getStatus(), OrderEvent.DELETE.toOrderOperation());
            return R.ok().add("data", targetStatus);
        } else {
            return R.error(-1, "删除失败");
        }
    }

    /**
     * 买家取消订单
     */
    @RequestMapping(value = "/api/new/buyer/order/cancel/{orderId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public R buyerCancelOrder(@PathVariable("orderId") Long orderId,
                              @RequestParam(value = "orderType", defaultValue = "1") Integer orderType) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        OrderLevel orderLevel = OrderLevel.fromInt(orderType);
        OrderBase orderBase = orderReadLogic.findOrder(orderId, orderLevel);
        if (!orderBase.getCreatedAt().before(new DateTime(Date.from(Instant.now())).minusMinutes(5).toDate())) {
            throw new JsonResponseException(new Translate("订单需要５分钟后才能关闭").toString());
//            return R.error(-1, "订单需要５分钟后才能关闭");

        }
        if (!Objects.equal(commonUser.getId(), orderBase.getBuyerId())) {
            log.error("the order(id={},type={}) not belong to buyer(id={})",
                    orderId, orderType, commonUser.getId());
            throw new JsonResponseException("order.not.belong.to.buyer");

        }
        if (!orderWriteLogic.updateOrderStatusByOrderEvent(orderBase, orderLevel, OrderEvent.BUYER_CANCEL)) {
            return R.error(-1, "取消订单失败");
        }

        Flow flow = flowPicker.pick(orderBase, orderLevel);
        Integer targetStatus = flow.target(orderBase.getStatus(), OrderEvent.BUYER_CANCEL.toOrderOperation());
        return R.ok().add("data", targetStatus);
    }


}
