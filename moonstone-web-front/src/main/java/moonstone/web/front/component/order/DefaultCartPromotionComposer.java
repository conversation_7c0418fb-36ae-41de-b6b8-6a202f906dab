package moonstone.web.front.component.order;

import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import moonstone.cache.ItemCacheHolder;
import moonstone.cart.dto.RichCart;
import moonstone.cart.dto.RichCartItem;
import moonstone.common.model.CommonUser;
import moonstone.item.model.Sku;
import moonstone.order.dto.RichSku;
import moonstone.promotion.api.PromotionPicker;
import moonstone.promotion.component.PromotionExecutor;
import moonstone.promotion.component.QualifiedPromotions;
import moonstone.promotion.enums.PromotionStatus;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.service.PromotionReadService;
import moonstone.shop.model.Shop;
import moonstone.weShop.model.WeShopSku;
import moonstone.weShop.service.WeShopSkuReadService;
import moonstone.web.core.fileNew.enums.SourceTypeEnum;
import moonstone.web.front.component.promotion.CartPromotionComposer;
import moonstone.web.front.component.promotion.ShopPromotionCenter;
import moonstone.web.front.item.app.ItemConvertor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * Created by cp on 4/27/17.
 */
@AllArgsConstructor
@Component
public class DefaultCartPromotionComposer implements CartPromotionComposer {

    private final QualifiedPromotions qualifiedPromotions;

    private final PromotionPicker promotionPicker;

    private final PromotionExecutor promotionExecutor;

    private final ShopPromotionCenter shopPromotionCenter;

    private final ItemCacheHolder itemCacheHolder;

    private final WeShopSkuReadService weShopSkuReadService;

    private final PromotionReadService promotionReadService;

    private final ItemConvertor itemConvertor;

    @Override
    public void composePromotions(CommonUser buyer, List<RichCart> richCarts) {
        for (RichCart richCart : richCarts) {
            List<Promotion> promotionsOfShop = shopPromotionCenter.findByShopId(richCart.getShopId());

            List<Promotion> defaultPromotions = Lists.newArrayList();
            Promotion conditionReductionPromotion = retrieveDefaultPromotion(promotionsOfShop, PromotionType.SHOP_CONDITION_REDUCTION);
            if (conditionReductionPromotion != null) {
                defaultPromotions.add(conditionReductionPromotion);
            }
            richCart.setShopPromotions(defaultPromotions);

            Shop shop = makeShop(richCart);
            for (RichCartItem richCartItem : richCart.getCartItems()) {
                Function<RichSku, Sku> priceGenerator = richSku -> {
                    List<Promotion> promotions = qualifiedPromotions.findSkuPromotions(richSku, buyer, shop);
                    //过滤掉用户营销
                    filterUserPromotion(promotions);
                    richCartItem.setSkuPromotions(promotions);
                    Long defaultPromotionId = promotionPicker.defaultSkuPromoiton(promotions);
                    richCartItem.setPromotionId(defaultPromotionId);
                    if (Objects.nonNull(richCart.getWeShopId())) {
                        calculateTheWeShopSkuPrice(richCart, richSku);
                    }
                    if (richCartItem.getPromotionId() != null) {
                        richSku.setPromotionId(richCartItem.getPromotionId());
                        richSku.setTax(richCartItem.getTax());
                        promotionExecutor.processSkuOrder(buyer, shop, richSku);
                        richCartItem.setSkuPromotionPrice(richSku.getSkuPromotionPrice());
                    }
                    return richSku.getSku();
                };

                fullFillTheSkuPrice(richCartItem, priceGenerator);
            }
        }
    }

    @Override
    public void appendFlag(List<RichCart> richCarts, Long shopId) {
        if (CollectionUtils.isEmpty(richCarts)) {
            return;
        }

        // 门店限定卷
        List<Promotion> subStoreOnly = promotionReadService.find(shopId, PromotionType.SUB_STORE_ONLY,
                List.of(PromotionStatus.PUBLISHED, PromotionStatus.ONGOING)).getResult();
        if (CollectionUtils.isEmpty(subStoreOnly)) {
            return;
        }

        richCarts.stream()
                .filter(richCart -> shopId.equals(richCart.getShopId()))
                .map(RichCart::getCartItems)
                .forEach(cartItems ->
                        cartItems.forEach(cartItem -> {
                            itemConvertor.appendFlagBySubStoreOnly(cartItem::setFlagList, subStoreOnly, cartItem.getSku().getItemId());
                            if (cartItem.getFlagList() == null) {
                                cartItem.setFlagList(new ArrayList<>());
                            }
                            if (Objects.equals(cartItem.getSourceType(), SourceTypeEnum.JD.getCode())) {
                                cartItem.getFlagList().add("jdCloudItem");
                            }
                        }));
    }

    /**
     * calculate the WeShopSku price
     *
     * @param richCart cart
     * @param richSku  skuOrigin
     */
    private void calculateTheWeShopSkuPrice(RichCart richCart, RichSku richSku) {
        WeShopSku weShopSku = weShopSkuReadService.findByWeShopIdAndSkuId(richCart.getWeShopId(), richSku.getSku().getId(), -999)
                .orElse(null);
        Sku sku = new Sku();
        BeanUtils.copyProperties(richSku.getSku(), sku);
        richSku.setSku(sku);
        if (Objects.isNull(weShopSku)) {
            sku.setStatus(-1);
            return;
        } else {
            richSku.setTax(weShopSku.getTax());
        }
        sku.setPrice(Optional.ofNullable(weShopSku.getPrice()).orElseGet(() -> sku.getPrice() + weShopSku.getDiffPrice()).intValue());
    }

    /**
     * fulfill the weShopSku price
     *
     * @param richCartItem          cart item
     * @param skuPriceCopyGenerator sku Price generator, @warn must return a new Sku for security
     */
    private void fullFillTheSkuPrice(RichCartItem richCartItem, Function<RichSku, Sku> skuPriceCopyGenerator) {
        RichSku richSku = makeRichSku(richCartItem);
        richCartItem.setSku(skuPriceCopyGenerator.apply(richSku));
    }

    protected Promotion retrieveDefaultPromotion(List<Promotion> promotions, PromotionType promotionType) {
        Promotion defaultPromotion = null;
        for (Promotion promotion : promotions) {
            if (PromotionType.from(promotion.getType()) != promotionType) {
                continue;
            }
            if (defaultPromotion == null) {
                defaultPromotion = promotion;
            } else {
                if (promotion.getId() > defaultPromotion.getId()) {
                    defaultPromotion = promotion;
                }
            }
        }
        return defaultPromotion;
    }

    private void filterUserPromotion(List<Promotion> promotions) {
        Iterables.removeIf(promotions, new Predicate<Promotion>() {
            @Override
            public boolean apply(Promotion promotion) {
                return PromotionType.isUserPromotion(promotion.getType());
            }
        });
    }

    private RichSku makeRichSku(RichCartItem richCartItem) {
        RichSku richSku = new RichSku();
        richSku.setSku(richCartItem.getSku());
        richSku.setQuantity(richCartItem.getCartItem().getQuantity());
        richSku.setItem(itemCacheHolder.findItemById(richCartItem.getSku().getItemId()));
        richSku.setExtra(richCartItem.getSku().getExtraMap());
        return richSku;
    }

    private Shop makeShop(RichCart richCart) {
        Shop shop = new Shop();
        shop.setId(richCart.getShopId());
        shop.setUserId(richCart.getSellerId());
        return shop;
    }
}
