package moonstone.web.front.component.order;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.api.Result;
import moonstone.common.api.ResultCode;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.order.api.Y800OrderIdGenerator;
import moonstone.order.dto.DistributionOrderGroup;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.OrderDetail;
import moonstone.order.dto.OrderGroup;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.model.*;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.vo.PayInfoVO;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.service.WeShopReadService;
import moonstone.web.core.fileNew.logic.RefundLogic;
import moonstone.web.core.fileNew.logic.RefundReturnDetailLogic;
import moonstone.web.core.fileNew.logic.ShopOrderLogic;
import moonstone.web.core.order.OrderReadLogic;
import moonstone.web.core.order.dto.OrderGroupViewObject;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.model.ServiceProvider;
import moonstone.web.front.component.order.convert.OrderInfoConvertor;
import moonstone.web.front.component.order.dto.EnhanceOrderCriteria;
import moonstone.web.front.component.order.dto.OrderGroupNewViewObject;
import moonstone.web.front.shop.substore.component.SubStoreUserIdentityComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单相关接口
 */
@Controller
@Slf4j
public class OrderReader {

    @Autowired
    private OrderReadLogic orderReadLogic;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private WeShopCacheHolder weShopCacheHolder;

    @Resource
    private WeShopReadService weShopReadService;

    @Resource
    private SubStoreReadService subStoreReadService;

    @Resource
    private SubStoreTStoreGuiderReadService storeGuiderReadService;

    @Resource
    private UserReadService<User> userReadService;

    @Autowired
    private Y800OrderIdGenerator y800OrderIdGenerator;

    @Resource
    private ShopCacheHolder shopCacheHolder;

    @Autowired
    private OrderExtraInfoDecorateHelper orderExtraInfoDecorateHelper;

    @Autowired
    private OrderAuthStatusDescribeHelper orderAuthStatusDescribeHelper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private OrderInfoConvertor orderInfoConvertor;

    @Resource
    private SubStoreUserIdentityComponent subStoreUserIdentityComponent;

    @Resource
    private RefundLogic refundLogic;

    @Resource
    private RefundReturnDetailLogic refundReturnDetailLogic;

    @Resource
    private ShopOrderLogic shopOrderLogic;

    @GetMapping(value = "/api/order/getOrderId")
    @ResponseBody
    public String getOrderId(Long id) {
        return y800OrderIdGenerator.getDeclareId(shopOrderReadService.findById(id).getResult());
    }

    @GetMapping(value = "/api/order/getOrderIds")
    @ResponseBody
    public List<String> getOrderIds(@RequestParam("ids") Long[] ids) {
        return Stream.of(ids)
                .map(this::getOrderId)
                .collect(Collectors.toList());
    }

    /**
     * 使用目标的criteria条件更改原来的criteria
     * todo: 将其重构后 用于App在outFrom区分后, 不同的domain中进行对搜索条件进行一次多domain(不同业务实体)聚合
     */
    public void limitUserIdBySubStoreName(EnhanceOrderCriteria criteria) {
        if (!ObjectUtils.isEmpty(criteria.getGuiderName())) {
            // 快照
            var bySnapshot = subStoreUserIdentityComponent.findUserIdsByName(criteria.getShopId(), criteria.getGuiderName(),
                    SubStoreUserIdentityEnum.STORE_GUIDER.getCode(), OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER);

            // 当前
            List<Long> matchNameIds = storeGuiderReadService.findUserIdsByName(criteria.getGuiderName(), null, null).getResult();
            if (CollectionUtils.isEmpty(matchNameIds) && CollectionUtils.isEmpty(bySnapshot)) {
                log.error("{} failed to query by GuiderName:{}", LogUtil.getClassMethodName(), criteria.getGuiderName());
                /// 不需要查了 不存在符合条件的
                criteria.setShopId(-999L);
            } else {
                criteria.setRefererIds(mergeAll(bySnapshot, matchNameIds));
            }
        }
        if (!ObjectUtils.isEmpty(criteria.getSubStoreName())) {
            // 快照
            var snapshotUserIds = subStoreUserIdentityComponent.findUserIdsByName(criteria.getShopId(), criteria.getSubStoreName(),
                    SubStoreUserIdentityEnum.SUB_STORE.getCode(), OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER);

            // 当前
            List<Long> ids = subStoreReadService.findIdsByName(criteria.getSubStoreName(), null, null).getResult();
            if (CollectionUtils.isEmpty(ids) && CollectionUtils.isEmpty(snapshotUserIds)) {
                log.error("{} failed to query by SubStore Name:{}", LogUtil.getClassMethodName(), criteria.getSubStoreName());
                /// 不需要查了 不存在符合条件的
                criteria.setShopId(-999L);
            } else {
                if (!CollectionUtils.isEmpty(ids)) {
                    criteria.setOutShopIds(ids.stream().map(Object::toString).collect(Collectors.toList()));
                }
                if (!CollectionUtils.isEmpty(snapshotUserIds)) {
                    criteria.setSnapshotSubStoreUserIds(snapshotUserIds);
                }
            }
        }
    }

    private List<Long> mergeAll(List<Long> list1, List<Long> list2) {
        var result = new HashSet<Long>();

        if (!CollectionUtils.isEmpty(list1)) {
            result.addAll(list1);
        }
        if (!CollectionUtils.isEmpty(list2)) {
            result.addAll(list2);
        }

        return new ArrayList<>(result);
    }

    /**
     * 若入参中有服务商名称（serviceProviderName），则置空 subShopName 参数；<br/>
     * 并根据 serviceProviderName 查询对应的门店列表（out_shop_id）
     */
    public void limitOutShopIdByServiceProviderName(EnhanceOrderCriteria criteria, Long sellerShopId) {
        if (StringUtils.isBlank(criteria.getServiceProviderName())) {
            return;
        }
        criteria.setSubShopName(null);

        // 快照
        var snapshotUserIds = subStoreUserIdentityComponent.findUserIdsByName(criteria.getShopId(), criteria.getServiceProviderName(),
                SubStoreUserIdentityEnum.SERVICE_PROVIDER.getCode(), OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER);
        if (!CollectionUtils.isEmpty(snapshotUserIds)) {
            criteria.setSnapshotServiceProviderUserIds(snapshotUserIds);
        }

        // 当前
        //查询服务提供商
        Pattern pattern = Pattern.compile("^.*" + criteria.getServiceProviderName() + ".*$", Pattern.CASE_INSENSITIVE);
        Query query = new Query();
        query.addCriteria(Criteria.where("name").regex(pattern));
        var serviceProviderList = mongoTemplate.find(query, ServiceProvider.class);
        if (CollectionUtils.isEmpty(serviceProviderList) && CollectionUtils.isEmpty(snapshotUserIds)) {
            log.debug("serviceProviderName={}, 查询不到任何服务商", criteria.getServiceProviderName());
            criteria.setOutShopIds(Lists.newArrayList("-1"));
            return;
        }

        //查询服务提供商下的门店
        var subStoreUserIds = new ArrayList<Long>();
        serviceProviderList.forEach(serviceProvider -> serviceProviderCache.findBelongSubStoreUserId(sellerShopId, serviceProvider.getUserId())
                .ifPresent(subStoreUserIds::addAll));
        var subStores = subStoreReadService.findByUserIds(subStoreUserIds, sellerShopId).getResult();
        if (CollectionUtils.isEmpty(subStores) && CollectionUtils.isEmpty(snapshotUserIds)) {
            log.debug("serviceProviderName={}, 查询不到任何属于它的门店", criteria.getServiceProviderName());
            criteria.setOutShopIds(Lists.newArrayList("-1"));
            return;
        }

        if (!CollectionUtils.isEmpty(subStores)) {
            criteria.setOutShopIds(subStores.stream().map(subStore -> subStore.getId().toString()).collect(Collectors.toList()));
        }
    }

    /**
     * 根据支付单号来指定对应的主订单
     *
     * @param criteria
     */
    public void limitShopOrderIdByPaymentOutId(EnhanceOrderCriteria criteria) {
        if (StringUtils.isBlank(criteria.getPaymentOutId())) {
            return;
        }

        // 当前支付单只会关联主订单
        var list = paymentReadService.findOrderIdsByOutId(criteria.getPaymentOutId(), OrderLevel.SHOP);
        if (CollectionUtils.isEmpty(list)) {
            criteria.setOrderId(-1L);
            return;
        }

        if (CollectionUtils.isEmpty(criteria.getOrderIds())) {
            criteria.setOrderIds(new ArrayList<>());
        }
        criteria.getOrderIds().addAll(list);
    }

    /**
     * 搜索订单,目前太过繁杂
     * **需要重构,将功能模块区分开来((
     * - 利润显示
     * - 用户无利润显示
     * - 经销商显示包括下级的门店订单
     * - 分销商显示自己推广的订单
     * - 用户搜索自己的订单
     *
     * @param criteria 搜索条件
     * @return 结果
     */
    @RequestMapping(value = "/api/order/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<Paging<OrderGroupViewObject>> findBy(EnhanceOrderCriteria criteria) {
        if (!criteria.getNoAuth() && Objects.isNull(UserUtil.getCurrentUser())) {
            throw new ApiException(ResultCode.UN_AUTHORIZED);
        }
        criteria.setOperatorId(UserUtil.getUserId());
        criteria.handleShipmentStartEnd();

        limitRefererIdByRefererMobile(criteria);
        limitUserIdBySubStoreName(criteria);
        limitOutShopIdByServiceProviderName(criteria, UserUtil.getCurrentShopId());
        limitShopOrderIdByPaymentOutId(criteria);

        DateUtil.washTheDate(criteria, EnhanceOrderCriteria.class);
        criteria.fulfillOrderStatusBySettleType();
        criteria.setBuyerId(UserUtil.getUserId());
        // fix the buyerId problem
        if (criteria.getBuyerId() != null && criteria.getShopId() != null) {
            if (shopCacheHolder.findShopById(criteria.getShopId()).getUserId().equals(criteria.getBuyerId())) {
                criteria.setBuyerId(null);
            }
        }
        // use Slice convert dto (because it require a service insert, for speed consider use slice instead a dynamic domain)
        try {
            // 重新校验一下依旧存活的业务 去删除废弃的代码 以保证重构
            log.debug("OrderReader.findBy, criteria map={}", criteria.toMap());
            if (criteria.getShopId() == null) {
                return Response.fail("商家ID为空");
            }

            Response<Paging<OrderGroup>> queryRes = orderReadLogic.pagingOrder(criteria.unwrap());
            if (!queryRes.isSuccess()) {
                return Response.fail(queryRes.getError());
            }

            // 打包额外的数据, 门店的数据
            var orderGroupView = orderAuthStatusDescribeHelper.describeAuthStatus(queryRes.getResult());
            // 填充代付状态
            orderInfoConvertor.appendAgentPayStatus(orderGroupView.getData());

            return Response.ok(orderExtraInfoDecorateHelper.decorateOrderWithExtraInfo(orderGroupView));
        } catch (Exception ex) {
            log.error("Fail to query Order by {}", criteria, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @RequestMapping(value = "/api/order/paging/new", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Result<DataPage<OrderGroupViewObject>> findByNew(EnhanceOrderCriteria criteria) {
        if (!criteria.getNoAuth() && Objects.isNull(UserUtil.getCurrentUser())) {
            return Result.fail("user.not.login");
        }
        criteria.setOperatorId(UserUtil.getUserId());
        limitRefererIdByRefererMobile(criteria);
        limitUserIdBySubStoreName(criteria);
        DateUtil.washTheDate(criteria, EnhanceOrderCriteria.class);
        criteria.fulfillOrderStatusBySettleType();
        // use Slice convert dto (because it require a service insert, for speed consider use slice instead a dynamic domain)
        Response<Paging<OrderGroup>> queryRes;
        //获取分页信息
        try {
            // 重新校验一下依旧存活的业务 去删除废弃的代码 以保证重构
            queryRes = orderReadLogic.pagingOrder(criteria.unwrap());
        } catch (Exception ex) {
            queryRes = Response.fail(ex.getMessage());
        }
        if (!queryRes.isSuccess()) {
            return Result.fail(queryRes.getError());
        }
        // 打包额外的数据, 门店的数据
        //获取分页信息
        TotalCalculation totalCalculation = TotalCalculation.build(criteria.getSize(), criteria.getPageNo(), queryRes.getResult().getTotal());
        return Result.data(DataPage.build(totalCalculation, orderExtraInfoDecorateHelper.decorateOrderWithExtraInfo(
                orderAuthStatusDescribeHelper.describeAuthStatus(queryRes.getResult())).getData()
        ));
    }

    @RequestMapping(value = "/api/order/new/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<OrderGroupNewViewObject> findByNew(@RequestParam Map<String, String> orderCriteria) {
        Response<Paging<OrderGroupViewObject>> orderGroupVo = findBy(JSON.parseObject(JSON.toJSONString(orderCriteria), EnhanceOrderCriteria.class));
        OrderGroupNewViewObject orderGroupNewsViewObject = new OrderGroupNewViewObject();
        for (OrderGroupViewObject orderGroupVos : orderGroupVo.getResult().getData()) {
            var ops = orderGroupVos.getSkuOrderAndOperations();
            SkuOrder skuOrder = (SkuOrder) ops.get(0).order();
            orderGroupNewsViewObject.setPrice((orderGroupNewsViewObject.getPrice() == null ? 0L : orderGroupNewsViewObject.getPrice()) + (skuOrder.getFee() == null ? 0L : skuOrder.getFee()));
            orderGroupNewsViewObject.setQuantity((orderGroupNewsViewObject.getQuantity() == null ? 0L : orderGroupNewsViewObject.getQuantity()) + (skuOrder.getQuantity() == null ? 0L : skuOrder.getQuantity()));
            orderGroupNewsViewObject.setTax((orderGroupNewsViewObject.getTax() == null ? 0L : orderGroupNewsViewObject.getTax()) + (skuOrder.getTax() == null ? 0L : skuOrder.getTax()));
            orderGroupNewsViewObject.setShipFee((orderGroupNewsViewObject.getShipFee() == null ? 0L : orderGroupNewsViewObject.getShipFee()) + (skuOrder.getShipFee() == null ? 0L : skuOrder.getShipFee()));
        }
        orderGroupNewsViewObject.setOrderGroupVo(orderGroupVo.getResult());
        return Response.ok(orderGroupNewsViewObject);
    }


    /**
     * 重定位导购员mobile信息至导购员id
     *
     * @param orderCriteria 搜索条件
     */
    public void limitRefererIdByRefererMobile(EnhanceOrderCriteria orderCriteria) {
        Optional.ofNullable(orderCriteria.getRefererMobile())
                .map(userReadService::findByMobile)
                .map(Response::getResult)
                .map(User::getId)
                .ifPresent(orderCriteria::setRefererId);
    }


    @RequestMapping(value = "/api/buyer/order/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Paging<OrderGroup> findForBuyer(@RequestParam(required = false) Map<String, Object> param) {
        final Long buyerId = UserUtil.getUserId();
        if (buyerId == null) {
            throw new JsonResponseException("user.not.login");
        }
        EnhanceOrderCriteria orderCriteria = Json.OBJECT_MAPPER.convertValue(param, EnhanceOrderCriteria.class);
        orderCriteria.setOperatorId(null);
        orderCriteria.setBuyerId(buyerId);
        Response<Paging<OrderGroup>> findResp = orderReadLogic.pagingOrder(orderCriteria);
        if (!findResp.isSuccess()) {
            log.error("fail to find order for buyer(id={}),criteria={},cause:{}",
                    buyerId, orderCriteria, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        return findResp.getResult();
    }

    @RequestMapping(value = "/api/seller/order/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Paging<OrderGroup> findForSeller(@RequestParam EnhanceOrderCriteria orderCriteria) {
        final Long shopId = UserUtil.<CommonUser>getCurrentUser().getShopId();
        orderCriteria.setShopId(shopId);
        orderCriteria.setOperatorId(UserUtil.getUserId());
        Response<Paging<OrderGroup>> findResp = orderReadLogic.pagingOrder(orderCriteria);
        if (!findResp.isSuccess()) {
            log.error("fail to find order for seller(shop id={}),criteria={},cause:{}",
                    shopId, orderCriteria, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        return findResp.getResult();
    }

    @RequestMapping(value = "/api/we/order/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<Paging<DistributionOrderGroup>> findForWeDistribution(EnhanceOrderCriteria orderCriteria) {
        orderCriteria.setOutFrom(OrderOutFrom.WE_SHOP.Code());
        orderCriteria.setOperatorId(UserUtil.getUserId());

        Response<Paging<OrderGroup>> rOrderGroupPaging = orderReadLogic.pagingOrder(orderCriteria);
        if (!rOrderGroupPaging.isSuccess()) {
            log.error("failed to find order for weDistribution, criteria={}, error code: {}", orderCriteria, rOrderGroupPaging.getError());
            throw new JsonResponseException(rOrderGroupPaging.getError());
        }
        List<DistributionOrderGroup> orderGroups = new ArrayList<>();
        Paging<OrderGroup> orderGroupPaging = rOrderGroupPaging.getResult();
        for (OrderGroup orderGroup : orderGroupPaging.getData()) {
            DistributionOrderGroup distributionOrderGroup = CopyUtil.copy(orderGroup, DistributionOrderGroup.class);
            String weShopName;
            String outShopId = distributionOrderGroup.getShopOrder().getOutShopId();
            if (ObjectUtils.isEmpty(outShopId)) {
                log.error("shop order(id={}) lost outShopId", distributionOrderGroup.getShopOrder().getId());
                throw new JsonResponseException("shop.order.lost.out.shop.id");
            } else {
                Long weShopId = Long.valueOf(outShopId);
                Response<WeShop> rWeShop = weShopCacheHolder.findByWeShopId(weShopId).map(Response::ok).orElseGet(() -> weShopReadService.findById(weShopId));
                if (!rWeShop.isSuccess()) {
                    log.error("failed to find weShop by id={}, error code: {}", outShopId, rWeShop.getError());
                    throw new JsonResponseException(rWeShop.getError());
                }
                weShopName = rWeShop.getResult().getName();
            }
            distributionOrderGroup.setWeShopName(weShopName);
            orderGroups.add(distributionOrderGroup);
        }
        return Response.ok(new Paging<>(orderGroupPaging.getTotal(), orderGroups));
    }

	/**
	 * 查询订单收件信息
	 *
	 * @param id 订单id
	 * @return 收件信息
	 */
    @RequestMapping(value = "/api/order/{id}/showReceiverInfo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<OrderDetail> showReceiverInfo(@PathVariable("id") Long id) {
        return orderReadLogic.showReceiverInfo(id);
    }

    @RequestMapping(value = "/api/order/{id}/showPayerInfo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<OrderDetail> showPayerInfo(@PathVariable("id") Long id) {
        return orderReadLogic.showPayerInfo(id);
    }
    @RequestMapping(value = "/api/order/{id}/detail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<OrderDetail> detail(@PathVariable("id") Long shopOrderId) {
        Response<ShopOrder> shopOrderR = shopOrderReadService.findById(shopOrderId);
        if (!shopOrderR.isSuccess()) {
            return Response.fail(shopOrderR.getError());
        }
        ShopOrder shopOrder = shopOrderR.getResult();
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return Response.fail("user.not.login");
        }
        log.info("show-order-detail shopOrder:{},user:{}", shopOrder, user);


        if (shopOrder.getOutFrom() != null && OrderOutFrom.SUB_STORE.Code().equals(shopOrder.getOutFrom())) {
            return getOrderDetail(shopOrder, user, shopOrderId);
        }
        //添加阶梯分销订单权限
        if (shopOrder.getOutFrom() != null && OrderOutFrom.LEVEL_Distribution.Code().equals(shopOrder.getOutFrom())) {
            log.info("[OrderOutFrom.LEVEL_Distribution] shopOrderId={}", shopOrderId);
            return getOrderDetail(shopOrder, user, shopOrderId);

        }
        if (!Objects.equals(shopOrder.getBuyerId(), user.getId())
                && !Objects.equals(shopOrder.getShopId(), user.getShopId())) {
            return Response.fail("order.not.owner");
        }

        return getOrderDetail(shopOrder, user, shopOrderId);
    }

    /**
     * 获取订单详情
     * @param shopOrderId 订单id
     * @return 订单详情
     */
    @RequestMapping(value = "/api/order/{id}/v2/detail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Result<OrderDetail> detailV2(@PathVariable("id") Long shopOrderId) {
        log.info("获取订单详情 请求参数 id {}", shopOrderId);
        Response<ShopOrder> shopOrderR = shopOrderReadService.findById(shopOrderId);
        if (!shopOrderR.isSuccess()) {
            throw new ApiException(shopOrderR.getError());
        }
        ShopOrder shopOrder = shopOrderR.getResult();
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            throw new ApiException("用户未登录");
        }
        log.info("show-order-detail shopOrder:{},user:{}", shopOrder, user);
        if (shopOrder.getOutFrom() != null && OrderOutFrom.SUB_STORE.Code().equals(shopOrder.getOutFrom())) {
            return Result.data(getOrderDetail(shopOrder, user, shopOrderId).getResult());
        }
        //添加阶梯分销订单权限
        if (shopOrder.getOutFrom() != null && OrderOutFrom.LEVEL_Distribution.Code().equals(shopOrder.getOutFrom())) {
            log.info("[OrderOutFrom.LEVEL_Distribution] shopOrderId={}", shopOrderId);
            return Result.data(getOrderDetail(shopOrder, user, shopOrderId).getResult());

        }
        if (!Objects.equals(shopOrder.getBuyerId(), user.getId())
                && !Objects.equals(shopOrder.getShopId(), user.getShopId())) {
            throw new ApiException("订单不属于当前用户，无法查看");
        }
		OrderDetail result = null;
		try {
			result = getOrderDetail(shopOrder, user, shopOrderId).getResult();
		} catch (Exception e) {
            log.error("获取订单详情失败 {}", e.getMessage(), e);
            throw new ApiException("查询订单详情失败");
		}
		return Result.data(result);
    }

    private Response<OrderDetail> getOrderDetail(ShopOrder shopOrder, CommonUser user, Long shopOrderId) {
        Response<OrderDetail> detailResponse;
        if (Objects.equals(user.getId(), shopOrder.getBuyerId())) {
            detailResponse = orderReadLogic.orderDetail(shopOrderId);
            if (detailResponse.isSuccess()) {
                OrderDetail orderDetail = detailResponse.getResult();
                if (Objects.nonNull(orderDetail)) {
                    List<OrderReceiverInfo> orderReceiverInfos = orderDetail.getOrderReceiverInfos();
                    if (!CollectionUtils.isEmpty(orderReceiverInfos)) {
                        orderReceiverInfos.forEach(item -> {
                            // 解密
                            ReceiverInfo receiverInfo = item.getReceiverInfo();
                            receiverInfo.decrypt();
                        });
                    }
                }
            }
        } else {
            detailResponse = orderReadLogic.orderDetail(shopOrderId);
            if (detailResponse.isSuccess()) {
                OrderDetail orderDetail = detailResponse.getResult();
                if (Objects.nonNull(orderDetail)) {
                    PayInfoVO payInfo = orderDetail.getPayInfo();
                    if (Objects.nonNull(payInfo)) {
                        payInfo.desensitization();
                    }
                    List<OrderReceiverInfo> orderReceiverInfos = orderDetail.getOrderReceiverInfos();
                    if (!CollectionUtils.isEmpty(orderReceiverInfos)) {
                        orderReceiverInfos.forEach(item -> {
                            // 脱敏
                            ReceiverInfo receiverInfo = item.getReceiverInfo();
                            receiverInfo.desensitization();
                        });
                    }
                }
            }
        }
        OrderDetail orderDetail = detailResponse.getResult();
        // 获取当前订单的还在流转的退款单号
        Refund refund = refundLogic.getExistFlowRefundByOrderId(shopOrderId);
        if (refund != null) {
            orderDetail.setRefundId(refund.getId());
            Map<String, Object> query = new HashMap<>();
            query.put("refundId", refund.getId());
            RefundReturnDetail refundReturnDetail = refundReturnDetailLogic.getOne(query);
            if (refundReturnDetail!= null) {
                orderDetail.setSellerAddress(refundReturnDetail.getSellerAddress());
            }
        }
        detailResponse.setResult(orderDetail);
        return detailResponse;
    }


    @GetMapping(value = "/api/order/countRefund")
    @ResponseBody
    public Long countRefund(OrderCriteria orderCriteria) {
        DateUtil.washTheDate(orderCriteria, OrderCriteria.class);
        orderCriteria.setStatus(Arrays.asList(OrderStatus.REFUND_APPLY_AGREED.getValue()
                , OrderStatus.REFUND.getValue()
                , OrderStatus.REFUND_PROCESSING.getValue()
                , OrderStatus.RETURN_APPLY_AGREED.getValue()
                , OrderStatus.RETURN.getValue()
                , OrderStatus.RETURN_CONFIRMED.getValue()));
        return orderReadLogic.countShopOrder(orderCriteria).getResult();
    }

    @GetMapping(value = "/api/order/countSell")
    @ResponseBody
    public Long countSell(OrderCriteria orderCriteria) {
        DateUtil.washTheDate(orderCriteria, OrderCriteria.class);
        List<OrderStatus> except = Arrays.asList(OrderStatus.NOT_PAID
                , OrderStatus.TIMEOUT_CANCEL
                , OrderStatus.BUYER_CANCEL
                , OrderStatus.SELLER_CANCEL
                , OrderStatus.DELETED
                , OrderStatus.REFUND_APPLY_AGREED
                , OrderStatus.REFUND
                , OrderStatus.REFUND_PROCESSING
                , OrderStatus.RETURN_APPLY_AGREED
                , OrderStatus.RETURN
                , OrderStatus.RETURN_CONFIRMED);
        orderCriteria.setStatus(Stream.of(OrderStatus.values())
                .filter(status -> !except.contains(status))
                .map(OrderStatus::getValue).collect(Collectors.toList())
        );
        return orderReadLogic.countShopOrder(orderCriteria).getResult();
    }

    @RequestMapping(value = "/api/order/countNotPaid", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Long countNotPaid(@RequestParam(required = false) Long shopId) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        OrderCriteria orderCriteria = new OrderCriteria();
        orderCriteria.setStatusStr("0");
        if (shopId != null) {
            orderCriteria.setShopId(shopId);
        }
        orderCriteria.setBuyerId(commonUser.getId());

        Response<Long> rNum = orderReadLogic.countShopOrder(orderCriteria);
        if (!rNum.isSuccess()) {
            log.error("fail to count not paid orders, cause: {}", rNum.getError());
            throw new JsonResponseException(rNum.getError());
        }
        return rNum.getResult();
    }

    @RequestMapping(value = "/api/we/order/countNotPaid", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Long weCountNotPaid() {
        CommonUser commonUser = UserUtil.getCurrentUser();

        OrderCriteria orderCriteria = new OrderCriteria();
        orderCriteria.setStatusStr("0");
        orderCriteria.setBuyerId(commonUser.getId());
        orderCriteria.setOutFrom(OrderOutFrom.WE_SHOP.Code());

        Response<Long> rNum = orderReadLogic.countWeShopOrder(orderCriteria);
        if (!rNum.isSuccess()) {
            log.error("fail to count not paid orders, cause: {}", rNum.getError());
            throw new JsonResponseException(rNum.getError());
        }
        return rNum.getResult();
    }

    /**
     * @param orderCriteria 订单查询
     * @param way           类型
     */
    @GetMapping(value = "/api/order/count-by-status-all")
    @ResponseBody
    public Response<Map<String, Long>> countByStatusAll(OrderCriteria orderCriteria, @RequestParam(defaultValue = "-1") Integer way, @RequestParam(required = false) Long weShopId) {
        log.info("普通商城 查询各个订单状态下的订单数量 请求路径 orderCriteria {} way {} weShopId {}", JSONUtil.toJsonStr(orderCriteria),way,weShopId);
        Long toBeShipped;//待付款 status=0
        // 重定义订单搜索条件 有关下级渠道 如微店
        // redefine the order search criteria  about outFrom like weShop
        fulfillTheOrderCriteria(orderCriteria, weShopId);

        List<Integer> status = new ArrayList<>();
        status.add(0);
        orderCriteria.setStatus(status);
        toBeShipped = countByStatus(orderCriteria, way);
        log.info("查询到待付款数量 {}",toBeShipped);

        Long shipped;//待发货 status=1
        status = new ArrayList<>();
        status.add(1);
        orderCriteria.setStatus(status);
        shipped = countByStatus(orderCriteria, way);
        log.info("查询到待发货数量 {}",shipped);

        Long toBeReceived;//待收货 status=2
        status = new ArrayList<>();
        status.add(2);
        orderCriteria.setStatus(status);
        toBeReceived = countByStatus(orderCriteria, way);
        log.info("查询到待收货数量 {}",toBeReceived);

        Map<String, Long> map = new HashMap<>(8);
        map.put(CountIndex.tobeshipped.name(), toBeShipped);
        map.put(CountIndex.shipped.name(), shipped);
        map.put(CountIndex.toBeReceived.name(), toBeReceived);


        return Response.ok(map);
    }

    /**
     * fulfill the orderCriteria OutFrom RefererId
     *
     * @param orderCriteria order criteria
     * @param weShopId      weShopId
     */
    private void fulfillTheOrderCriteria(OrderCriteria orderCriteria, Long weShopId) {
        if (Objects.nonNull(weShopId)) {
            orderCriteria.setOutFrom(OrderOutFrom.WE_SHOP.Code());
            orderCriteria.setRefererId(weShopCacheHolder.findByWeShopId(weShopId)
                    .orElseThrow(() -> new RuntimeException(Translate.of("店铺[%s]不存在", weShopId)))
                    .getUserId());
        }
    }


    @GetMapping(value = "/api/order/count-by-status")
    @ResponseBody
    public Long countByStatus(OrderCriteria orderCriteria, @RequestParam(defaultValue = "0") Integer way) {
        CommonUser user = java.util.Optional.ofNullable((CommonUser) UserUtil.getCurrentUser()).orElseThrow(() -> new JsonResponseException("user.not.login"));
        DateUtil.washTheDate(orderCriteria, OrderCriteria.class);
        switch (way) {
            case 0 -> /// 购买端
                    orderCriteria.setBuyerId(user.getId());
            case 1 -> /// 商户端
                    orderCriteria.setShopId(user.getShopId());
            case 2 -> {
                /// 门店端
                List<SubStore> subStores = subStoreReadService.findByUserIdAndStatus(user.getId(), null).getResult();
                if (subStores == null || subStores.isEmpty()) {
                    return 0L;
                }
                orderCriteria.setOutShopId(subStores.get(0).getId() + "");
                orderCriteria.setOutFrom(OrderOutFrom.SUB_STORE.Code());
            }
            case 3 -> {
                /// 导购员端
                orderCriteria.setRefererId(user.getId());
                orderCriteria.setOutFrom(OrderOutFrom.SUB_STORE.Code());
            }
            default -> {

            }
        }
        log.info("根据条件 查询符合的订单数 请求条件 {}",JSONUtil.toJsonStr(orderCriteria));
        List<ShopOrder> shopOrderList = shopOrderLogic.list(orderCriteria.toMap());
        return (long) shopOrderList.size();
    }

    enum CountIndex {
        /**
         * index of display
         */
        tobeshipped,
        shipped,
        toBeReceived
    }
}