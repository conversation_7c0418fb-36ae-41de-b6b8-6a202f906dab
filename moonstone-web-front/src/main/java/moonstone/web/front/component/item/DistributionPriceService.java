package moonstone.web.front.component.item;

import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.exception.InvalidException;
import moonstone.item.dto.ItemDistributionPrice;
import moonstone.item.dto.SkuDistributionPrice;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.ItemWriteService;
import moonstone.item.service.SkuReadService;
import moonstone.item.service.SkuWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/12/4.
 */
@Slf4j
@Service
public class DistributionPriceService {
    @RpcConsumer
    private ItemWriteService itemWriteService;

    @RpcConsumer
    private ItemReadService itemReadService;

    @RpcConsumer
    private SkuWriteService skuWriteService;

    @RpcConsumer
    private SkuReadService skuReadService;

    @Transactional
    public Response<Boolean> setItemDistributionPrice(ItemDistributionPrice itemDistributionPrice, Item existItem){
        try {
            for(SkuDistributionPrice skuDistributionPrice:itemDistributionPrice.getSkuPrices()){
                // modify by liuchao 20190401
                /*if (skuDistributionPrice.getProfit() > skuDistributionPrice.getDistributionPrice()){
                    log.error("profit can not bigger than distribution price");
                    throw new JsonResponseException("distribution.price.smaller.than.profit");
                }*/
                Response<Sku> rSku = skuReadService.findSkuById(skuDistributionPrice.getSkuId());
                if (!rSku.isSuccess()){
                    log.error("failed to find sku by id={}, error code: {}", skuDistributionPrice.getSkuId(), rSku.getError());
                    throw new JsonResponseException(rSku.getError());
                }
                Sku existSku = rSku.getResult();
                if (!Objects.equal(existSku.getItemId(), itemDistributionPrice.getItemId())){
                    log.error("sku(id={}) is not belong to item(id={})", existSku.getId(), itemDistributionPrice.getItemId());
                    throw new JsonResponseException("sku.not.belong.to.item");
                }
                Sku sku = new Sku();
                sku.setId(skuDistributionPrice.getSkuId());
                Map<String, Integer> extraPrice = existSku.getExtraPrice();
                Map<String, String> extra = existSku.getExtraMap();
                if (extraPrice == null) {
                    extraPrice = new HashMap<>();
                }
                extraPrice.put(DistributionConstants.SKU_DISTRIBUTION_PRICE, skuDistributionPrice.getDistributionPrice().intValue());
                // modify by liuchao 20190401
                extraPrice.put(DistributionConstants.SUPPLY_PRICE,skuDistributionPrice.getSupplyPrice().intValue());
                /*extraPrice.put(DistributionConstants.SKU_PROFIT, skuDistributionPrice.getProfit().intValue());*/
                sku.setExtraPrice(extraPrice);
                if (extra == null) {
                    extra = new HashMap<>();
                }
                extra.put(DistributionConstants.SELL_IN_WE_SHOP, "true");
                sku.setExtraMap(extra);
                Response<Boolean> response = skuWriteService.updateSku(sku);
                if (!response.getResult()){
                    log.error("failed to update sku({}), error code: {}", sku, response.getError());
                    throw new JsonResponseException(response.getError());
                }
            }

            Item item = new Item();
            item.setId(itemDistributionPrice.getItemId());
            Map<String, String> itemExtra = existItem.getExtra();
            if (itemExtra == null) {
                itemExtra = new HashMap<>();
            }
            itemExtra.put(DistributionConstants.SELL_IN_WE_SHOP, "true");
            item.setExtra(itemExtra);
            Response<Boolean> response = itemWriteService.update(item);
            if (!response.getResult()){
                log.error("failed to update item({}), error code: {}", item, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to set distribution price ({}), cause: {}", itemDistributionPrice, Throwables.getStackTraceAsString(e));
            return Response.fail("distribution.price.set.fail");
        }
    }
    @Transactional
    public Response<Boolean> updateSkuPrice(List<Sku> skuList){
        Response<Boolean> res=new Response<Boolean>();
        boolean result=true;
        for(Sku sku:skuList){
            result= skuWriteService.updateSku(sku).getResult();
        }
        res.setResult(result);
        return res;
    }

}
