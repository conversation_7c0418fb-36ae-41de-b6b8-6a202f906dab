package moonstone.web.front.component.order.convert;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.encrypt.constant.RegexConstant;
import com.danding.encrypt.utils.AESUtils;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.ItemSnapShotCacheHolder;
import moonstone.category.model.ShopCategory;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.model.WithExtraMap;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.EncryptHelper;
import moonstone.order.bo.OrderReceiverInfoBO;
import moonstone.order.bo.ShopOrderProfitBO;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.OrderFlagEnum;
import moonstone.order.enu.ProfitWithdrawRecordStatusEnum;
import moonstone.order.model.*;
import moonstone.order.model.result.OrderPaymentInfoDO;
import moonstone.order.model.result.OrderShipmentInfoDO;
import moonstone.order.service.ShopOrderReadService;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.user.bo.PayerInfoBO;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.PayerInfoRecord;
import moonstone.user.model.User;
import moonstone.user.model.view.UserLevelView;
import moonstone.web.core.component.cache.UserWxCacheHolder;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.ShopCategoryItemCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.front.component.order.view.OrderExcelExportView;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 订单导出中的各种模型转换
 */
@Slf4j
@Component
public class OrderExportConvertor {

    @Autowired
    private UserWxCacheHolder userWxCacheHolder;

    @Resource
    private UserCacheHolder userCacheHolder;

    @Autowired
    private SubStoreCache subStoreCache;

    @Autowired
    private GuiderCache guiderCache;

    @Autowired
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private ItemCacheHolder itemCacheHolder;

    @Resource
    private ItemSnapShotCacheHolder itemSnapShotCacheHolder;

    @Autowired
    private ShopCategoryItemCache shopCategoryItemCache;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    private static final int GUIDER_PROFIT = 2 << 11;

    public PayerInfoBO convert(PayerInfoRecord payerInfoRecord) {
        PayerInfoBO target = new PayerInfoBO();
        target.setOrderId(payerInfoRecord.orderId());

        //用户的支付账号与名称 导出脱敏展示
        var out = PayerInfo.Helper.decodeWithoutCache(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey),
                payerInfoRecord.info());
        if (Objects.nonNull(out)) {
            if (out.getName() != null) {
                target.setPayerName(out.getName().replaceAll(RegexConstant.NAME,RegexConstant.NAME_REPLACE));
            }
            if (out.getNo() != null) {
                target.setPayerNo(out.getNo().replaceAll(RegexConstant.IDCARD,RegexConstant.IDCARD_REPLACE));
            }
        }

        return target;
    }

    public OrderReceiverInfoBO convert(OrderReceiverInfo receiverInfo, ShopOrder shopOrder) {
        OrderReceiverInfoBO target = new OrderReceiverInfoBO();
        if (shopOrder == null) {
            return target;
        }
        target.setOrderId(shopOrder.getId());

        //收件人信息
        if (receiverInfo != null && receiverInfo.getReceiverInfo() != null) {
            receiverInfo.getReceiverInfo().desensitization();
            target.setReceiveUserName(receiverInfo.getReceiverInfo().getReceiveUserName());
            target.setMobile(receiverInfo.getReceiverInfo().getMobile());

            var builder = new StringBuilder();
            builder.append(receiverInfo.getReceiverInfo().getProvince())
                    .append("-")
                    .append(receiverInfo.getReceiverInfo().getCity())
                    .append("-")
                    .append(receiverInfo.getReceiverInfo().getRegion());
            if (StrUtil.isNotBlank(receiverInfo.getReceiverInfo().getStreet())) {
                builder.append("-")
                        .append(receiverInfo.getReceiverInfo().getStreet());
            }
            builder.append(" ")
                    .append(receiverInfo.getReceiverInfo().getDetail());
            target.setAddress(builder.toString());
        } else {
            var mobile = userCacheHolder.findByUserId(shopOrder.getBuyerId()).map(User::getMobile).orElse(null);
            target.setMobile(mobile);
        }

        return target;
    }

    public Pair<Long, ShopOrderProfitBO> convert(ShopOrder shopOrder, List<BalanceDetail> balanceDetailList,
                                                 Map<Long, List<ProfitWithdrawRecord>> profitWithdrawRecordMap,
                                                 Map<Long, AccountStatementDetail> accountStatementDetailMap,
                                                 Map<Long, WithDrawProfitApply> withdrawApplyMap,
                                                 Map<Long, List<OrderRoleSnapshot>> roleSnapshotMap) {
        if (shopOrder == null || StringUtils.isBlank(shopOrder.getOutFrom())) {
            return null;
        }

        ShopOrderProfitBO targetBO = new ShopOrderProfitBO();
        if (CollectionUtils.isEmpty(balanceDetailList)) {
            return Pair.of(shopOrder.getId(), targetBO);
        }

        //预计收益
        balanceDetailList.stream()
                .filter(profit -> !profit.isPresent())
                .forEach(profit -> {
                    String status = "待提现";
                    if (shopOrder.getStatus() == 3) {
                        status = "利润状态异常";
                    }

                    var roleSnapshots = roleSnapshotMap.get(shopOrder.getId());
                    if (CollectionUtils.isEmpty(roleSnapshots)) {
                        appendProfitInfo(targetBO, profit, shopOrder, status);
                    } else {
                        appendProfitInfo(targetBO, profit, status, roleSnapshots);
                    }
                });

        //可提现收益
        balanceDetailList.stream()
                .filter(BalanceDetail::isPresent)
                .forEach(profit -> {
                    var isPaid = isPaid(profit.getId(), profitWithdrawRecordMap, withdrawApplyMap);
                    var audited = isAudited(profit.getId(), profitWithdrawRecordMap);
                    var accountStatementCreated = (accountStatementDetailMap.get(profit.getId()) != null);
                    var apply = !audited && isApplied(profit.getId(), profitWithdrawRecordMap);

                    String status = isPaid ? "已支付" : (audited ? "已审核，待付款" :
                            (apply ? "待审核" : (accountStatementCreated ? "已生成账单" : "待提现")));

                    var roleSnapshots = roleSnapshotMap.get(shopOrder.getId());
                    if (CollectionUtils.isEmpty(roleSnapshots)) {
                        appendProfitInfo(targetBO, profit, shopOrder, status);
                    } else {
                        appendProfitInfo(targetBO, profit, status, roleSnapshots);
                    }
                });

        return Pair.of(shopOrder.getId(), targetBO);
    }

    private boolean isPaid(Long balanceDetailId, Map<Long, List<ProfitWithdrawRecord>> profitWithdrawRecordMap,
                           Map<Long, WithDrawProfitApply> withdrawApplyMap) {
        List<ProfitWithdrawRecord> list = profitWithdrawRecordMap.get(balanceDetailId);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }

        return list.stream()
                .map(profitWithdrawRecord -> withdrawApplyMap.get(profitWithdrawRecord.withdrawId()))
                .filter(Objects::nonNull)
                .anyMatch(WithDrawProfitApply::isPaid);
    }

    /**
     * 从快照信息里定位利润归属
     *
     * @param shopOrderProfit
     * @param profit
     * @param status
     * @param orderRoleSnapshots
     */
    private void appendProfitInfo(ShopOrderProfitBO shopOrderProfit, BalanceDetail profit, String status,
                                  List<OrderRoleSnapshot> orderRoleSnapshots) {
        var snapshot = findSnapshot(profit, orderRoleSnapshots);
        if (snapshot == null) {
            return;
        }

        var currentRole = SubStoreUserIdentityEnum.parse(snapshot.getUserRole());
        if (currentRole == null) {
            return;
        }
        switch (currentRole) {
            case STORE_GUIDER -> {
                shopOrderProfit.setCRole("导购");
                shopOrderProfit.setCProfit(toPrice(profit.getChangeFee()));
                shopOrderProfit.setCStatus(status);
                shopOrderProfit.setCName(snapshot.getName());

                shopOrderProfit.setCMobile(snapshot.getMobile());
            }
            case SUB_STORE -> {
                shopOrderProfit.setBProfit(toPrice(profit.getChangeFee()));
                shopOrderProfit.setBRole("门店");
                shopOrderProfit.setBName(snapshot.getName());
                shopOrderProfit.setBStatus(status);
                ShopOrder shopOrder = shopOrderReadService.findById(snapshot.getShopOrderId()).getResult();
                shopOrderProfit.setBId(shopOrder.getOutShopId());
                shopOrderProfit.setBMobile(snapshot.getMobile());
                shopOrderProfit.setBProvince(snapshot.getProvince());
                shopOrderProfit.setBCity(snapshot.getCity());
            }
            case SERVICE_PROVIDER -> {
                if ((profit.getStatus() & GUIDER_PROFIT) == GUIDER_PROFIT) {
                    shopOrderProfit.setCRole("导购");
                    shopOrderProfit.setCProfit(toPrice(profit.getChangeFee()));
                    shopOrderProfit.setCStatus(status);

                    var name = orderRoleSnapshots.stream()
                            .filter(Objects::nonNull)
                            .filter(entity -> SubStoreUserIdentityEnum.STORE_GUIDER.getCode().equals(entity.getUserRole()))
                            .map(OrderRoleSnapshot::getName)
                            .findAny().orElse("#[无导购, 故服务商享受]");
                    shopOrderProfit.setCName(name);
                } else {
                    shopOrderProfit.setARole("服务商");
                    shopOrderProfit.setAProfit(toPrice(profit.getChangeFee()));
                    shopOrderProfit.setAStatus(status);
                    shopOrderProfit.setAName(snapshot.getName());
                    shopOrderProfit.setAProvince(snapshot.getProvince());
                    shopOrderProfit.setAMobile(snapshot.getMobile());
                }
            }
        }
    }

    private OrderRoleSnapshot findSnapshot(BalanceDetail profit, List<OrderRoleSnapshot> orderRoleSnapshots) {
        if (CollectionUtils.isEmpty(orderRoleSnapshots)) {
            return null;
        }

        var found = orderRoleSnapshots.stream()
                .filter(Objects::nonNull)
                .filter(orderRoleSnapshot -> orderRoleSnapshot.getUserId().equals(profit.getUserId()))
                .findAny()
                .orElse(null);
        if (found != null && SubStoreUserIdentityEnum.SUB_STORE.getCode().equals(found.getUserRole())) {
            return found;
        }

        // 是导购利润
        if ((profit.getStatus() & GUIDER_PROFIT) == GUIDER_PROFIT) {
            return orderRoleSnapshots.stream()
                    .filter(orderRoleSnapshot -> SubStoreUserIdentityEnum.STORE_GUIDER.getCode().equals(orderRoleSnapshot.getUserRole()))
                    .findAny()
                    .orElse(null);
        }

        return found;
    }

    /**
     * 依照具体情况，填充 导购/门店/服务商 的利润
     *
     * @param shopOrderProfit
     * @param profit
     * @param shopOrder
     * @param status
     */
    public void appendProfitInfo(ShopOrderProfitBO shopOrderProfit, BalanceDetail profit, ShopOrder shopOrder,
                                 String status) {
        var subStore = subStoreCache.findByShopIdAndUserId(shopOrder.getShopId(), profit.getUserId());
        if (subStore.isPresent()) {
            // 门店利润
            shopOrderProfit.setBId(String.valueOf(subStore.get().getId()));
            shopOrderProfit.setBProfit(toPrice(profit.getChangeFee()));
            shopOrderProfit.setBRole("门店");
            shopOrderProfit.setBName(subStore.get().getName());
            shopOrderProfit.setBStatus(status);
            shopOrderProfit.setBMobile((subStore.get().getMobile()));
        } else if ((profit.getStatus() & GUIDER_PROFIT) == GUIDER_PROFIT) {
            // 导购利润
            shopOrderProfit.setCRole("导购");
            shopOrderProfit.setCProfit(toPrice(profit.getChangeFee()));
            shopOrderProfit.setCStatus(status);

            Optional<SubStoreTStoreGuider> guider = findGuider(shopOrder);
            if (guider.isPresent()) {
                shopOrderProfit.setCName(guider.get().getStoreGuiderNickname());
                shopOrderProfit.setCMobile((guider.get().getStoreGuiderMobile()));
                shopOrderProfit.setCExisted(true);
            } else {
                shopOrderProfit.setCName("#[无导购, 故服务商享受]");
                shopOrderProfit.setCExisted(false);
            }
        } else {
            // 服务商利润
            shopOrderProfit.setARole("服务商");
            shopOrderProfit.setAProfit(toPrice(profit.getChangeFee()));
            shopOrderProfit.setAStatus(status);

            var service = serviceProviderCache.findServiceProviderByShopIdAndUserId(shopOrder.getShopId(), profit.getUserId());
            shopOrderProfit.setAName(service.map(UserLevelView::getRelation).map(WithExtraMap::getExtra).map(extra -> extra.get("name")).orElse(""));
        }
    }

    private Optional<SubStoreTStoreGuider> findGuider(ShopOrder shopOrder) {
        if (shopOrder.getReferenceId() != null) {
            return guiderCache.findByShopIdAndUserId(shopOrder.getShopId(), shopOrder.getReferenceId());
        } else {
            return guiderCache.findByShopIdAndUserId(shopOrder.getShopId(), shopOrder.getBuyerId());
        }
    }

    private boolean isAudited(Long balanceDetailId, Map<Long, List<ProfitWithdrawRecord>> profitWithdrawRecordMap) {
        List<ProfitWithdrawRecord> list = profitWithdrawRecordMap.get(balanceDetailId);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }

        return list.stream()
                .filter(Objects::nonNull)
                .anyMatch(e -> ProfitWithdrawRecordStatusEnum.AUDIT_FINISHED.getCode().equals(e.status()));
    }

    private boolean isApplied(Long balanceDetailId, Map<Long, List<ProfitWithdrawRecord>> profitWithdrawRecordMap) {
        List<ProfitWithdrawRecord> list = profitWithdrawRecordMap.get(balanceDetailId);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }

        return list.stream()
                .filter(Objects::nonNull)
                .anyMatch(e -> ProfitWithdrawRecordStatusEnum.WAIT_FOR_AUDIT.getCode().equals(e.status()));
    }

    /**
     * 价格字段格式化
     *
     * @param fee
     * @return
     */
    public String toPrice(Long fee) {
        if (fee == null) {
            return null;
        }
        return BigDecimal.valueOf(fee).divide(new BigDecimal(100), 2, RoundingMode.DOWN).toString();
    }

    public void appendProfitInfo(OrderExcelExportView target, ShopOrderProfitBO shopOrderProfit) {
        try {
            if (shopOrderProfit == null) {
                return;
            }
            target.setBId(shopOrderProfit.getBId());

            target.setAProvince(shopOrderProfit.getAProvince());
            target.setAMobile(DesensitizedUtil.mobilePhone(shopOrderProfit.getAMobile()));
            target.setBProvince(shopOrderProfit.getBProvince());
            target.setBCity(shopOrderProfit.getBCity());
            target.setBMobile(DesensitizedUtil.mobilePhone(shopOrderProfit.getBMobile()));
            target.setCMobile(DesensitizedUtil.mobilePhone(shopOrderProfit.getCMobile()));

            target.setAName(shopOrderProfit.getAName());
            target.setAProfit(shopOrderProfit.getAProfit());
            target.setARole(shopOrderProfit.getARole());
            target.setAStatus(shopOrderProfit.getAStatus());

            target.setBName(shopOrderProfit.getBName());
            target.setBProfit(shopOrderProfit.getBProfit());
            target.setBRole(shopOrderProfit.getBRole());
            target.setBStatus(shopOrderProfit.getBStatus());

            target.setCName(shopOrderProfit.getCName());
            target.setCProfit(shopOrderProfit.getCProfit());
            target.setCRole(shopOrderProfit.getCRole());
            target.setCStatus(shopOrderProfit.getCStatus());
        } catch (Exception ex) {
            log.error("OrderExportConvertor.appendProfitInfo error ", ex);
        }
    }

    public void appendPayerInfo(OrderExcelExportView target, PayerInfoBO payerInfo) {
        try {
            if (payerInfo == null) {
                return;
            }

            target.setPayerName(payerInfo.getPayerName());
            target.setPayerNo(payerInfo.getPayerNo());
        } catch (Exception ex) {
            log.error("OrderExportConvertor.appendPayerInfo error ", ex);
        }
    }

    public void appendUserWxInfo(OrderExcelExportView target, ShopOrder shopOrder) {
        try {
            if (shopOrder == null) {
                return;
            }

            //用户的微信账号信息
            var userWx = userWxCacheHolder.findByUserIdAndShopId(shopOrder.getBuyerId(), shopOrder.getShopId());
            if (userWx != null) {
                target.setOpenId(userWx.getOpenId());
                target.setWxBuyerName(userWx.getNickName());
            }
        } catch (Exception ex) {
            log.error("OrderExportConvertor.appendPayerInfo error ", ex);
        }
    }

    public void appendReceiverInfo(OrderExcelExportView target, OrderReceiverInfoBO orderReceiverInfo) {
        try {
            target.setMobile(orderReceiverInfo.getMobile());
            target.setReceiveUserName(orderReceiverInfo.getReceiveUserName());
            target.setAddress(orderReceiverInfo.getAddress());
        } catch (Exception ex) {
            log.error("OrderExportConvertor.appendReceiverInfo error ", ex);
        }
    }

    public void appendItemInfo(OrderExcelExportView target) {
        try {

            if(StringUtils.isEmpty(target.getCategoryNameSnapshot())){
                // 类目  如果订单上不存在则使用当前的类目
                String categoryName = getCategoryName(target.getItemId(), target.getShopId());
                target.setCategoryNameSnapshot(categoryName);
                log.info("订单导出 CategoryName 取当前的 {}", categoryName);
            }else{
                log.info("订单导出 CategoryName 取订单上的 {}", target.getCategoryNameSnapshot());
            }
            //商品
            var item = itemCacheHolder.findItemById(target.getItemId());
            if (item != null) {
                target.setBarCode(item.getBarCode());
                target.setItemName(item.getName());
            }

            //商品的交易快照
            var snapShot = itemSnapShotCacheHolder.findBySnapShotId(target.getItemSnapshotId());
            if (snapShot != null) {
                target.setBarCode(snapShot.getBarCode());
                target.setItemCode(snapShot.getItemCode());
                target.setItemName(StringUtils.isNotBlank(snapShot.getName()) ? snapShot.getName() : target.getItemName());
            }
        } catch (Exception ex) {
            log.error("OrderExportConvertor.appendItemInfo error ", ex);
        }
    }

    public String getCategoryName(Long itemId, Long shopId) {
        try {
            //当前 shopId 下的所有商品类目信息
            List<ShopCategoryItemCache.Category> categoryList = shopCategoryItemCache.findByShopId(shopId);
            if (CollectionUtils.isEmpty(categoryList)) {
                return StringUtils.EMPTY;
            }

            //过滤出当前 itemId 对应的类目信息
            categoryList = categoryList.stream()
                    .filter(category -> {
                        if (CollectionUtils.isEmpty(category.categoryItemList())) {
                            return false;
                        }

                        return category.categoryItemList().stream()
                                .filter(e -> e != null && e.getItemId() != null)
                                .anyMatch(shopCategoryItem -> shopCategoryItem.getItemId().equals(itemId));
                    })
                    .map(category -> new ShopCategoryItemCache.Category(category.category(),
                            category.categoryItemList().stream()
                                    .filter(shopCategoryItem -> shopCategoryItem.getItemId().equals(itemId))
                                    .collect(Collectors.toList())))
                    .collect(Collectors.toList());

            //构造并返回
            return buildCategoryName(categoryList);
        } catch (Exception ex) {
            log.error("OrderExportConvertor.getCategoryName error ", ex);
            return StringUtils.EMPTY;
        }
    }

    private String buildCategoryName(List<ShopCategoryItemCache.Category> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return "#[空白店铺类别]";
        }

        var cnameBuilder = new StringBuilder();
        var head = true;

        for (ShopCategoryItemCache.Category category : categoryList) {
            ShopCategory name = category.category();
            if (name == null || name.getId() == 0L) {
                continue;
            }

            if (!head) {
                cnameBuilder.append(",");
            } else {
                head = false;
            }

            if (ObjectUtils.isEmpty(name.getName())) {
                cnameBuilder.append("#[空白店铺类别]");
            } else {
                cnameBuilder.append(name.getName());
            }
        }
        return cnameBuilder.toString();
    }

    public void appendPaymentInfo(OrderExcelExportView target, OrderPaymentInfoDO payment) {
        try {
            if (payment != null) {
                target.setPaySerialNo(payment.getPaySerialNo());
                target.setPaidAt(DateUtil.toString(payment.getPaidAt()));
                target.setPaymentOutId(payment.getOutId());
            }
        } catch (Exception ex) {
            log.error("OrderExportConvertor.appendPaymentInfo error ", ex);
        }
    }

    public void appendShipmentInfo(OrderExcelExportView target, OrderShipmentInfoDO shipmentByShopOrder,
                                   OrderShipmentInfoDO shipmentBySkuOrder) {
        try {
            OrderShipmentInfoDO shipment = shipmentByShopOrder != null ? shipmentByShopOrder : shipmentBySkuOrder;

            if (shipment != null) {
                target.setConfirmAt(DateUtil.toString(shipment.getConfirmAt()));
                target.setShipmentName(shipment.getShipmentCorpName());
                target.setShipmentId(shipment.getShipmentSerialNo());
                target.setShipmentTime(DateUtil.toString(shipment.getCreatedAt()));
            }
        } catch (Exception ex) {
            log.error("OrderExportConvertor.appendShipmentInfo error ", ex);
        }
    }

    public void appendShopOrderInfo(OrderExcelExportView target, ShopOrder shopOrder) {
        try {
            if (shopOrder.getOutFrom() != null) {
                target.setOutFromName(switch (OrderOutFrom.fromCode(shopOrder.getOutFrom())) {
                    case SUB_STORE -> "门店分销";
                    case LEVEL_Distribution -> "阶梯分销";
                    case WE_SHOP -> "选品分销";
                    default -> "普通分销";
                });
            }

            target.setDeclaredNo(shopOrder.getDeclaredId());
        } catch (Exception ex) {
            log.error("OrderExportConvertor.appendShopOrderInfo error ", ex);
        }
    }

    public OrderExcelExportView convert(SkuOrder skuOrder) {
        try {
            OrderExcelExportView target = new OrderExcelExportView();

            target.setOrderId(skuOrder.getOrderId());
            target.setShopName(skuOrder.getShopName());
            target.setBuyerId(skuOrder.getBuyerId());
            target.setItemName(skuOrder.getItemName());
            target.setQuantity(skuOrder.getQuantity());

            target.setOuterSkuId(skuOrder.getOuterSkuId());
            target.setShipFee(toPrice(skuOrder.getShipFee()));

            target.setCreateAt(DateUtil.toString(skuOrder.getCreatedAt()));
            target.setFee(toPrice(skuOrder.getFee()));
            target.setTax(toPrice(skuOrder.getTax()));
            target.setOriginFee(toPrice(skuOrder.getFee() / skuOrder.getQuantity()));
            target.setDiscount(toPrice(skuOrder.getDiscount()));

            switch (skuOrder.getPushStatus()) {
                case 2 -> target.setPushStatus("已推送");
                case 4 -> target.setPushStatus("待支付单推送");
                case -1 -> target.setPushStatus("推送失败");
                default -> target.setPushStatus("无需推送");
            }

            target.setStatus(OrderStatus.fromInt(skuOrder.getStatus()).intoString());
            target.setSkuOrderId(skuOrder.getId());
            target.setShopId(skuOrder.getShopId());
            target.setItemId(skuOrder.getItemId());
            target.setItemSnapshotId(skuOrder.getItemSnapshotId());

            target.setOrderFlags(String.join(",", OrderFlagEnum.getFrontEndFlagNames(skuOrder.getFlag())));
            // 设置类目
            target.setCategoryNameSnapshot(skuOrder.getCategoryNameSnapshot());
            return target;
        } catch (Exception ex) {
            log.error("OrderExportConvertor.appendSkuOrderInfo error ", ex);
            return null;
        }
    }

    public void appendUserInfo(OrderExcelExportView target, User user) {
        if (user == null || target == null) {
            return;
        }

        // 如果是明文 则展示密文 如果是密文 则不变
        if (!StringUtils.isBlank(AESUtils.decrypt(user.getMobile()))) {
            target.setPayerPhone(AESUtils.decrypt(user.getMobile()).replaceAll(RegexConstant.PHONE,RegexConstant.PHONE_REPLACE));
        } else {
            target.setPayerPhone(user.getMobile().replaceAll(RegexConstant.PHONE,RegexConstant.PHONE_REPLACE));
        }

    }

}
