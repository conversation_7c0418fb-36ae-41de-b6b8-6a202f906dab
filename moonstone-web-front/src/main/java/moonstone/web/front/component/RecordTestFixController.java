package moonstone.web.front.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.web.core.component.RecordManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

@Profile({"test", "dev", "integration", "stag"})
@RestController
@Slf4j
@RequestMapping("/api/record")
public class RecordTestFixController {
    @Value("${record.key_set.name:RecordKeySet}")
    private String recordKeySetNameInRedis;
    @Autowired
    private RecordManager recordManager;
    @Autowired
    private JedisPool jedisPool;

    @DeleteMapping("/deleteAll")
    public APIResp<Boolean> deleteAll() {
        try (Jedis jedis = jedisPool.getResource()) {
            for (String smember : jedis.smembers(recordKeySetNameInRedis)) {
                jedis.del(smember);
            }
        }
        return APIResp.ok(true);
    }

    @PostMapping("/mockDay")
    public APIResp<Boolean> mockDay() {
        recordManager.batchUpdateRecord();
        return APIResp.ok(true);
    }
}

