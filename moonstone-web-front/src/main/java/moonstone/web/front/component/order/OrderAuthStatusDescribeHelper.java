package moonstone.web.front.component.order;

import io.terminus.common.model.Paging;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.order.dto.OrderGroup;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.ShopOrder;
import moonstone.shop.model.Shop;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.web.core.order.dto.OrderGroupViewObject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class OrderAuthStatusDescribeHelper {
    private final ShopCacheHolder shopCacheHolder;

    /**
     * 修饰订单审核状态
     *
     * @param queryPaging 查询结果
     * @return 带有审核数据的Paging
     */
    public Paging<OrderGroupViewObject> describeAuthStatus(Paging<OrderGroup> queryPaging) {
        /// 描述化审核状态
        List<OrderGroupViewObject> orderGroupViewObjects = new ArrayList<>();
        for (OrderGroup orderGroup : queryPaging.getData()) {
            OrderGroupViewObject orderGroupViewObject = new OrderGroupViewObject();
            BeanUtils.copyProperties(orderGroup, orderGroupViewObject);
            /// 1. 判断shop是否开启审核
            Shop shop = shopCacheHolder.findShopById(orderGroup.getShopOrder().getShopId());
            /// 2. 如果开启了审核则 设置订单显示状态
            if (ShopFunctionSlice.build(shop).isShopOpenOrderAuth()) {
                if (Objects.equals(OrderStatus.PAID.getValue(), orderGroupViewObject.getShopOrder().getStatus())) {
                    orderGroupViewObject.setWaitingSellerAuthDesc(orderGroupViewObject.getWaitingSellerAuth() != null && orderGroupViewObject.getWaitingSellerAuth() ? "待审核" : "审核通过");
                }
            }
            OrderStatus status = OrderStatus.fromInt(orderGroupViewObject.getShopOrder().getStatus());
            /// 3. 此时如果订单状态为null 则再设置其本来的订单状态
            if (orderGroupViewObject.getWaitingSellerAuthDesc() == null) {
                orderGroupViewObject.setWaitingSellerAuthDesc(status.intoString());
            }
            // TODO 评价状态
            buildEvaluateStatus(orderGroupViewObject);
            orderGroupViewObjects.add(orderGroupViewObject);
        }
        Paging<OrderGroupViewObject> paging = new Paging<>();
        paging.setTotal(queryPaging.getTotal());
        paging.setData(orderGroupViewObjects);
        return paging;
    }

    private void buildEvaluateStatus(OrderGroupViewObject orderGroupViewObject) {
        ShopOrder shopOrder = orderGroupViewObject.getShopOrder();
        if (!Objects.equals(0, shopOrder.getStatus())) { // 除了未付款,其他都显示评价按钮
            orderGroupViewObject.setEvaluateStatus(shopOrder.getEvaluateStatus());
        }
    }

}
