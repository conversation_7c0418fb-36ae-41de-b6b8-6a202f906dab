/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */

package moonstone.web.front.component.cart;

import com.google.common.base.Throwables;
import com.google.common.collect.FluentIterable;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.ShopCacheHolder;
import moonstone.cart.dto.RichCart;
import moonstone.cart.dto.RichCartItem;
import moonstone.cart.model.CartItem;
import moonstone.cart.service.CartReadService;
import moonstone.cart.service.CartWriteService;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.LogUtil;
import moonstone.item.model.Item;
import moonstone.item.model.ItemDetail;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.shop.model.Shop;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> [modify], Wx[Origin]
 */
@Slf4j
@Component
public class CartReader {

    @Resource
    private CartReadService cartReadService;

    @Resource
    private CartWriteService cartWriteService;

    @Resource
    private ShopCacheHolder shopCacheHolder;

    @Resource
    private ItemCacheHolder itemCacheHolder;

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private SkuReadService skuReadService;

    /**
     * 获取当前登录用户的永久购物车
     *
     * @param commonUser 当前登录用户
     * @return 购物车信息
     */
    public Response<List<RichCart>> findByUser(CommonUser commonUser) {
        try {
            List<RichCart> richCarts = getUserCart(commonUser.getId(), null);
            return Response.ok(richCarts);
        } catch (Exception e) {
            log.error("fail to get permanent cart by user={}, cause:{}", commonUser, Throwables.getStackTraceAsString(e));
            return Response.fail("cart.query.fail");
        }
    }

    /**
     * 获取当前登录用户某店铺的永久购物车
     *
     * @param commonUser 当前登录用户
     * @return 购物车信息
     */
    public Response<List<RichCart>> findByUserAndShopId(CommonUser commonUser, Long shopId) {
        try {
            return Response.ok(getUserCart(commonUser.getId(), shopId));
        } catch (Exception e) {
            log.error("{} fail to query Cart by User[{}] ShopId[{}]", LogUtil.getClassMethodName(),
                    commonUser.getId(), shopId, e);
            return Response.fail("cart.query.fail");
        }
    }

    protected List<RichCart> getUserCart(Long userId, Long shopId) {
        // 获取购物车商品列表
        List<CartItem> cartItems = shopId == null ? cartReadService.findByUserId(userId).getResult()
                : cartReadService.findByUserIdAndShopId(userId, shopId).getResult();
        if (Objects.requireNonNull(cartItems).isEmpty()) {
            return Collections.emptyList();
        }
        // 根据店铺分组
        ImmutableListMultimap<Long, CartItem> shopIdCartItems = FluentIterable
                .from(cartItems)
                .index(CartItem::getShopId);
        // 拼装
        List<RichCart> richCarts = new ArrayList<>();
        for (Long oneShopId : shopIdCartItems.keySet()) {
            // 获取店铺详情,若查找失败,就跳过
            Shop shop = shopCacheHolder.findShopById(oneShopId);
            if (shop == null) {
                log.warn("{} fail to find shop by id={} when user[Id => {}] view cart, skip",
                        LogUtil.getClassMethodName(), shopId, userId);
                continue;
            }

            // 获取购物车记录详情,包括商品的详情,若商品失效,就跳过
            List<RichCartItem> richCartItems = convertToRichCartItem(shopIdCartItems.get(oneShopId));
            if (richCartItems.isEmpty()) {
                continue;
            }
            RichCart richCart = new RichCart();
            richCart.setShopId(oneShopId);
            richCart.setShopName(shop.getName());
            richCart.setShopImage(shop.getImageUrl());
            richCart.setSellerId(shop.getUserId());
            richCart.setCartItems(richCartItems);
            richCart.flushUpdateAt();
            richCarts.add(richCart);
        }

        return richCarts;
    }

    protected List<RichCartItem> convertToRichCartItem(List<CartItem> cartItems) {
        List<RichCartItem> richCartItems = Lists.newArrayList();
        // 获取sku,商品名,商品主图
        for (CartItem cartItem : cartItems) {
            Sku sku = skuReadService.findSkuById(cartItem.getSkuId()).getResult();
            if (Objects.isNull(sku)) {
                log.warn("{} fail to view sku[Id => {}] of cart[Id => {}, ShopId => {}, BuyerId => {}]", LogUtil.getClassMethodName(), cartItem.getSkuId(), cartItem.getId(), cartItem.getShopId(), cartItem.getBuyerId());
                continue;
            }
            // 商品失效,就删除这条购物车
            Item item = itemCacheHolder.findItemById(sku.getItemId());
            if (item == null || Objects.equals(item.getStatus(), -3)) {
                log.warn("item id={} already deleted, remove from cart", item == null ? "not found" : item.getId());
                cartWriteService.deleteById(cartItem.getId());
                continue;
            }

            //定时改价改图
            itemReadService.processItemForActivity(item, new ItemDetail());
            itemReadService.processSkuForActivity(Lists.newArrayList(sku));

            RichCartItem richCartItem = new RichCartItem();
            richCartItem.setSku(sku);
            richCartItem.setCartItem(cartItem);
            richCartItem.setItemName(item.getName());
            richCartItem.setItemStatus(item.getStatus());
            richCartItem.setItemImage(item.getMainImage_());
            richCartItem.setSourceType(item.getSourceType());

            richCartItems.add(richCartItem);
        }
        return richCartItems;
    }
}
