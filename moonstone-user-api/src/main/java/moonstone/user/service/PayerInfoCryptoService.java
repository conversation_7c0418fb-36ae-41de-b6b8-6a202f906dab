package moonstone.user.service;

import moonstone.user.vo.PayerInfoDeCodeReq;
import moonstone.user.vo.PayerInfoDeCodeRes;
import org.apache.commons.codec.DecoderException;

import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;

public interface PayerInfoCryptoService {
    PayerInfoDeCodeRes decode(PayerInfoDeCodeReq req) throws KeyStoreException, IOException, CertificateException, NoSuchAlgorithmException, DecoderException;
}
