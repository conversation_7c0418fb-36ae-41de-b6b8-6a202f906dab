<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>moonstone-mall</artifactId>
        <groupId>moonstone</groupId>
        <version>1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>showcase-admin</artifactId>
    <dependencies>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-project-admin</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>moonstone</groupId>
                    <artifactId>moonstone-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>moonstone</groupId>
                    <artifactId>moonstone-web-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>danding-x-core-p6spy</artifactId>
            <version>1.2.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>danding-x-core-apm-http</artifactId>
            <version>1.2.1-RELEASE</version>
        </dependency>
        <!--mysql数据源代理工具：主要用于sql执行日志、事务分析-->
        <dependency>
            <groupId>com.github.gavlyukovskiy</groupId>
            <artifactId>p6spy-spring-boot-starter</artifactId>
            <version>1.6.4</version>
        </dependency>
        <dependency>
            <groupId>co.elastic.apm</groupId>
            <artifactId>apm-agent-api</artifactId>
            <version>1.34.1</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.9</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-web-admin</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <!-- moonstone standlone -->
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-common</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-user</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-item</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-category</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-trade</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-decoration</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-distribution</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!-- spring-boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jetty</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!-- pay -->
        <dependency>
            <groupId>io.terminus.pay</groupId>
            <artifactId>terminus-pay-bundle</artifactId>
            <version>${terminus-pay.version}</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <version>${scala.version}</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.8</version>
        </dependency>
    </dependencies>


    <build>
        <finalName>showcase-admin</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>1.5.2.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
