application:
  name: supply-shop-admin

environment: pre

logging:
#  file: /nfs/terminus-admin/terminus-admin.log
#  level.*: INFO
#  level.io.terminus: DEBUG
  config: classpath:config/test/logback.xml

ext.messages.classpath: messages_zh_CN

server:
  context-path: /
  port: 8080

session:
  cookie-domain: admin-inte.mall.cb3a8021ca3764031b900ba7774c3639b.cn-zhangjiakou.alicontainer.com
  cookie-context-path: /
  cookie-name: msid
  cookie-max-age: 1800
  source: redis
  serialize-type: json
  redis-host: redis-svc
  redis-port: 6379
  redis-index: 0
  redis-cluster: false
  redis-test-on-borrow: true
  redis-max-total: 10
  redis-max-idle: 0
  redis-prefix: afsession
  redis-auth: dd654321

parana:
  web:
    url: http://admin-inte.mall.cb3a8021ca3764031b900ba7774c3639b.cn-zhangjiakou.alicontainer.com
  mall:
    url: http://mall-pre.yang800.cn
  h5:
    url: https://m.mall.yang800.com
  wxa:
    url: https://m.mall.yang800.com
    appId: wx90e48a315747ccb9
    appSecret: ebb83ced7885897e4fd5ecfc403ed92d
  we:
    seller:
      url: https://m.mall.yang800.com
      appId: wx0fb538f6dc093892
      appSecret: 5d260bcf67eecf19e96a341b7b4eb95a
    buyer:
      url: https://m.mall.yang800.com
      appId: wxb25691b47b974630
      appSecret: 088dedd8ec3382f5ba981a2560afc187
  wxopen:
    componentAppId: wxfb7d4a7ab73f6ba6
    componentSecret: a9a41dcfd8e7b564a2ec93c0ee58fb9f
    componentToken: wx569abcf3c77ff1de
    componentAesKey: 0987654321qazwsxedcrfvtgbyhnujmik1234567890
    requestdomain: https://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com;https://www.yang800.com;https://m.yang800.com
    wsrequestdomain: wss://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com;wss://www.yang800.com
    uploaddomain: https://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com;https://www.yang800.com
    downloaddomain: https://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com;https://www.yang800.com
  alipayOpen:
    serviceAppId: 2021004143615727
    serviceAppPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp2EifbZj3XISeVR4odPh1kW1R+Ae0MuPVALJOSeXLqlqGZxyRckqXvNQPOL4MxPU4RGL9Q4ADoA7Y7IK8IBvBrdSUIzTIBzcB20T9X1q16JOeuKe1muONNb08sXYGyDeX4OGi0Bd6JWxeXix4LG8PZXXt9Q2MP/WfSeWe2UUyRTgaq+lkja2J6BsEESn52h0WiU02Sw3fC+PkqmDInLuhAp4k8FVe1P4Xt9CMcOdMOIfGAFsq118Md7s0er9j5uzBzx6DWQwK0SDREiqSk+/0dap5MUgZqYiUaBiZo3WgcDEicMyiM/qmKg/eyrG7s7y6t6PM+n8F4H2IDJIHNytywIDAQAB
    serviceAppPrivateKey: MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCr/o2Vrl75G8fVkm59AqE06Ux8sBA+vPbm820NU+gGG54Q1ADdfCfEIiUXtL4BIUCP+xJw2zVAsCsBA5eyp1W5miweZ4l1WTKx3eQWEIZSDfhKuOEb1rwRsMh18HGSSJFSip1ApvO6gsVs8FItKoI52uy1FPXMm2sioERuf5+FLnWmLnS0FgbLdhjw7dQs/Bbaf5CWRhO90DZusx/WOXRSZLqatk9lXCyQYWJZu9EJfdRHMlCoezNB4lqEN4wujvbEJHY4dONJ+Rv7iQ3jsq9WZCdJAwSlNS12DixgUMwsv+n7kPQBQD88QhBg8y2suCygECI9kpN/AWQRoK7zecIzAgMBAAECggEBAJdZKrIafQxse87eSkfLNpxe56WZMNJkakgU0VO3ECe/ybo5NIGyLTQUb9uKeuB3qF6Kq1GQNC42V8FOrgDR60el+UjQB0+yLEAEaB9abzhpfqISSlS7A0f17pCKAwacwPSHcZVg1Hy9colIeKeyDmJm2YEHbrybzwzrRwyNVYxVQjJSKqCTGIkPigOyHwafO5EljZJIcdTgmmH+bC1lAn8GLqe8IN9NRY+mQkQdGoQDDH03xDZLz/aRWPHcZOJnqMu1BLWDfLZ+n/GxbxbX++/X1tC3CekVKUV3H6oI0L0T7VC8p8ZQH2ct5ALmF26KmlVfBCLovVuvFhriOdOAgxECgYEA2iVMX68RlO1XYag3K03LO9FJMgWh1dG6VdyxMfaXSeRYBNkH1RAV+sSyeWFO0IlW5EzxRBkRV43qNgpDbVJvf6SW9yUUSk8aCpDGzd+OOkMxJxJMUfl3I0p1W5Kh9WO1ld5ml+ruui82DLf0Y1q++/d27QgNIBcQUbtVo/LO08UCgYEAydcR8tgxbNnQU5GRDH+ifE4TJ9maCloy10NxEIICNaJGyHaNNXxogSlo/tFY4aKnggDbt4sSbzYAQl0TeYT7YSBKWXc4Ncj4ctXCqfLMeM34AwaPO2Sfvv8kfWEzB81BJiq0Zyvv43a54LHWYDBH5kBYiHfdCnuWQiwjcYl4BZcCgYBalY5nVT1JyFy2srFmDAHOrxNj8C7JqTNlICV8h9c+2dErAixcbzeRUEiEWsHtmno4hzLQglBPOSD+m7hwFwEx0djWe9E3Ii3AIWpMZv6advhcLx7+E0dDolGlWvhh+6uVtiqH/whuy9f35BNZhn80BgieWO+KWclvBwmC8PqtuQKBgQCk5eauBd5rVZATlitwkJ4I/Cg+OFLW/lXm9BxdJjnz4338rI41Eky2qUQ0WohI3wAG//iBfUc0QGP509zjPLe65AyaBwKPPn9sIiOFgO14DJ4Aqs098rln6z6+iDiOuSvuqJj2aTj1FrxJTEEeGJPFEvDBnVujAtEBgtn6GhtKRQKBgQDD0DibDThMs0Vh2NvLX2vqr3Ufd7FJqmJ2vXp/1ypODjxeqjTu8v8yZZaG54mmvdenNlkpIpymMdfWNC9v1mWjfqG2XM8WnaXVnjkfrMcDGfdFkSQTB5IyZIWlTrkMES7zefQ1xdYkE+TaY3WIcwqPX4zEGwAiAsQfbCf5CLPWTw==
    serviceAppDecryptKey: huqzAm2wBuWhTVFG8Lcfhw==
    authNotifyUrl: https://mtest.mall-pre.yang800.cn/backend/api/shopAppAuth/alipay/authNotify
  substore:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3
  levelDistribution:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3

image:
  base:
    url: http://dante-img.oss-cn-hangzhou.aliyuncs.com
  protocol: http
  domain: dante-img.oss-cn-hangzhou.aliyuncs.com
  upload.allowedFileTypes: jpeg,jpg,gif,png,webp

oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  appKey: LTAI5tNGqjjEfJ1n3xbV42v9
  appSecret: ******************************
  bucketName: dante-img

search:
  #  host: localhost
  host: es7177-svc.parana-inte.svc.cluster.local
  port: 9200

item.search:
  index-name: items
  index-type: item
  mapping-path: item_mapping.json
  full-dump-range: 3000
  batch-size: 100

shop.search:
  index-name: shops
  index-type: shop
  mapping-path: shop_mapping.json
  full-dump-range: 3000
  batch-size: 100

weShopItem.search:
  index-name: t_we_shop_items
  index-type: we_shop_item
  mapping-path: we_shop_item_mapping.json
  full-dump-range: 3000
  batch-size: 100

mybatis:
  mapper-locations:  classpath*:mapper/*Mapper.xml
  type-aliases-package: >
    moonstone.(item|category|brand|shop|spu|user|file|membership|countryImage|integral).model,
    moonstone.(adv|thirdParty|shopWxa|wxa|shopMini).model,
    moonstone.(weCart|weShop|weDistributionApplication).model,
    moonstone.user.address.model,
    moonstone.user.area.model,
    moonstone.(order|cart|express|promotion|delivery|settle).model
  typeHandlersPackage: moonstone.common.type.handler

hazelcast:
  config: hazelcast-config.xml
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************************************************************
    username: parana_test
    password: b80c157@c3791
  elasticsearch:
    uris: http://es7177-svc.parana-inte.svc.cluster.local:9200
    connection-timeout: 3s
  data:
    mongodb:
      uri: mongodb://mongo.parana-inte.svc.cluster.local:27017/parana-pre
  http:
    multipart:
      max-file-size: 50Mb
      max-request-size: 50Mb
    server:
      undertow.max-http-post-size: 10MB
      tomcat.max-http-post-size: 10MB
      jetty.max-http-post-size: 10MB
  redis:
    host: redis-svc
    port: 6379
    password: dd654321
    timeout: 500
    database: 0
    jedis:
      pool:
        max-active: 100
        max-wait: -1
        max-idle: 8
        min-idle: 0

express.100:
  key: bAWWUdnn9775_test
  customer: 837CEA86CF64D4FEC5B4CEC59589E2D5_test
  regularUrl: http://poll.kuaidi100.com/poll/query.do

settle:
  enable: true
  commission.rate.default: 0

enable.order.auto.cancel.job: true

msg:
#  current:
#    smsService: aliYunSmsService
#    emailService: javaxEmailService
  aliyun:
    appKey: LTAI5tCR5p7wUaszs7VDrv9g
    appSecret: ******************************
#  javaxemail:
#    mailServerHost: smtp.exmail.qq.com
#    mailServerPort: 465
#    fromAddress: Y800<<EMAIL>>
#    userName: <EMAIL>
#    password: Dd111111

msg.template.list:
  - key: sms.user.login.code
    title: 登录验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_145495759","smsParam":{"code":"{{code}}"}}
  - key: sms.user.register.code
    title: 注册验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143863719","smsParam":{"code":"{{code}}"}}
  - key: sms.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868717","smsParam":{"code":"{{code}}"}}
  - key: sms.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868720","smsParam":{"code":"{{code}}"}}

msg.email:
  templates:
    - key: email.error.notice
      templateName: sendEmailErrorNotice
      subject: "但丁商城 - 异常通知"

wechat.applet:
  enable: true
  mchId: 1488913512
  appId: wx86fe5657944bcaa2
  secret: 8ac1cbf290b0fe98ff1142acf94e7351

pay:
  debug: false
  channel:
    alipay: enable
  notifyUrl: http://mall-pre.yang800.cn/backend/api/order/paid
  notifyUrlByRegisterPay: http://mall.yang800.cn/backend/api/order/paid/userRegister
  refundNotifyUrl: http://mall-pre.yang800.cn/backend/api/refund/notify
  returnUrl: http://mall-pre.yang800.cn/buyer/trade-success
  h5ReturnUrl: http://m.mall.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com/order/pay-success
  unionpay.wap.token.verifyCerDir: /nfs/terminus-admin
  unionpay.pc.token.verifyCerDir: /nfs/terminus-admin
  unionpay.app.token.verifyCerDir: /nfs/terminus-admin
  certFilePath: /nfs/certFiles/terminus-web
  allinpay:
    pay: https://syb-test.allinpay.com/apiweb/unitorder/pay
    refund: https://syb-test.allinpay.com/apiweb/tranx/refund
    query: https://syb-test.allinpay.com/apiweb/tranx/query
    ystBaseUrl: https://open.allinpay.com/gateway

alipay:
  pid: ****************
  key: pil5t34m4qsdor9ujuffkqfvrgfjt3mp
  account: <EMAIL>

#curator:
#  zk.host: zk-svc
#  zk.port: 2181
#  leader.path: /admin-leader

Y800:
  partnerCode: webB2C
  partnerKey: yang800testWebSC
  sourcePlatform: WEBSC
  sourcePlatformV3: webB2CV3
  merchantCode:
  wd.support: http://wd-support
  yang.support: http://yang-support-svc
  finance.project.url: http://staticstest.yang800.cn/xhr
  open.api.gate: http://outtest.order.yang800.cn/open/apiv2
nio:
  post.url: http://sync.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com/xhr/

#微分销商城前台域名
#m.mall.yang800: https://m.mall.yang800.com

#积分商城生成二维码的路径
m.mall.jifen.path: /nfs/certFiles/terminus-web/levelDistribution/image/
#积分商城生成二维码的url
m.mall.jifen.url: https://m.mall.yang800.com/ladder-distribution/credits-shop

#pay.integral.token:
#  returnUrl: http://mall.yang800.com/buyer/trade-success
#  refundNotifyUrl: http://mall.yang800.com/backend/api/refund/notify

#功能开关
function:
  switch:
    aoXinPush: false
    financePush: true
    unifiedPayment: true

mercury:
  pay:
    host: http://ccs-order-pre.yang800.com/mercury/pay/proxyAPI
    appCode: DDFXSC
    merchantCode: M2019030614100738125
    customs:
      notify: http://parana-pre-admin.mall.cb3a8021ca3764031b900ba7774c3639b.cn-zhangjiakou.alicontainer.com/backend/api/customs/payment/declare/notify

#ebpCode: 3301964J31

wx:
  backend:
    url: https://api.weixin.qq.com/cgi-bin

express.tms:
  host: http://tms-server.cb3a8021ca3764031b900ba7774c3639b.cn-zhangjiakou.alicontainer.com/api/
  appKey: 10000
  secret: E6AC95EA2C0343A2
aliyun:
  realNameCertification:
    appKey: 204084473
    appSecret: ERZchuOkDUYUYhDEPz4upEaZlGEL6Q2p

order.auto.cancel.in.minutes: 15

withdraw.apply:
  onlinePay:
#    certFilePath: /nfs/withdrawCertFiles/terminus-web
    callbackUrl: http://mall-pre.yang800.cn/backend/api/withdrawPayment

agentPay.payment:
  callbackUrl: http://mall-pre.yang800.cn/backend/api/agentPayPayment

fundsTransfer.payment:
  callbackUrl: http://mall-pre.yang800.cn/backend/api/fundsTransferPayment

enterpriseWithdraw:
  callbackUrl: http://mall-pre.yang800.cn/backend/api/enterpriseWithdraw


# MQ
rocketmq:
  name-server: rocketmq-nameserver-svc.parana-inte.svc.cluster.local:9876
  producer:
    group: "PARANA"
    enableMsgTrace: false
imm:
  end-point: imm.cn-hangzhou.aliyuncs.com
  project—name: app_imm_project


# 阿里云走测试环境的mercury
y800:
  gateApi: http://outpre.order.yang800.cn/open/apiv3

