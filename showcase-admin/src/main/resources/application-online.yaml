application:
  name: supply-shop-admin

environment: online

logging:
#  file: /nfs/terminus-admin/terminus-admin.log
#  level.*: INFO
#  level.io.terminus: DEBUG
  config: classpath:config/online/logback.xml

ext.messages.classpath: messages_zh_CN

server:
  context-path: /
  port: 8080

session:
  cookie-domain: admin-mall.yang800.com
  cookie-context-path: /
  cookie-name: msid
  cookie-max-age: 1800
  source: redis
  serialize-type: json
  redis-host: parana-redis-svc
  redis-port: 6379
  redis-index: 0
  redis-cluster: false
  redis-test-on-borrow: true
  redis-max-total: 10
  redis-max-idle: 0
  redis-prefix: afsession
  #redis-auth: dd654321

aoxin:
  gateway: http://************:61/API
parana:
  web:
    url: http://admin.mall.yang800.com
  mall:
    url: http://mall.yang800.com
  h5:
    url: https://m.mall.yang800.com
  wxa:
    url: https://m.mall.yang800.com
    appId: wx90e48a315747ccb9
    appSecret: ebb83ced7885897e4fd5ecfc403ed92d
  we:
    seller:
      url: https://m.mall.yang800.com
      appId: wx0fb538f6dc093892
      appSecret: 5d260bcf67eecf19e96a341b7b4eb95a
    buyer:
      url: https://m.mall.yang800.com
      appId: wxb25691b47b974630
      appSecret: 088dedd8ec3382f5ba981a2560afc187
  wxopen:
    componentAppId: wxfb7d4a7ab73f6ba6
    componentSecret: a9a41dcfd8e7b564a2ec93c0ee58fb9f
    componentToken: wx569abcf3c77ff1de
    componentAesKey: 0987654321qazwsxedcrfvtgbyhnujmik1234567890
    requestdomain: https://m.mall.yang800.com;https://www.yang800.com;https://m.yang800.com
    wsrequestdomain: wss://m.mall.yang800.com;wss://www.yang800.com
    uploaddomain: https://m.mall.yang800.com;https://www.yang800.com
    downloaddomain: https://m.mall.yang800.com;https://www.yang800.com
  alipayOpen:
    serviceAppId: 2021004149664712
    serviceAppPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp2EifbZj3XISeVR4odPh1kW1R+Ae0MuPVALJOSeXLqlqGZxyRckqXvNQPOL4MxPU4RGL9Q4ADoA7Y7IK8IBvBrdSUIzTIBzcB20T9X1q16JOeuKe1muONNb08sXYGyDeX4OGi0Bd6JWxeXix4LG8PZXXt9Q2MP/WfSeWe2UUyRTgaq+lkja2J6BsEESn52h0WiU02Sw3fC+PkqmDInLuhAp4k8FVe1P4Xt9CMcOdMOIfGAFsq118Md7s0er9j5uzBzx6DWQwK0SDREiqSk+/0dap5MUgZqYiUaBiZo3WgcDEicMyiM/qmKg/eyrG7s7y6t6PM+n8F4H2IDJIHNytywIDAQAB
    serviceAppPrivateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCSyvCy60+ne5f5Qf2Of6AtRgILpwpJ989NI3r0Le4PsM4ALxZDdDcIe1AIzVFdznTv+binzxP58yYIJeL/v2shN0QkHQhe7R2Iuy3SHdM3ndAXz0HRy3VCbEwxIHKAqqrcZXNJyunOCKSbbGsaXAsdXSOjnVfG/1FBSZim95dNbIPPP/3PW+26y8jjrp062qrlBGc4Vg6CSMBpybfO1Mul7sjfy+gz9io+UNkf6EWSoC1Iw/EL3CpPO0Ve66yk3jmoLH6Z1NnT5PUeX3ZrPu/qtLfYPrzQYS5ei+Tc4vi728JlEdMFxwLs7QTmqoJdkpXmrj5n9XpoGMyZF8Iz9X/JAgMBAAECggEAPpMWEh8vIGUWPqH71V/gRwxd2NvHDgr+KpLiWe6uWa/z0K5B3lVPFGWEk4v6kHNu89FO/vHOQTF+V+DWqFDP+SPW/1IE/Ho1qdCo0rX2EPJL/6pRE36YqM0DmbmMNf8KwNLaV22RR0v1529LJLxx3H2UsK1k+qZh2+Pkl+BZdh4SxetdOWmivbbDqkayPWtOnY8cJBF7jrbW3AX6ysNEK3u3Kr6yso2spy+b3anuQENbdaLrMefiby1uGn9hJx8Erpq2wmQzJ/ZYJw/tSKb4D77KhcfAbT90YBFJPGIQsXXmSZ2c4jGrpYMqi8l9c+M0WC6yPI/CakdSHEQIs2Kl0QKBgQDXvaWnLGb9FfqaEpfK+gEPO7XfbYdbFNbn0ty2OCV9ZM86mEY0eCIJ3XLXraA5cfgvBSrKCAskfnIhjOoD0Pb4EeMnwaRabGp+MFlheImCEjnD1Rg1DttBlS+t2YYA3lcJioHjQrrsRk1do06HwbERP6p8Ob4YGo3NM6Q/szfVtQKBgQCuL4LelCi4reS7+HYW10NyJJOLFuUsVB9VNs1r4MxmfCMYmQqyW1x+093Nt1InzKra0d3tAwAOQ4N+tE/5R0nK+CYVggjqZTznPiYBE4xca1g69bk3oWpYqdKYVQ/bXLFgxQwdi11oGBsYEOCb35omobC5dFzfOmvUP9DESvUORQKBgH479pCx13UhBihe1riqvjH2oz2frgKqV/D/nat1gOI1OVg0v2d2H5ycGdV/P3A9cpW4tUfPmw14ezmMZWVkQQ34EW0yzgMmys0ZZ0M0L2mPVoNyzZ5GGh7utx5HWDRr5IutosCiTBNyM1Sl+I5Q1FWeZhgYBVW/O9ek7udi6zm9AoGBAKvwkkqMDbZfUQ6LsGXTj+w4VrxRmV8smtFPC2+Vs67mku5RA9IThTZchPHwJ9TJn03pJBw1ffSWiFKmQttal2kXkuBgP9TAomHRY0pktYT1IV7x+CcF2ygyWoOsU77BvQY8/DyduVJrmTMkVdMH4e6IZR5r0tE3pgEaDUX2h/fRAoGAXcCaxh1xtX8TmW0FQ4n7pzF1dVdKYYePovo8eCFBHS1PhMUXaxnOa++pcOwvK9dcjfpGdoA9dalUD2PdRjoAjXlTRy43j5t/JrBA3NkoOz5QU5G4GakC+XLy63T3e6NLNN4rK/J5aFtMFzyLu9tp2IU83vSZdFGQwMmSUB0JojE=
    serviceAppDecryptKey: huqzAm2wBuWhTVFG8Lcfhw==
    authNotifyUrl: https://mall.yang800.com/backend/api/shopAppAuth/alipay/authNotify
  substore:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3
  levelDistribution:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3

image:
  base:
    url: http://dante-img.oss-cn-hangzhou.aliyuncs.com
  protocol: http
  domain: dante-img.oss-cn-hangzhou.aliyuncs.com
  upload.allowedFileTypes: jpeg,jpg,gif,png,webp

oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  appKey: LTAI5tNGqjjEfJ1n3xbV42v9
  appSecret: ******************************
  bucketName: dante-img

search:
#  host: localhost
  host: es-cn-83l41t1fc0008hxah.elasticsearch.aliyuncs.com
  port: 9200
  username: elastic
  password: Qw$12345678

item.search:
  index-name: items
  index-type: item
  mapping-path: item_mapping.json
  full-dump-range: 3000
  batch-size: 100

shop.search:
  index-name: shops
  index-type: shop
  mapping-path: shop_mapping.json
  full-dump-range: 3000
  batch-size: 100

weShopItem.search:
  index-name: t_we_shop_items
  index-type: we_shop_item
  mapping-path: we_shop_item_mapping.json
  full-dump-range: 3000
  batch-size: 100

mybatis:
  mapper-locations:  classpath*:mapper/*Mapper.xml
  type-aliases-package: >
    moonstone.(item|category|brand|shop|spu|user|file|membership|countryImage|integral).model,
    moonstone.(adv|thirdParty|shopWxa|wxa|shopMini).model,
    moonstone.(weCart|weShop|weDistributionApplication).model,
    moonstone.user.address.model,
    moonstone.user.area.model,
    moonstone.(order|cart|express|promotion|delivery|settle).model
  typeHandlersPackage: moonstone.common.type.handler

hazelcast:
  config: hazelcast-config.xml
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************************************************************
    #    url: *******************************************************************************************************************************
    username: parana
    password: Danding$%654
  elasticsearch:
    uris: http://es-cn-83l41t1fc0008hxah.elasticsearch.aliyuncs.com:9200
    connection-timeout: 3s
    username: elastic
    password: Qw$12345678
  data:
    mongodb:
      uri: mongodb://parana:<EMAIL>:3717,dds-uf674e213ffa6b742.mongodb.rds.aliyuncs.com:3717/parana-online

  http:
    multipart:
      max-file-size: 50Mb
      max-request-size: 50Mb
    server:
      undertow.max-http-post-size: 10MB
      tomcat.max-http-post-size: 10MB
      jetty.max-http-post-size: 10MB
  redis:
    host: parana-redis-svc
    port: 6379
    timeout: 500
    database: 0
    jedis:
      pool:
        max-active: 100
        max-wait: -1
        max-idle: 8
        min-idle: 0

express.100:
  key: bAWWUdnn9775
  customer: 837CEA86CF64D4FEC5B4CEC59589E2D5
  regularUrl: http://poll.kuaidi100.com/poll/query.do

settle:
  enable: true
  commission.rate.default: 0

enable.order.auto.cancel.job: true

msg:
  current:
    smsService: aliYunSmsService
    emailService: javaxEmailService
  aliyun:
    appKey: LTAI5tCR5p7wUaszs7VDrv9g
    appSecret: ******************************
  javaxemail:
    mailServerHost: smtp.exmail.qq.com
    mailServerPort: 465
    fromAddress: Y800<<EMAIL>>
    userName: <EMAIL>
    password: Dd111111

msg.template.list:
  - key: sms.user.login.code
    title: 登录验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_145495759","smsParam":{"code":"{{code}}"}}
  - key: sms.user.register.code
    title: 注册验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143863719","smsParam":{"code":"{{code}}"}}
  - key: sms.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868717","smsParam":{"code":"{{code}}"}}
  - key: sms.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868720","smsParam":{"code":"{{code}}"}}

msg.email:
  templates:
    - key: email.error.notice
      templateName: sendEmailErrorNotice
      subject: "但丁商城 - 异常通知"

wechat.applet:
  enable: true
  mchId: 1488913512
  appId: wx86fe5657944bcaa2
  secret: 8ac1cbf290b0fe98ff1142acf94e7351

pay:
  xinbada:
    url: https://open.sinbaad.com
  easy-pay:
    gateway: https://newpay.bhecard.com/api_gateway.do
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2WTfvas1JvvaRuJWIKmKlBLmkRvr2O7Fu3k/zvhJs+X1JQorPWq/yZduY6HKu0up7Qi3T6ULHWyKBS1nRqhhHpmLHnI3sIO8E/RzNXJiTd9/bpXMv+H8F8DW5ElLxCIVuwHBROkBLWS9fIpslkFPt+r13oKFnuWhXgRr+K/YkJQIDAQAB
  allinpay:
    pay: https://vsp.allinpay.com/apiweb/unitorder/pay
    refund: https://vsp.allinpay.com/apiweb/tranx/refund
    query: https://vsp.allinpay.com/apiweb/tranx/query
    ystBaseUrl: https://open.allinpay.com/gateway
  debug: false
  channel:
    alipay: enable
  notifyUrl: http://mall.yang800.com/backend/api/order/paid
  refundNotifyUrl: http://mall.yang800.com/backend/api/refund/notify
  returnUrl: http://mall.yang800.com/buyer/trade-success
  h5ReturnUrl: https://m.mall.yang800.com/order/pay-success
  unionpay.wap.token.verifyCerDir: /nfs/terminus-admin
  unionpay.pc.token.verifyCerDir: /nfs/terminus-admin
  unionpay.app.token.verifyCerDir: /nfs/terminus-admin
  certFilePath: /nfs/certFiles/terminus-web

alipay:
  pid: ****************
  key: pil5t34m4qsdor9ujuffkqfvrgfjt3mp
  account: <EMAIL>

curator:
  zk.host: zk-svc
  zk.port: 2181
  leader.path: /admin-leader

Y800:
  partnerCode: webB2C
  partnerKey: webB2C123
  sourcePlatform: WEBSC
  sourcePlatformV3: WEBSC
  merchantCode:
  wd.support: http://wd.api.yang800.com
  yang.support: http://support.yang800.com
  finance.project.url: http://statics.yang800.com/xhr
  open.api.gate: ${Y800.yang.support}/api/
nio:
  post.url: http://sync.yang800.com/xhr

#微分销商城前台域名
m.mall.yang800: https://m.mall.yang800.com

#积分商城生成二维码的路径
m.mall.jifen.path: /nfs/certFiles/terminus-web/levelDistribution/image/
#积分商城生成二维码的url
m.mall.jifen.url: https://m.mall.yang800.com/ladder-distribution/credits-shop

pay.integral.token:
  returnUrl: http://mall.yang800.com/buyer/trade-success
  refundNotifyUrl: http://mall.yang800.com/backend/api/refund/notify

#功能开关
function:
  switch:
    aoXinPush: false
    financePush: true
    unifiedPayment: true

mercury:
  pay:
    host: http://ccs.order.yang800.com/mercury/pay/proxyAPI
    appCode: DDFXSC
    merchantCode: M2019032719271231031
    customs:
      notify: http://admin.mall.yang800.com//backend/api/customs/payment/declare/notify

ebpCode: 3301964J31

wx:
  backend:
    url: https://api.weixin.qq.com/cgi-bin

y800:
  gateApi: http://out.order.yang800.com/open/apiv3

gx.api.mall: http://supply-user.yang800.com
oms:
  api:
    admin: http://pangu-admin

express.tms:
  host: https://tms-server.dataeta.com/api/
  appKey: 19870
  secret: CBF1C96E6894B779
aliyun:
  realNameCertification:
    appKey: 204084473
    appSecret: ERZchuOkDUYUYhDEPz4upEaZlGEL6Q2p

order.auto.cancel.in.minutes: 15

withdraw.apply:
  onlinePay:
    certFilePath: /nfs/withdrawCertFiles/terminus-web
    callbackUrl: http://mall.yang800.com/backend/api/withdrawPayment

agentPay.payment:
  callbackUrl: http://mall.yang800.com/backend/api/agentPayPayment

fundsTransfer.payment:
  callbackUrl: http://mall.yang800.com/backend/api/fundsTransferPayment

enterpriseWithdraw:
  callbackUrl: http://mall.yang800.com/backend/api/enterpriseWithdraw



# MQ
rocketmq:
  name-server: rocketmq-nameserver-svc.mq.svc.cluster.local:9876;rocketmq-nameserver-b-svc.mq.svc.cluster.local:9876
  producer:
    group: "PARANA"
    enableMsgTrace: false
imm:
  end-point: imm.cn-hangzhou.aliyuncs.com
  project—name: app_imm_project