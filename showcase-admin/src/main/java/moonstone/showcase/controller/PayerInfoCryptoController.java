package moonstone.showcase.controller;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.user.vo.PayerInfoDeCodeReq;
import moonstone.user.vo.PayerInfoDeCodeRes;
import moonstone.user.model.PayerInfo;
import moonstone.user.service.PayerInfoCryptoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("api/payerInfo")
public class PayerInfoCryptoController {

    @Resource
    private PayerInfoCryptoService payerInfoCryptoService;

    /**
     * 解密
     * @return
     */
    @PostMapping("decode")
    public Result<PayerInfoDeCodeRes> decode(@RequestBody PayerInfoDeCodeReq req){
        log.info("解密前 {}", JSONObject.toJSONString(req));

        PayerInfoDeCodeRes res;
        try {
            res = payerInfoCryptoService.decode(req);
            log.info("解密后 {}", JSONObject.toJSONString(res));
            return Result.data(res);
        } catch (Exception e) {
            log.error("解密失败", e);
            return Result.fail("解密失败");
        }
    }

}
