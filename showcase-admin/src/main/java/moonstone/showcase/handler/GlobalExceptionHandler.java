package moonstone.showcase.handler;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.exception.ApiException;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Collections;

/**
 * author：书生
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(NullPointerException.class)
    public Result<String> handleNullPointerException(NullPointerException e) {
        log.error("【空指针异常】 {}", e.getMessage(), e);
        return Result.fail(e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<String> validException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        String errMsg = bindingResult.getFieldErrors().get(0).getDefaultMessage();
        return Result.fail(errMsg);
    }

    /**
     * 参数类型转换错误
     *
     * @param exception 错误
     * @return 错误信息
     */
    @ExceptionHandler(HttpMessageConversionException.class)
    public Result parameterTypeException(HttpMessageConversionException exception) {
        log.error("参数类型转换错误 {}", exception.getMessage(), exception);
        return Result.data(Collections.emptyList());
    }

    @ExceptionHandler(ApiException.class)
    public Result<Object> customException(ApiException e) {
        log.error("【系统错误】 {}", e.getMsg(),e);
        return Result.fail(e.getCode(),e.getMsg());
    }

    @ExceptionHandler(Exception.class)
    public Result<String> exception(Exception e) {
        log.error("【系统异常】{}", e.getMessage(), e);
        return Result.fail(e.getMessage());
    }

}
