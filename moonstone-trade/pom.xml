<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>moonstone-mall</artifactId>
        <groupId>moonstone</groupId>
        <version>1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>moonstone-trade</artifactId>
    <version>1.0.0.RELEASE</version>


    <dependencies>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>danding-x-core-p6spy</artifactId>
            <version>1.2.1-RELEASE</version>
        </dependency>
        <!--mysql数据源代理工具：主要用于sql执行日志、事务分析-->
        <dependency>
            <groupId>com.github.gavlyukovskiy</groupId>
            <artifactId>p6spy-spring-boot-starter</artifactId>
            <version>1.5.7</version>
        </dependency>
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
            <version>3.8.2</version>
        </dependency>

        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-trade-api</artifactId>
            <version>1.0.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-common</artifactId>
            <version>1.0.0.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.github.kevinsawicki</groupId>
            <artifactId>http-request</artifactId>
            <version>6.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.terminus.boot.rpc</groupId>
            <artifactId>rpc-common</artifactId>
            <version>1.0.2.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <version>1.3.0</version>
            <artifactId>mybatis-spring</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
