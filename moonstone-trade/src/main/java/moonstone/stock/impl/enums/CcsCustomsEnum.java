package moonstone.stock.impl.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum CcsCustomsEnum {
    HANGZHOU("hangzhou", "2900", "HANGZHOU", "杭州"),
    TIANJIN("tianjin", "0215", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "天津"),
    SHANGHAI("shanghai", "2232", "SHANGHAI", "上海"),
    JINYI("zhejiang", "2924", "JINYI", "金义"),
    SHAOXING("shaoxing", "2964", "SHAOXING", "绍兴"),
    YANCHENG("yancheng", "2372", "YANCHENG", "盐城"),
    YANTAI("yantai", "4261", "YANTAI", "烟台"),
    WEIHAI("weihai", "4262", "WEIHAI", "威海"),
    YIWU("zhejiang", "2925", "<PERSON>I<PERSON><PERSON>", "义乌"),
    CHONGQING("chongqing", "8013", "CHON<PERSON><PERSON><PERSON>", "重庆"),
    HAIKOU("haikou", "6409", "HAIKOU", "海口"),
    KUNMING("kunming", "8634", "KUNMING", "昆明"),

    GUANGZHOU_NS("guangzhou", "5165", "GUANGZHOU_NS", "广州南沙"),
    GUANGZHOU_HP("guangzhou", "5219", "GUANGZHOU_HP", "广州黄埔"),
    QINGDAO_JIMO("qingdao_jimo", "4260", "QINGDAO_JIMO", "青岛即墨"),
    HU_ZHOU("huzhou", "2979", "HUZHOU", "湖州保B"),
    QINGDAO_HETAO("qingdao_hetao", "4272", "QINGDAO_HETAO", "青岛河套"),
    HANGZHOU_XIASHA("xiasha", "2991", "XIASHA", "杭州下沙"),
    DEQING("deqing", "2973", "DEQING", "德清B保"),
    ;

    private String code;

    private String hsCode;

    private String customCode;

    private String name;

    CcsCustomsEnum(String code, String hsCode, String customCode, String name) {
        this.code = code;
        this.hsCode = hsCode;
        this.customCode = customCode;
        this.name = name;
    }

    public static CcsCustomsEnum findByName(String customName) {
        if(StringUtils.isEmpty(customName)){
            return null;
        }
        for (CcsCustomsEnum ccsCustomsEnum : CcsCustomsEnum.values()) {
            if (ccsCustomsEnum.getName().equals(customName)) {
                return ccsCustomsEnum;
            }
        }
        return null;
    }
}
