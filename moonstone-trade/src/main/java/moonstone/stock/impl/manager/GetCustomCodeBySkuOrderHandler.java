package moonstone.stock.impl.manager;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.BondedType;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.order.model.SkuOrder;
import moonstone.stock.impl.enums.CcsCustomsEnum;
import moonstone.stock.impl.enums.ItemCustomsEum;
import moonstone.stock.impl.service.DepotCustomManager;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class GetCustomCodeBySkuOrderHandler {

    @Resource
    private SkuCustomReadService skuCustomReadService;

    public String getCustomCodeByOrder(SkuOrder skuOrder) {
//        // 如果设置了手动关区则使用手动的
//        if (Optional.ofNullable(skuOrder.getExtra()).map(extra -> extra.get(DepotCustomManager.CustomBySkuExtra.custom.name())).filter(StringUtils::hasLength).isPresent()) {
//            return skuOrder.getExtra().get(DepotCustomManager.CustomBySkuExtra.custom.name());
//        }
        Long shopOrderId = skuOrder.getOrderId();
        Long skuOrderId = skuOrder.getId();

        BondedType bondedType = BondedType.fromInt(skuOrder.getIsBonded());
        if(!bondedType.isBonded()){
            log.error("非跨境订单 无口岸信息 {} {}", shopOrderId, skuOrderId);
            return "NO_CUSTOM";
        }

        // 如果设置了手动关区则使用手动的
        Map<String, String> extra = skuOrder.getExtra();
        if (extra != null && StringUtils.hasLength(extra.get(DepotCustomManager.CustomBySkuExtra.custom.name()))) {
            return extra.get(DepotCustomManager.CustomBySkuExtra.custom.name());
        }

        SkuCustom skuCustom = skuCustomReadService.getBySkuId(skuOrder.getSkuId());
        // TODO 确认town的值维护逻辑
        String customName = skuCustom.getTown();

        CcsCustomsEnum customsEum = CcsCustomsEnum.findByName(customName);
        String customCode = customsEum.getCustomCode();
        log.info("跨境订单 口岸信息 {}",customCode);
        return customCode;
    }
}
