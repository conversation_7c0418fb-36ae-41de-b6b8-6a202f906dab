/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.ReceiverInfo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 收货信息DAO
 *
 * Author  : panxin
 * Date    : 2:02 PM 3/5/16
 * Mail    : <EMAIL>
 */
@Repository
public class ReceiverInfoDao extends MyBatisDao<ReceiverInfo>{

    /**
     * 根据用户ID查询收货地址
     *
     * @param userId 用户ID
     * @return 收货地址信息
     */
    public List<ReceiverInfo> findByUserId(Long userId){
        return getSqlSession().selectList(sqlId("findByUserId"), userId);
    }

    /**
     * 设置默认地址
     *
     * @param id 需要设置默认地址的ID
     * @param userId 用户ID
     */
    public void makeDefault(Long id, Long userId) {
        getSqlSession().update(sqlId("makeDefault"), ImmutableMap.of(
                "id", id,
                "userId", userId));
    }

    /**
     * 更新身份证信息
     *
     * @param receiverInfo 收货地址信息
     */
    public void updateReceiverPaper(ReceiverInfo receiverInfo) {
        getSqlSession().update(sqlId("updatePaper"), receiverInfo);
    }

    /**
     * 删除收货地址
     *
     * @param id 收货地址ID
     * @param userId 收货地址所属用户ID
     */
    public void deleteAddressByIdAndUserId(Long id, Long userId){
        getSqlSession().update(sqlId("deleteByIdAndUserId"), ImmutableMap.of(
                "id", id, "userId", userId));
    }

    /**
     * 根据用户id查询默认的收货地址
     *
     * @param userId 用户id
     * @return 默认的收货地址
     */
    public ReceiverInfo findDefaultByUserId(Long userId) {
        return getSqlSession().selectOne(sqlId("findDefaultByUserId"), userId);
    }

    /**
     * 更新地址相关Id信息
     *
     * @param receiverInfo 需要修改的receiverInfo
     */
    public Boolean updateAreaId(ReceiverInfo receiverInfo) {
        return getSqlSession().update(sqlId("updateAreaId"), receiverInfo) == 1;
    }

    /**
     * 获得地址信息最大id
     *
     */
    public Long maxId(){
        return getSqlSession().selectOne(sqlId("maxId"));
    }

    public void makeDefaultById(Long id, Long userId, Long i) {
        getSqlSession().update(sqlId("makeDefaultById"), ImmutableMap.of(
                "id", id,
                "userId", userId,"isDefault",i));
    }

    public List<ReceiverInfo> findByUserIdLimitOne(Long userId) {
        return getSqlSession().selectList(sqlId("findByUserIdLimitOne"), userId);
    }

    public boolean batchUpdateReceiverInfos(List<ReceiverInfo> receiverInfoList) {
        return getSqlSession().update(sqlId("batchUpdateReceiverInfos"), receiverInfoList) == receiverInfoList.size();
    }
}
