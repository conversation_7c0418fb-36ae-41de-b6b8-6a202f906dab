/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.B3Hash;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.exception.ApiException;
import moonstone.common.model.Either;
import moonstone.common.model.ExtraView;
import moonstone.common.model.ImageView;
import moonstone.common.utils.EncryptHelper;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.api.AbstractPersistedOrderMaker;
import moonstone.order.convert.OrderRoleSnapshotConvertor;
import moonstone.order.dto.PersistedOrderInfosByOrderStruct;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.impl.dao.*;
import moonstone.order.impl.manager.OrderManager;
import moonstone.order.model.*;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.PayerInfoService;
import moonstone.order.strategy.ShopOrderStatusStrategy;
import moonstone.shop.service.SubStoreReadService;
import moonstone.stock.model.DepotView;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.StoreProxy;
import moonstone.user.service.StoreProxyReadService;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * (子)订单写服务
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-27
 */
@Service
@Slf4j
public record OrderWriteServiceImpl(AbstractPersistedOrderMaker orderMaker,
                                    OrderManager orderManager,
                                    ShopOrderDao shopOrderDao,
                                    SkuOrderDao skuOrderDao,
                                    FeeViewDao feeViewDao,
                                    ExtraViewDao extraViewDao,
                                    DepotViewDao depotViewDao,
                                    ProfitViewDao profitViewDao,
                                    ImageInfoDao imageInfoDao,
                                    ShopOrderStatusStrategy shopOrderStatusStrategy,
                                    BalanceDetailDao balanceDetailDao,
                                    MongoTemplate mongoTemplate,
                                    SqlSession sqlSession,
                                    // for the profit view
                                    SubStoreReadService subStoreReadService,
                                    OrderRoleSnapshotConvertor orderRoleSnapshotConvertor,
                                    PayerInfoService payerInfoService,
                                    StoreProxyReadService storeProxyReadService) implements OrderWriteService {
    /**
     * 创建订单, 这里要将richOrder进行拆分, 持久化到对应的数据库表中去
     *
     * @param richOrder 订单信息
     * @return 店铺订单id列表
     */
    @Override
    public Response<List<Long>> create(RichOrder richOrder) {
        try {
            log.info("[order-make](debug) richOrder:{}", JSON.toJSONString(richOrder));
            PersistedOrderInfosByOrderStruct persistedOrderInfos = orderMaker.make(richOrder);

            //添加角色快照信息
            appendOrderRoleSnapshots(persistedOrderInfos);

            log.info("[order-create](debug) PersistedOrderInfo:{}", JSON.toJSONString(persistedOrderInfos));
            // protect the payer info
            String payerName = richOrder.getPayerName();
            String payerId = richOrder.getPayerId();
            List<Long> shopOrderIds = orderManager.create(persistedOrderInfos);
            int i = 0;
            for (Long shopOrderId : shopOrderIds) {
                PayerInfo payerInfo = PayerInfo.Helper.create(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey),
                        payerName, payerId, shopOrderId, richOrder.getRichSkusByShops().get(i++).getShop().getId());
                // check and patch the if this is the first order
                mongoTemplate.insert(payerInfo);
                try {
                  payerInfoService.save(payerInfo);
                }
                catch (Exception e){
                  log.error("FAIL TO CREATE THE PAYER-INFO[{}]", payerInfo, e);
                }
            }
            return Response.ok(shopOrderIds);
        } catch (Exception e) {
            log.error("failed to create {}, cause:{}", richOrder, Throwables.getStackTraceAsString(e));
            throw new ApiException("订单创建失败");
        }
    }

    /**
     * 添加角色快照信息
     *
     * @param persistedOrderInfos
     */
    private void appendOrderRoleSnapshots(PersistedOrderInfosByOrderStruct persistedOrderInfos) {
        persistedOrderInfos.getShopOrderAndSkuOrders().stream()
                .filter(entity -> entity != null && entity.getShopOrder() != null &&
                        OrderOutFrom.SUB_STORE.Code().equals(entity.getShopOrder().getOutFrom()))
                .forEach(entity -> entity.setOrderRoleSnapshots(convert(entity.getShopOrder())));
    }

    private List<OrderRoleSnapshot> convert(ShopOrder shopOrder) {
        List<OrderRoleSnapshot> resultList = new ArrayList<>();

        //导购
        if (shopOrder.getRefererId() != null) {
            var snapshot = orderRoleSnapshotConvertor.convert(shopOrder.getRefererId(),
                    shopOrder.getShopId(), shopOrder.getBuyerId());
            if (snapshot != null) {
                resultList.add(snapshot);
            }
        }

        //门店和服务商
        if (StringUtils.isNotBlank(shopOrder.getOutShopId())) {
            var snapshots = orderRoleSnapshotConvertor.convert(Long.valueOf(shopOrder.getOutShopId()), shopOrder.getBuyerId());
            if (!CollectionUtils.isEmpty(snapshots)) {
                resultList.addAll(snapshots);
            }
        }
        return resultList;
    }

    /**
     * 店铺订单级别的状态发生改变, 一般会引起其下子订单的状态随之发生改变,调用方负责判断状态变迁的合法性
     *
     * @param shopOrderId   店铺订单id
     * @param currentStatus 当前状态 只有店铺订单的当前状态是currentStatus才会做状态更新
     * @param newStatus     新的目标状态
     * @return 状态是否改变成功
     */
    @Override
    public Response<Boolean> shopOrderStatusChanged(Long shopOrderId, Integer currentStatus, Integer newStatus) {
        try {
            List<SkuOrder> skuOrders = skuOrderDao.findByOrderId(shopOrderId);
            if (CollectionUtils.isEmpty(skuOrders)) {
                log.error("no skuOrders found for ShopOrder(id={})", shopOrderId);
                return Response.fail("skuOrder.not.found");
            }
            //检查所有的子订单状态是否和父订单的状态是否一致
            for (SkuOrder skuOrder : skuOrders) {
                if (!Objects.equal(skuOrder.getStatus(), currentStatus)) {
                    log.error("skuOrder(id={}) status is {}, but shopOrder(id={}) status is {}, " +
                                    "shopOrder status change not supported because of inconsistent status sku orders",
                            skuOrder.getId(), skuOrder.getStatus(), shopOrderId, currentStatus);
                    return Response.fail("skuOrder.inconsistent.status");
                }
            }

            orderManager.shopOrderStatusChanged(shopOrderId, currentStatus, newStatus);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to change shopOrder(id={})'s status from {} to {}, cause:", shopOrderId,
                    currentStatus, newStatus, e);
            return Response.fail("shop.order.status.update.fail");
        }
    }

    /**
     * 店铺订单级别的状态发生批量改变, 一般会引起其下子订单的状态随之发生改变, 部分更新成功也视为成功
     *
     * @param shopOrderIds  店铺订单id列表
     * @param currentStatus 当前状态  只有店铺订单的当前状态是currentStatus才会做状态更新
     * @param newStatus     新的目标状态
     * @return 状态更新成功的店铺订单id列表
     */
    @Override
    public Response<List<Long>> batchShopOrderStatusChanged(List<Long> shopOrderIds,
                                                            Integer currentStatus, Integer newStatus) {
        if (CollectionUtils.isEmpty(shopOrderIds)) {
            return Response.ok(Collections.emptyList());
        }
        List<Long> succeedIds = Lists.newArrayListWithCapacity(shopOrderIds.size());
        for (Long shopOrderId : shopOrderIds) {
            Response<Boolean> r = shopOrderStatusChanged(shopOrderId, currentStatus, newStatus);
            if (r.isSuccess()) {
                succeedIds.add(shopOrderId);
            } else {
                log.error("failed to update shopOrder(id={}) status to {}, error code:{}",
                        shopOrderId, newStatus, r.getError());
            }
        }
        if (CollectionUtils.isEmpty(succeedIds)) {
            return Response.fail("shop.order.status.update.fail");
        } else if (Objects.equal(succeedIds.size(), shopOrderIds.size())) {
            return Response.ok(succeedIds);
        } else {
            log.warn("batch update shop order status partially failed");
            return Response.ok(succeedIds);
        }
    }

    /**
     * sku订单级别的状态发生改变, 可能会引起对应的店铺订单状态也发生改变,
     * 此时默认的规则是将店铺订单的状态设置为各sku订单状态中最大的值
     *
     * @param skuOrderId    sku订单id
     * @param currentStatus 当前状态, 只有sku订单的当前状态是currentStatus才会做状态更新
     * @param newStatus     新的目标状态
     * @return 状态是否改变成功
     */
    @Override
    public Response<Boolean> skuOrderStatusChanged(Long skuOrderId, Integer currentStatus, Integer newStatus) {

        try {
            SkuOrder skuOrder = skuOrderDao.findById(skuOrderId);
            if (skuOrder == null) {
                log.error("skuOrder(id={}) not found", skuOrderId);
                return Response.fail("sku.order.not.found");
            }

            Long shopOrderId = skuOrder.getOrderId();
            List<SkuOrder> skuOrders = skuOrderDao.findByOrderId(shopOrderId);
            for (SkuOrder so : skuOrders) {
                if (Objects.equal(so.getId(), skuOrderId)) {
                    so.setStatus(newStatus);
                    break;
                }
            }
            Integer shopOrderStatus = shopOrderStatusStrategy.status(skuOrders);
            orderManager.skuOrderStatusChanged(skuOrderId, currentStatus, newStatus, shopOrderId, shopOrderStatus);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update skuOrder(id={})'s status to {}, cause:",
                    skuOrderId, newStatus, e);
            return Response.fail("skuOrder.update.fail");
        }
    }

    /**
     * sku订单级别的状态发生批量改变, 可能会引起对应的店铺订单状态也发生改变, 部分sku订单更新成功也视为成功
     * <p>
     * 默认的规则是将店铺订单的状态设置为各sku订单状态中最大的值
     *
     * @param skuOrderIds   sku订单id 列表
     * @param currentStatus 当前状态  只有sku订单的当前状态是currentStatus才会做状态更新
     * @param newStatus     新的目标状态
     * @return 状态更新成功的sku订单id列表
     */
    @Override
    public Response<List<Long>> batchSkuOrderStatusChanged(List<Long> skuOrderIds, Integer currentStatus, Integer newStatus) {
        if (CollectionUtils.isEmpty(skuOrderIds)) {
            return Response.ok(Collections.emptyList());
        }
        List<Long> succeedIds = Lists.newArrayListWithCapacity(skuOrderIds.size());
        for (Long skuOrderId : skuOrderIds) {
            Response<Boolean> r = skuOrderStatusChanged(skuOrderId, currentStatus, newStatus);
            if (r.isSuccess()) {
                succeedIds.add(skuOrderId);
            } else { //订单更新失败了, log先, 因为允许部分成功, 所以需要继续处理剩下的
                log.error("failed to update skuOrder(id={})'s status to {}, error code:{}",
                        skuOrderId, newStatus, r.getError());
            }
        }
        if (succeedIds.isEmpty()) {
            return Response.fail("sku.order.status.update.fail");
        } else if (Objects.equal(succeedIds.size(), skuOrderIds.size())) {
            return Response.ok(succeedIds);
        } else {
            log.warn("batch update sku order status partially failed");
            return Response.ok(succeedIds);
        }
    }


    /**
     * 对应(子)订单级别的状态发生改变, 注意, 这里不会引起其他实体的改变了
     *
     * @param orderId    (子)订单id
     * @param orderLevel (子)订单级别
     * @param newStatus  新的目标状态
     * @return 状态是否改变成功
     */
    @Override
    public Response<Boolean> updateOrderStatus(Long orderId, OrderLevel orderLevel, Integer newStatus) {
        try {
            boolean success;
            Set<Integer> cancelSet = Stream.of(OrderStatus.TIMEOUT_CANCEL, OrderStatus.BUYER_CANCEL, OrderStatus.SELLER_CANCEL).map(OrderStatus::getValue).collect(Collectors.toSet());
            switch (orderLevel) {
                case SHOP -> {
                    success = shopOrderDao.updateStatus(orderId, newStatus);
                    if (cancelSet.contains(newStatus) && success) {
                        skuOrderDao.updatePushStatusByOrderId(orderId, SkuOrderPushStatus.NOT_NEED.value(), null);
                    }
                }
                case SKU -> {
                    success = skuOrderDao.updateStatus(orderId, newStatus);
                    if (cancelSet.contains(newStatus) && success) {
                        skuOrderDao.updatePushStatus(orderId, SkuOrderPushStatus.NOT_NEED.value(), null);
                    }
                }
                default -> {
                    log.error("unknown orderLevel:  " + orderLevel);
                    return Response.fail("order.level.unknown");
                }
            }
            if (!success) {
                log.error("failed to update order(id={}, level={})'s status to : {}", orderId, orderLevel, newStatus);
                return Response.fail("order.update.fail");
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update order(id={}, level={})'s status to : {}, cause:{}",
                    orderId, orderLevel, newStatus, Throwables.getStackTraceAsString(e));
            return Response.fail("order.status.update.fail");
        }
    }

    /**
     * 对应(子)订单级别的状态发生批量改变, 注意, 这里不会引起其他实体的改变了, 部分更新成功也视为成功
     *
     * @param orderIds   (子)订单id列表
     * @param orderLevel (子)订单级别
     * @param newStatus  新的目标状态
     * @return 状态改变成功的(子)订单id
     */
    @Override
    public Response<List<Long>> batchUpdateOrderStatus(List<Long> orderIds, OrderLevel orderLevel, Integer newStatus) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Response.ok(Collections.emptyList());
        }
        List<Long> succeedIds = Lists.newArrayListWithCapacity(orderIds.size());
        for (Long orderId : orderIds) {
            Response<Boolean> r = updateOrderStatus(orderId, orderLevel, newStatus);
            if (r.isSuccess()) {
                succeedIds.add(orderId);
            } else {//订单更新失败了, log先, 因为允许部分成功, 所以需要继续处理剩下的
                log.error("failed to update order(id={}, level={})'s status to : {}, error code: {}",
                        orderId, orderLevel, newStatus, r.getError());
            }
        }
        if (succeedIds.isEmpty()) {
            return Response.fail("order.status.update.fail");
        } else if (Objects.equal(succeedIds.size(), orderIds.size())) {
            return Response.ok(succeedIds);
        } else {
            log.warn("batch update order status partially failed");
            return Response.ok(succeedIds);
        }
    }

    /**
     * 批量标记(子)订单为是否申请过逆向流程
     *
     * @param orderIds   (子)订单id列表
     * @param orderLevel (子)订单级别
     * @param hasRefund  是否申请逆向流程
     * @return 是否标记成功
     */
    @Override
    public Response<Boolean> batchMarkHasRefund(List<Long> orderIds, OrderLevel orderLevel, Boolean hasRefund) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Response.ok(Boolean.TRUE);
        }
        try {
            orderManager.batchMarkHasRefund(orderIds, orderLevel, hasRefund);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to mark Order(ids={}, orderLevel={})'s hasRefund to {}, cause:{}",
                    orderIds, orderLevel, hasRefund, Throwables.getStackTraceAsString(e));
            return Response.fail("order.update.fail");
        }
    }

    @Override
    public Response<Boolean> batchMarkCommented(List<Long> skuOrderIds, Integer commentFlag) {
        if (CollectionUtils.isEmpty(skuOrderIds)) {
            return Response.ok(Boolean.TRUE);
        }
        try {
            orderManager.batchMarkCommented(skuOrderIds, commentFlag);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to mark skuOrder(ids={})'s comment flag {}, cause:{}",
                    skuOrderIds, commentFlag, Throwables.getStackTraceAsString(e));
            return Response.fail("order.update.fail");
        }
    }

    @Override
    public Response<Boolean> setItemSnapshot(Long skuOrderId, Long itemSnapshotId) {
        try {
            SkuOrder skuOrder = skuOrderDao.findById(skuOrderId);
            if (skuOrder == null) {
                log.error("sku order(id={}) not found", skuOrderId);
                return Response.fail("sku.order.not.found");
            }

            SkuOrder updatedSkuOrder = new SkuOrder();
            updatedSkuOrder.setId(skuOrderId);
            updatedSkuOrder.setItemSnapshotId(itemSnapshotId);
            return Response.ok(skuOrderDao.update(updatedSkuOrder));
        } catch (Exception e) {
            log.error("fail to set item snapshot(id={}) for sku order(id={}),cause:{}",
                    itemSnapshotId, skuOrderId, Throwables.getStackTraceAsString(e));
            return Response.fail("set.item.snapshot.fail");
        }
    }

    @Override
    public Response<Boolean> markShopOrderCommented(Long shopOrderId) {
        try {
            ShopOrder shopOrder = shopOrderDao.findById(shopOrderId);
            if (shopOrder == null) {
                log.error("shop order not found where id={}", shopOrderId);
                return Response.fail("shop.order.not.found");
            }
            if (Objects.equal(shopOrder.getCommented(), Boolean.TRUE)) {
                return Response.ok(Boolean.TRUE);
            }

            ShopOrder toUpdated = new ShopOrder();
            toUpdated.setId(shopOrderId);
            toUpdated.setCommented(Boolean.TRUE);
            boolean success = shopOrderDao.update(toUpdated);
            if (!success) {
                log.error("fail to mark shop order(id={}) commented", shopOrderId);
                return Response.fail("shop.order.mark.commented.fail");
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to mark shop order(id={}) commented,cause:{}",
                    shopOrderId, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.order.mark.commented.fail");
        }
    }

    @Override
    public Response<Boolean> updateOrderExtra(Long id, OrderLevel level, Map<String, String> extra) {
        try {
            switch (level) {
                case SHOP:
                    ShopOrder shopOrder = new ShopOrder();
                    shopOrder.setId(id);
                    shopOrder.setExtra(extra);
                    shopOrderDao.update(shopOrder);
                    return Response.ok(Boolean.TRUE);
                case SKU:
                    SkuOrder skuOrder = new SkuOrder();
                    skuOrder.setId(id);
                    skuOrder.setExtra(extra);
                    skuOrderDao.update(skuOrder);
                    return Response.ok(Boolean.TRUE);
                default:
                    return Response.fail("unknown.order.level");
            }
        } catch (Exception e) {
            log.error("fail to update order extra by orderId={}, level={}, extra={}, cause:{}",
                    id, level, extra, Throwables.getStackTraceAsString(e));
            return Response.fail("update.order.extra.fail");
        }
    }

    @Override
    public Response<Boolean> markSkuOrderAfterSale(Long skuOrderId, Boolean hasApplyAfterSale) {
        try {
            SkuOrder skuOrder = new SkuOrder();
            skuOrder.setId(skuOrderId);
            skuOrder.setHasApplyAfterSale(hasApplyAfterSale);
            skuOrderDao.update(skuOrder);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to mark skuOrder(id={}) afterSale flag to {}, cause:{}",
                    skuOrderId, hasApplyAfterSale, Throwables.getStackTraceAsString(e));
            return Response.fail("mark.after.sale.flag.fail");
        }
    }

    /**
     * 商家审核订单，主要是修改订单的商家备注
     *
     * @param shopOrderId 店铺订单id
     * @param sellerNote  卖家备注
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> updateShopOrderAuth(Long shopOrderId, String sellerNote) {
        try {
            ShopOrder shopOrder = new ShopOrder();
            shopOrder.setId(shopOrderId);
            shopOrder.setSellerNote(sellerNote);
            shopOrderDao.update(shopOrder);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to update shopOrder(id={}) auth by sellerNote={}, cause:{}",
                    shopOrderId, sellerNote, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.order.auth.update.fail");
        }
    }

    /**
     * 根据主订单id，修改主订单下的所有符合状态的子订单的推送状态
     *
     * @param shopOrderId       主订单id
     * @param targetPushStatus  目标推送状态
     * @param currentPushStatus 当前推送状态, 只有sku订单的当前推送状态是currentStatus才会做推送状态更新
     * @return 状态是否改变成功
     */
    @Override
    public Response<Boolean> updateSkuOrderPushStatusByOrderId(Long shopOrderId, Integer targetPushStatus, Integer currentPushStatus) {
        try {
            return Response.ok(skuOrderDao.updatePushStatusByOrderId(shopOrderId, targetPushStatus, currentPushStatus));
        } catch (Exception e) {
            log.error("fail to update shopOrders push status by shopOrderId={}, currentPushStatus={}, targetPushStatus={}, cause:{}",
                    shopOrderId, currentPushStatus, targetPushStatus, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.order.pushStatus.update.fail");
        }
    }

    /**
     * 根据子订单id列表，修改主订单下的所有符合状态的子订单的推送状态
     *
     * @param skuOrderIds       主订单id
     * @param targetPushStatus  目标推送状态
     * @param currentPushStatus 当前推送状态, 只有sku订单的当前推送状态是currentStatus才会做推送状态更新
     * @return 状态是否改变成功
     */
    @Override
    public Response<Boolean> updateSkuOrderPushStatusBySkuOrderIds(List<Long> skuOrderIds, Integer targetPushStatus, Integer currentPushStatus) {
        try {
            if (CollectionUtils.isEmpty(skuOrderIds)) {
                return Response.fail("sku.find.fail");
            }
            skuOrderDao.updatePushStatusBySkuOrderIds(skuOrderIds, targetPushStatus, currentPushStatus);
            return Response.ok(true);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("fail to update SkuOrders push status by skuOrderIds={},currentPushStatus={},targetPushStatus={},cause:", Arrays.toString(skuOrderIds.toArray()), currentPushStatus, targetPushStatus, e);
            return Response.fail("sku.order.pushStatus.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateShopOrderShipmentTime(Long shopOrderId, Date firstShipmentAt, Date lastShipmentAt) {
        try {
            ShopOrder shopOrder = new ShopOrder();
            shopOrder.setId(shopOrderId);
            shopOrder.setFirstShipmentAt(firstShipmentAt);
            shopOrder.setLastShipmentAt(lastShipmentAt);
            return Response.ok(shopOrderDao.update(shopOrder));
        } catch (Exception e) {
            log.error("fail to update shop order(id={}) to firstShipmentAt={}, lastShipmentAt={}, cause: {}",
                    shopOrderId, firstShipmentAt, lastShipmentAt, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.order.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateDeclaredIdAndCustoms(Long orderId, String declaredId, String customs) {
        ShopOrder update = new ShopOrder();
        update.setId(orderId);
        update.setDeclaredId(declaredId);
        update.setDepotCustomName(customs);
        try {

            return Response.ok(shopOrderDao.update(update));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} update:{}", LogUtil.getClassMethodName(), update);
            return Response.fail("shop.order.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateOrderRefererId(Long orderId, Long proxyId) {
        try {
            ShopOrder updateOrder = new ShopOrder();
            updateOrder.setId(orderId);
            updateOrder.setRefererId(proxyId);
            shopOrderDao.update(updateOrder);
            return Response.ok(true);
        } catch (Exception ex) {
            log.error("{} fail to update order[{}] refererId[{}]", LogUtil.getClassMethodName(), orderId, proxyId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Either<Boolean> markHasRefund(OrderBase orderBase) {
        try {
            switch (OrderLevel.ofOrderBase(orderBase)) {
                case SHOP: {
                    ShopOrder update = new ShopOrder();
                    update.setId(orderBase.getId());
                    update.setHasRefund(true);
                    for (SkuOrder skuOrder : skuOrderDao.findByOrderId(orderBase.getId())) {
                        markHasRefund(skuOrder);
                    }
                    return Either.ok(shopOrderDao.update(update));
                }
                case SKU: {
                    SkuOrder update = new SkuOrder();
                    update.setId(orderBase.getId());
                    update.setHasRefund(true);
                    return Either.ok(skuOrderDao.update(update));
                }
                default:
                    return Either.error(Translate.of("未支持的订单退款标记过程"));
            }
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    Long toLong(Integer i) {
        if (i == null) return null;
        return i.longValue();
    }

    @Override
    public void writeOrderInfo(Long orderId) {
        var order = shopOrderDao.findById(orderId);
        var feeInfo = B3Hash.copyWithHash(new FeeView(null, order.getOriginFee(), order.getFee(),
                order.getOriginFee(), order.getFee(), null, toLong(order.getShipFee()), toLong(order.getOriginShipFee()), toLong(order.getDiscount()),
                null, toLong(order.getDiffFee()), toLong(order.getDistributionRate()), toLong(order.getCommissionRate()), null));
        var extraInfo = B3Hash.copyWithHash(new ExtraView(null, order.getExtraJson(), order.getTagsJson(), null));
        long feeId = feeViewDao.createOrFind(feeInfo).id();
        long extraId = extraViewDao.createOrFind(extraInfo).id();
        var update = new ShopOrder();
        update.setId(orderId);
        update.setFeeId(feeId);
        update.setExtraId(extraId);
        Integer outFromType = order.getOutFrom() == null ?
                null : OrderOutFrom.fromCode(order.getOutFrom()).Id();
        update.setOutFromType(outFromType);
        if (order.getOutFrom() != null) {
            // fill the fee's info and extra's info
            update.setProfitId(generateProfitView(orderId, order.getShopId(), OrderOutFrom.fromCode(order.getOutFrom()), order));
        }
        shopOrderDao.update(update);
        // now to create sku-order info
        for (SkuOrder skuOrder : skuOrderDao.findByOrderId(orderId)) {
            feeInfo = B3Hash.copyWithHash(new FeeView(null, skuOrder.getOriginFee(), skuOrder.getFee(), skuOrder.getOriginFee(), skuOrder.getFee(),
                    skuOrder.getTax(), skuOrder.getShipFee(), skuOrder.getShipFeeDiscount(), skuOrder.getDiscount(), skuOrder.getAfterDiscountFee(),
                    toLong(skuOrder.getDiffFee()), toLong(skuOrder.getDistributionRate()), toLong(skuOrder.getCommissionRate()), null));
            extraInfo = B3Hash.copyWithHash(new ExtraView(null, skuOrder.getExtraJson(), skuOrder.getTagsJson(), null));
            var depotInfo = B3Hash.copyWithHash(new DepotView(null, skuOrder.getDepotCode(), skuOrder.getDepotName(), null));
            var skuImageInfo = B3Hash.copyWithHash(new ImageView(null, skuOrder.getSkuImage(), null));
            var subOrderUpdate = new SkuOrder();
            subOrderUpdate.setId(skuOrder.getId());
            subOrderUpdate.setFeeId(feeViewDao.createOrFind(feeInfo).id());
            subOrderUpdate.setExtraId(extraViewDao.createOrFind(extraInfo).id());
            subOrderUpdate.setDepotId(depotViewDao.createOrFind(depotInfo).id());
            subOrderUpdate.setSkuImageId(imageInfoDao.createOrFind(skuImageInfo).id());
            subOrderUpdate.setOutFromType(outFromType);
            skuOrderDao.update(subOrderUpdate);
        }
    }

    @Override
    public void updateOrderPushException(Long shopOrderId, Integer exceptionType, String exceptionReason) {
        shopOrderDao.updateOrderPushException(shopOrderId, exceptionType, exceptionReason);
    }

    public Long generateProfitView(Long orderId, Long shopId, OrderOutFrom outFrom, ShopOrder order) {
    	if (outFrom == null) return null;
		var profitRelated = balanceDetailDao.findOrderProfit(shopId, orderId);
		var profitBelong = new HashMap<Long, List<BalanceDetail>>();
		for (BalanceDetail balanceDetail : profitRelated) {
			profitBelong.computeIfAbsent(balanceDetail.getUserId(), i -> new LinkedList<>())
					.add(balanceDetail);
		}
		if (profitBelong.isEmpty()) return null;
        ProfitView  profitView = null;

		switch (outFrom) {
			// weShop is only one profit
			case WE_SHOP -> profitView = new ProfitView(null, profitRelated.get(0).getChangeFee(), null, null, null, null, null);
			// subStore has different profit view
			case SUB_STORE -> {
				// find the subStore first
				if (order.getOutShopId() == null) break;
				Long ownerId = subStoreReadService.findById(Long.parseLong(order.getOutShopId())).getResult().getUserId();
				Long guiderUserId = order.getRefererId();
				profitView = new ProfitView(null,
						profitBelong.getOrDefault(ownerId, Collections.emptyList())
						.stream().map(BalanceDetail::getChangeFee).reduce(0L, Long::sum),
						profitBelong.getOrDefault(guiderUserId, Collections.emptyList())
						.stream().map(BalanceDetail::getChangeFee).reduce(0L, Long::sum),
						// service-provider-profit, actually it will belong to the service-provider... damn it
						profitRelated.stream().filter(p -> !p.getUserId().equals(ownerId) && !p.getUserId().equals(guiderUserId))
						.map(BalanceDetail::getChangeFee).reduce(0L, Long::sum)
						, null, null, null);
			}
			// level got three too
			case LEVEL_Distribution -> {
			    var supper = storeProxyReadService.findByShopIdAndUserId(order.getShopId(), order.getRefererId()).take();
				profitView = new ProfitView(null, profitBelong.getOrDefault(order.getRefererId(), Collections.emptyList())
						.stream().map(BalanceDetail::getChangeFee).reduce(0L, Long::sum),
						supper.map(StoreProxy::getSupperId).map(profitBelong::get)
                        .orElse(Collections.emptyList()).stream().map(BalanceDetail::getChangeFee).reduce(0L, Long::sum),
                        supper.map(StoreProxy::getSupperId).map(supperId -> profitRelated.stream().filter(p -> !p.getUserId().equals(supperId) && !p.getUserId().equals(order.getRefererId())))
                        .orElse(Stream.empty()).map(BalanceDetail::getChangeFee).reduce(0L, Long::sum),
                        null, null, null);
			}
			default -> {}
		}
		if (profitView == null) return null;
        var profitInfo = B3Hash.copyWithHash(profitView);
        // the profit is found now, construct it with level info
        return profitViewDao.createOrFind(profitInfo).id();
    }
}
