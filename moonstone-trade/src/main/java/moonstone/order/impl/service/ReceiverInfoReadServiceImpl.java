/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.OrderReceiverInfoDao;
import moonstone.order.impl.dao.ReceiverInfoDao;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderReceiverInfo;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.service.ReceiverInfoReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Author  : panxin
 * Date    : 2:03 PM 3/5/16
 * Mail    : <EMAIL>
 */
@Slf4j
@Service
@RpcProvider
public class ReceiverInfoReadServiceImpl implements ReceiverInfoReadService {

    private final ReceiverInfoDao receiverInfoDao;

    private final OrderReceiverInfoDao orderReceiverInfoDao;

    @Autowired
    public ReceiverInfoReadServiceImpl(ReceiverInfoDao receiverInfoDao,
                                       OrderReceiverInfoDao orderReceiverInfoDao) {
        this.receiverInfoDao = receiverInfoDao;
        this.orderReceiverInfoDao = orderReceiverInfoDao;
    }

    @Override
    public Response<List<ReceiverInfo>> findByUserId(Long userId) {
        Response<List<ReceiverInfo>> resp = new Response<List<ReceiverInfo>>();
        try {
            List<ReceiverInfo> receiverInfos = receiverInfoDao.findByUserId(userId);
            receiverInfos.forEach(ReceiverInfo::decrypt);
            resp.setResult(receiverInfos);
        } catch (Exception e) {
            log.error("failed to find receive address by userId = {}, cause: {}", userId,
                    Throwables.getStackTraceAsString(e));
            resp.setError("receive.address.find.fail");
        }
        return resp;
    }

    @Override
    public Response<Paging<ReceiverInfo>> findPageByUserId(Long userId, Integer pageNo, Integer pageSize) {
        try {
            Map<String, Object> parameter = new HashMap<>();
            parameter.put("userId", userId);
            parameter.put("offset", (pageNo - 1) * pageSize);
            parameter.put("limit", pageSize);
            parameter.put("orderByCriteria", " order by is_default desc, id desc");

            Paging<ReceiverInfo> paging = receiverInfoDao.paging(parameter);
            paging.getData().forEach(ReceiverInfo::decrypt);
            return Response.ok(paging);
        } catch (Exception ex) {
            log.error("ReceiverInfoReadServiceImpl.findPageByUserId error, userId={}, pageNo={}, pageSize={}", userId, pageNo, pageSize, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<ReceiverInfo> findById(Long id) {
        try {
            final ReceiverInfo receiverInfo = receiverInfoDao.findById(id);
            if (receiverInfo == null) {
                log.error("no receiver info (id={}) found ", id);
                return Response.fail("receiver.info.not.found");
            }
            receiverInfo.decrypt();
            return Response.ok(receiverInfo);
        } catch (Exception e) {
            log.error("failed to find receiveInfo by id = {}, cause: {}", id,
                    Throwables.getStackTraceAsString(e));
            return Response.fail("receive.address.find.fail");
        }
    }

    /**
     * 根据(子)订单查找对应的收货人信息
     *
     * @param orderId    (子)订单id
     * @param orderLevel 订单级别
     * @return 符合条件的收货人信息列表
     */
    @Override
    public Response<List<ReceiverInfo>> findByOrderId(Long orderId, OrderLevel orderLevel) {
        List<OrderReceiverInfo> orderReceiverInfos = orderReceiverInfoDao.findByOrderIdAndOrderLevel(orderId, orderLevel);
        if (CollectionUtils.isEmpty(orderReceiverInfos)) {
            return Response.ok(Collections.emptyList());
        }
        List<ReceiverInfo> receiverInfos = Lists.newArrayListWithCapacity(orderReceiverInfos.size());
        for (OrderReceiverInfo orderReceiverInfo : orderReceiverInfos) {
            orderReceiverInfo.getReceiverInfo().decrypt();
            receiverInfos.add(orderReceiverInfo.getReceiverInfo());
        }
        return Response.ok(receiverInfos);
    }

    @Override
    public Response<Optional<ReceiverInfo>> findDefaultByUserId(Long userId) {
        try {
            ReceiverInfo receiverInfo = receiverInfoDao.findDefaultByUserId(userId);
            Optional<ReceiverInfo> optional = Optional.ofNullable(receiverInfo);
            optional.ifPresent(ReceiverInfo::decrypt);
            return Response.ok(optional);
        } catch (Exception e) {
            log.error("fail to find default receiverInfo by userId={},cause:{}",
                    userId, Throwables.getStackTraceAsString(e));
            return Response.fail("default.receive.info.find.fail");
        }
    }

    @Override
    public Response<Long> maxId() {
        Response<Long> resp = new Response<Long>();
        try {
            Long n = receiverInfoDao.maxId();
            resp.setResult(n);
        } catch (Exception e) {
            log.error("failed to count receive address, cause: {}", Throwables.getStackTraceAsString(e));
            resp.setError("receive.address.find.fail");
        }
        return resp;
    }

    @Override
    public Response<List<ReceiverInfo>> findByUserIdLimitOne(Long userId) {
        Response<List<ReceiverInfo>> resp = new Response<List<ReceiverInfo>>();
        try {
            List<ReceiverInfo> receiverInfos = receiverInfoDao.findByUserIdLimitOne(userId);
            receiverInfos.forEach(ReceiverInfo::decrypt);
            resp.setResult(receiverInfos);
        } catch (Exception e) {
            log.error("failed to find receive address by userId = {}, cause: {}", userId,
                    Throwables.getStackTraceAsString(e));
            resp.setError("receive.address.find.fail");
        }
        return resp;
    }
    @Override
    public Response<List<OrderReceiverInfo>> findByOrderIds(List<Long> orderIds, OrderLevel orderLevel) {
        try {
            List<OrderReceiverInfo> resultList = orderReceiverInfoDao.findByOrderIdsAndOrderLevel(orderIds, orderLevel);
            resultList.forEach(item -> item.getReceiverInfo().decrypt());
            return Response.ok(resultList);
        } catch (Exception ex) {
            log.error("ReceiverInfoReadServiceImpl.findByOrderIds error ", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Map<Long, ReceiverInfo> findMapByOrderIds(List<Long> orderIds, OrderLevel orderLevel) {
        var list = findByOrderIds(orderIds, orderLevel).getResult();

        return CollectionUtils.isEmpty(list) ? Collections.emptyMap() :
                list.stream().collect(Collectors.toMap(OrderReceiverInfo::getOrderId, OrderReceiverInfo::getReceiverInfo, (k1, k2) -> k1));
    }

    @Override
    public Paging<ReceiverInfo> paging(Map<String, Object> query) {
        return receiverInfoDao.paging(query);
    }

    @Override
    public boolean batchUpdateReceiverInfos(List<ReceiverInfo> receiverInfoList) {
        return receiverInfoDao.batchUpdateReceiverInfos(receiverInfoList);
    }


}
