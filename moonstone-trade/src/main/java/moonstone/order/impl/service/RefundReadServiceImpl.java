/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Arguments;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.enums.RefundReasonTypeEnum;
import moonstone.common.enums.RefundReturnDetailApplyReceiptTypeEnum;
import moonstone.common.enums.RefundReturnDetailRefundReceiptTypeEnum;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.ImageUrlHandler;
import moonstone.common.utils.LogUtil;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuComboRelation;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuComboRelationService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.AfterSaleTimeRecordWrapper;
import moonstone.order.api.FlowPicker;
import moonstone.order.dto.*;
import moonstone.order.dto.fsm.Flow;
import moonstone.order.dto.fsm.OrderOperation;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.view.SkuOrderView;
import moonstone.order.impl.dao.OrderRefundDao;
import moonstone.order.impl.dao.RefundDao;
import moonstone.order.impl.dao.ShopOrderDao;
import moonstone.order.impl.dao.SkuOrderDao;
import moonstone.order.model.*;
import moonstone.order.model.result.RefundExportDO;
import moonstone.order.model.result.RefundItemDO;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.RefundReturnDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 退款单相关读服务
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-22
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class RefundReadServiceImpl implements RefundReadService {

    private final RefundDao refundDao;

    private final OrderRefundDao orderRefundDao;

    private final SkuOrderDao skuOrderDao;

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private SkuComboRelationService skuComboRelationService;

    @Resource
    private SkuReadService skuReadService;

    private final ShopOrderDao shopOrderDao;

    private final FlowPicker flowPicker;

    private final RefundReturnDetailService refundReturnDetailService;


    /**
     * 根据id查询退款单, 如果查询不到,报错
     *
     * @param id sku退款单id
     * @return sku退款单
     */
    @Override
    public Response<Refund> findById(Long id) {
        try {
            Refund refundApply = refundDao.findById(id);
            if (refundApply == null) {
                log.error("no order refund(id={}) found", id);
                return Response.fail("order.refund.not.found");
            }
            return Response.ok(refundApply);
        } catch (Exception e) {
            log.error("failed to find order refund(id={}), cause:{}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("order.refund.find.fail");
        }
    }

    /**
     * 根据(子)订单和级别查询退款单列表
     * 如果是店铺订单，则会把下面的子订单对应的也查出来
     *
     * @param orderId    (子)订单id
     * @param orderLevel 订单级别
     * @return 对应的退款单列表
     */
    @Override
    public Response<List<Refund>> findAllByOrderIdAndOrderLevel(Long orderId, OrderLevel orderLevel) {
        try {
            List<OrderRefund> orderRefunds = orderRefundDao.findByOrderIdAndOrderType(orderId, orderLevel);
            if (orderLevel.equals(OrderLevel.SHOP)) {
                List<SkuOrder> skuOrders = skuOrderDao.findByOrderId(orderId);
                List<Long> skuOrderIds = skuOrders.stream().filter(Objects::nonNull).map(OrderBase::getId).collect(Collectors.toList());
                List<OrderRefund> skuOrderRefunds = orderRefundDao.findByOrderIdsAndOrderType(skuOrderIds, OrderLevel.SKU);
                orderRefunds.addAll(skuOrderRefunds);
            }
            HashSet<OrderRefund> orderRefundSet = new HashSet<>(orderRefunds);
            orderRefunds.clear();
            orderRefunds.addAll(orderRefundSet);
            List<Long> refundIds = orderRefunds.stream().filter(Objects::nonNull).map(OrderRefund::getRefundId).collect(Collectors.toList());
            List<Refund> refunds = refundDao.findByIds(refundIds);
            return Response.ok(refunds);
        } catch (Exception e) {
            log.error("failed to find all refunds by orderId={}, orderLevel={}, cause:{}",
                    orderId, orderLevel, Throwables.getStackTraceAsString(e));
            return Response.fail("order.refund.find.fail");
        }
    }

    /**
     * 根据(子)订单和级别查询退款单
     *
     * @param orderId    (子)订单id
     * @param orderLevel 订单级别
     * @return 对应的退款单
     */
    @Override
    public Response<List<Refund>> findByOrderIdAndOrderLevel(Long orderId, OrderLevel orderLevel) {
        try {
            List<OrderRefund> orderRefunds = orderRefundDao.findByOrderIdAndOrderType(orderId, orderLevel);
            if (CollectionUtils.isEmpty(orderRefunds)) {
                return Response.ok(Collections.emptyList());
            }
            if (orderRefunds.size() == 1) {
                OrderRefund orderRefund = orderRefunds.get(0);
                Long refundId = orderRefund.getRefundId();
                Refund refund = refundDao.findById(refundId);
                if (refund == null) {
                    log.error("no refund(id={}) found", refundId);
                    return Response.fail("refund.not.found");
                }
                final List<Refund> result = Lists.newArrayList(refund);
                return Response.ok(result);
            } else {
                List<Long> refundIds = Lists.newArrayListWithCapacity(orderRefunds.size());
                for (OrderRefund orderRefund : orderRefunds) {
                    refundIds.add(orderRefund.getRefundId());
                }
                List<Refund> refunds = refundDao.findByIds(refundIds);
                return Response.ok(refunds);
            }
        } catch (Exception e) {
            log.error("failed to find order refund(orderId={}, orderLevel={}), cause:{}",
                    orderId, orderLevel, Throwables.getStackTraceAsString(e));
            return Response.fail("order.refund.find.fail");
        }
    }

    /**
     * sku退款单分页列表
     *
     * @param refundCriteria 退款单查询条件
     * @return 分页的sku退款单列表
     */
    @Override
    public Response<Paging<RefundList>> findBy(RefundCriteria refundCriteria) {
        try {
            if (refundCriteria.getStatus() != null && refundCriteria.getStatus().isEmpty()) {
                return Response.ok(Paging.empty());
            }
            //退款类型对应的字符串（仅退款：refund。退货退款：return）
            if (StrUtil.isNotBlank(refundCriteria.getRefundTypeStr()) && refundCriteria.getRefundTypeStr().equals("refund")) {
                refundCriteria.setRefundTypes(List.of(1, 2));
            }
            if (StrUtil.isNotBlank(refundCriteria.getRefundTypeStr()) && refundCriteria.getRefundTypeStr().equals("return")) {
                refundCriteria.setRefundTypes(List.of(3));
            }
            if (CollUtil.isEmpty(refundCriteria.getStatus())) {
                if (ObjUtil.isNotEmpty(refundCriteria.getStatusInt())) {
                    refundCriteria.setStatus(List.of(refundCriteria.getStatusInt()));
                }
            }
            PageInfo pageInfo = new PageInfo(refundCriteria.getPageNo(), refundCriteria.getSize());
            //把订单id,订单类型转为退款单id列表
            transformOrderIdAndOrderType(refundCriteria);
            if (refundCriteria.getOrderId() != null && CollectionUtils.isEmpty(refundCriteria.getIds())) {
                return Response.ok(Paging.empty());
            }
            //处理查询时间
            handleDate(refundCriteria);
            Map<String, Object> params = refundCriteria.toMap();
            log.info("toMap后的参数 {}", params);
            Paging<Refund> refundP = refundDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), params);
            if (Objects.equals(refundP.getTotal(), 0L)) {
                return Response.ok(Paging.empty());
            }
            Integer skuOrderLimit = refundCriteria.getSkuOrderLimit();
            List<RefundList> refundLists = composeRefundList(refundP.getData(), skuOrderLimit);
            return Response.ok(new Paging<>(refundP.getTotal(), refundLists));
        } catch (Exception e) {
            log.error("failed to find order refunds by {}, cause:{}",
                    refundCriteria, Throwables.getStackTraceAsString(e));
            return Response.fail("order.refund.find.fail");
        }
    }

    private void handleDate(RefundCriteria refundCriteria) {
        if (refundCriteria.getStartAt() != null) {
            refundCriteria.setStartAt(DateUtil.withTimeAtStartOfDay(refundCriteria.getStartAt()));
        }
        if (refundCriteria.getEndAt() != null) {
            refundCriteria.setEndAt(DateUtil.withTimeAtEndOfDay(refundCriteria.getEndAt()));
        }
    }

    private void transformOrderIdAndOrderType(RefundCriteria refundCriteria) {
        Long orderId = refundCriteria.getOrderId();
        if (null == orderId) {
            return;
        }
        Integer orderLevel = MoreObjects.firstNonNull(refundCriteria.getOrderLevel(), OrderLevel.SHOP.getValue());

        List<OrderRefund> orderRefunds = orderRefundDao.findByOrderIdAndOrderType(orderId, OrderLevel.fromInt(orderLevel));
        List<Long> refundIs = retrieveRefundIds(orderRefunds);

        //如果按照主单退款找不到，则按照子单再查一次
        if (CollectionUtils.isEmpty(refundIs) && OrderLevel.fromInt(orderLevel) == OrderLevel.SHOP) {
            List<SkuOrder> skuOrders = skuOrderDao.findByOrderId(orderId);
            if (CollectionUtils.isEmpty(skuOrders)) {
                return;
            }

            List<Long> skuOrderIds = Lists.newArrayListWithCapacity(skuOrders.size());
            for (SkuOrder skuOrder : skuOrders) {
                skuOrderIds.add(skuOrder.getId());
            }
            List<OrderRefund> orderRefundsOfSkus = orderRefundDao.findByOrderIdsAndOrderType(skuOrderIds, OrderLevel.SKU);
            refundIs = retrieveRefundIds(orderRefundsOfSkus);
        }

        refundCriteria.setIds(refundIs);
    }

    private List<Long> retrieveRefundIds(List<OrderRefund> orderRefunds) {
        return Lists.transform(orderRefunds, OrderRefund::getRefundId);
    }

    private List<RefundList> composeRefundList(final List<Refund> refunds, Integer skuOrderLimit) {
        List<RefundList> result = Lists.newArrayList();

        Map<Long, OrderRefund> byRefundId = new HashMap<>(8);
        Map<Long, SkuOrderView> bySkuOrderId = new HashMap<>(8);
        ArrayListMultimap<Long, SkuOrderView> byShopOrderId = ArrayListMultimap.create();

        //准备一些组装数据需要用的参数
        prepareData(refunds, byRefundId, byShopOrderId, bySkuOrderId, skuOrderLimit);

        for (Refund refund : refunds) {
            try {
                RefundList refundList = new RefundList();
                refundList.setRefund(refund);
                Map<String, Object> query = new HashMap<>();
                query.put("refundId", refund.getId());
                RefundReturnDetail refundReturnDetail = refundReturnDetailService.getOne(query);
                if (ObjUtil.isEmpty(refundReturnDetail)) {
                    // 对于历史数据的处理
                    refundList.setApplyReceiptType(RefundReturnDetailApplyReceiptTypeEnum.RECEIPT_SUCCESS.getCode());
                    if (refund.getStatus().equals(OrderStatus.REFUND.getValue())) {
                        refundList.setRefundReceiptType(RefundReturnDetailRefundReceiptTypeEnum.RECEIPT_SUCCESS.getCode());
                    } else {
                        refundList.setRefundReceiptType(RefundReturnDetailRefundReceiptTypeEnum.RECEIPT_FAIL.getCode());
                    }
                } else {
                    refundList.setApplyReceiptType(refundReturnDetail.getApplyReceiptType());
                    refundList.setApplyReceiptMsg(refundReturnDetail.getApplyReceiptMsg());
                    refundList.setRefundReceiptType(refundReturnDetail.getRefundReceiptType());
                    refundList.setSellerAddress(refundReturnDetail.getSellerAddress());
                }
                //若已经存在发货单order-shipments，则已发货
                if (refundDao.findRefundAfterShipped(refund.getId()) > 0L) {
                    refundList.setActualRefundType(Refund.RefundType.AFTER_SALE_REFUND.value());
                }

                OrderRefund orderRefund = byRefundId.get(refund.getId());
                switch (orderRefund.getOrderLevel()) {
                    case SHOP:
                        refundList.setSkuOrders(Lists.newArrayList(
                                byShopOrderId.get(orderRefund.getOrderId())));
                        break;
                    case SKU:
                        refundList.setSkuOrders(Lists.newArrayList(
                                bySkuOrderId.get(orderRefund.getOrderId())));
                        break;
                    default:
                        log.error("order refund (id={}) level invalid", orderRefund.getId());
                }
                result.add(refundList);
            } catch (Exception e) {
                log.error("refund (id={}) compose fail, cause:{}, skip",
                        refund.getId(), Throwables.getStackTraceAsString(e));
            }
        }
        return result;
    }

    private void prepareData(List<Refund> source,
                             Map<Long, OrderRefund> byRefundId,
                             ArrayListMultimap<Long, SkuOrderView> byShopOrderId,
                             Map<Long, SkuOrderView> bySkuOrderId, Integer skuOrderLimit) {
        if (CollectionUtils.isEmpty(source)) {
            return;
        }
        List<Long> refundIds = Lists.transform(source, Refund::getId);
        //根据退款单id列表查询绑定关系
        List<OrderRefund> orderRefunds = orderRefundDao.findByRefundIds(refundIds);
        List<Long> orderIds = Lists.transform(orderRefunds, OrderRelation::getOrderId);
        //一个退款单只会对应一个绑定关系
        for (OrderRefund orderRefund : orderRefunds) {
            byRefundId.put(orderRefund.getRefundId(), orderRefund);
        }
        //这里绑定关系可能是店铺订单或者sku订单,所以先把这2种情况可能对应的订单都找出来
        List<SkuOrder> skuOrders = skuOrderDao.findByIds(orderIds);
        for (SkuOrder skuOrder : skuOrders) {
            SkuOrderView view = getSkuOrderView(skuOrder);
            bySkuOrderId.put(skuOrder.getId(), view);
        }
        List<SkuOrder> skuOrderByShopOrderIds;
        if (Arguments.isNull(skuOrderLimit) || skuOrderLimit < 0) {
            skuOrderByShopOrderIds = skuOrderDao.findByOrderIds(orderIds);
        } else {
            skuOrderByShopOrderIds = getSkuOrderByOrderIdsForLimit(orderIds, skuOrderLimit);
        }
        for (SkuOrder skuOrder : skuOrderByShopOrderIds) {
            byShopOrderId.put(skuOrder.getOrderId(), getSkuOrderView(skuOrder));
        }
    }

    private SkuOrderView getSkuOrderView(SkuOrder skuOrder) {
        SkuOrderView view = new SkuOrderView();
        BeanUtils.copyProperties(skuOrder, view);
        Sku sku1 = skuReadService.findSkuById(skuOrder.getSkuId()).getResult();
        Optional.ofNullable(sku1)
                .map(Sku::getSpecification)
                .ifPresent(view::setSpecification);
        Optional.ofNullable(sku1)
                .map(Sku::getImage)
                .ifPresent(view::setSkuImage);
        Item item = itemReadService.findById(skuOrder.getItemId()).getResult();
        if (ObjectUtil.isNotEmpty(item) && item.isCombination()) {
            Optional.ofNullable(sku1)
                    .map(Sku::getName)
                    .ifPresent(view::setSpecification);
            List<SkuComboDTO> skuComboList = Lists.newArrayList();
            List<SkuComboRelation> skuComboRelationList = skuComboRelationService.getSkuComboRelationListBySkuId(skuOrder.getSkuId());
            for (SkuComboRelation skuComboRelation : skuComboRelationList) {
                SkuComboDTO entity = BeanUtil.copyProperties(skuComboRelation, SkuComboDTO.class);
                Long comboSkuId = entity.getComboSkuId();
                Sku sku = skuReadService.findSkuById(comboSkuId).getResult();
                entity.setComboSkuPrice(sku.getPrice());
                entity.setComboSkuStockQuantity(sku.getStockQuantity());
                entity.setComboSkuSpecification(sku.getSpecification());
                Item comboItem = itemReadService.findById(skuComboRelation.getComboItemId()).getResult();
                entity.setComboItemMainImage(ImageUrlHandler.complete(comboItem.getMainImage()));
                skuComboList.add(entity);
            }
            view.setSkuComboList(skuComboList);
        }
        return view;
    }

    /**
     * 根据退款单id查询关联的(子)订单id列表
     *
     * @param refundId 退款单id
     * @return 关联的(子)订单列表
     */
    @Override
    public Response<List<OrderRefund>> findOrderIdsByRefundId(Long refundId) {
        try {
            List<OrderRefund> orderRefunds = orderRefundDao.findByRefundId(refundId);
            return Response.ok(orderRefunds);
        } catch (Exception e) {
            log.error("failed to find orders for refund(id={}), cause:{}",
                    refundId, Throwables.getStackTraceAsString(e));
            return Response.fail("order.refund.find.fail");
        }
    }

    @Override
    public Response<Refund> findByOutId(String outId) {
        try {
            Refund refund = refundDao.findByOutId(outId);
            return Response.ok(refund);
        } catch (Exception e) {
            log.error("failed to find refund by outId {}, cause:{}",
                    outId, Throwables.getStackTraceAsString(e));
            return Response.fail("orderRefund.find.fail");
        }
    }

    @Override
    public Response<List<Refund>> findByTradeNo(String tradeNo) {
        try {
            List<Refund> refunds = refundDao.findByTradeNo(tradeNo);
            return Response.ok(refunds);
        } catch (Exception e) {
            log.error("failed to find refund by trade no {}, cause:{}",
                    tradeNo, Throwables.getStackTraceAsString(e));
            return Response.fail("orderRefund.find.fail");
        }
    }

    @Override
    public Response<Paging<Refund>> findRefundBy(Integer pageNo, Integer size, RefundCriteria refundCriteria) {
        try {
            PageInfo pageInfo = new PageInfo(pageNo, size);
            Map<String, Object> params = refundCriteria == null ? Maps.newHashMap() : refundCriteria.toMap();
            Paging<Refund> skuOrders = refundDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), params);
            return Response.ok(skuOrders);
        } catch (Exception e) {
            log.error("failed to find order refunds by {}, pageNo={}, size={}, cause:{}",
                    refundCriteria, pageNo, size, Throwables.getStackTraceAsString(e));
            return Response.fail("order.refund.find.fail");
        }
    }

    @Override
    public Response<RefundDetail> findDetailById(Long refundId) {
        try {

            RefundDetail detail = new RefundDetail();
            Refund refund = refundDao.findById(refundId);
            if (Arguments.isNull(refund)) {
                return Response.fail("refund.not.exist");
            }
            refund.setReasonTypeDesc(RefundReasonTypeEnum.getDesc(refund.getReasonType()));
            //根据退款单id列表查询绑定关系
            List<OrderRefund> orderRefunds = orderRefundDao.findByRefundId(refundId);
            //一个退款单只会对应一个绑定关系
            OrderRefund orderRefund = orderRefunds.get(0);

            ShopOrder shopOrder = new ShopOrder();
            List<SkuOrder> skuOrderList = new ArrayList<>();
            switch (orderRefund.getOrderLevel()) {
                case SHOP:
                    shopOrder = shopOrderDao.findById(orderRefund.getOrderId());
                    List<SkuOrder> skuOrders = skuOrderDao.findByOrderId(orderRefund.getOrderId());
                    for (SkuOrder skuOrder : skuOrders){
                        skuOrderList.add(getSkuOrderView(skuOrder));
                    }
                    break;
                case SKU:
                    SkuOrder skuOrder = skuOrderDao.findById(orderRefund.getOrderId());
                    skuOrderList.add(getSkuOrderView(skuOrder));
                    shopOrder = shopOrderDao.findById(skuOrder.getOrderId());
                    break;
                default:
                    log.error("order refund (id={}) level invalid", orderRefund.getId());
            }

            // 解析 feeDetailJson 为 feeDetail 对象
            if (StrUtil.isNotBlank(shopOrder.getFeeDetailJson())) {
                shopOrder.setFeeDetail(JSON.parseObject(shopOrder.getFeeDetailJson(), ShopOrderFeeDetailDTO.class));
            }

            detail.setRefund(refund);
            detail.setShopOrder(shopOrder);
            detail.setSkuOrderList(skuOrderList);

            // 计算总税费
            Long totalTax = 0L;
            for (SkuOrder skuOrder : skuOrderList) {
                totalTax += skuOrder.getTax() != null ? skuOrder.getTax() : 0L;
            }
            detail.setTotalTax(totalTax);

            try {
                Flow flow = flowPicker.pick(skuOrderList.get(0), OrderLevel.SKU);
                Set<OrderOperation> operations = flow.availableOperations(refund.getStatus());
                detail.setOperations(operations);
            } catch (Exception e) {
                log.error("fail to get refund({})'s operations, just don't return it's operations, cause",
                        JSON.toJSONString(refund), e);
            }

            detail.setAfterSaleTimeRecord(AfterSaleTimeRecordWrapper.wrap(refund));
            Map<String, Object> query = new HashMap<>();
            query.put("refundId", refundId);
            RefundReturnDetail refundReturnDetail = refundReturnDetailService.getOne(query);
            if (ObjUtil.isNotEmpty(refundReturnDetail)) {
                detail.setExpressNo(refundReturnDetail.getExpressNo());
                detail.setExpressCompany(refundReturnDetail.getExpressCompany());
                detail.setSellerAddress(refundReturnDetail.getSellerAddress());
                detail.setApplyReceiptType(refundReturnDetail.getApplyReceiptType());
                detail.setApplyReceiptMsg(refundReturnDetail.getApplyReceiptMsg());
                detail.setRefundReceiptType(refundReturnDetail.getRefundReceiptType());
            }
            return Response.ok(detail);

        } catch (Exception e) {
            log.error("failed to find refund detail by id {}, cause:{}",
                    refundId, Throwables.getStackTraceAsString(e));
            return Response.fail("orderRefund.find.fail");
        }
    }

    /**
     * 根据订单id查询对应的子订单(每个订单限制了子单返回数量)
     *
     * @param shopOrderIds 店铺订单id
     * @param limit        限制数量
     * @return 子单集合
     */
    private List<SkuOrder> getSkuOrderByOrderIdsForLimit(List<Long> shopOrderIds, Integer limit) {

        List<SkuOrder> skuOrders = Lists.newArrayList();
        Set<Long> idSet = new HashSet<>(shopOrderIds);
        for (Long shopOrderId : idSet) {
            skuOrders.addAll(skuOrderDao.findByOrderIdWithLimit(shopOrderId, limit));
        }
        return skuOrders;
    }

    @Override
    public Response<List<Refund>> findByShopOrderIds(List<Long> orderIdList) {
        try {
            if (CollectionUtils.isEmpty(orderIdList)) {
                log.info("{} empty orderIdList", LogUtil.getClassMethodName());
                return Response.ok(new ArrayList<>());
            }
            List<Long> refundIds = orderRefundDao.listRefundIdByShopOrderIds(orderIdList);
            if (CollectionUtils.isEmpty(refundIds)) {
                return Response.ok(new ArrayList<>());
            }
            return Response.ok(refundDao.findByIds(refundIds));
        } catch (Exception ex) {
            log.error("{} orderIdList:{}", LogUtil.getClassMethodName(), orderIdList);
            ex.printStackTrace();
            return Response.fail("order.refund.find.fail");
        }
    }

    @Override
    public Response<OrderRefund> findAllByOrderIdAndOrderLevelLimitShopOrder(Long orderId, OrderLevel shop, Integer status) {
        try {
            OrderRefund refund = orderRefundDao.findAllByOrderIdAndOrderLevelLimitShopOrder(orderId, shop.getValue(), status);
            return Response.ok(refund);
        } catch (Exception ex) {
            log.error("{} orderId:{}", LogUtil.getClassMethodName(), orderId);
            ex.printStackTrace();
            return Response.fail("order.refund.find.fail");
        }
    }

    @Override
    public Response<Map<Long, List<RefundItemDO>>> findRefundItems(List<Long> refundIds) {
        try {
            List<RefundItemDO> list = new ArrayList<>();

            List<RefundItemDO> listByShopOrder = orderRefundDao.findRefundItemByShopOrder(refundIds);
            if (!CollectionUtils.isEmpty(listByShopOrder)) {
                list.addAll(listByShopOrder);
            }
            List<RefundItemDO> listBySkuOrder = orderRefundDao.findRefundItemBySkuOrder(refundIds);
            if (!CollectionUtils.isEmpty(listBySkuOrder)) {
                list.addAll(listBySkuOrder);
            }

            return Response.ok(list.stream().collect(Collectors.groupingBy(RefundItemDO::getRefundId)));
        } catch (Exception ex) {
            log.error("{} refundIds:{}", LogUtil.getClassMethodName(), refundIds, ex);
            return Response.fail("order.refund.find.fail");
        }
    }

    @Override
    public Response<List<OrderRefund>> findOrderRefund(OrderLevel orderLevel, List<Long> orderIds) {
        try {
            if (orderLevel == null || CollectionUtils.isEmpty(orderIds)) {
                return Response.ok(Collections.emptyList());
            }

            return Response.ok(orderRefundDao.findByOrderIdsAndOrderType(orderIds, orderLevel));
        } catch (Exception ex) {
            log.error("{} findOrderRefund:orderLevel={}， orderIds={}", LogUtil.getClassMethodName(), orderLevel, orderIds, ex);
            return Response.fail("order.refund.find.fail");
        }
    }

    @Override
    public Response<Map<Long, ShopOrder>> findShopOrder(List<Long> refundIds) {
        try {
            if (CollectionUtils.isEmpty(refundIds)) {
                return Response.ok(Collections.emptyMap());
            }

            var orderRefunds = orderRefundDao.findByRefundIds(refundIds);
            if (CollectionUtils.isEmpty(orderRefunds)) {
                return Response.ok(Collections.emptyMap());
            }

            Map<Long, ShopOrder> map = new HashMap<>();
            appendByShopOrder(map, orderRefunds.stream().filter(o -> o.getOrderLevel().getValue() == OrderLevel.SHOP.getValue())
                    .collect(Collectors.toList()));
            appendBySkuOrder(map, orderRefunds.stream().filter(o -> o.getOrderLevel().getValue() == OrderLevel.SKU.getValue())
                    .collect(Collectors.toList()));

            return Response.ok(map);
        } catch (Exception ex) {
            log.error("RefundReadServiceImpl.findShopOrder error", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<RefundExportDO>> findForExport(RefundCriteria criteria) {
        try {
            if (criteria == null) {
                return Response.fail("入参不能为空");
            }

            return Response.ok(refundDao.findForExport(criteria));
        } catch (Exception ex) {
            log.error("RefundReadServiceImpl.findForExport error, criteria={}", JSON.toJSONString(criteria), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<Refund>> findByIds(List<Long> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return Response.ok(Collections.emptyList());
            }

            return Response.ok(refundDao.findByIds(ids));
        } catch (Exception ex) {
            log.error("RefundReadServiceImpl.findByIds ", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Map<Long, Refund> findMapByIds(List<Long> ids) {
        var list = findByIds(ids).getResult();
        return CollectionUtils.isEmpty(list) ? Collections.emptyMap() :
                list.stream().collect(Collectors.toMap(Refund::getId, o -> o, (k1, k2) -> k1));
    }

    @Override
    public Response<List<OrderRefund>> findRefundedByOrderIds(List<Long> orderIds, OrderLevel orderLevel) {
        try {
            if (CollectionUtils.isEmpty(orderIds)) {
                return Response.fail("入参缺失");
            }

            return Response.ok(orderRefundDao.findRefundedByOrderIds(orderIds, orderLevel.getValue()));
        } catch (Exception ex) {
            log.error("RefundReadServiceImpl.findRefundedByShopOrderIds error, orderIds={}", JSON.toJSONString(orderIds), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public ShopOrder findShopOrderByRefundId(Long refundId) {
        // 先根据 退款id查询 订单和退款单的关系
        List<OrderRefund> orderRefundList = orderRefundDao.findByRefundId(refundId);
        if (CollectionUtils.isEmpty(orderRefundList)) {
            return null;
        }
        OrderRefund orderRefund = orderRefundList.get(0);
        Map<String, Object> query = new HashMap<>();
        query.put("id", orderRefund.getOrderId());
        return shopOrderDao.selectOne(query);
    }

    private void appendBySkuOrder(Map<Long, ShopOrder> map, List<OrderRefund> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        var skuOrders = skuOrderDao.findByIds(list.stream().map(OrderRefund::getOrderId)
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(skuOrders)) {
            return;
        }

        var shopOrders = shopOrderDao.findByIds(skuOrders.stream()
                .map(SkuOrder::getOrderId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(shopOrders)) {
            return;
        }

        var skuOrderMap = skuOrders.stream().collect(Collectors.toMap(SkuOrder::getId, o -> o, (k1, k2) -> k1));
        var shopOrderMap = shopOrders.stream().collect(Collectors.toMap(ShopOrder::getId, o -> o, (k1, k2) -> k1));
        list.forEach(orderRefund -> {
            var skuOrder = skuOrderMap.get(orderRefund.getOrderId());
            if (skuOrder == null) {
                return;
            }

            map.put(orderRefund.getRefundId(), shopOrderMap.get(skuOrder.getOrderId()));
        });
    }

    private void appendByShopOrder(Map<Long, ShopOrder> map, List<OrderRefund> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        var shopOrders = shopOrderDao.findByIds(list.stream().map(OrderRefund::getOrderId)
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(shopOrders)) {
            return;
        }

        var orderMap = shopOrders.stream().collect(Collectors.toMap(ShopOrder::getId, o -> o, (k1, k2) -> k1));
        list.forEach(orderRefund -> {
            map.put(orderRefund.getRefundId(), orderMap.get(orderRefund.getOrderId()));
        });
    }
}
