/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.mongo.BaseAddress;
import moonstone.order.impl.dao.ReceiverInfoDao;
import moonstone.order.impl.manager.ReceiverInfoManager;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.service.ReceiverInfoWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.google.common.base.Preconditions.checkArgument;
import static io.terminus.common.utils.Arguments.notNull;

/**
 * Author  : panxin
 * Date    : 2:03 PM 3/5/16
 * Mail    : <EMAIL>
 */
@Slf4j
@Service
@RpcProvider
public class ReceiverInfoWriteServiceImpl implements ReceiverInfoWriteService {

    @Autowired
    private ReceiverInfoDao receiverInfoDao;

    @Autowired
    private ReceiverInfoManager receiveInfoManager;

    @Autowired
    private MongoTemplate mongoTemplate;


    @Override
    public Response<Long> createReceiverInfo(ReceiverInfo tradeAddress) {
        Response<Long> resp = new Response<>();
        try{
            if (tradeAddress == null) {
                log.warn("receive address data is null, can not be created");
                return  Response.fail("receive.address.create.fail");
            }
            List<ReceiverInfo> receiverInfos = receiverInfoDao.findByUserId(tradeAddress.getUserId());
            tradeAddress.setIsDefault(receiverInfos.isEmpty());
            appendAreaId(tradeAddress);
            receiverInfoDao.create(tradeAddress);
            resp.setResult(tradeAddress.getId());
        }catch (Exception e) {
            log.error("failed to create receive address cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("receive.address.create.fail");
        }
        return resp;
    }

    @Override
    public void appendAreaId(ReceiverInfo tradeAddress) {
        List<String> directlyCityList = new ArrayList<>();
        directlyCityList.add("上海");
        directlyCityList.add("北京");
        directlyCityList.add("重庆");
        directlyCityList.add("天津");
        if (StrUtil.isNotBlank(tradeAddress.getProvince()) && directlyCityList.contains(tradeAddress.getProvince())) {
            tradeAddress.setProvince(tradeAddress.getProvince() + "市");
        }
        BaseAddress provinceInfo = mongoTemplate.findOne(new Query(Criteria.where("areaName").is(tradeAddress.getProvince())), BaseAddress.class);
        if (provinceInfo != null) {
            tradeAddress.setProvinceId(Integer.valueOf(provinceInfo.getId()));
        } else {
            String province = tradeAddress.getProvince() + "省";
            provinceInfo = mongoTemplate.findOne(new Query(Criteria.where("areaName").is(province)), BaseAddress.class);
            if (provinceInfo != null) {
                tradeAddress.setProvinceId(Integer.valueOf(provinceInfo.getId()));
                tradeAddress.setProvince(province);
            } else {
                log.warn("当前地址为 {} 未找到省份信息", JSONUtil.toJsonStr(tradeAddress.getProvince()));
            }
        }
        BaseAddress cityInfo = mongoTemplate.findOne(new Query(Criteria.where("areaName").is(tradeAddress.getCity())), BaseAddress.class);
        if (cityInfo != null) {
            tradeAddress.setCityId(Integer.valueOf(cityInfo.getId()));
        } else {
            String city = tradeAddress.getCity() + "市";
            provinceInfo = mongoTemplate.findOne(new Query(Criteria.where("areaName").is(city)), BaseAddress.class);
            if (provinceInfo != null) {
                tradeAddress.setCityId(Integer.valueOf(provinceInfo.getId()));
                tradeAddress.setCity(city);
            } else {
                log.warn("当前地址为 {} 未找到市级信息", JSONUtil.toJsonStr(tradeAddress.getCity()));
            }
        }
        BaseAddress regionInfo = mongoTemplate.findOne(new Query(Criteria.where("areaName").is(tradeAddress.getRegion())), BaseAddress.class);
        if (regionInfo != null) {
            tradeAddress.setRegionId(Integer.valueOf(regionInfo.getId()));
        } else {
            String region = tradeAddress.getRegion() + "县";
            provinceInfo = mongoTemplate.findOne(new Query(Criteria.where("areaName").is(region)), BaseAddress.class);
            if (provinceInfo != null) {
                tradeAddress.setRegionId(Integer.valueOf(provinceInfo.getId()));
                tradeAddress.setRegion(region);
            } else {
                log.warn("当前地址为 {} 未找到区级信息", JSONUtil.toJsonStr(tradeAddress.getRegion()));
            }
        }
    }

    @Override
    public boolean batchUpdateReceiverInfos(List<ReceiverInfo> receiverInfoList) {
        return receiverInfoDao.batchUpdateReceiverInfos(receiverInfoList);
    }

    @Override
    public Response<Boolean> updateReceiverInfo(ReceiverInfo tradeAddress) {
        Response<Boolean> resp = new Response<>();
        try{
            if (tradeAddress == null) {
                log.warn("receive address data is null, can not be updated");
                return  Response.fail("receive.address.update.fail");
            }
            resp.setResult(receiverInfoDao.update(tradeAddress));
        }catch (Exception e) {
            log.error("failed to update receive address cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("receive.address.update.fail");
        }
        return resp;
    }

    @Override
    public Response<Boolean> updateReceiverPaper(ReceiverInfo receiverInfo){
        Response<Boolean> resp = new Response<>();
        try{
            if (receiverInfo.getId() == 0L) {
                log.warn("receive ID data is Zero, can not be updated");
                return  Response.fail("receive.address.update.fail");
            }
            receiverInfoDao.updateReceiverPaper(receiverInfo);
            resp.setResult(Boolean.TRUE);
        }catch (Exception e) {
            log.error("failed to update receive address cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("receive.address.update.fail");
        }
        return resp;
    }

    @Override
    public Response<Boolean> updateAreaId(ReceiverInfo tradeAddress) {
        Response<Boolean> resp = new Response<>();
        try{
            if (tradeAddress == null) {
                log.warn("receive address data is null, can not be updated");
                return  Response.fail("receive.address.update.fail");
            }
            resp.setResult(receiverInfoDao.updateAreaId(tradeAddress));
        }catch (Exception e) {
            log.error("failed to update receive address cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("receive.address.update.fail");
        }
        return resp;
    }

    @Override
    public Response<Boolean> makeDefault(Long addressId, Long userId) {
        try {
            checkArgument(notNull(addressId), "address.id.empty");
            // 查找当前用户的所有收货地址, 判断需要设置的地址是否属于当前用户
            List<ReceiverInfo> addressList = receiverInfoDao.findByUserId(userId);
            boolean found = false;
            for (ReceiverInfo address : addressList) {
                if (Objects.equal(address.getId(), addressId)) {
                    found = true;
                    break;
                }
            }
            if (!found){
                log.error("receive address id = {} not belong to user userId = {}",
                        addressId, userId);
                throw new ServiceException("authorize.fail");
            }
            receiveInfoManager.makeDefault(addressId, userId, addressList);
            return Response.ok(Boolean.TRUE);
        }catch (IllegalArgumentException e) {
            log.warn("failed to make default by addressId = {}, error : {}", addressId, e.getMessage());
            return Response.fail("receive.address.id.is.null");
        }catch (Exception e) {
            log.error("failed to make default by addressId = {}, error:{}", addressId,
                    Throwables.getStackTraceAsString(e));
            return Response.fail("receive.address.make.default.fail");
        }
    }

    @Override
    public Response<Boolean> deleteAddressByAddressIdAndUserId(Long addressId, Long userId) {
        try{
            checkArgument(notNull(addressId), "address.id.empty");
            checkArgument(notNull(userId), "user.id.empty");
            receiverInfoDao.deleteAddressByIdAndUserId(addressId, userId);
            return Response.ok(Boolean.TRUE);
        }catch (IllegalArgumentException e) {
            log.warn("failed to delete receive address by receiveId = {}, error : ", addressId, e.getMessage());
            return Response.fail("receive.address.id.is.null");
        }catch (Exception e) {
            log.error("failed to delete receive address by receiveId = {}, userId = {}, error : {}",
                    addressId, userId, Throwables.getStackTraceAsString(e));
            return Response.fail("receive.address.delete.fail");
        }
    }

    @Override
    public Response<Boolean> makeDefaultById(Long addressId, Long userId) {
        try {
            checkArgument(notNull(addressId), "address.id.empty");
            // 查找当前用户的所有收货地址, 判断需要设置的地址是否属于当前用户
            List<ReceiverInfo> addressList = receiverInfoDao.findByUserId(userId);

            for (ReceiverInfo address : addressList) {
                receiveInfoManager.makeDefaultById(address.getId(), userId,0L);
            }
            receiveInfoManager.makeDefaultById(addressId, userId,1L);
            return Response.ok(Boolean.TRUE);
        }catch (IllegalArgumentException e) {
            log.warn("failed to make default by addressId = {}, error : {}", addressId, e.getMessage());
            return Response.fail("receive.address.id.is.null");
        }catch (Exception e) {
            log.error("failed to make default by addressId = {}, error:{}", addressId,
                    Throwables.getStackTraceAsString(e));
            return Response.fail("receive.address.make.default.fail");
        }
    }
}
