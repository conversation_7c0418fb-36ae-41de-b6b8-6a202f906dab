/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.encrypt.constant.RegexConstant;
import com.danding.encrypt.utils.AESUtils;
import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import com.google.common.collect.Iterables;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimaps;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Arguments;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.Either;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.*;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.enu.OrderFlagEnum;
import moonstone.order.impl.dao.*;
import moonstone.order.model.*;
import moonstone.order.service.OrderReadService;
import moonstone.promotion.impl.dao.PromotionDao;
import moonstone.promotion.model.Promotion;
import moonstone.user.application.UserWxCacheHolder;
import moonstone.user.cache.UserProfileCacheHolder;
import moonstone.user.model.UserProfile;
import moonstone.user.model.UserWx;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 订单列表读服务
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-23
 */
@Slf4j
@Service
@AllArgsConstructor
@Data
public class OrderReadServiceImpl implements OrderReadService {

    private final SkuOrderDao skuOrderDao;
    private final ShopOrderDao shopOrderDao;
    private final OrderPaymentDao orderPaymentDao;
    private final PaymentDao paymentDao;
    private final OrderShipmentDao orderShipmentDao;
    private final OrderReceiverInfoDao orderReceiverInfoDao;
    private final OrderInvoiceDao orderInvoiceDao;
    private final InvoiceDao invoiceDao;
    private final ShipmentDao shipmentDao;
    private final OrderCommentDao orderCommentDao;
    private final PromotionDao promotionDao;
    private final UserProfileCacheHolder userProfileCacheHolder;
    private final UserWxCacheHolder userWxCacheHolder;
    private final MongoTemplate mongoTemplate;


    private final Integer autoCancelInMinutes = 15;
    private final Integer autoConfirmInMinutes = 60;

    @Override
    public Either<List<SkuOrderCountResult>> querySkuOrderCount(OrderCriteria orderCriteria) {
        try {
            return Either.ok(skuOrderDao.selectCountOrderNumBySkuOrderAndCriteria(orderCriteria));
        } catch (Exception ex) {
            log.error("{} fail to querySkuOrderCount[{}]", LogUtil.getClassMethodName(), orderCriteria.toMap(), ex);
            return Either.error(ex);
        }
    }

    /**
     * 订单分页列表, 如果带了状态过滤, 则表示以子订单维度查询, 否则是以订单维度查询
     * <p>
     * 即使以子订单为维度进行查询, 也需要按照订单id进行归组, 但此时订单的条数是不确定的
     *
     * @param orderCriteria 查询条件
     * @return 分页订单
     */
    @Override
    public Response<Paging<OrderGroup>> findBy(OrderCriteria orderCriteria) {
        try {
            PageInfo pageInfo = new PageInfo(orderCriteria.getPageNo(), orderCriteria.getSize());
            if (orderCriteria.getOrderId() != null && orderCriteria.getOrderId() != 0) {
                Long orderId;
                if (orderCriteria.getId() != null && orderCriteria.getId() != 0) {
                    orderId = orderCriteria.getId();
                    orderCriteria.setId(orderCriteria.getOrderId());
                    orderCriteria.setOrderId(orderId);
                } else {
                    // query the sku-order only
                    orderCriteria.setId(orderCriteria.getOrderId());
                    SkuOrder sOrder = skuOrderDao.findById(orderCriteria.getId());
                    orderCriteria.setOrderId(sOrder.getOrderId());
                }
                return skuOrderPagination(orderCriteria, pageInfo);
            } else {
                orderCriteria.transformShopOrderId();
            }
            /// 首先进行ShopOrder筛选再进行SkuOrder筛选
            if (CollectionUtils.isEmpty(orderCriteria.getStatus()) && orderCriteria.getPushStatus().equals(0) &&
                    orderCriteria.getShipmentStartAt() == null && orderCriteria.getShipmentEndAt() == null) {
                return shopOrderPagination(orderCriteria, pageInfo);
            } else {
                List<Integer> protectedStatus = orderCriteria.getStatus();
                orderCriteria.setStatus(null);
                handleDate(orderCriteria); // 提前做日期区间处理，为下面shopOrderDao.listIdsBy()准备
                orderCriteria.setOrderIds(shopOrderDao.listIdsBy(orderCriteria));
                orderCriteria.setStatus(protectedStatus);
                if (CollectionUtils.isEmpty(orderCriteria.getOrderIds())) {
                    return Response.ok(Paging.empty());
                }
                return skuOrderPagination(orderCriteria, pageInfo);
            }
        } catch (Exception e) {
            log.error("failed to find paging orders by {}, cause:{}",
                    orderCriteria, Throwables.getStackTraceAsString(e));
            e.printStackTrace();
            return Response.fail("order.find.fail");
        }
    }

    /**
     * 店铺订单计数
     * @return 订单数量
     */
    @Override
    public Response<Long> countShopOrder(OrderCriteria orderCriteria) {
        try {
            orderCriteria.transformShopOrderId();
            if (CollectionUtils.isEmpty(orderCriteria.getStatus())) {
                return Response.ok(shopOrderDao.count(orderCriteria));
            } else {
                List<Integer> protectStatus = orderCriteria.getStatus();
                orderCriteria.setStatus(null);
                orderCriteria.setOrderIds(shopOrderDao.listIdsBy(orderCriteria));
                orderCriteria.setStatus(protectStatus);
                if (CollectionUtils.isEmpty(orderCriteria.getOrderIds())) {
                    return Response.ok(0L);
                }
                return Response.ok(skuOrderDao.countShopOrder(orderCriteria));
            }
        } catch (Exception e) {
            log.error("failed to count orders by orderCriteria={}, cause: ", orderCriteria, e);
            return Response.fail("order.count.fail");
        }
    }

    /**
     * 将店铺订单构建成分页订单
     *
     * @param shopOrderPaging 分页店铺订单
     * @return 分页订单
     */
    @Override
    public Response<Paging<OrderGroup>> buildOrderGroup(Paging<ShopOrder> shopOrderPaging) {
        if (shopOrderPaging.getTotal() == 0L) {
            return Response.ok(Paging.empty());
        }
        final List<ShopOrder> shopOrders = shopOrderPaging.getData();
        List<Long> shopOrderIds = Lists.newArrayListWithCapacity(shopOrders.size());
        for (ShopOrder shopOrder : shopOrders) {
            shopOrderIds.add(shopOrder.getId());
        }
        List<SkuOrder> skuOrders = Collections.emptyList();

        if (!CollectionUtils.isEmpty(shopOrderIds)) {
            skuOrders = skuOrderDao.findByOrderIds(shopOrderIds);
        }

        ListMultimap<Long, SkuOrder> byShopOrderId = Multimaps.index(skuOrders, SkuOrder::getOrderId);
        List<OrderGroup> orderGroups = makeOrderGroups(byShopOrderId, shopOrders);

        Paging<OrderGroup> p = new Paging<>(shopOrderPaging.getTotal(), orderGroups);
        return Response.ok(p);
    }

    /**
     * 因为没有状态过滤, 那么就分页查询订单列表
     *
     * @param orderCriteria 查询条件
     * @param pageInfo      分页信息
     * @return 查询结果
     */
    private Response<Paging<OrderGroup>> shopOrderPagination(OrderCriteria orderCriteria, PageInfo pageInfo) {
        //处理查询时间,如果结束时间和开始时间相同,自动为结束时间+1天
        handleDate(orderCriteria);
        log.debug("{} criteria[{}]", LogUtil.getClassMethodName(), JSON.toJSONString(orderCriteria));
        Paging<ShopOrder> pShopOrders = shopOrderDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), orderCriteria.toMap());
        if (pShopOrders.getTotal() == 0L) {
            return Response.ok(Paging.empty());
        }
        final List<ShopOrder> shopOrders = pShopOrders.getData();
        List<Long> shopOrderIds = Lists.newArrayListWithCapacity(shopOrders.size());
        for (ShopOrder shopOrder : shopOrders) {
            shopOrderIds.add(shopOrder.getId());
        }
        List<SkuOrder> skuOrders;

        if (Arguments.isNull(orderCriteria.getSkuOrderLimit()) || orderCriteria.getSkuOrderLimit() < 0) {
            skuOrders = skuOrderDao.findByOrderIds(shopOrderIds);
        } else {
            skuOrders = getSkuOrderByOrderIdsForLimit(shopOrderIds, orderCriteria.getSkuOrderLimit());
        }

        ListMultimap<Long, SkuOrder> byShopOrderId = Multimaps.index(skuOrders, SkuOrder::getOrderId);

        List<OrderGroup> orderGroups = makeOrderGroups(byShopOrderId, shopOrders);

        Paging<OrderGroup> p = new Paging<>(pShopOrders.getTotal(), orderGroups);
        return Response.ok(p);
    }

    private void handleDate(OrderCriteria orderCriteria) {
        if (orderCriteria.getStartAt() != null) {
            orderCriteria.setStartAt(DateUtil.withTimeAtStartOfDay(orderCriteria.getStartAt()));
        }
        if (orderCriteria.getEndAt() != null) {
            orderCriteria.setEndAt(DateUtil.withTimeAtEndOfDay(orderCriteria.getEndAt()));
        }
        if (orderCriteria.getShipmentStartAt() != null) {
            orderCriteria.setShipmentStartAt(DateUtil.withTimeAtStartOfDay(orderCriteria.getShipmentStartAt()));
        }
        if (orderCriteria.getShipmentEndAt() != null) {
            orderCriteria.setShipmentEndAt(DateUtil.withTimeAtEndOfDay(orderCriteria.getShipmentEndAt()));
        }
    }

    /**
     * 根据订单id查询对应的子订单(每个订单限制了子单返回数量)
     *
     * @param shopOrderIds 店铺订单id
     * @param limit        限制数量
     * @return 子单集合
     */
    private List<SkuOrder> getSkuOrderByOrderIdsForLimit(List<Long> shopOrderIds, Integer limit) {

        List<SkuOrder> skuOrders = Lists.newArrayList();
        Set<Long> idSet = new HashSet<>(shopOrderIds);
        for (Long shopOrderId : idSet) {
            skuOrders.addAll(skuOrderDao.findByOrderIdWithLimit(shopOrderId, limit));
        }
        return skuOrders;
    }

    /**
     * 因为有按照状态过滤, 那么分页查询子订单列表
     *
     * @param orderCriteria 查询条件
     * @param pageInfo      分页信息
     * @return 查询结果
     */
    private Response<Paging<OrderGroup>> skuOrderPagination(OrderCriteria orderCriteria, PageInfo pageInfo) {
        if (orderCriteria.getOrderIds() != null && orderCriteria.getOrderIds().isEmpty()) {
            log.warn("{} orderCriteria:{} is empty because empty OrderIds", LogUtil.getClassMethodName(), orderCriteria);
            return Response.ok(Paging.empty());
        }
        //处理查询时间,如果结束时间和开始时间相同,自动为结束时间+1天
        handleDate(orderCriteria);
        Paging<SkuOrder> pSkuOrders = skuOrderDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), orderCriteria.toMap());
        if (pSkuOrders.getTotal() == 0L) {
            return Response.ok(Paging.empty());
        }
        final List<SkuOrder> skuOrders = pSkuOrders.getData();
        ListMultimap<Long, SkuOrder> byShopOrderId = Multimaps.index(skuOrders, SkuOrder::getOrderId);

        List<Long> shopOrderIds = Lists.newArrayList(byShopOrderId.keySet());
        List<ShopOrder> shopOrders = shopOrderDao.findByIds(shopOrderIds);
        List<OrderGroup> orderGroups = makeOrderGroups(byShopOrderId, shopOrders);
        sortOrderGroupsByCreateAtDesc(orderGroups);
        Paging<OrderGroup> p = new Paging<>(pSkuOrders.getTotal(), orderGroups);
        return Response.ok(p);
    }

    private void sortOrderGroupsByCreateAtDesc(List<OrderGroup> orderGroups) {
        orderGroups.sort((o1, o2) -> {
            DateTime o1CreateAt = new DateTime(o1.getShopOrder().getCreatedAt());
            DateTime o2CreateAt = new DateTime(o2.getShopOrder().getCreatedAt());
            if (o1CreateAt.isAfter(o2CreateAt)) {
                return -1;
            } else if (o1CreateAt.isBefore(o2CreateAt)) {
                return 1;
            } else {
                return 0;
            }
        });
    }

    /**
     * 根据店铺订单和子订单, 构建对应的订单分组
     *
     * @param byShopOrderId 按照店铺订单分组的子订单
     * @param shopOrders    店铺订单
     * @return 订单分组列表
     */
    private List<OrderGroup> makeOrderGroups(ListMultimap<Long, SkuOrder> byShopOrderId, List<ShopOrder> shopOrders) {
        List<OrderGroup> orderGroups = Lists.newArrayListWithCapacity(shopOrders.size());
        for (ShopOrder shopOrder : shopOrders) {
            Long shopTax = 0L;

            OrderGroup orderGroup = new OrderGroup();
            orderGroup.setShopOrder(shopOrder);
            boolean hasOrderNotAuth = Boolean.FALSE;
            if (byShopOrderId.containsKey(shopOrder.getId())) {
                List<SkuOrder> skuOrders = byShopOrderId.get(shopOrder.getId());
                List<OrderGroup.SkuOrderAndOperation> skuOrderAndOperations
                        = Lists.newArrayListWithCapacity(skuOrders.size());
                for (SkuOrder skuOrder : skuOrders) {
                    OrderGroup.SkuOrderAndOperation skuOrderAndOperation = new OrderGroup.SkuOrderAndOperation(skuOrder, null, null,
                            OrderFlagEnum.getFrontEndFlagCodes(skuOrder.getFlag()));
                    skuOrderAndOperations.add(skuOrderAndOperation);

                    if (skuOrder.getTax() != null) {
                        shopTax += skuOrder.getTax();
                    }
                    //订单处于已支付（待发货）状态，且订单的推送状态处于待审核状态
                    if (skuOrder.getPushStatus() != null && skuOrder.getPushStatus() == SkuOrderPushStatus.WAITING_SELLER_AUTH.value()
                            && skuOrder.getStatus() == OrderStatus.PAID.getValue()) {
                        hasOrderNotAuth = Boolean.TRUE;
                    }
                }
                orderGroup.setSkuOrderAndOperations(skuOrderAndOperations);
            }

            try {
                Optional.ofNullable(userWxCacheHolder.findByUserIdAndShopId(orderGroup.getShopOrder().getBuyerId(), orderGroup.getShopOrder().getShopId()))
                        .map(UserWx::getAvatarUrl)
                        .ifPresent(orderGroup::setBuyerAvatar);
                Optional.ofNullable(userProfileCacheHolder.getUserProfileByUserId(orderGroup.getShopOrder().getBuyerId()))
                        .map(UserProfile::getAvatar)
                        .ifPresent(orderGroup::setBuyerAvatar);
            } catch (Exception ignore) {

            }

            orderGroup.setWaitingSellerAuth(hasOrderNotAuth);

            orderGroup.setShopTax(shopTax);

            orderGroups.add(orderGroup);
            if (mongoTemplate.exists(new Query(Criteria.where("orderId").is(shopOrder.getId())), FirstOrderMark.class)) {
                orderGroup.setTag("首单");
            }
        }
        return orderGroups;
    }

    @Override
    public Response<OrderDetail> findOrderDetailById(Long shopOrderId) {
        try {
            OrderDetail orderDetail = new OrderDetail();

            //获取元数据
            ShopOrder shopOrder = shopOrderDao.findById(shopOrderId);
            List<SkuOrder> skuOrders = skuOrderDao.findByOrderId(shopOrderId);

            //计算总税费
            Long totalTax = 0L;
            for (SkuOrder skuOrder : skuOrders) {
                totalTax += skuOrder.getTax();
            }
            orderDetail.setTotalTax(totalTax);

            //返回订单信息
            setOrderInfo(orderDetail, shopOrder, skuOrders);

            //计算总优惠
            setDiscount(orderDetail);

            //查询支付单信息
            setPaymentInfo(orderDetail, shopOrderId);

            //平台营销信息
            setGlobalPromotion(orderDetail);

            //查询配送单信息
            setShipmentInfo(orderDetail, shopOrderId);

            //查询发票信息
            setInvoiceInfo(orderDetail, shopOrderId);

            //查询用户收货地址
            setReceiverInfo(orderDetail, shopOrderId);

            //查询评价信息
            setCommentInfo(orderDetail, shopOrderId, skuOrders);

            return Response.ok(orderDetail);
        } catch (Exception e) {
            log.error("fail to find order detail info by shop order id {}, cause:{}",
                    shopOrderId, Throwables.getStackTraceAsString(e));
            return Response.fail("order.detail.find.fail");
        }
    }

    @Override
    public Response<Boolean> checkOrderIfHasShip(Long orderId, OrderLevel orderLevel) {
        try {
            List<OrderShipment> orderShipments = orderShipmentDao.findByOrderIdAndOrderType(orderId, orderLevel.getValue());
            //如果根据skuOrderId查不到,则检查总单
            if (CollectionUtils.isEmpty(orderShipments) && orderLevel == OrderLevel.SKU) {
                SkuOrder skuOrder = skuOrderDao.findById(orderId);
                if (skuOrder == null) {
                    log.error("sku order where id={} not found", orderId);
                    return Response.fail("sku.order.not.found");
                }
                orderShipments = orderShipmentDao.findByOrderIdAndOrderType(skuOrder.getOrderId(), OrderLevel.SHOP.getValue());
            }
            return Response.ok(!CollectionUtils.isEmpty(orderShipments));
        } catch (Exception e) {
            log.error("fail to check order(id={},level={}) if has ship,cause:{}",
                    orderId, orderLevel, Throwables.getStackTraceAsString(e));
            return Response.fail("order.check.if.has.ship.fail");
        }
    }

    private void setDiscount(OrderDetail orderDetail) {
        int totalDiscountOfSkuOrders = 0;
        for (SkuOrder skuOrder : orderDetail.getSkuOrders()) {
            totalDiscountOfSkuOrders += MoreObjects.firstNonNull(skuOrder.getDiscount(), 0).intValue();
        }

        orderDetail.setTotalDiscountOfSkuOrders(totalDiscountOfSkuOrders);
        final ShopOrder shopOrder = orderDetail.getShopOrder();
        if (Objects.equal(shopOrder.getOutFrom(), OrderOutFrom.COMMUNITY_OPERATION.getOrderOutFormCode())) {
            //社区运营 总优惠已摊到品上了
            orderDetail.setDiscount(totalDiscountOfSkuOrders);
            return;
        }
        int discountOfShopOrder = MoreObjects.firstNonNull(shopOrder.getDiscount(), 0);
        int discountOfShipFee = MoreObjects.firstNonNull(shopOrder.getOriginShipFee(), 0) - MoreObjects.firstNonNull(shopOrder.getShipFee(), 0);
        orderDetail.setDiscount(discountOfShopOrder + totalDiscountOfSkuOrders + discountOfShipFee);
    }

    private void setGlobalPromotion(OrderDetail orderDetail) {
        Payment payment = orderDetail.getPayment();
        if (payment != null) {
            if (payment.getPromotionId() != null) {
                Promotion promotion = promotionDao.findById(payment.getPromotionId());
                orderDetail.setPromotion(promotion);
            }
        }
    }

    /**
     * 商品详情返回订单信息
     */
    private void setOrderInfo(OrderDetail orderDetail, ShopOrder shopOrder, List<SkuOrder> skuOrders) {
        if (StrUtil.isNotBlank(shopOrder.getFeeDetailJson())) {
            shopOrder.setFeeDetail(JSON.parseObject(shopOrder.getFeeDetailJson(), ShopOrderFeeDetailDTO.class));
        }
        orderDetail.setShopOrder(shopOrder);
        if (Objects.equal(shopOrder.getStatus(), OrderStatus.NOT_PAID.getValue())) {
            //计算autoCancelTime
            Date autoCancelTime =
                    new DateTime(shopOrder.getCreatedAt()).plusMinutes(autoCancelInMinutes).toDate();
            orderDetail.setAutoCancelTime(autoCancelTime);
        }
        skuOrders.forEach(skuOrder -> {
            if (StrUtil.isNotBlank(skuOrder.getDiscountDetailJson())) {
                skuOrder.setDiscountDetail(JSON.parseObject(skuOrder.getDiscountDetailJson(), ShopSkuDiscountDetailDTO.class));
            }
        });
        orderDetail.setSkuOrders(skuOrders);
    }

    /**
     * 商品详情返回支付信息
     */
    private void setPaymentInfo(OrderDetail orderDetail, Long shopOrderId) {
        List<OrderPayment> orderPayments =
                orderPaymentDao.findByOrderIdAndOrderType(shopOrderId, OrderLevel.SHOP.getValue());
        if (!CollectionUtils.isEmpty(orderPayments)) {
            List<Long> paymentIds = Lists.newArrayListWithCapacity(orderPayments.size());
            for (OrderPayment orderPayment : orderPayments) {
                paymentIds.add(orderPayment.getPaymentId());
            }
            List<Payment> payments = paymentDao.findByIds(paymentIds);
            Payment lastPayment = null;
            for (Payment payment : payments) {
                if (lastPayment == null) {
                    lastPayment = payment;
                } else {
                    if (payment.getUpdatedAt().after(lastPayment.getUpdatedAt())) {
                        lastPayment = payment;
                    }
                }
            }
            orderDetail.setPayment(lastPayment);
        }
    }

    /**
     * 商品详情返回发货信息
     */
    private void setShipmentInfo(OrderDetail orderDetail, Long shopOrderId) {
        List<OrderShipment> orderShipments = orderShipmentDao.findByOrderIdAndOrderType(shopOrderId, OrderLevel.SHOP.getValue());
        if (CollectionUtils.isEmpty(orderShipments)) {
            List<SkuOrder> skuOrders = skuOrderDao.findByOrderId(shopOrderId);
            if (CollectionUtils.isEmpty(skuOrders)) {
                log.error("skuOrder not found by shopOrderId {}", shopOrderId);
                throw new ServiceException("sku.order.not.found");
            }
            List<Long> skuOrderIds = skuOrders.stream().map(OrderBase::getId).collect(Collectors.toList());
            orderShipments = orderShipmentDao.findByOrderIdsAndOrderType(skuOrderIds, OrderLevel.SKU.getValue());
        }

        if (CollectionUtils.isEmpty(orderShipments)) {
            orderDetail.setShipType(0);
        } else {
            OrderShipment orderShipment = orderShipments.get(0);
            if (orderShipment.getOrderLevel() == OrderLevel.SHOP) {
                orderDetail.setShipType(1);
            } else {
                orderDetail.setShipType(2);
            }
        }

        //已发货
        if (!Objects.equal(orderDetail.getShipType(), 0)) {
            List<Long> shipmentIds = orderShipments.stream().map(OrderShipment::getShipmentId).collect(Collectors.toList());
            List<Shipment> shipments = shipmentDao.findByIds(shipmentIds);

            //取最新的且已确认收货的发货单
            Shipment shipment = null;
            for (Shipment s : shipments) {
                if (s.getConfirmAt() != null) {
                    shipment = s;
                }
            }

            //如果没有确认收货的发货单，则取最新的发货单
            if (shipment == null) {
                shipment = Iterables.getLast(shipments);
            }

            //计算autoConfirmTime
            Date autoConfirmTime =
                    new DateTime(shipment.getCreatedAt()).plusMinutes(autoConfirmInMinutes).toDate();
            orderDetail.setAutoConfirmTime(autoConfirmTime);
            orderDetail.setShipAt(shipment.getCreatedAt());
            orderDetail.setConfirmAt(shipment.getConfirmAt());
        }
    }

    /**
     * 商品详情返回发票信息
     */
    private void setInvoiceInfo(OrderDetail orderDetail, Long shopOrderId) {
        List<OrderInvoice> orderInvoices =
                orderInvoiceDao.findByOrderIdAndOrderType(shopOrderId, OrderLevel.SHOP.getValue());
        List<Invoice> invoices = invoiceDao.findByIds(Lists.transform(orderInvoices, OrderInvoice::getInvoiceId));
        orderDetail.setInvoices(invoices);
    }

    /**
     * 商品详情返回收货地址信息
     */
    private void setReceiverInfo(OrderDetail orderDetail, Long shopOrderId) {
        List<OrderReceiverInfo> orderReceiverInfos =
                orderReceiverInfoDao.findByOrderIdAndOrderLevel(shopOrderId, OrderLevel.SHOP);
        orderReceiverInfos.forEach(item -> item.getReceiverInfo().decrypt());
        orderDetail.setOrderReceiverInfos(orderReceiverInfos);
    }

    /**
     * 订单详情返回评价信息
     *
     * @param orderDetail
     * @param shopOrderId
     */
    private void setCommentInfo(OrderDetail orderDetail, Long shopOrderId, List<SkuOrder> skuOrders) {
        for (SkuOrder skuOrder : skuOrders) {
            List<OrderComment> orderCommentList = orderCommentDao.findByItemIdAndSkuOrderId(skuOrder.getItemId(), skuOrder.getId());
            if (!CollectionUtils.isEmpty(orderCommentList)) {
                orderDetail.setCommentAt(orderCommentList.get(0).getCreatedAt());
                break;
            }
        }
    }

    public Response<Long> sumFeeByCriteria(OrderCriteria orderCriteria) {
        try {
            return Response.ok(shopOrderDao.sumFeeByCriteria(orderCriteria));
        } catch (Exception ex) {
            log.error("{} criteria:{}", LogUtil.getClassMethodName(), orderCriteria);
            ex.printStackTrace();
            return Response.fail("fail.sum.fee");
        }
    }

    @Override
    public Either<Long> findShopOrderByRefererId(Long userId, Long shopId) {
        try {
            return Either.ok(shopOrderDao.findShopOrderByRefererId(userId, shopId));
        } catch (Exception ex) {
            log.error("{} userId:{} shopId:{}", LogUtil.getClassMethodName(), userId, shopId, ex);
            ex.printStackTrace();
            return Either.ok(0L);
        }
    }
}
