package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.model.SimpleRulerJudgeBean;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.model.*;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.PaymentWriteService;
import moonstone.order.service.PushOrderJudge;
import moonstone.order.service.Y800PayMchManager;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Stream;

@RpcProvider
@Service
@Component
@Slf4j
public class PushOrderJudgeImpl implements PushOrderJudge, SimpleRulerJudgeBean<ShopOrder> {
    @Getter
    protected Set<Long> exceptShopId = new HashSet<>();
    @Getter
    protected Set<Long> pushShopId = new HashSet<>();

    @Autowired
    private ShopWriteService shopWriteService;
    @Autowired
    private ShopCacheHolder shopCacheHolder;
    @Autowired
    private Y800PayMchManager y800PayMchManager;
    @Autowired
    private PaymentReadService paymentReadService;
    @Autowired
    private PaymentWriteService paymentWriteService;

    public boolean allow(Long shopId) {
        return shopId != null && (y800PayMchManager.findPay800MchByShopId(shopId).map(Optional::ofNullable).orElse(Optional.empty())
                .map(Y800PayMch::getPayMch).isPresent()
                || ((pushShopId.contains(shopId) && !exceptShopId.contains(shopId)) || Optional.ofNullable(shopCacheHolder.findShopById(shopId).getExtra()).map(extra -> extra.get("requirePush")).filter("true"::equals).isPresent()));
    }

    public boolean allow(ShopOrder shopOrder) {
        boolean nonNullCheck = Objects.nonNull(shopOrder)
                && Objects.nonNull(shopOrder.getStatus());
        if (!nonNullCheck) {
            log.error("{} 订单状态不正常, 未通过Null检查 不允申报", LogUtil.getClassMethodName());
            return false;
        }
        if (!allow(shopOrder.getShopId())) {
            log.info("{} 订单[Id => {}]未配置申报资质, 请检查[Shop => {}]支付信息资质与推送资质配置", LogUtil.getClassMethodName(), shopOrder.getId(), shopOrder.getShopId());
            return false;
        }
        // 必须为可推送状态的订单, 同意退款或者已经发货之后流程的订单都不进行申报
        if (Stream.of(OrderStatus.PAID
                , OrderStatus.REFUND_APPLY
                , OrderStatus.REFUND_APPLY_REJECTED
                , OrderStatus.RETURN_APPLY
                , OrderStatus.RETURN_REJECTED).map(OrderStatus::getValue)
                .noneMatch(shopOrder.getStatus()::equals)) {
            log.debug("{} 订单状态[Id => {}, Status => {}]不允许推送, 只允许正确流程订单推送", LogUtil.getClassMethodName(), shopOrder.getId(), shopOrder.getStatus());
            markOrderPaymentNotPushAble(shopOrder.getId());
            return false;
        }
        return true;
    }

    private void markOrderPaymentNotPushAble(Long orderId) {
        for (Payment shouldMarkPayment : paymentReadService.findByOrderIdAndOrderLevel(orderId, OrderLevel.SHOP).getResult()) {
            // check if payment on no need push
            if (shouldMarkPayment.getPushStatus() == PaymentPushStatus.NO_NEED_PUSH.getValue()) {
                continue;
            }
            Payment updatePaymentPushStatus = new Payment();
            updatePaymentPushStatus.setId(shouldMarkPayment.getId());
            updatePaymentPushStatus.setPushStatus(PaymentPushStatus.NO_NEED_PUSH.getValue());
            log.info("{} 订单[Id => {}, 支付单 => {}, Channel => {}, 原推送状态[{}]已不可用, 重置为无需推送",
                    LogUtil.getClassMethodName(), orderId, shouldMarkPayment.getId(),
                    shouldMarkPayment.getChannel(), shouldMarkPayment.getPushStatus());
            paymentWriteService.update(updatePaymentPushStatus);
        }
    }

    public boolean allow(SkuOrder skuOrder) {
        return skuOrder != null && allow(skuOrder.getShopId());
    }

    public boolean addShopId(Long shopId) {
        shopCacheHolder.invalidate(shopId);
        if (!Optional.ofNullable(shopCacheHolder.findShopById(shopId)).map(Shop::getExtra).map(extra -> extra.get("requirePush")).filter("true"::equals).isPresent()) {
            Shop updateShop = new Shop();
            updateShop.setId(shopId);
            Map<String, String> extra = shopCacheHolder.findShopById(shopId).getExtra();
            if (extra == null) extra = new HashMap<>();
            extra.put("requirePush", "true");
            updateShop.setExtra(extra);
            return Boolean.TRUE.equals(shopWriteService.update(updateShop).getResult());
        }
        return true;
    }

    public boolean delShopId(Long shopId) {
        shopCacheHolder.invalidate(shopId);
        Shop shop = shopCacheHolder.findShopById(shopId);
        if (Optional.ofNullable(shop.getExtra()).map(extra -> extra.get("requirePush")).filter("true"::equals).isPresent()) {
            Map<String, String> extra = shop.getExtra();
            extra.remove("requirePush");
            Shop updateShop = new Shop();
            updateShop.setId(shop.getId());
            updateShop.setExtra(extra);
            return Boolean.TRUE.equals(shopWriteService.update(updateShop).getResult());
        }
        return true;
    }

    @Profile({"dev", "test", "stag", "integration"})
    @Configuration
    class devInit {
        @PostConstruct
        public void init() {
            /// 测试店铺Id
            try {
                addShopId(1L);
                addShopId(82L);
            } catch (Exception ex) {
                log.error("{}", LogUtil.getClassMethodName("FAIL-INIT-SHOP-DECLARED"), ex);
            }
        }
    }

    @Profile("online")
    @Configuration
    class onlineInit {
        @PostConstruct
        private void init() {
            /// 测试店铺Id
            try {
                addShopId(1L);
                addShopId(27L);
                addShopId(28L);
                addShopId(29L);
                addShopId(30L);
                addShopId(31L);
                addShopId(32L);
                addShopId(33L);
                addShopId(35L);
            } catch (Exception ex) {
                log.error("{}", LogUtil.getClassMethodName("FAIL-INIT-SHOP-DECLARED"), ex);
            }
        }
    }
}
