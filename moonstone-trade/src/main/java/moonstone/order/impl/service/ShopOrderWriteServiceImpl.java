package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.impl.dao.ShopOrderDao;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.ShopOrderWriteService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ShopOrderWriteServiceImpl implements ShopOrderWriteService {

    @Resource
    private ShopOrderDao shopOrderDao;

    @Override
    public Response<Boolean> update(ShopOrder parameter) {
        try {
            if (parameter == null || parameter.getId() == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(shopOrderDao.update(parameter));
        } catch (Exception ex) {
            log.error("ShopOrderWriteServiceImpl.update error, parameter={}", JSON.toJSONString(parameter), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> batchUpdateConfirmAtInfo(List<ShopOrder> updateList) {
        try {
            if (CollectionUtils.isEmpty(updateList)) {
                return Response.fail("入参缺失");
            }

            return Response.ok(shopOrderDao.batchUpdateConfirmAtInfo(updateList) > 0);
        } catch (Exception ex) {
            log.error("ShopOrderWriteServiceImpl.batchUpdateConfirmAtInfo error ", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> deletePushError(ShopOrder shopOrder) {
        try {
            var extra = shopOrder.getExtra();
            if (CollectionUtils.isEmpty(extra) || !extra.containsKey(ShopOrderExtra.orderPushErrorMessage.name())) {
                return Response.ok(true);
            }
            extra.remove(ShopOrderExtra.orderPushErrorMessage.name());
            extra.remove(ShopOrderExtra.orderPushErrorDetailType.name());
            extra.remove(ShopOrderExtra.identityError.name());

            var update = new ShopOrder();
            update.setId(shopOrder.getId());
            update.setExtraJson(JSON.toJSONString(extra));
            return update(update);
        } catch (Exception ex) {
            log.error("ShopOrderWriteServiceImpl.deletePushError error, shopOrder={}", JSON.toJSONString(shopOrder), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public void updateOrderActualPurchaser(List<Long> orderIds, Long buyerId) {
        shopOrderDao.updateOrderActualPurchaser(orderIds, buyerId);
    }
}
