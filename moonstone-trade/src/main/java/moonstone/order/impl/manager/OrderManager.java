/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.manager;

import com.danding.encrypt.utils.AESUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.utils.JsonMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderOutFrom;
import moonstone.order.dto.PersistedOrderInfosByOrderStruct;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.impl.dao.OrderInvoiceDao;
import moonstone.order.impl.dao.OrderReceiverInfoDao;
import moonstone.order.impl.dao.ShopOrderDao;
import moonstone.order.impl.dao.SkuOrderDao;
import moonstone.order.model.*;
import moonstone.order.service.OrderRoleSnapshotWriteService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-28
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderManager {

    private final ShopOrderDao shopOrderDao;

    private final SkuOrderDao skuOrderDao;

    private final OrderReceiverInfoDao orderReceiverInfoDao;

    private final OrderInvoiceDao orderInvoiceDao;

    private OrderRoleSnapshotWriteService orderRoleSnapshotWriteService;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();


    /**
     * ## createWithStruct 持久化订单相关的实体
     * 如果该实体的ignoreSupper是false则抛出错误
     *
     * @param persistedOrderInfosByOrderStruct 带有结构信息的订单列表
     * @return 订单Id
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public List<Long> create(PersistedOrderInfosByOrderStruct persistedOrderInfosByOrderStruct) {
        List<Long> orderIdList = new ArrayList<>();
        /// 循环创建订单
        persistedOrderInfosByOrderStruct.getShopOrderAndSkuOrders().forEach(shopOrderAndSkuOrder -> {
            /// 拒绝 空子订单 订单
            if (shopOrderAndSkuOrder.getSkuOrders().isEmpty()) {
                return;
            }
            /// 1. 创建shopOrder
            ShopOrder shopOrder = shopOrderAndSkuOrder.getShopOrder();
            shopOrderDao.create(shopOrder);
            orderIdList.add(shopOrder.getId());
            /// 2. 循环创建SkuOrder
            for (SkuOrder skuOrder : shopOrderAndSkuOrder.getSkuOrders()) {
                skuOrder.setOrderId(shopOrder.getId());
                skuOrder.setShipping(false);
                skuOrder.setInventoryId(shopOrder.getInventoryId());
                skuOrderDao.create(skuOrder);
            }
            /// 3. 循环创建收货人信息
            for (int i = 0; i < shopOrderAndSkuOrder.getReceiverInfos().size(); i++) {
                OrderReceiverInfo orderReceiverInfo = shopOrderAndSkuOrder.getReceiverInfos().get(i);
                /// 根据不同的订单对应级别创建收货人信息
                if (orderReceiverInfo.getOrderLevel().equals(OrderLevel.SHOP)) {
                    orderReceiverInfo.setOrderId(shopOrder.getId());
                } else {
                    orderReceiverInfo.setOrderId(shopOrderAndSkuOrder.getSkuOrders().get(i).getId());
                }
                if (orderReceiverInfo.getReceiverInfo() != null) {
                    // 加密收件人信息
                    encryptReceiverInfo(orderReceiverInfo);
                    orderReceiverInfoDao.create(orderReceiverInfo);
                }
            }
            /// 4. 循环创建发票信息
            for (int i = 0; i < shopOrderAndSkuOrder.getOrderInvoices().size(); i++) {
                OrderInvoice orderInvoice = shopOrderAndSkuOrder.getOrderInvoices().get(i);
                /// 根据不同的订单对应级别创建发票信息
                if (orderInvoice.getOrderLevel().equals(OrderLevel.SHOP)) {
                    orderInvoice.setOrderId(shopOrder.getId());
                } else {
                    orderInvoice.setOrderId(shopOrderAndSkuOrder.getSkuOrders().get(i).getId());
                }
                if (orderInvoice.getInvoiceId() != null) {
                    orderInvoiceDao.create(orderInvoice);
                }
            }

            // 订单的角色快照信息
            createOrderRoleSnapshot(shopOrderAndSkuOrder.getOrderRoleSnapshots(), shopOrder);
        });
        return orderIdList;
    }

    private void encryptReceiverInfo(OrderReceiverInfo orderReceiverInfo) {
        ReceiverInfo receiverInfo = orderReceiverInfo.getReceiverInfo();
        receiverInfo.setReceiveUserName(AESUtils.encrypt(receiverInfo.getReceiveUserName()));
        receiverInfo.setPaperNo(AESUtils.encrypt(receiverInfo.getPaperNo()));
        receiverInfo.setPhone(AESUtils.encrypt(receiverInfo.getPhone()));
        receiverInfo.setMobile(AESUtils.encrypt(receiverInfo.getMobile()));
        receiverInfo.setDetail(AESUtils.encrypt(receiverInfo.getDetail()));
        try {
            orderReceiverInfo.setEncryptReceiverInfoJson(objectMapper.writeValueAsString(receiverInfo));
        } catch (Exception ignored) {
            // nothing to do
        }
    }


    /**
     * 创建订单的角色信息快照
     *
     * @param shopOrder
     */
    private void createOrderRoleSnapshot(List<OrderRoleSnapshot> orderRoleSnapshots, ShopOrder shopOrder) {
        if (shopOrder == null || shopOrder.getId() == null || CollectionUtils.isEmpty(orderRoleSnapshots) ||
                !OrderOutFrom.SUB_STORE.Code().equals(shopOrder.getOutFrom())) {
            return;
        }

        orderRoleSnapshots.forEach(orderRoleSnapshot -> {
            orderRoleSnapshot.setShopOrderId(shopOrder.getId());
            orderRoleSnapshot.setOrderType(OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER.getCode());
        });

        var response = orderRoleSnapshotWriteService.create(orderRoleSnapshots);
        if (response == null || !response.isSuccess() || !response.getResult()) {
            throw new RuntimeException("创建订单的角色信息快照失败！");
        }
    }

    /**
     * 更新主订单数据
     *
     * @param shopOrder 主订单
     * @return 成功
     */
    public Boolean updateShopOrder(ShopOrder shopOrder) {
        try {
            return shopOrderDao.update(shopOrder);
        } catch (Exception ex) {
            log.error("failed update shopOrder" + ex.getMessage() + ex);
            ex.printStackTrace();
            return false;
        }
    }

    /**
     * 因为店铺订单状态改变, 引起子订单状态联动改变
     *
     * @param shopOrderId   店铺订单id
     * @param currentStatus 店铺订单当前状态
     * @param newStatus     店铺订单目标状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void shopOrderStatusChanged(Long shopOrderId, Integer currentStatus, Integer newStatus) {
        boolean success = shopOrderDao.updateStatus(shopOrderId, currentStatus, newStatus);
        if (!success) {
            throw new ServiceException("shopOrder.status.update.fail");
        }
        success = skuOrderDao.updateStatusByOrderId(shopOrderId, currentStatus, newStatus);
        Set<Integer> cancelSet = Stream.of(OrderStatus.TIMEOUT_CANCEL, OrderStatus.BUYER_CANCEL, OrderStatus.SELLER_CANCEL).map(OrderStatus::getValue).collect(Collectors.toSet());
        if (success && cancelSet.contains(newStatus)) {
            //“99”表示不传参数，传null会报错
            skuOrderDao.updatePushStatusByOrderId(shopOrderId, SkuOrderPushStatus.NOT_NEED.value(), 99);
        }
        if (!success) {
            throw new ServiceException("skuOrder.status.update.fail");
        }
    }

    /**
     * 因为sku订单状态改变, 可能引起店铺订单状态改变
     *
     * @param skuOrderId      sku订单id
     * @param currentStatus   当前状态
     * @param newStatus       sku订单目标状态
     * @param shopOrderId     店铺订单id
     * @param shopOrderStatus 店铺订单的目标状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void skuOrderStatusChanged(Long skuOrderId, Integer currentStatus, Integer newStatus,
                                      Long shopOrderId, Integer shopOrderStatus) {
        log.info("更新skuOrder状态 skuOrderId {} currentStatus {} newStatus {} shopOrderId {} shopOrderStatus {}", skuOrderId, currentStatus, newStatus,shopOrderId,shopOrderStatus);
        Set<Integer> cancelSet = Stream.of(OrderStatus.TIMEOUT_CANCEL, OrderStatus.BUYER_CANCEL, OrderStatus.SELLER_CANCEL).map(OrderStatus::getValue).collect(Collectors.toSet());
        boolean success = skuOrderDao.updateStatus(skuOrderId, currentStatus, newStatus);
        if (!success) {
            throw new ServiceException("skuOrder.status.update.fail");
        }

        if (cancelSet.contains(newStatus)) {
            skuOrderDao.updatePushStatus(skuOrderId, SkuOrderPushStatus.NOT_NEED.value(), null);
        }
        success = shopOrderDao.updateStatus(shopOrderId, shopOrderStatus);

        if (!success) {
            throw new ServiceException("shopOrder.status.update.fail");
        }
    }

    /**
     * 批量标记是否有过逆向流程
     *
     * @param orderIds   (子)订单id
     * @param orderLevel 订单级别
     * @param hasRefund  是否申请逆向流程
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchMarkHasRefund(List<Long> orderIds, OrderLevel orderLevel, Boolean hasRefund) {
        switch (orderLevel) {
            case SHOP:
                shopOrderDao.batchMarkHasRefundByIds(orderIds, hasRefund);
                skuOrderDao.batchMarkHasRefundByOrderIds(orderIds, hasRefund);
                break;
            case SKU:
                skuOrderDao.batchMarkHasRefundByIds(orderIds, hasRefund);
                break;
            default:
                break;
        }
    }

    /**
     * 批量标记sku订单评价标记位
     *
     * @param skuOrderIds sku订单id
     * @param commentFlag 评价标记位
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchMarkCommented(List<Long> skuOrderIds, Integer commentFlag) {
        skuOrderDao.batchMarkCommentedByIds(skuOrderIds, commentFlag);
    }

    public boolean update(SkuOrder skuOrder) {
        return skuOrderDao.update(skuOrder);
    }

    public boolean update(ShopOrder shopOrder) {
        return shopOrderDao.update(shopOrder);
    }
}
