/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */

package moonstone.cart.impl.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Arguments;
import lombok.extern.slf4j.Slf4j;
import moonstone.cart.impl.dao.CartItemDao;
import moonstone.cart.model.CartItem;
import moonstone.cart.service.CartWriteService;
import moonstone.item.model.Sku;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Copyright (c) 2015 杭州端点网络科技有限公司
 * Date: 3/10/16
 * Time: 4:51 PM
 * Author: 2015年 <a href="mailto:<EMAIL>">张成栋</a>
 */
@Slf4j
@Service
@RpcProvider
public class CartWriteServiceImpl implements CartWriteService {
    private final CartItemDao cartItemDao;

    @Autowired
    public CartWriteServiceImpl(CartItemDao cartItemDao) {
        this.cartItemDao = cartItemDao;
    }

    /**
     * 改变永久购物车商品数量
     *
     * @param sku       商品信息
     * @param quantity  改变数量 正数为增负数为减
     * @param userId    用户id
     * @param buyerNote
     * @return 操作后购物车内该商品的数量
     */
    @Override
    public Response<Integer> changeCart(Sku sku, Integer quantity, Long userId, String buyerNote) {
        Response<Integer> result = new Response<>();
        try {
            result.setResult(doChangeCart(sku, quantity, userId,buyerNote));
        }catch (ServiceException se) {
            result.setError(se.getMessage());
        }catch (Exception e) {
            log.error("fail to change permanent cart by sku={}, quantity={}, userId={},cause:{}",
                      sku, quantity, userId, Throwables.getStackTraceAsString(e));
            result.setError("cart.change.fail");
        }
        return result;
    }

    /**
     * 批量改变购物车数量, 例如在订单预览页返回购物车修改
     *
     * @param skuAndQuantities 商品与数量
     * @param userId         登录用户
     * @return 操作后最终购物车商品数量
     */
    @Override
    public Response<Map<Long, Integer>> changeCart(Map<Sku, Integer> skuAndQuantities, Long userId) {
        try {
            if (skuAndQuantities == null || skuAndQuantities.isEmpty()) {
                return Response.ok(Collections.emptyMap());
            }

            // 逐条加入购物车, 暂不考虑性能优化
            Map<Long, Integer> finalSkuAndQuantity = Maps.newHashMap();
            for (Map.Entry<Sku, Integer> skuAndQuantity : skuAndQuantities.entrySet()) {
                finalSkuAndQuantity.put(skuAndQuantity.getKey().getId(),
                                        doChangeCart(skuAndQuantity.getKey(), skuAndQuantity.getValue(), userId, ""));
            }

            return Response.ok(finalSkuAndQuantity);
        } catch (Exception e) {
            log.error("fail batch add {} to cart for user:{}, cause:{}", skuAndQuantities, userId,
                      Throwables.getStackTraceAsString(e));
            return Response.fail("cart.change.fail");
        }
    }

    /**
     * 根据id删除一条购物车商品
     *
     * @param id 购物商品记录的id
     * @return 操作成功
     */
    @Override
    public Response<Boolean> deleteById(Long id) {
        try {
            cartItemDao.delete(id);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to delete CartItem(id={}), cause:{}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("cart.delete.fail");
        }
    }

    /**
     * 清空购物车
     *
     * @param userId 当前登录id
     * @return 是否删除成功
     */
    @Override
    public Response<Boolean> deleteCart(Long userId) {
        try {
            CartItem cartItem = new CartItem();
            cartItem.setBuyerId(userId);
            cartItemDao.deleteBy(cartItem);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to delete cart by userIdOrCookie={},cause:{}",
                      userId, Throwables.getStackTraceAsString(e));
            return Response.fail("cart.delete.fail");
        }
    }

    /**
     * 批量删除永久购物车中的商品
     *
     * @param skuIds 商品skuId组
     * @param userId 当前登录用户id
     * @return 是否删除成功
     */
    @Override
    public Response<Boolean> batchDelete(List<Long> skuIds, Long userId) {
        try {
            if (Arguments.isNullOrEmpty(skuIds)) {
                return Response.ok(Boolean.TRUE);
            }
            cartItemDao.batchDeleteBySkuIds(userId, skuIds);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to batch delete permanent by skuIds={},userId={},cause:{}",
                      skuIds, userId, Throwables.getStackTraceAsString(e));
            return Response.fail("cart.delete.fail");
        }
    }

    /**
     * 提交商品到订单
     *
     * @param skuAndQuantity 商品与数量
     * @param buyer          买家
     * @return 需求清单编号
     */
    @Override
    public Response<Long> submitCart(Map<Long, Integer> skuAndQuantity, BaseUser buyer) {
        return null;
    }

    /**
     * 更改购物车数量
     *
     * @param sku       商品id
     * @param quantity  商品数量
     * @param userId    买家id
     * @param buyerNote
     * @return 操作后购物车内数量
     */
    private Integer doChangeCart(Sku sku, Integer quantity, Long userId, String buyerNote) {
        List<CartItem> cartItems = cartItemDao.list(ImmutableMap.of("buyerId", userId, "skuId", sku.getId()));
        // 购物车没有商品,减少商品
        if (cartItems.isEmpty() && quantity <= 0) {
            return 0;
        }
        JSONObject extraMap = new JSONObject();
        boolean existNote = StrUtil.isNotBlank(buyerNote);
        if (existNote) {
            extraMap.set("buyerNote", buyerNote);
        }
        // 购物车没有商品,增加商品
        if (cartItems.isEmpty() && quantity > 0) {
            CartItem cartItem = new CartItem();
            cartItem.setBuyerId(userId);
            cartItem.setQuantity(quantity);
            cartItem.setShopId(sku.getShopId());
            cartItem.setSkuId(sku.getId());
            cartItem.setSnapshotPrice(sku.getPrice());
            cartItem.setExtraJson(extraMap.toString());
            cartItemDao.create(cartItem);
            return quantity;
        }

        // 没有变更数量
        if (quantity == 0) {
            return cartItems.get(0).getQuantity();
        }

        // 购物车有商品,更改数量
        CartItem cartItem = cartItems.get(0);
        Integer current = cartItem.getQuantity() + quantity;
        if (current <= 0) {
            cartItemDao.delete(cartItem.getId());
            return 0;
        } else {
            if (existNote) {
                cartItem.setExtraJson(extraMap.toString());
            }
            cartItem.setSnapshotPrice(sku.getPrice());
            cartItem.setQuantity(current);
            cartItemDao.update(cartItem);
            return current;
        }
    }
}
