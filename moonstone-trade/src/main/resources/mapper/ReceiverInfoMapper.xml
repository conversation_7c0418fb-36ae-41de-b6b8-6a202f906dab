<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="ReceiverInfo">

    <resultMap id="ReceiverInfoMap" type="ReceiverInfo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="receive_user_name" property="receiveUserName"/>
        <result column="phone" property="phone"/>
        <result column="mobile" property="mobile"/>
        <result column="email" property="email"/>
        <result column="is_default" property="isDefault"/>
        <result column="status" property="status"/>
        <result column="province" property="province"/>
        <result column="province_id" property="provinceId"/>
        <result column="city" property="city"/>
        <result column="city_id" property="cityId"/>
        <result column="region" property="region"/>
        <result column="region_id" property="regionId"/>
        <result column="street" property="street"/>
        <result column="street_id" property="streetId"/>
        <result column="detail" property="detail"/>
        <result column="postcode" property="postcode"/>
        <result column="extra_json" property="extraJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="paper_no" property="paperNo"/>
        <result column="paper_front" property="paperFront"/>
        <result column="paper_back" property="paperBack"/>
    </resultMap>

    <sql id="tb">
        parana_receiver_infos
    </sql>

    <sql id="cols_all">
      id, <include refid="cols_exclude_id" />
    </sql>

    <sql id="cols_exclude_id">
      user_id, receive_user_name, phone, mobile, email, is_default, status, province, province_id, city, city_id,
      region, region_id, street, street_id, detail, postcode,extra_json, created_at, updated_at, paper_no, paper_front, paper_back
    </sql>

    <sql id="vals">
        #{userId}, #{receiveUserName}, #{phone}, #{mobile}, #{email}, #{isDefault}, #{status}, #{province}, #{provinceId},
        #{city}, #{cityId}, #{region}, #{regionId}, #{street}, #{streetId}, #{detail}, #{postcode},#{extraJson}, now(), now(), #{paperNo},
        #{paperFront}, #{paperBack}
    </sql>

    <sql id="criteria">
        where 1 = 1
        <if test="id != null">AND id = #{id}</if>
        <if test="userId != null">AND user_id = #{userId}</if>
        <if test="receive_user_name">AND receive_user_name = #{receiveUserName}</if>
        <if test="phone != null">AND phone = #{phone}</if>
        <if test="mobile != null">AND mobile = #{mobile}</if>
        <if test="email != null">AND email = #{email}</if>
        <if test="isDefault != null">AND is_default = #{isDefault}</if>
        <if test="status != null">AND status = #{status}</if>
        <if test="status == null">AND status = 1 </if>
        <if test="province != null">AND province = #{province}</if>
        <if test="provinceId != null">AND province_id = #{provinceId}</if>
        <if test="city != null">AND city = #{city}</if>
        <if test="cityId != null">AND city_id = #{cityId}</if>
        <if test="region != null">AND region = #{region}</if>
        <if test="regionId != null">AND region_id = #{regionId}</if>
        <if test="street != null">AND street = #{street}</if>
        <if test="streetId != null">AND street_id = #{streetId}</if>
        <if test="detail != null">AND detail = #{detail}</if>
        <if test="postcode != null">AND postcode = #{postcode}</if>
        <if test="paperNo != null">AND paper_no = #{paperNo}</if>
        <if test="paperFront != null">AND paper_front = #{paperFront}</if>
        <if test="paperBack != null">AND paper_back = #{paperBack}</if>
    </sql>

    <insert id="create" parameterType="ReceiverInfo" keyProperty="id" useGeneratedKeys="true">
      INSERT INTO
      <include refid="tb" />
      (<include refid="cols_exclude_id" />)
      VALUES
      (<include refid="vals" />)
    </insert>

    <update id="update" parameterType="ReceiverInfo">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="receiveUserName">receive_user_name = #{receiveUserName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="email != null">email = #{email},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="status != null">status = #{status},</if>
            <if test="province != null">province = #{province},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="city != null">city = #{city},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="region != null">region = #{region},</if>
            <if test="regionId != null">region_id = #{regionId},</if>
            <if test="street != null">street = #{street},</if>
            <if test="streetId != null">street_id = #{streetId},</if>
            <if test="detail != null">detail=#{detail},</if>
            <if test="postcode != null">postcode=#{postcode},</if>
            <if test="extraJson != null">extra_json=#{extraJson},</if>
            <if test="paperNo != null">paper_no = #{paperNo},</if>
            <if test="paperFront != null">paper_front = #{paperFront},</if>
            <if test="paperBack != null">paper_back = #{paperBack},</if>
            <if test="street == null">street = null,</if>
            <if test="streetId == null">street_id = null,</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteByIdAndUserId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = -1, updated_at = now()
        WHERE id = #{id} AND user_id = #{userId}
    </update>

    <!--  通过ID查询收货地址  -->
    <select id="findById" parameterType="long" resultMap="ReceiverInfoMap">
        SELECT id, <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            id = #{id}
        </where>
    </select>

    <!--  通过用户ID查询收货地址  -->
    <select id="findByUserId" parameterType="long" resultMap="ReceiverInfoMap">
        SELECT id, <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            user_id = #{userId} AND status = 1
        </where>
        order by is_default desc
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM  <include refid="tb"/>
        <include refid="criteria"/>
    </select>

    <select id="paging" parameterType="map" resultMap="ReceiverInfoMap">
        SELECT id, <include refid="cols_exclude_id"/>
        FROM <include refid="tb"/>
        <include refid="criteria"/>
        <if test="orderByCriteria != null">
            ${orderByCriteria}
        </if>
        LIMIT #{offset}, #{limit}
    </select>

    <update id="makeDefault" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET is_default = TRUE, updated_at = now()
        WHERE id = #{id} AND user_id = #{userId}
    </update>

    <update id="batchDefault" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET is_default = FALSE , updated_at = now()
        WHERE user_id =#{userId}
    </update>

    <update id="updatePaper" parameterType="ReceiverInfo">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="receiveUserName">receive_user_name = #{receiveUserName},</if>
            <if test="paperNo != null">paper_no = #{paperNo},</if>
            <if test="paperFront != null">paper_front = #{paperFront},</if>
            <if test="paperBack != null">paper_back = #{paperBack},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="findDefaultByUserId" parameterType="long" resultMap="ReceiverInfoMap">
        SELECT id, <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            user_id = #{userId} AND status = 1 and is_default = 1 limit 1
        </where>
    </select>

    <update id="updateAreaId" parameterType="ReceiverInfo">
        UPDATE
        <include refid="tb"/>
        <set>
            province_id = #{provinceId},
            city_id = #{cityId},
            region_id = #{regionId},
            street = null,
            street_id = null,
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="maxId" resultType="Long">
        SELECT max(id)
        FROM  <include refid="tb"/>
    </select>

    <update id="makeDefaultById" parameterType="map">
        UPDATE <include refid="tb"/>
        SET is_default = #{isDefault}, updated_at = now()
        WHERE id = #{id} AND  user_id = #{userId}
    </update>

    <!--  通过用户ID查询收货地址  -->
    <select id="findByUserIdLimitOne" parameterType="long" resultMap="ReceiverInfoMap">
        SELECT id, <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            user_id = #{userId} and status=1 order by is_default desc limit 1
        </where>
    </select>

    <update id="batchUpdateReceiverInfos" parameterType="list">
        <foreach collection="list" item="receiverInfo" separator=";">
            UPDATE
            <include refid="tb"/>
            <set>
                <if test="receiverInfo.receiveUserName != null and '' != receiverInfo.receiveUserName ">
                    receive_user_name = #{receiverInfo.receiveUserName},
                </if>
                <if test="receiverInfo.phone != null and '' != receiverInfo.phone">phone = #{receiverInfo.phone},</if>
                <if test="receiverInfo.mobile != null and '' != receiverInfo.mobile">mobile = #{receiverInfo.mobile},
                </if>
                <if test="receiverInfo.email != null and '' != receiverInfo.email">email = #{receiverInfo.email},</if>
                <if test="receiverInfo.isDefault != null">is_default = #{receiverInfo.isDefault},</if>
                <if test="receiverInfo.status != null">status = #{receiverInfo.status},</if>
                <if test="receiverInfo.province != null and '' != receiverInfo.province">province =
                    #{receiverInfo.province},
                </if>
                <if test="receiverInfo.provinceId != null">province_id = #{receiverInfo.provinceId},</if>
                <if test="receiverInfo.city != null and '' != receiverInfo.city">city = #{receiverInfo.city},</if>
                <if test="receiverInfo.cityId != null">city_id = #{receiverInfo.cityId},</if>
                <if test="receiverInfo.region != null and '' != receiverInfo.region">region = #{receiverInfo.region},
                </if>
                <if test="receiverInfo.regionId != null">region_id = #{receiverInfo.regionId},</if>
                <if test="receiverInfo.street != null and '' != receiverInfo.street">street = #{receiverInfo.street},
                </if>
                <if test="receiverInfo.streetId != null ">street_id = #{receiverInfo.streetId},</if>
                <if test="receiverInfo.detail != null and '' != receiverInfo.detail">detail = #{receiverInfo.detail},
                </if>
                <if test="receiverInfo.postcode != null and '' != receiverInfo.postcode">postcode =
                    #{receiverInfo.postcode},
                </if>
                <if test="receiverInfo.extraJson != null and '' != receiverInfo.extraJson">extra_json =
                    #{receiverInfo.extraJson},
                </if>
                <if test="receiverInfo.paperNo != null and '' != receiverInfo.paperNo">paper_no =
                    #{receiverInfo.paperNo},
                </if>
                <if test="receiverInfo.paperFront != null and '' != receiverInfo.paperFront">paper_front =
                    #{receiverInfo.paperFront},
                </if>
                <if test="receiverInfo.paperBack != null and '' != receiverInfo.paperBack">paper_back =
                    #{receiverInfo.paperBack},
                </if>
                updated_at = now()
            </set>
            WHERE id = #{receiverInfo.id}
        </foreach>
    </update>

</mapper>