<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="SkuOrder">

    <resultMap id="SkuOrderMap" type="SkuOrder">
        <id column="id" property="id"/>
        <result column="sku_image_id" property="skuImageId"/>
        <result column="sku_id" property="skuId"/>
        <result column="sku_version" property="skuVersion"/>
        <result column="quantity" property="quantity"/>
        <result column="fee" property="fee"/>
        <result column="status" property="status"/>
        <result column="is_bonded" property="isBonded"/>
        <result column="tax" property="tax"/>
        <result column="order_id" property="orderId"/>
        <result column="gather_order_id" property="gatherOrderId"/>
        <result column="buyer_id" property="buyerId"/>
        <result column="out_id" property="outId"/>
        <result column="buyer_name" property="buyerName_"/>
        <result column="out_buyer_id" property="outBuyerId"/>
        <result column="item_id" property="itemId"/>
        <result column="item_name" property="itemName"/>
        <result column="is_third_party_item" property="isThirdPartyItem"/>
        <result column="sku_image" property="skuImage"/>
        <result column="attrs_json" property="attrsJson"/>
        <result column="shop_id" property="shopId"/>
        <result column="shop_name" property="shopName"/>
        <result column="out_shop_id" property="outShopId"/>
        <result column="out_from" property="outFrom"/>
        <result column="company_id" property="companyId"/>
        <result column="outer_sku_id" property="outerSkuId"/>
        <result column="push_status" property="pushStatus"/>
        <result column="push_error_msg" property="pushErrorMsg"/>
        <result column="push_time" property="pushTime"/>
        <result column="sku_attributes" property="skuAttributes"/>
        <result column="channel" property="channel"/>
        <result column="pay_type" property="payType"/>
        <result column="shipment_type" property="shipmentType"/>
        <result column="origin_fee" property="originFee"/>
        <result column="after_discount_fee" property="afterDiscountFee"/>
        <result column="discount" property="discount"/>
        <result column="ship_fee" property="shipFee"/>
        <result column="ship_fee_discount" property="shipFeeDiscount"/>
        <result column="integral" property="integral"/>
        <result column="balance" property="balance"/>
        <result column="promotion_id" property="promotionId"/>
        <result column="item_snapshot_id" property="itemSnapshotId"/>
        <result column="has_refund" property="hasRefund"/>
        <result column="invoiced" property="invoiced"/>
        <result column="commented" property="commented"/>
        <result column="has_apply_after_sale" property="hasApplyAfterSale"/>
        <result column="commission_rate" property="commissionRate"/>
        <result column="distribution_rate" property="distributionRate"/>
        <result column="diff_fee" property="diffFee"/>
        <result column="extra_json" property="extraJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="shipment_at" property="shipmentAt"/>
        <result column="depot_name" property="depotName"/>
        <result column="depot_code" property="depotCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="fee_id" property="feeId"/>
        <result column="profit_id" property="profitId"/>
        <result column="extra_id" property="extraId"/>
        <result column="depot_id" property="depotId"/>
        <result column="out_from_type" property="outFromType"/>
        <result column="flag" property="flag"/>
        <result column="category_name_snapshot" property="categoryNameSnapshot"/>
        <result column="shipping_warehouse_type" property="shippingWarehouseType"/>
        <result column="shipping" property="shipping"/>
        <result column="discount_detail_json" property="discountDetailJson"/>
    </resultMap>
    <sql id="tb">
        parana_sku_orders
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `sku_id`,`sku_version`,`sku_image_id`,`quantity`,`fee`,`status`,`is_bonded`, `tax`,
        `order_id`,`buyer_id`,`out_id`,`buyer_name`,`out_buyer_id`,`item_id`,
        `item_name`,`is_third_party_item`,`sku_image`,`shop_id`,`shop_name`,`out_shop_id`,`out_from`,`company_id`,`outer_sku_id`
        ,`depot_name`, `supplier_name`,`push_status`,`push_error_msg`,`push_time`,
        `sku_attributes`,`channel`,`pay_type`,`shipment_type`,`shipment_at`,`origin_fee`,`after_discount_fee`,`discount`,
        `ship_fee`,`ship_fee_discount`,`integral`,`balance`,`promotion_id`,`item_snapshot_id`,`has_refund`,`invoiced`,
        `commented`, `has_apply_after_sale` , `commission_rate`,`distribution_rate`, `diff_fee`
        ,`extra_json`,`tags_json`,`created_at`,`updated_at`,
        `fee_id`, `profit_id`, `extra_id`, `depot_id`, `out_from_type`, `depot_code`, `flag`, `category_name_snapshot`, `shipping_warehouse_type`,shipping,discount_detail_json
        , `inventory_id`
    </sql>

    <sql id="vals">
        #{skuId},#{skuVersion},#{skuImageId},#{quantity},#{fee},#{status},#{isBonded},#{tax},#{orderId},#{buyerId},#{outId},#{buyerName_},#{outBuyerId},#{itemId},
        #{itemName},#{isThirdPartyItem},#{skuImage},#{shopId},#{shopName},#{outShopId},#{outFrom},#{companyId},#{outerSkuId}
        ,#{depotName}, #{supplierName},#{pushStatus},#{pushErrorMsg},#{pushTime},
        #{skuAttributes},#{channel},#{payType},#{shipmentType},#{shipmentAt},#{originFee},#{afterDiscountFee},#{discount},
        #{shipFee},#{shipFeeDiscount},#{integral},#{balance},#{promotionId},#{itemSnapshotId},#{hasRefund}, #{invoiced},
        #{commented},#{hasApplyAfterSale},#{commissionRate},#{distributionRate},
        #{diffFee},#{extraJson},#{tagsJson},now(),now(),
        #{feeId}, #{profitId}, #{extraId}, #{depotId}, #{outFromType}, #{depotCode}, #{flag}, #{categoryNameSnapshot}, #{shippingWarehouseType},#{shipping},#{discountDetailJson}
        , #{inventoryId}
    </sql>

    <insert id="create" parameterType="SkuOrder" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>
<select id="findBuyerAndSkuInfo" parameterType="map" resultMap="SkuOrderMap">
    SELECT
    id, order_id, buyer_id, shop_id, item_id, created_at
    FROM
    <include refid="tb"/>
    <where>
        id > #{afterId}
        AND
        created_at > #{afterTime}
        AND
        `status` > 0
        order by id asc
    </where>
</select>

    <select id="findOrderIdById" parameterType="map" resultType="long">
        SELECT order_id from
        <include refid="tb"/>
        <where>id = #{id}</where>
    </select>
    <select id="findSkuOrderBy" parameterType="map" resultMap="SkuOrderMap">
        SELECT
        id, shop_id, sku_id, sku_image_id, item_id, quantity, order_id, buyer_id, company_id, is_bonded,
        push_status, push_time, push_error_msg, fee_id, profit_id, extra_id, depot_id, is_third_party_item, outer_sku_id,
        promotion_id, item_snapshot_id, has_refund, invoiced, created_at, updated_at, out_from_type, `status`
        ,item_name, flag
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
            <if test="orderBy == null">
                order by `order_id` desc
            </if>
            limit #{offset}, #{limit}
        </where>
    </select>

    <select id="findOuterSkuInfo" parameterType="map" resultMap="SkuOrderMap">
      SELECT
      depot_code, outer_sku_id
      FROM
      <include refid="tb"/>
      <where>
        order_id = #{orderId}
      </where>
    </select>

    <select id="findById" parameterType="Long" resultMap="SkuOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findItemIdAndShopByShopOrderId" parameterType="map" resultMap="SkuOrderMap">
        SELECT
        item_id, shop_id
        FROM
        <include refid="tb"/>
        WHERE order_id = #{orderId}
    </select>

    <select id="findByIds" parameterType="list" resultMap="SkuOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <update id="update" parameterType="SkuOrder">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now()
            <if test="skuVersion != null">, `sku_version` = #{skuVersion}</if>
            <if test="profitId != null">, `profit_id` = #{profitId}</if>
            <if test="depotId != null">, `depot_id` = #{depotId}</if>
            <if test="depotCode != null">, `depot_code` = #{depotCode}</if>
            <if test="feeId != null">, `fee_id` = #{feeId}</if>
            <if test="extraId != null">, `extra_id` = #{extraId}</if>
            <if test="outFromType != null">, `out_from_type` = #{outFromType}</if>
            <if test="skuImageId != null">, `sku_image_id` = #{skuImageId}</if>
            <if test="quantity != null">,`quantity` = #{quantity}</if>
            <if test="fee != null">,`fee` = #{fee}</if>
            <if test="status!=null">, `status`=#{status}</if>
            <if test="isBonded!=null">, `is_bonded`=#{isBonded}</if>
            <if test="tax!=null">, `tax`=#{tax}</if>
            <if test="outId != null">,`out_id` = #{outId}</if>
            <if test="outBuyerId != null">,`out_buyer_id` = #{outBuyerId}</if>
            <if test="gatherOrderId != null">gather_order_id = #{gatherOrderId},</if>
            <if test="outShopId != null">,`out_shop_id` = #{outShopId}</if>
            <if test="outFrom != null">,`out_from` = #{outFrom}</if>
            <if test="companyId != null">,`company_id` = #{companyId}</if>
            <if test="outerSkuId != null">,`outer_sku_id` = #{outerSkuId}</if>
            <if test="pushStatus!=null">,`push_status`=#{pushStatus}</if>
            <if test="pushErrorMsg!=null">,`push_error_msg`=#{pushErrorMsg}</if>
            <if test="pushTime!=null">,`push_time`=#{pushTime}</if>
            <if test="channel != null">,`channel` = #{channel}</if>
            <if test="payType != null">,`pay_type` = #{payType}</if>
            <if test="shipmentType != null">,`shipment_type` = #{shipmentType}</if>
            <if test="originFee != null">,`origin_fee` = #{originFee}</if>
            <if test="afterDiscountFee != null">,`after_discount_fee` = #{afterDiscountFee}</if>
            <if test="discount != null">,`discount` = #{discount}</if>
            <if test="shipFee != null">,`ship_fee` = #{shipFee}</if>
            <if test="shipFeeDiscount != null">,`ship_fee_discount` = #{shipFeeDiscount}</if>
            <if test="integral != null">,`integral` = #{integral}</if>
            <if test="balance != null">,`balance` = #{balance}</if>
            <if test="itemSnapshotId != null">,`item_snapshot_id` = #{itemSnapshotId}</if>
            <if test="hasRefund != null">,`has_refund` = #{hasRefund}</if>
            <if test="commented != null">, `commented` = #{commented}</if>
            <if test="invoiced != null">,`invoiced` = #{invoiced}</if>
            <if test="commented != null">, `commented` = #{commented}</if>
            <if test="extraJson != null">,`extra_json` = #{extraJson}</if>
            <if test="tagsJson != null">,`tags_json` = #{tagsJson}</if>
            <if test="hasApplyAfterSale != null">, `has_apply_after_sale` = #{hasApplyAfterSale}</if>
            <if test="commissionRate != null">, `commission_rate` = #{commissionRate}</if>
            <if test="distributionRate != null">, `distribution_rate` = #{distributionRate}</if>
            <if test="diffFee != null">, `diff_fee` = #{diffFee}</if>
            <if test="shipmentAt != null">, `shipment_at`=#{shipmentAt}</if>
            <if test="depotName != null">, `depot_name`=#{depotName}</if>
            <if test="supplierName != null">, `supplier_name`=#{supplierName}</if>
            <if test="flag != null">, `flag`=#{flag}</if>
            <if test="shippingWarehouseType != null">, `shipping_warehouse_type`=#{shippingWarehouseType}</if>
            <if test="shipping != null">, `shipping`=#{shipping}</if>
        </set>
        WHERE id=#{id}
    </update>


    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <sql id="criteria">
        <if test="status != null">AND status IN
            <foreach collection="status" open="(" separator="," close=")" item="st">
                #{st}
            </foreach>
        </if>
        <if test="id!= null">AND id = #{id}</if>
        <if test="status == null">AND status != -14</if>
        <if test="skuVersion != null">AND sku_version = #{skuVersion}</if>
        <if test="orderId != null">AND order_id = #{orderId}</if>
        <if test="orderIds != null">AND order_id IN
            <foreach collection="orderIds" open="(" separator="," close=")" item="oId">
                #{oId}
            </foreach>
        </if>

        <if test="buyerId != null">AND buyer_id = #{buyerId}</if>
        <if test="shopId != null">AND shop_id = #{shopId}</if>
        <if test="companyId != null">AND company_id = #{companyId}</if>
        <if test="outId != null">AND out_id = #{outId}</if>
        <if test="outBuyerId != null">AND out_buyer_id = #{outBuyerId}</if>
        <if test="outShopId != null">AND `out_shop_id` = #{outShopId}</if>
        <if test="outFrom != null">AND `out_from` = #{outFrom}</if>
        <if test="shopName != null">AND `shop_name` = #{shopName}</if>
        <if test="weShopId != null and weShopUserId != null">AND (`out_shop_id` = #{weShopId} OR `buyer_id` =
            #{weShopUserId})
        </if>
        <if test="hasRefund != null">AND has_refund = #{hasRefund}</if>
        <if test="invoiced != null">AND invoiced = #{invoiced}</if>
        <if test="isThirdPartyItem != null">and is_third_party_item = #{isThirdPartyItem}</if>
        <if test="pushStatus != null">AND push_status = #{pushStatus}</if>
        <if test="startAt != null">AND <![CDATA[created_at >= #{startAt}]]> </if>
        <if test="endAt != null">AND <![CDATA[created_at <= #{endAt}]]> </if>
        <if test="shipmentAt != null">AND `shipment_at` = #{shipmentAt}</if>
        <if test="shipmentStartAt != null">AND (<![CDATA[shipment_at >= #{shipmentStartAt}]]>)</if>
        <if test="shipmentEndAt != null">AND ( <![CDATA[shipment_at <= #{shipmentEndAt}]]>)</if>
        <if test="depotName != null">and `depot_name`=#{depotName}</if>
        <if test="supplierName != null">and `supplier_name`=#{supplierName}</if>
        <if test="shippingWarehouseType != null">and `shipping_warehouse_type`=#{shippingWarehouseType}</if>
    </sql>

    <select id="paging" parameterType="map" resultMap="SkuOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <if test="orderBy == null">
            order by `id` desc
        </if>
        <if test="orderBy != null">
            <foreach collection="orderBy" item="seq" open="order by" separator=",">
                #{seq}
            </foreach>
        </if>
        <if test="offset!=null">
            limit #{offset} , #{limit}
        </if>
    </select>

    <select id="count" parameterType="map" resultType="long">
        select count(1) from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="listSkuOrdersBy" parameterType="map" resultMap="SkuOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="countShopOrder" parameterType="map" resultType="long">
        select count(1) from(
        select count(1) from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        group by order_id
        ) a
    </select>

    <select id="findByOrderId" parameterType="long" resultMap="SkuOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where order_id=#{orderId}
    </select>

    <select id="findByOrderIdAndSkuId" parameterType="map" resultMap="SkuOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where order_id=#{orderId} and sku_id=#{skuId} LIMIT 1
    </select>

    <select id="findByOrderIdAndOuterSkuId" parameterType="map" resultMap="SkuOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where order_id=#{orderId} and outer_sku_id=#{outerSkuId} LIMIT 1
    </select>

    <select id="findByOrderIdWithStatus" parameterType="map" resultMap="SkuOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where order_id=#{orderId} and status=#{currentStatus}
    </select>

    <select id="findByOrderIds" parameterType="list" resultMap="SkuOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where order_id in
        <foreach item="orderId" collection="list" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

    <select id="findByOrderIdWithLimit" parameterType="map" resultMap="SkuOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where order_id =#{orderId} limit #{limit}
    </select>

    <select id="findPushStatusWithStatus" parameterType="map" resultMap="SkuOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where push_status = #{pushStatus} and status = #{currentStatus}
        ORDER BY updated_at
        LIMIT 1
    </select>

    <update id="batchInvoiced" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET updated_at=now(), invoiced=1
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET updated_at=now(), status=#{newStatus}
        WHERE id=#{id}
        <if test="currentStatus!=null">
            and `status`=#{currentStatus}
        </if>
    </update>

    <update id="updatePushResult" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at=now(), push_status=#{pushStatus}, push_time=now()
            <if test="pushErrorMsg != null">, push_error_msg=#{pushErrorMsg}</if>
        </set>
        WHERE id=#{id}
        <if test="currentPushStatus!=null">
            and `push_status`=#{currentPushStatus}
        </if>
    </update>

    <update id="updatePushStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET updated_at=now(), push_status=#{pushStatus}
        WHERE id=#{id}
        <if test="currentStatus!=null">
            and `status`=#{currentStatus}
        </if>
    </update>

    <update id="updatePushStatusBySkuOrderIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET updated_at=now(), push_status=#{pushStatus}
        WHERE id in
        <foreach collection="ids" item="subId" open="(" close=")" separator=",">
            #{subId}
        </foreach>
        <if test="currentStatus!=null">
            and `status`=#{currentStatus}
        </if>
    </update>

    <update id="updatePushStatusByOrderId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET updated_at=now(), push_status=#{targetPushStatus}
        WHERE order_id=#{orderId}
        <if test="currentPushStatus!=null and currentPushStatus!=99">
            and `push_status`=#{currentPushStatus}
        </if>
    </update>

    <update id="updateStatusByOrderId" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET updated_at=now(), status=#{newStatus}
        WHERE order_id=#{orderId} and status=#{currentStatus}
    </update>

    <update id="updateShopInfoIdByOrderId" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET updated_at=now(), shop_id=#{shopId},shop_name =#{shopName}
        WHERE order_id=#{orderId}
    </update>

    <update id="updateExtraJson" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET extra_json =#{extraJson}, `updated_at`=now()
        WHERE id = #{id}
    </update>

    <update id="updateExtraJsonByOrderId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET extra_json =#{extraJson}, `updated_at`=now()
        WHERE order_id = #{shopOrderId}
    </update>

    <update id="updateTagsJson" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET tags_json =#{tagsJson}, `updated_at`=now()
        WHERE id = #{id}
    </update>

    <update id="updateTagsJsonByOrderId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET tags_json =#{tagsJson}, `updated_at`=now()
        WHERE order_id = #{shopOrderId}
    </update>

    <update id="updateShippingWarehouseTypeByOrderId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `shipping_warehouse_type` = #{shippingWarehouseType}, `updated_at`=now()
        <if test="shipping != null">, `shipping`=#{shipping}</if>
        where order_id=#{orderId}
    </update>

    <update id="batchMarkHasRefundByIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `has_refund`=#{hasRefund} , `updated_at`=now()
        WHERE `id` IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <update id="batchMarkHasRefundByOrderIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `has_refund`=#{hasRefund} , `updated_at`=now()
        WHERE `order_id` IN
        <foreach item="id" collection="orderIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <update id="batchMarkCommentedByIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET `commented` = #{commented}, `updated_at`=now()
        WHERE `id` IN
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </update>

    <resultMap id="SkuOrderCountResult" type="moonstone.order.dto.SkuOrderCountResult">
        <result column="sku_id" property="skuId"/>
        <result column="num" property="num"/>
    </resultMap>

    <select id="selectCountOrderNumBySkuOrderAndCriteria" parameterType="map" resultMap="SkuOrderCountResult">
        SELECT
        `sku_id`,count(`order_id`) as num
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        group by `sku_id`
        order by `sku_id`
    </select>
    <select id="findNoItemSnapShotId" parameterType="map" resultMap="SkuOrderMap">
        SELECT
        `id`, `item_id`
        FROM
        <include refid="tb"/>
        <where>
            item_snapshot_id is null
            order by id desc
            limit #{count}
        </where>
    </select>

    <update id="removeGatherId" parameterType="map">
        update
        <include refid="tb"/>
        <set>
            gather_order_id = null
        </set>
        <where>
            gather_order_id = #{gatherOrderId}
        </where>
    </update>
    <select id="selectOne" parameterType="map" resultMap="SkuOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        limit 1;
    </select>

    <select id="selectList" parameterType="map" resultMap="SkuOrderMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>
</mapper>
