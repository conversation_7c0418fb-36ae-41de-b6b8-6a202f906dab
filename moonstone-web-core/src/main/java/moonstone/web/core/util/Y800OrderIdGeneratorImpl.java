package moonstone.web.core.util;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.stock.impl.service.DepotCustomManager;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import moonstone.web.core.mirror.model.MirrorSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class Y800OrderIdGeneratorImpl implements moonstone.order.api.Y800OrderIdGenerator {
    @Autowired
    private OutSystemIdProviderImpl provider;
    @Resource
    private DepotCustomManager depotCustomManager;
    @Resource
    private OrderWriteService orderWriteService;
    @Resource
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private SourceShopQuerySlice sourceShopQuerySlice;

    @Override
    public Either<String> getDeclareId(Long shopOrderId) {
        try {
            //获取订单数据 进行获取关区与订单数据 进行查询订单申报号
            ShopOrder shopOrder = shopOrderReadService.findById(shopOrderId).getResult();
            String no = Optional.ofNullable(shopOrder)
                    .map(this::getOrCreateDeclareId)
                    .orElseThrow(() -> new JsonResponseException(new Translate("获取申报单号失败，请检查订单与库存").toString()));
            return Either.ok(no);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} shopOrderId:{}", LogUtil.getClassMethodName("获取申报单号"), shopOrderId);
            return Either.error(ex);
        }
    }

    private String getOrCreateDeclareId(ShopOrder shopOrder) {
        boolean notSeqUpdateResult = notSeqUpdate(shopOrder);

        if (!ObjectUtils.isEmpty(shopOrder.getDeclaredId()) && notSeqUpdateResult) {
            log.info("shopOrderId={}, 已有申报单号且非seq更新，直接返回", shopOrder.getId());
            return shopOrder.getDeclaredId();
        }
        // 试图 获取关区
        String custom = shopOrder.getDepotCustomName();
        String oldDepotCustomName = shopOrder.getDepotCustomName();
        if (!notSeqUpdateResult) {
            custom = null;
            shopOrder.setDepotCustomName(null);
            log.info("shopOrderId={}, 获取申报单号，当前为seq更新，DepotCustomName设置为null", shopOrder.getId());
        }
        if (ObjectUtils.isEmpty(custom)) {
            try {
                custom = depotCustomManager.getCustomCodeByShopOrder(shopOrder);
                log.info("{} 跨境订单口岸信息 {}", shopOrder.getId(), custom);

                shopOrder.setDepotCustomName(custom);
            } catch (Exception ex) {
                log.error("{} 获取口岸信息失败 {}", LogUtil.getClassMethodName(), shopOrder.getId(), ex);
                shopOrder.setDepotCustomName(DepotCustomManager.originCustomConfig.SHANGHAI.getCode());
            }
        }
        // 使用关区与订单去获取申报单号
        String declareId = getDeclareId(shopOrder, custom, oldDepotCustomName);
        return declareId;
    }

    /**
     * 获取shopOrder的 Y800 推送Id
     *
     * @param shopOrder 数据库中已经实体化的订单
     * @return declare id
     */
    @Override
    public String getDeclareId(ShopOrder shopOrder) {
        refreshShopOrder(shopOrder);

        if (!ObjectUtils.isEmpty(shopOrder.getDeclaredId()) && notSeqUpdate(shopOrder)) {
            log.info("shopOrderId={}, 已有申报单号且非seq更新，直接返回", shopOrder.getId());
            return shopOrder.getDeclaredId();
        }
        // 试图 获取关区
        String custom = shopOrder.getDepotCustomName();
        String oldDepotCustomName = shopOrder.getDepotCustomName();
        if (!notSeqUpdate(shopOrder)) {
            custom = null;
            shopOrder.setDepotCustomName(null);
            log.info("shopOrderId={}, 获取申报单号，当前为seq更新，DepotCustomName设置为null", shopOrder.getId());
        }
        // 如果Custom 为 Null 则重置获取单号
        if (ObjectUtils.isEmpty(custom)) {
            // WeShop 供销来源则特殊获取单号
            if (OrderOutFrom.WE_SHOP.Code().equals(shopOrder.getOutFrom()) && sourceShopQuerySlice.queryProjectIdByShopIdAndSource(shopOrder.getShopId(), MirrorSource.GongXiao.name()).isSuccess()) {
                return getDeclareId(shopOrder, DepotCustomManager.originCustomConfig.HANGZHOU.getCode(), oldDepotCustomName);
            }
            // 获取订单对应的子订单的商品仓库号
            try {
                custom = depotCustomManager.getCustomCodeByOrder(shopOrder);
                log.info("shopOrderId={}, 获取订单对应的子订单的商品仓库号={}", shopOrder.getId(), custom);
                shopOrder.setDepotCustomName(custom);
            } catch (Exception ex) {
                log.error("{} get custom failed shopOrderId:{}", LogUtil.getClassMethodName(), shopOrder.getId(), ex);
                shopOrder.setDepotCustomName(DepotCustomManager.originCustomConfig.SHANGHAI.getCode());
            }
        }
        if (custom == null) {
            log.warn("{} ShopOrder[Id => {}] Still use DOMESTIC CUSTOM", LogUtil.getClassMethodName(), shopOrder.getId());
            custom = DepotCustomManager.originCustomConfig.DOMESTIC.getCode();
        }
        // 使用关区与订单去获取申报单号
        return getDeclareId(shopOrder, custom, oldDepotCustomName);
    }

    private String getDeclareId(ShopOrder shopOrder, String customs, String oldCustoms) {
        log.info("shopOrderId {} customs {} 构造申报单号开始...", shopOrder.getId(), customs);
        // 如果已经具有申报号 并且没有修改批次号 则直接使用已有的申报号
        if (Objects.equals(customs, shopOrder.getDepotCustomName())
                && !ObjectUtils.isEmpty(shopOrder.getDeclaredId())
                && notSeqUpdate(shopOrder)) {
            return shopOrder.getDeclaredId();
        }
        // 如果关区为空 申报个屁, 返回空
        if (ObjectUtils.isEmpty(customs)) {
            log.error("shopOrderId {} 关区为空，无法申报", shopOrder.getId());
            return null;
        }
        // 获取批次号, 默认为0
        int seq = 0;
        if (shopOrder.getExtra() != null && shopOrder.getExtra().containsKey(KEY.seq.name())) {
            Map<String, String> extra = shopOrder.getExtra();
            try {
                seq = Integer.parseInt(extra.get(KEY.seq.name()));
            } catch (Exception ex) {
                log.error("{} seq Parse error {} ex:{}", LogUtil.getClassMethodName("SEQ_PARSE"), extra.get(KEY.seq.name()), ex);
                throw ex;
            }
        }
        // 根据关区获取 申报号类型
        OutSystemIdProviderImpl.type type = customs
                .startsWith(DepotCustomManager.originCustomConfig.SHANGHAI.getCode())
                ? OutSystemIdProviderImpl.type.ShangHaiPushSystem : OutSystemIdProviderImpl.type.CustomPushSystem;
        // 获取申报号
        String declareId = provider.getId(shopOrder.getId(), seq, shopOrder.getCreatedAt(), type);
        // 申报单号不一致时更新数据库
        if (!declareId.equals(shopOrder.getDeclaredId())) {
            ShopOrder update = new ShopOrder();
            update.setId(shopOrder.getId());
            update.setDeclaredId(declareId);
            update.setDepotCustomName(customs);
            Response<Boolean> rUpdate = orderWriteService.updateDeclaredIdAndCustoms(update.getId(), update.getDeclaredId(), update.getDepotCustomName());
            if (!rUpdate.isSuccess()) {
                log.error("{} orderId {}", LogUtil.getClassMethodName("更新数据失败"), update.getId());
            }
            log.info("shopOrderId {},原declareId {}, 原depotCustomName {}, 更新declareId {}, depotCustomName {}成功", shopOrder.getId(),
                    shopOrder.getDeclaredId(), oldCustoms, declareId, customs);
        } else if (ObjectUtils.isEmpty(shopOrder.getDepotCustomName())) {
            ShopOrder update = new ShopOrder();
            update.setId(shopOrder.getId());
            update.setDepotCustomName(customs);
            Response<Boolean> rUpdate = orderWriteService.updateDeclaredIdAndCustoms(update.getId(), update.getDeclaredId(), update.getDepotCustomName());
            if (!rUpdate.isSuccess()) {
                log.error("{} orderId {}", LogUtil.getClassMethodName("更新数据失败"), update.getId());
            }
            log.info("shopOrderId {},原depotCustomName {}, 更新depotCustomName {}成功", shopOrder.getId(), oldCustoms, customs);
        }
        return declareId;
    }

    private void refreshShopOrder(ShopOrder shopOrder) {
        var result = shopOrderReadService.findById(shopOrder.getId());
        if (result == null || !result.isSuccess() || result.getResult() == null) {
            log.error("shopOrderId={}, refreshShopOrder error, 主订单查询失败, errorMessage={}",
                    shopOrder.getId(), result != null ? result.getError() : "");
            return;
        }

        log.info("shopOrderId={}, refreshShopOrder before, declaredId={}, depotCustomName={}, extra={}",
                shopOrder.getId(), shopOrder.getDeclaredId(), shopOrder.getDepotCustomName(), shopOrder.getExtra());

        if (StringUtils.isNotBlank(result.getResult().getDeclaredId())) {
            shopOrder.setDeclaredId(result.getResult().getDeclaredId());
        }
        if (StringUtils.isNotBlank(result.getResult().getDepotCustomName())) {
            shopOrder.setDepotCustomName(result.getResult().getDepotCustomName());
        }
        if (!CollectionUtils.isEmpty(result.getResult().getExtra())) {
            shopOrder.setExtra(result.getResult().getExtra());
        }

        log.info("shopOrderId={}, refreshShopOrder after, declaredId={}, depotCustomName={}, extra={}",
                shopOrder.getId(), shopOrder.getDeclaredId(), shopOrder.getDepotCustomName(), shopOrder.getExtra());
    }

    private boolean notSeqUpdate(ShopOrder order) {
        if (ObjectUtils.isEmpty(order.getDepotCustomName())) {
            return false;
        }
        if (ObjectUtils.isEmpty(order.getDeclaredId())) {
            return false;
        }
        Map<String, String> extra = order.getExtra() == null ? new HashMap<>(8) : order.getExtra();
        String seq = extra.get(KEY.seq.name());
        return seq == null || Integer.parseInt(seq) == provider.decode(order.getDeclaredId()).getSeq();
    }

    private enum KEY {
        /**
         * key
         */
        seq
    }
}
