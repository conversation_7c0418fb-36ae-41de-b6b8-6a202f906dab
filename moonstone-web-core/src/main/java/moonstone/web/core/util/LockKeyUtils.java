package moonstone.web.core.util;

import moonstone.order.model.WithDrawProfitApply;

/**
 * 锁的key
 */
public class LockKeyUtils {

    /**
     * 下单, 一次一个用户只能下一个，后面有需要再细分吧
     *
     * @param userId
     * @return
     */
    public static String orderCreate(Long userId) {
        return String.format("parana-order-create-%s", userId);
    }

    /**
     * 发起提现申请
     *
     * @param userId
     * @return
     */
    public static String withdrawProfitApplyCreate(Long shopId, Long userId) {
        return String.format("[%s][shopId=%s](%s)", WithDrawProfitApply.class.getSimpleName(), shopId, userId);
    }

    /**
     * 提现申请单审核
     *
     * @param applyId
     * @return
     */
    public static String withdrawProfitApplyAudit(Long applyId) {
        return String.format("[%s](%s)", WithDrawProfitApply.class.getSimpleName(), applyId);
    }

    /**
     * 确认收货
     *
     * @param shopOrderId
     * @return
     */
    public static String getShipConfirmLockName(Long shopOrderId) {
        return String.format("[ShipConfirm](%s))", shopOrderId);
    }

    /**
     * 创建支付配置
     *
     * @param shopId
     * @return
     */
    public static String shopPayInfoCreate(Long shopId) {
        return String.format("shopPayInfo-create-%s", shopId);
    }

    /**
     * 全量发布支付配置
     *
     * @param shopId
     * @return
     */
    public static String shopPayInfoFullRelease(Long shopId) {
        return String.format("shopPayInfo-fullRelease-%s", shopId);
    }

    /**
     * 灰度发布支付配置
     *
     * @param shopId
     * @return
     */
    public static String shopPayInfoGreyRelease(Long shopId) {
        return String.format("shopPayInfo-greyRelease-%s", shopId);
    }

    /**
     * 自动创建代付订单
     *
     * @return
     */
    public static String autoCreateAgentPayOrder() {
        return "parana-agentPayOrder-auto-create";
    }

    /**
     * 自动发起代付订单支付
     *
     * @return
     */
    public static String autoPayAgentPayOrder() {
        return "parana-agentPayOrder-auto-pay";
    }

    /**
     * 自动发起代付订单支付
     *
     * @return
     */
    public static String queryWithdrawPaymentResult() {
        return "parana-withdrawPayment-auto-query";
    }

    /**
     * 代付订单发起代付
     *
     * @param shopOrderId
     * @return
     */
    public static String agentPayOrderCreate(Long shopOrderId) {
        return String.format("parana-agentPayOrderCreate-%s-create", shopOrderId);
    }

    /**
     * 代付订单发起代付
     *
     * @param agentPayOrderId
     * @return
     */
    public static String agentPayOrderPay(Long agentPayOrderId) {
        return String.format("parana-agentPayOrderPay-%s-pay", agentPayOrderId);
    }

    /**
     * 资金调拨单创建
     *
     * @param relatedId
     * @param relatedType
     * @return
     */
    public static String fundsTransferPaymentCreate(Long relatedId, Integer relatedType) {
        return String.format("parana-fundsTransferPayment-create-%s-%s", relatedId, relatedType);
    }

    /**
     * 资金调拨单创建
     *
     * @param id
     * @return
     */
    public static String fundsTransferPaymentPay(Long id) {
        return String.format("parana-fundsTransferPayment-pay-%s", id);
    }

    /**
     * 发起小B提现
     *
     * @param shopId
     * @return
     */
    public static String createEnterpriseWithdraw(Long shopId) {
        return String.format("parana-enterpriseWithdraw-create-%s", shopId);
    }

    /**
     * 支付小B提现
     *
     * @param withdrawId
     * @return
     */
    public static String payEnterpriseWithdraw(Long withdrawId) {
        return String.format("parana-enterpriseWithdraw-pay-%s", withdrawId);
    }

    /**
     * 小B提现支付回调
     *
     * @param withdrawId
     * @return
     */
    public static String enterpriseWithdrawCallback(Long withdrawId) {
        return String.format("parana-enterpriseWithdraw-callback-%s", withdrawId);
    }

    /**
     * 发布支付配置
     *
     * @param shopId
     * @param tradeType
     * @return
     */
    public static String payChannelRelationKey(Long shopId, Integer tradeType) {
        return String.format("pay_channel_relation-%s", shopId + "-" + tradeType);
    }

}
