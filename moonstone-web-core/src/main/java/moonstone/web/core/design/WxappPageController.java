package moonstone.web.core.design;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import moonstone.common.api.Result;
import moonstone.common.api.ResultCode;
import moonstone.common.utils.IResultCode;
import moonstone.common.utils.R;
import moonstone.common.utils.UserUtil;
import moonstone.web.core.design.model.WxAppAddParam;
import moonstone.wxapp.model.WxappPage;
import moonstone.wxapp.service.WxappPageReadService;
import moonstone.wxapp.service.WxappPageWriteService;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;


/**
 * 微信小程序diy页面表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-16 09:56:22
 */
@RestController
@RequestMapping("/api/{shopId}/design/page")
public class WxappPageController {
    @RpcConsumer
    private WxappPageWriteService wxappPageWriteService;
    @RpcConsumer
    private WxappPageReadService wxappPageReadService;


    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@PathVariable("shopId") Long shopId, @RequestParam Long pageId, @RequestParam String data) {
        WxappPage wxappPage = new WxappPage();
        Boolean isCreate = false;
        if (pageId != null && pageId > 0) {
            Response<WxappPage> response = wxappPageReadService.findById(pageId);
            wxappPage = response.getResult();
        } else {
            wxappPage.setPageType(20);
            wxappPage.setShopId(shopId);
            wxappPage.setIsDelete(0);
            wxappPage.setFlag(0);
            isCreate = true;
        }
        JSONObject pageDataJSON = JSON.parseObject(data);
        String pageName = pageDataJSON.getJSONObject("page").getJSONObject("params").getString("name");
        //wxappPage.setPageData(Base64Utils.encodeToString(data.getBytes()));
        wxappPage.setPageData(data);
        wxappPage.setPageName(pageName);
        if (isCreate) {
            Response<Long> response = wxappPageWriteService.create(wxappPage);
            pageId = response.getResult();
        } else {
            wxappPageWriteService.update(wxappPage);
        }
//        return R.ok("保存成功").put("url", "/design/edit.html/"+pageId);
        return R.ok("保存成功");
    }

    @RequestMapping("/newSave")
    public R add(@PathVariable("shopId") Long shopId, @RequestParam(required = false) Long pageId, @RequestParam String data) {

        //Long userId=getLoginUserId();

        WxappPage wxappPage = new WxappPage();
        Boolean isCreate = false;
        if (pageId != null && pageId > 0) {
            //只查找预发环境的数据 flag=0 预发环境
            Response<WxappPage> response = wxappPageReadService.findByIdLimitOne(pageId, 0);
            if (response.isSuccess() && response.getResult() != null) {
                wxappPage = response.getResult();
            } else {
                wxappPage.setPageType(10);
                wxappPage.setShopId(shopId);
                wxappPage.setIsDelete(0);
                wxappPage.setFlag(0);
                isCreate = true;
            }
        } else {
            Response<WxappPage> resxappPage = wxappPageReadService.findByShopIdAndStatus(shopId);
            if (resxappPage.isSuccess() && ObjectUtils.isEmpty(resxappPage.getResult())) {
                wxappPage.setPageType(10);
                wxappPage.setShopId(shopId);
                wxappPage.setIsDelete(0);
                wxappPage.setFlag(0);
                isCreate = true;
            } else {
                wxappPage = resxappPage.getResult();
            }
        }
        wxappPage.setPageData(data);
        wxappPage.setPageName("首页");
        if (isCreate) {
            Response<Long> response = wxappPageWriteService.create(wxappPage);
            pageId = response.getResult();
            return R.ok();
        } else {
            wxappPageWriteService.update(wxappPage);
            return R.ok();
        }
    }

    /**
     * 保存
     */
    @PostMapping("/newSave/v2")
    public Result add(@PathVariable("shopId") Long shopId, @RequestBody WxAppAddParam wxAppAddParam) {

        //Long userId=getLoginUserId();
        Long pageId = wxAppAddParam.getPageId();
        String data = wxAppAddParam.getData();
        WxappPage wxappPage = new WxappPage();
        Boolean isCreate = false;
        if (pageId != null && pageId > 0) {
            //只查找预发环境的数据 flag=0 预发环境
            Response<WxappPage> response = wxappPageReadService.findByIdLimitOne(pageId, 0);
            if (response.isSuccess() && response.getResult() != null) {
                wxappPage = response.getResult();
            } else {
                wxappPage.setPageType(10);
                wxappPage.setShopId(shopId);
                wxappPage.setIsDelete(0);
                wxappPage.setFlag(0);
                isCreate = true;
            }
        } else {
            Response<WxappPage> resxappPage = wxappPageReadService.findByShopIdAndStatus(shopId);
            if (resxappPage.isSuccess() && ObjectUtils.isEmpty(resxappPage.getResult())) {
                wxappPage.setPageType(10);
                wxappPage.setShopId(shopId);
                wxappPage.setIsDelete(0);
                wxappPage.setFlag(0);
                isCreate = true;
            } else {
                wxappPage = resxappPage.getResult();
            }
        }
        wxappPage.setPageData(data);
        wxappPage.setPageName("首页");
        if (isCreate) {
            Response<Long> response = wxappPageWriteService.create(wxappPage);
            pageId = response.getResult();
            return Result.success(ResultCode.SUCCESS);
        } else {
            wxappPageWriteService.update(wxappPage);
            return Result.success(ResultCode.SUCCESS);
        }
    }

    /**
     * 预发正式发布
     */
    @RequestMapping("/publish")
    public R upLoad(@PathVariable("shopId") Long shopId) {

       // Long userId=getLoginUserId();

        Response<Boolean> response = wxappPageWriteService.updateByIdAndShopId(shopId);
        //todo  异常逻辑处理
        return R.ok().add("msg", response.getResult());
    }

    @RequestMapping("/getPage")
    public R getPageId(@PathVariable("shopId") Long shopId) {

        //  Long userId=getLoginUserId();
        String advanceData = "";
        String publishData = "";

        Response<WxappPage> yfresponse = wxappPageReadService.findByShopIdLimitOne(shopId, 0);//预发
        if (yfresponse.isSuccess() && yfresponse.getResult() != null) {
            advanceData = yfresponse.getResult().getPageData();
        }
        Response<WxappPage> zsresponse = wxappPageReadService.findByShopIdLimitOne(shopId, 1);//正式
        if (zsresponse.isSuccess() && zsresponse.getResult() != null) {
            publishData = zsresponse.getResult().getPageData();
        }

        //todo  异常逻辑处理
        return R.ok().add("shopId", shopId)
                .add("advanceData", advanceData)
                .add("publishData", publishData);
    }

    /**
     * 查询页面信息
     */
    @PostMapping("/getPage/v2")
    public Result<Map<String, Object>> getPageIdV2(@PathVariable("shopId") Long shopId) {

        //  Long userId=getLoginUserId();
        String advanceData = "";
        String publishData = "";

        Response<WxappPage> yfresponse = wxappPageReadService.findByShopIdLimitOne(shopId, 0);//预发
        if (yfresponse.isSuccess() && yfresponse.getResult() != null) {
            advanceData = yfresponse.getResult().getPageData();
        }
        Response<WxappPage> zsresponse = wxappPageReadService.findByShopIdLimitOne(shopId, 1);//正式
        if (zsresponse.isSuccess() && zsresponse.getResult() != null) {
            publishData = zsresponse.getResult().getPageData();
        }

        //todo  异常逻辑处理
        HashMap<String, Object> data = new HashMap<>();
        data.put("shopId", shopId);
        data.put("advanceData", advanceData);
        data.put("publishData", publishData);
        return Result.data(data);
//        return R.ok().add("shopId", shopId)
//                .add("advanceData", advanceData)
//                .add("publishData", publishData);
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@PathVariable("shopId") Long shopId, @RequestParam Long pageId) {
        Response<Boolean> response = wxappPageWriteService.softDelete(pageId);
        //todo  异常逻辑处理
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/setHome")
    public R setHome(@PathVariable("shopId") Long shopId, @RequestParam Long pageId) {
        Response<Boolean> response = wxappPageWriteService.setHome(shopId, pageId);
        //todo  异常逻辑处理
        return R.ok();
    }

    private long getLoginUserId() {
        return Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
    }
}
