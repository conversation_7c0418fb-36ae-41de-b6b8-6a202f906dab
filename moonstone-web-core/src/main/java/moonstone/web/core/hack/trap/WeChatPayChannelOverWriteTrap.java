package moonstone.web.core.hack.trap;

import com.google.common.base.Throwables;
import io.terminus.pay.api.TokenProvider;
import io.terminus.pay.model.RefundParams;
import io.terminus.pay.model.TradeRequest;
import io.terminus.pay.wechatpay.common.channel.WechatpayChannel;
import io.terminus.pay.wechatpay.common.event.WechatpayRefundRequestEvent;
import io.terminus.pay.wechatpay.common.model.params.WxRefundParams;
import io.terminus.pay.wechatpay.common.model.response.WxRefundResponse;
import io.terminus.pay.wechatpay.common.model.token.WxToken;
import io.terminus.pay.wechatpay.common.request.WxRefundRequest;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.event.RefundSerialNoUpdateEvent;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.aopalliance.intercept.MethodInterceptor;
import org.springframework.aop.framework.ProxyFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;


@Slf4j
@Component
public class WeChatPayChannelOverWriteTrap implements BeanPostProcessor {

    @Autowired
    private List<TokenProvider> wxTokenProviderList;
    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    MethodInterceptor refundRequestTrap = invocation -> {
        log.debug("WeChatPayChannelOverWriteTrap.MethodInterceptor, interceptedMethodName={}", invocation.getMethod().getName());
        RefundParams refund = (RefundParams) invocation.getArguments()[0];
        try {
            Type triggerType = invocation.getThis().getClass().getGenericSuperclass();
            Type triggerRequire = ((ParameterizedType) triggerType).getActualTypeArguments()[0];
            TokenProvider<? extends WxToken> tokenProvider = (TokenProvider<? extends WxToken>) wxTokenProviderList.stream().filter(provider -> {
                Type genericTpe = provider.getClass().getGenericSuperclass();
                if (genericTpe instanceof ParameterizedType) {
                    Type tokenType = ((ParameterizedType) genericTpe).getActualTypeArguments()[0];
                    return ((Class) tokenType).isAssignableFrom((Class) triggerRequire);
                }
                return false;
            }).findFirst().orElse(null);
            if (tokenProvider == null)
                return TradeRequest.fail("not.provider.found");
            //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
            WxToken token = tokenProvider.findToken(refund.getSellerNo());
            WxRefundParams params = new WxRefundParams(token, refund);
            WxRefundResponse response = WxRefundRequest.refund(token, params);

            TradeRequest request = null;
            if (response.isSuccess()) {
                request = TradeRequest.ok(null);
                EventSender.publish(new WechatpayRefundRequestEvent(refund, request));
                EventSender.publish(new RefundSerialNoUpdateEvent(refund.getTradeNo(), refund.getChannel(), refund.getRefundNo(), response.getRefundId()));
            } else {
                request = TradeRequest.fail(response.getErrorMsg());
            }
            return request;

        } catch (Exception e) {
            log.error("refundRequest fail, refund={}, cause={}", refund, Throwables.getStackTraceAsString(e));
            return TradeRequest.fail("refund.fail");
        }
    };

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof WechatpayChannel) {
            ProxyFactory proxyFactory = new ProxyFactory();
            proxyFactory.setTarget(bean);
            proxyFactory.addAdvice(refundRequestTrap);
            log.warn("{} capture the old wechatPayChannel component [{}]", LogUtil.getClassMethodName(), beanName);
            return proxyFactory.getProxy();
        }
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

}
