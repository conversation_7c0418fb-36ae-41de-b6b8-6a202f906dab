package moonstone.web.core.fileNew.vo;

import lombok.Data;
import moonstone.web.core.fileNew.dto.ShopCategoryDto;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ItemsVo {

    /**
     * ID
     */
    private Long id;

    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * 后台类目 ID
     */
    private Long categoryId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 是否保税(1:保税，0:完税)
     *
     * @see moonstone.common.enums.BondedType  保税类型变化
     */
    private Integer isBonded;

    /**
     * 是否为第三方商品（0：为自建商品，1：第三方商品）
     *
     * @see moonstone.item.emu.ThirdPartyItemType
     */
    private Integer isThirdPartyItem;


    /**
     * 商品来源(1：代塔仓自有，2：京东云交易)
     */
    private Integer sourceType;

    /**
     * 商品来源名称
     */
    private String sourceTypeName;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品简介
     */
    private String intro;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 最低实际售价
     */
    private Integer lowPrice;

    /**
     * 最高实际售价
     */
    private Integer highPrice;

    /**
     * 售价
     */
    private Integer price;

    /**
     * 库存类型, 0: 不分仓存储, 1: 分仓存储
     */
    private Integer stockType;

    /**
     * 平台可售库存
     */
    private Integer stockQuantity = 0;

    /**
     * 仓库现货库存
     */
    private Integer thirdSkuStockQuantity = 0;

    /**
     * 销量
     */
    private Integer saleQuantity;

    /**
     * 状态  1: 上架, -1:下架, -2:冻结, -3:删除
     *
     * @see moonstone.item.emu.ItemStatusEnum
     */
    private Integer status;

    /**
     * 是否参与活动 0-不参与 1-参与
     */
    private Integer tips;

    /**
     * 排序
     */
    private Integer index;

    /**
     * 商品类型 1为普通商品, 2为组合商品
     */
    private Integer type;

    /**
     * 售罄状态
     *
     * @see moonstone.item.emu.ItemSellOutStatusEnum
     */
    private Integer sellOutStatus = 1;

    /**
     * 区域限售模板id
     */
    private Long restrictedSalesAreaTemplateId;

    /**
     * 限售区域模板名称
     */
    private String restrictedSalesAreaTemplateName;


    /**
     * 服务商佣金
     */
    private String serviceProviderCommission;

    /**
     * 门店佣金
     */
    private String subStoreCommission;

    /**
     * 导购佣金
     */
    private String guiderCommission;

    /**
     * 服务商的佣金比例
     */
    private Long serviceProviderCommissionRate;

    /**
     * 门店的佣金比例
     */
    private Long subStoreCommissionRate;

    /**
     * 导购的的佣金比例
     */
    private Long guiderCommissionRate;

    /**
     * 仓库商品sku列表
     */
    private List<String> outerSkuIds;

    /**
     * 目录
     */
    private ShopCategoryDto shopCategory;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 是否可用礼金
     */
    private String useCashGiftDesc;

    /**
     * 是否有规格
     */
    private Boolean whetherExistSpec;

}
