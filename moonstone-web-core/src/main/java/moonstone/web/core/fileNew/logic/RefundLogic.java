package moonstone.web.core.fileNew.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dt.tms.sdk.api.response.QueryTmsExpressListResponse;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.ResultCode;
import moonstone.common.constants.RedisKeyConstant;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.RefundReasonTypeEnum;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.UserUtil;
import moonstone.event.OrderRefundEvent;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.*;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.RefundWriteService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.web.core.express.component.TmsExpressService;
import moonstone.web.core.fileNew.dto.*;
import moonstone.web.core.fileNew.strategy.processor.RefundProcessor;
import moonstone.web.core.refund.application.RefundOrderStatusRecordApp;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RefundLogic {

	@Resource
	private TmsExpressService tmsExpressService;

	@Resource
	private ShopOrderLogic shopOrderLogic;

	@Resource
	private SkuOrderLogic skuOrderLogic;

	@Resource
	private RefundProcessor refundProcessor;

	@Resource
	private PaymentV2Logic paymentV2Logic;

	@Resource
	private RedissonClient redissonClient;

	@Resource
	private RefundReadService refundReadService;

	@Resource
	private RefundWriteService refundWriteService;

	@Resource
	private RefundOrderStatusRecordApp refundOrderStatusRecordApp;

	@Resource
	private OrderRefundLogic orderRefundLogic;

	@Resource
	private ShopReadService shopReadService;

	@Resource
	private RefundLogic self;


	/**
	 * 获取退款原因下拉框
	 *
	 * @return 退款原因下拉框
	 */
	public List<DropDownBoxVo> refundReasonList() {
		List<DropDownBoxVo> dropDownBoxVoList = new ArrayList<>();
		for (RefundReasonTypeEnum value : RefundReasonTypeEnum.values()) {
			DropDownBoxVo entity = new DropDownBoxVo();
			entity.setId(String.valueOf(value.getCode()));
			entity.setName(value.getDesc());
			dropDownBoxVoList.add(entity);
		}
		return dropDownBoxVoList;
	}

	/**
	 * 获取快递公司下拉框
	 *
	 * @return 快递公司下拉框
	 */
	public List<DropDownBoxVo> refundExpressCompanyList() {
		QueryTmsExpressListResponse response = tmsExpressService.getExpressCompanyList();
		List<QueryTmsExpressListResponse.Express> expressList = response.getData();
		List<DropDownBoxVo> dropDownBoxVoList = new ArrayList<>();
		for (QueryTmsExpressListResponse.Express express : expressList) {
			DropDownBoxVo entity = new DropDownBoxVo();
			entity.setId(express.getExpressCode());
			entity.setName(express.getExpressTitle());
			dropDownBoxVoList.add(entity);
		}
		return dropDownBoxVoList;
	}


	/**
	 * 申请退款
	 *
	 * @param req 请求参数
	 * @return 退款单id
	 */
	public Long apply(RefundReqDto req) {
		checkApplyParam(req);
		Map<String, Object> query = new HashMap<>();
		query.put("orderId", req.getOrderId());
		SkuOrder skuOrder = skuOrderLogic.getOne(query);
		query = new HashMap<>();
		query.put("id", req.getOrderId());
		ShopOrder shopOrder = shopOrderLogic.getOne(query);
		if (ObjUtil.isEmpty(shopOrder) || ObjUtil.isEmpty(skuOrder)) {
			throw new ApiException("未找到需要退款的订单信息");
		}
		if (shopOrder.getStatus() < OrderStatus.PAID.getValue()) {
			throw new ApiException("当前订单状态不支持退款");
		}
		CommonUser currentUser = UserUtil.getCurrentUser();
		log.info("登录人信息 {}", JSONUtil.toJsonStr(currentUser));
		Long shopId = currentUser.getShopId();
		if (!shopOrder.getBuyerId().equals(currentUser.getId())) {
			// 当不是本人申请退款的时候
			if (!Objects.equals(shopId, shopOrder.getShopId())) {
				throw new ApiException("当前操作人不允许对该订单发起退款申请");
			}
		}
		List<Payment> payments = paymentV2Logic.listByOrderId(req.getOrderId()).stream().filter(payment -> payment.getStatus() == OrderStatus.PAID.getValue()).toList();
		if (CollUtil.isEmpty(payments)) {
			throw new ApiException("未找到已完成支付的支付单信息");
		}
		Payment payment = payments.get(0);
		//  仅退款的情况下
		if (req.getRefundType() == Refund.RefundType.ON_SALE_REFUND.value()) {
			if (shopOrder.getStatus() == OrderStatus.CONFIRMED.getValue()) {
				req.setRefundType(Refund.RefundType.AFTER_SALE_REFUND.value());
			}
			if (ObjUtil.isEmpty(req.getBuyerReceivedStatus())) {
				req.setBuyerReceivedStatus(false);
			}
		}
		// 退货退款类型下
		if (req.getRefundType() == Refund.RefundType.AFTER_SALE_RETURN.value()) {
			// 买家已收到货
			req.setBuyerReceivedStatus(true);
		}
		RefundApplyDto refundApplyDto = BeanUtil.copyProperties(req, RefundApplyDto.class);
		refundApplyDto.setPayment(payment);
		refundApplyDto.setShopOrder(shopOrder);
		refundApplyDto.setSkuOrder(skuOrder);
		String lockKey = RedisKeyConstant.ORDER_REFUND_APPLY_PREFIX + req.getOrderId();
		RLock lock = redissonClient.getLock(lockKey);
		try {
			boolean flag = lock.tryLock(0, 5, TimeUnit.SECONDS);
			if (!flag) {
				throw new ApiException("服务繁忙，请稍后重试");
			}
			Long refundId = refundProcessor.apply(refundApplyDto);
			OrderRefund orderRefund = new OrderRefund();
			orderRefund.setOrderId(req.getOrderId());
			orderRefund.setOrderLevel(OrderLevel.fromInt(req.getOrderType()));
			refundOrderStatusRecordApp.persistOrderOriginStatusForRefund(refundId, Collections.singletonList(orderRefund));
			OrderEvent event = refundApplyDto.getRefundType() == Refund.RefundType.AFTER_SALE_RETURN.value() ? OrderEvent.RETURN_APPLY : OrderEvent.REFUND_APPLY;
			EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, event.toOrderOperation()));
			return refundId;
		} catch (Exception e) {
			log.error("订单为 {} 申请退款失败 失败原因 {}", req.getOrderId(), e.getMessage(), e);
			throw new ApiException(e.getMessage());
		} finally {
			if (lock.isLocked()) {
				if (lock.isHeldByCurrentThread()) {
					lock.unlock();
				}
			}
		}
	}

	/**
	 * 判断是否存在尚在流转的退款单
	 *
	 * @param orderId  交易单号
	 * @param refundId 退款单号
	 */
	private void checkExistFlowRefund(Long orderId, Long refundId) {
		List<Refund> refundList = findByOrderId(orderId);
		for (Refund refund : refundList) {
			if (ObjUtil.isNotEmpty(refundId) && refundId.equals(refund.getId())) {
				log.info("当前流转的退款单 和 传入进来的退款单号是一致的");
				continue;
			}
			OrderStatus refundStatus = OrderStatus.fromInt(refund.getStatus());
			switch (refundStatus) {
				case REFUND -> throw new ApiException("该订单已退款，不允许重复退款");
				case REFUND_PROCESSING -> throw new ApiException("该订单退款处理中，不允许重复退款");
				case REFUND_APPLY, REFUND_APPLY_AGREED, RETURN_APPLY, RETURN_APPLY_AGREED, RETURN ->
						throw new ApiException("该订单存在尚在流转的退款单，不允许重复申请退款");
			}
		}
	}


	private void checkApplyParam(RefundReqDto req) {
		if (ObjUtil.isEmpty(req.getRefundType())) {
			throw new ApiException("退款类型不能为空");
		}
		if (ObjUtil.isEmpty(req.getOrderId())) {
			throw new ApiException("订单id不能为空");
		}
		if (ObjUtil.isEmpty(req.getReasonType())) {
			throw new ApiException("退款原因不能为空");
		}
		if (ObjUtil.isEmpty(req.getOrderType()) || req.getOrderType() != OrderLevel.SHOP.getValue()) {
			// 默认为店铺订单类型
			throw new ApiException("当前仅支持主单退款");
		}
		if (StrUtil.isBlank(req.getBuyerRemark())) {
			// 默认买家备注为空
			req.setBuyerRemark("");
		}
		if (StrUtil.isBlank(req.getImagesJson())) {
			req.setImagesJson("");
		}
		if (ObjUtil.isEmpty(req.getBuyerReceivedStatus())) {
			// 默认是未收到货
			req.setBuyerReceivedStatus(false);
		}
	}


	public Boolean agreeApply(RefundReqDto req) {
		checkAgreeApplyParam(req);
		CommonUser currentUser = UserUtil.getCurrentUser();
		if (currentUser == null) {
			throw new ApiException(ResultCode.UN_AUTHORIZED);
		}
		Refund refund = getById(req.getRefundId());
		if (ObjUtil.isEmpty(refund)) {
			throw new ApiException("未找到对应的退款单，无法进行同意退款操作");
		}
		if (!refund.getShopId().equals(currentUser.getShopId())) {
			throw new ApiException("该订单不属于你的商品，不允许操作");
		}
		Map<String, Object> query = new HashMap<>();
		query.put("refundId", req.getRefundId());
		OrderRefund orderRefund = orderRefundLogic.getOne(query);
		if (ObjUtil.isEmpty(orderRefund)) {
			throw new ApiException("未查询到关联的订单号");
		}
		Long orderId = orderRefund.getOrderId();
		checkExistFlowRefund(orderId, req.getRefundId());
		String lockKey = RedisKeyConstant.REFUND_APPLY_AGREE_PREFIX + orderId;
		RLock lock = redissonClient.getLock(lockKey);
		try {
			boolean flag = lock.tryLock(0, 5, TimeUnit.SECONDS);
			if (!flag) {
				throw new ApiException("该退款单已经同意，无需重复同意");
			}
			RefundApplyAgreeDto refundApplyAgreeDto = new RefundApplyAgreeDto();
			refundApplyAgreeDto.setRefundId(req.getRefundId());
			refundApplyAgreeDto.setSellerAddress(req.getSellerAddress());
			refundApplyAgreeDto.setRefund(refund);
			return refundProcessor.applyAgree(refundApplyAgreeDto);
		} catch (Exception e) {
			log.error("退款单为 {} 同意退款申请 失败原因 {}", req.getRefundId(), e.getMessage(), e);
			throw new ApiException(e.getMessage());
		} finally {
			if (lock.isLocked()) {
				if (lock.isHeldByCurrentThread()) {
					lock.unlock();
				}
			}
		}
	}

	private void checkAgreeApplyParam(RefundReqDto req) {
		if (ObjUtil.isEmpty(req.getRefundId())) {
			throw new ApiException("退款单号不能为空");
		}
	}

	public Boolean rejectApply(RefundReqDto req) {
		checkRejectApplyParam(req);
		CommonUser currentUser = UserUtil.getCurrentUser();
		if (currentUser == null) {
			throw new ApiException(ResultCode.UN_AUTHORIZED);
		}
		Refund refund = getById(req.getRefundId());
		if (ObjUtil.isEmpty(refund)) {
			throw new ApiException("未找到对应的退款单，无法进行同意退款操作");
		}
		if (!refund.getShopId().equals(currentUser.getShopId())) {
			throw new ApiException("该订单不属于你的商品，不允许操作");
		}
		Map<String, Object> query = new HashMap<>();
		query.put("refundId", req.getRefundId());
		OrderRefund orderRefund = orderRefundLogic.getOne(query);
		if (ObjUtil.isEmpty(orderRefund)) {
			throw new ApiException("未查询到关联的订单号");
		}
		Long orderId = orderRefund.getOrderId();
		checkExistFlowRefund(orderId, req.getRefundId());
		String lockKey = RedisKeyConstant.REFUND_APPLY_REJECT_PREFIX + orderId;
		RLock lock = redissonClient.getLock(lockKey);
		try {
			boolean flag = lock.tryLock(0, 5, TimeUnit.SECONDS);
			if (!flag) {
				throw new ApiException("该退款单已经驳回，无需重复驳回");
			}
			RefundApplyRejectDto refundApplyRejectDto = new RefundApplyRejectDto();
			refundApplyRejectDto.setRefundId(req.getRefundId());
			refundApplyRejectDto.setSellerNote(req.getSellerNote());
			refundApplyRejectDto.setRefund(refund);
			refundApplyRejectDto.setOrderId(orderId);
			return refundProcessor.applyReject(refundApplyRejectDto);
		} catch (Exception e) {
			log.error("退款单为 {} 拒绝退款申请 失败原因 {}", req.getRefundId(), e.getMessage(), e);
			throw new ApiException(e.getMessage());
		} finally {
			if (lock.isLocked()) {
				if (lock.isHeldByCurrentThread()) {
					lock.unlock();
				}
			}
		}
	}

	private void checkRejectApplyParam(RefundReqDto req) {
		if (ObjUtil.isEmpty(req.getRefundId())) {
			throw new ApiException("退款单号不能为空");
		}
		if (StrUtil.isBlank(req.getSellerNote())) {
			throw new ApiException("卖家备注不能为空");
		}
	}

	public Boolean uploadExpressInfo(RefundReqDto req) {
		checkUploadExpressInfoParam(req);
		CommonUser currentUser = UserUtil.getCurrentUser();
		if (currentUser == null) {
			throw new ApiException(ResultCode.UN_AUTHORIZED);
		}
		Refund refund = getById(req.getRefundId());
		if (ObjUtil.isEmpty(refund)) {
			throw new ApiException("未找到对应的退款单，无法进行上传快递单号操作");
		}
		if (!refund.getBuyerId().equals(currentUser.getId())) {
			if (!refund.getShopId().equals(currentUser.getShopId())) {
				throw new ApiException("该订单不属于你的商品，不允许操作");
			}
		}
		if (!refund.getRefundType().equals(Refund.RefundType.AFTER_SALE_RETURN.value())) {
			throw new ApiException("该退款单无需上传快递单号");
		}
		Map<String, Object> query = new HashMap<>();
		query.put("refundId", req.getRefundId());
		OrderRefund orderRefund = orderRefundLogic.getOne(query);
		if (ObjUtil.isEmpty(orderRefund)) {
			throw new ApiException("未查询到关联的订单号");
		}
		Long orderId = orderRefund.getOrderId();
		checkExistFlowRefund(orderId, req.getRefundId());
		String lockKey = RedisKeyConstant.REFUND_UPLOAD_EXPRESS_INFO_PREFIX + orderId;
		RLock lock = redissonClient.getLock(lockKey);
		try {
			boolean flag = lock.tryLock(0, 5, TimeUnit.SECONDS);
			if (!flag) {
				throw new ApiException("服务繁忙，请稍后重试");
			}
			RefundUploadExpressInfoDto refundUploadExpressInfoDto = new RefundUploadExpressInfoDto();
			refundUploadExpressInfoDto.setOrderId(orderId);
			refundUploadExpressInfoDto.setRefundId(req.getRefundId());
			refundUploadExpressInfoDto.setExpressNo(req.getExpressNo());
			refundUploadExpressInfoDto.setExpressCode(req.getExpressCode());
			refundUploadExpressInfoDto.setExpressCompany(req.getExpressCompany());
			refundUploadExpressInfoDto.setRefund(refund);
			return refundProcessor.uploadExpressInfo(refundUploadExpressInfoDto);
		} catch (Exception e) {
			log.error("退款单为 {} 上传物流单号 失败原因 {}", req.getRefundId(), e.getMessage(), e);
			throw new ApiException(e.getMessage());
		} finally {
			if (lock.isLocked()) {
				if (lock.isHeldByCurrentThread()) {
					lock.unlock();
				}
			}
		}
	}

	private void checkUploadExpressInfoParam(RefundReqDto req) {
		if (ObjUtil.isEmpty(req.getRefundId())) {
			throw new ApiException("退货单号不能为空");
		}
		if (StrUtil.isBlank(req.getExpressNo())) {
			throw new ApiException("快递单号不能为空");
		}
		if (StrUtil.isBlank(req.getExpressCompany())) {
			throw new ApiException("快递公司不能为空");
		}
	}

	public Boolean refund(Long refundId) {
		Refund refund = getById(refundId);
		if (ObjUtil.isEmpty(refund)) {
			throw new ApiException("该退款单未找到");
		}
		CommonUser currentUser = UserUtil.getCurrentUser();
		if (currentUser == null) {
			throw new ApiException(ResultCode.UN_AUTHORIZED);
		}
		if (!refund.getShopId().equals(currentUser.getShopId())) {
			throw new ApiException("该退款单不属于你的商品，不允许操作");
		}
		if (refund.getStatus() == OrderStatus.REFUND_PROCESSING.getValue()) {
			throw new ApiException("该订单已经处于退款中，请勿重复操作");
		}
		if (refund.getStatus() == OrderStatus.REFUND.getValue()) {
			throw new ApiException("该订单已经退款了，请勿重复操作");
		}
		Map<String, Object> query = new HashMap<>();
		query.put("refundId", refundId);
		OrderRefund orderRefund = orderRefundLogic.getOne(query);
		if (ObjUtil.isEmpty(orderRefund)) {
			throw new ApiException("未查询到关联的订单号");
		}
		Long orderId = orderRefund.getOrderId();
		checkExistFlowRefund(orderId, refundId);
		String lockKey = RedisKeyConstant.REFUND_HANDLE_PREFIX + orderId;
		RLock lock = redissonClient.getLock(lockKey);
		try {
			boolean flag = lock.tryLock(0, 10, TimeUnit.SECONDS);
			if (!flag) {
				throw new ApiException("退款已发起，请勿重复退款");
			}
			RefundHandleDto refundHandleDto = new RefundHandleDto();
			refundHandleDto.setRefundId(refundId);
			refundHandleDto.setOrderId(orderId);
			refundHandleDto.setRefund(refund);
			return refundProcessor.refundHandle(refundHandleDto);
		} catch (Exception e) {
			log.error("退款单为 {} 同意退款申请 失败原因 {}", refundId, e.getMessage(), e);
			throw new ApiException(e.getMessage());
		} finally {
			if (lock.isLocked()) {
				if (lock.isHeldByCurrentThread()) {
					lock.unlock();
				}
			}
		}
	}


	public List<Refund> findByOrderId(Long orderId) {
		Map<String, Object> query = new HashMap<>();
		query.put("orderId", orderId);
		List<OrderRefund> orderRefundList = orderRefundLogic.list(query);
		if (CollUtil.isEmpty(orderRefundList)) {
			return Collections.emptyList();
		}
		List<Long> refundIds = orderRefundList.stream().map(OrderRefund::getRefundId).toList();
		return refundReadService.findByIds(refundIds).getResult();
	}

	public void save(Refund refund) {
		refundWriteService.save(refund);
	}

	public List<DropDownBoxVo> getRefundStatusDropDownBox() {
		List<OrderStatus> refundStatusList = OrderStatus.getRefundStatus();
		return refundStatusList.stream().map(refundStatus -> {
			DropDownBoxVo entity = new DropDownBoxVo();
			entity.setId(String.valueOf(refundStatus.getValue()));
			switch (refundStatus) {
				case RETURN_APPLY_AGREED -> entity.setName("待买家退货");
				case RETURN -> entity.setName("待商家收货");
				case RETURN_REJECTED -> entity.setName("退货后商家拒绝");
				default -> entity.setName(refundStatus.getDesc());
			}
			return entity;
		}).toList();
	}

	public Refund getById(Long refundId) {
		return refundReadService.findById(refundId).getResult();
	}

	public void update(Refund refund) {
		refundWriteService.update(refund);
	}

	public Refund getExistFlowRefundByOrderId(Long orderId) {
		Map<String, Object> query = new HashMap<>();
		query.put("orderId", orderId);
		List<OrderRefund> orderRefundList = orderRefundLogic.list(query);
		if (CollUtil.isEmpty(orderRefundList)) {
			return null;
		}
		List<Long> refundIds = orderRefundList.stream().map(OrderRefund::getRefundId).toList();
		query = new HashMap<>();
		query.put("id", refundIds);
		List<Refund> refundList = refundReadService.findByIds(refundIds).getResult();
		List<Refund> list = refundList.stream().filter(refund -> {
			switch (OrderStatus.fromInt(refund.getStatus())) {
				case REFUND_APPLY, REFUND_APPLY_AGREED, RETURN_APPLY, RETURN_APPLY_AGREED, RETURN -> {
					return true;
				}
				default -> {
					return false;
				}
			}
		}).toList();
		if (CollUtil.isEmpty(list)) {
			return null;
		}
		return list.get(0);
	}

	public Boolean autoRefund(RefundAutoReqDto req) {
		// 自动发起申请退款
		Long refundId = self.autoApplyRefund(req);
		EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.REFUND_APPLY.toOrderOperation()));
		ThreadUtil.sleep(500);
        // 自动同意退款
		self.autoAgreeApply(refundId);
		ThreadUtil.sleep(1000);
		// 自动发起退款
		self.autoActualRefund(refundId);
		return true;
	}

	/**
	 * 自动发起实际退款
	 * @param refundId 退款单id
	 */
	@Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
	public void autoActualRefund(Long refundId) {
		Refund refund = getById(refundId);
		log.info("自动退款查询到的退款单信息 {}", JSONUtil.toJsonStr(refund));
		if (refund.getStatus() == OrderStatus.REFUND_PROCESSING.getValue()) {
			throw new ApiException("该订单已经处于退款中，请勿重复操作");
		}
		if (refund.getStatus() == OrderStatus.REFUND.getValue()) {
			throw new ApiException("该订单已经退款了，请勿重复操作");
		}
		Map<String, Object> query = new HashMap<>();
		query.put("refundId", refundId);
		OrderRefund orderRefund = orderRefundLogic.getOne(query);
		if (ObjUtil.isEmpty(orderRefund)) {
			throw new ApiException("未查询到关联的订单号");
		}
		Long orderId = orderRefund.getOrderId();
		checkExistFlowRefund(orderId, refundId);
		String lockKey = RedisKeyConstant.REFUND_HANDLE_PREFIX + orderId;
		RLock lock = redissonClient.getLock(lockKey);
		try {
			boolean flag = lock.tryLock(0, 10, TimeUnit.SECONDS);
			if (!flag) {
				throw new ApiException("退款已发起，请勿重复退款");
			}
			RefundHandleDto refundHandleDto = new RefundHandleDto();
			refundHandleDto.setRefundId(refundId);
			refundHandleDto.setOrderId(orderId);
			refundHandleDto.setRefund(refund);
			refundProcessor.refundHandle(refundHandleDto);
		} catch (Exception e) {
			log.error("退款单为 {} 同意退款申请 失败原因 {}", refundId, e.getMessage(), e);
			throw new ApiException(e.getMessage());
		} finally {
			if (lock.isLocked()) {
				if (lock.isHeldByCurrentThread()) {
					lock.unlock();
				}
			}
		}
	}

	/**
	 * 自动同意退款
	 * @param refundId 退款单id
	 */
	@Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
	public void autoAgreeApply(Long refundId) {
		Refund refund = getById(refundId);
		log.info("自动同意退款 退款单信息 {}", JSONUtil.toJsonStr(refund));
		if (ObjUtil.isEmpty(refund)) {
			throw new ApiException("未找到对应的退款单，无法进行同意退款操作");
		}
		Map<String, Object> query = new HashMap<>();
		query.put("refundId", refundId);
		OrderRefund orderRefund = orderRefundLogic.getOne(query);
		if (ObjUtil.isEmpty(orderRefund)) {
			throw new ApiException("未查询到关联的订单号");
		}
		Long orderId = orderRefund.getOrderId();
		checkExistFlowRefund(orderId, refundId);
		String lockKey = RedisKeyConstant.REFUND_APPLY_AGREE_PREFIX + orderId;
		RLock lock = redissonClient.getLock(lockKey);
		try {
			boolean flag = lock.tryLock(0, 5, TimeUnit.SECONDS);
			if (!flag) {
				throw new ApiException("该退款单已经同意，无需重复同意");
			}
			RefundApplyAgreeDto refundApplyAgreeDto = new RefundApplyAgreeDto();
			refundApplyAgreeDto.setRefundId(refundId);
			refundApplyAgreeDto.setSellerAddress("");
			refundApplyAgreeDto.setRefund(refund);
			refundProcessor.applyAgree(refundApplyAgreeDto);
		} catch (Exception e) {
			log.error("退款单为 {} 同意退款申请 失败原因 {}", refundId, e.getMessage(), e);
			throw new ApiException(e.getMessage());
		} finally {
			if (lock.isLocked()) {
				if (lock.isHeldByCurrentThread()) {
					lock.unlock();
				}
			}
		}
	}

	/**
	 * 自动发起退款
	 *
	 * @param req 请求参数
	 * @return 退款单id
	 */
	@Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
	public Long autoApplyRefund(RefundAutoReqDto req) {
		Long orderId = req.getOrderId();
		List<Refund> refundList = findByOrderId(orderId);
		// 自动退款单 有且仅有一个
		if (CollUtil.isNotEmpty(refundList)) {
			return refundList.get(0).getId();
		}
		Map<String, Object> query = new HashMap<>();
		query.put("orderId", orderId);
		SkuOrder skuOrder = skuOrderLogic.getOne(query);
		query = new HashMap<>();
		query.put("id", orderId);
		ShopOrder shopOrder = shopOrderLogic.getOne(query);
		Shop shop = shopReadService.findById(shopOrder.getShopId()).getResult();
		if (ObjUtil.isEmpty(shop)) {
			throw new ApiException("未找到店铺信息");
		}
		String salesPattern = shop.getExtra().get(ShopExtra.SalesPattern.getCode());
		if (!salesPattern.equals(ShopExtra.communityOperation.getCode())) {
			throw new ApiException("店铺id " + shop.getId() + "该店铺模式不是社群模式，无法发起自动退款");
		}
		List<Payment> payments = paymentV2Logic.listByOrderId(orderId).stream().filter(payment -> payment.getStatus() == OrderStatus.PAID.getValue()).toList();
		if (CollUtil.isEmpty(payments)) {
			throw new ApiException("未找到已完成支付的支付单信息");
		}
		Payment payment = payments.get(0);
		RefundApplyDto refundApplyDto = BeanUtil.copyProperties(req, RefundApplyDto.class);
		// 默认仅退款
		refundApplyDto.setRefundType(Refund.RefundType.ON_SALE_REFUND.value());
		// 默认退款原因：活动失败
		refundApplyDto.setReasonType(RefundReasonTypeEnum.ACTIVITY_FAIL.getCode());
		// 默认未收到货
		refundApplyDto.setBuyerReceivedStatus(false);
		refundApplyDto.setPayment(payment);
		refundApplyDto.setShopOrder(shopOrder);
		refundApplyDto.setSkuOrder(skuOrder);
		refundApplyDto.setSystemNote(req.getSystemNote());
		Long refundId = null;
		String lockKey = RedisKeyConstant.ORDER_REFUND_APPLY_PREFIX + orderId;
		RLock lock = redissonClient.getLock(lockKey);
		try {
			boolean flag = lock.tryLock(0, 5, TimeUnit.SECONDS);
			if (!flag) {
				throw new ApiException("服务繁忙，请稍后重试");
			}
			refundId = refundProcessor.apply(refundApplyDto);
		} catch (Exception e) {
			log.error("订单为 {} 申请退款失败 失败原因 {}", orderId, e.getMessage(), e);
			throw new ApiException(e.getMessage());
		} finally {
			if (lock.isLocked()) {
				if (lock.isHeldByCurrentThread()) {
					lock.unlock();
				}
			}
		}
		return refundId;
	}
}
