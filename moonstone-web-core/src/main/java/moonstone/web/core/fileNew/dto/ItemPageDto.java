package moonstone.web.core.fileNew.dto;

import lombok.Getter;
import lombok.Setter;
import moonstone.common.model.dto.PageDto;

import java.util.List;

/**
 * author：书生
 */
@Getter
@Setter
public class ItemPageDto extends PageDto {
    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 商品id集合
     */
    private List<Long> itemIdList;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品状态
     */
    private Integer status;

    /**
     * 售罄状态
     */
    private Integer sellOutStatus;

    /**
     * 商品分类ID
     */
    private List<Long> shopCategoryIds;

    /**
     * 可售地区模板ID
     */
    private Long restrictedSalesAreaTemplateId;

    /**
     * 仓库商品SKU集合
     */
    private List<String> outerSkuIds;

    /**
     * 是否分类
     */
    private Boolean isCategory;


    /**
     * 商品来源(1：代塔仓自有，2：京东云交易)
     */
    private Integer sourceType;

    /**
     * 是否使用礼金 0：否 1：是
     */
    private Integer isUseCashGift;

    /**
     * 品牌名称
     */
    private String brandName;

    private String brandId;

    private String idOrName;

}
