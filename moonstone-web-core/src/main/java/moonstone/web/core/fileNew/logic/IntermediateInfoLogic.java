package moonstone.web.core.fileNew.logic;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.IntermediateInfoMatchingTypeEnum;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.item.service.IntermediateInfoWriteService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class IntermediateInfoLogic {

    @Resource
    private IntermediateInfoReadService intermediateInfoReadService;

    @Resource
    private IntermediateInfoWriteService intermediateInfoWriteService;

    public IntermediateInfo getItemDefaultIntermediateInfo(Long itemId, Long shopId) {
        IntermediateInfo shopDefaultIntermediateInfo = getShopDefaultIntermediateInfo(shopId);
        IntermediateInfo itemIntermediateInfo = getItemIntermediateInfo(itemId);
        if (itemIntermediateInfo != null) {
            return itemIntermediateInfo;
        }else {
            return shopDefaultIntermediateInfo;
        }
    }


    public IntermediateInfo getSkuDefaultIntermediateInfo(Long skuId, Long shopId) {
        IntermediateInfo shopDefaultIntermediateInfo = getShopDefaultIntermediateInfo(shopId);
        IntermediateInfo itemIntermediateInfo = getSkuIntermediateInfo(skuId);
        if (itemIntermediateInfo != null) {
            return itemIntermediateInfo;
        }else {
            return shopDefaultIntermediateInfo;
        }
    }

    public IntermediateInfo getShopDefaultIntermediateInfo(Long shopId) {
        return intermediateInfoReadService.findWithActivityByThirdAndType(shopId, ThirdIntermediateType.SHOP).getResult();
    }

    public IntermediateInfo getItemIntermediateInfo(Long itemId) {
        List<IntermediateInfo> intermediateInfoList = intermediateInfoReadService.findAllByThirdAndType(itemId, ThirdIntermediateType.ITEM.getValue()).getResult();
        if (intermediateInfoList.isEmpty()) {
            return null;
        }
        var now = new Date();
        var activityConfig = intermediateInfoList.stream()
                .filter(config -> IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode().equals(config.getMatchingType()))
                .filter(config -> config.getMatchingStartTime().before(now) && now.before(config.getMatchingEndTime()))
                .toList();
        if (!CollectionUtils.isEmpty(activityConfig)) {
            return activityConfig.get(0);
        }

        // 取通常配置
        return intermediateInfoList.stream()
                .filter(config -> IntermediateInfoMatchingTypeEnum.NORMAL.getCode().equals(config.getMatchingType()))
                .findAny()
                .orElse(null);
    }
    public IntermediateInfo getSkuIntermediateInfo(Long skuId) {
        List<IntermediateInfo> intermediateInfoList = intermediateInfoReadService.findAllByThirdAndType(skuId, ThirdIntermediateType.SKU.getValue()).getResult();
        if (intermediateInfoList.isEmpty()) {
            return null;
        }
        var now = new Date();
        var activityConfig = intermediateInfoList.stream()
                .filter(config -> IntermediateInfoMatchingTypeEnum.ACTIVITY.getCode().equals(config.getMatchingType()))
                .filter(config -> config.getMatchingStartTime().before(now) && now.before(config.getMatchingEndTime()))
                .toList();
        if (!CollectionUtils.isEmpty(activityConfig)) {
            return activityConfig.get(0);
        }

        // 取通常配置
        return intermediateInfoList.stream()
                .filter(config -> IntermediateInfoMatchingTypeEnum.NORMAL.getCode().equals(config.getMatchingType()))
                .findAny()
                .orElse(null);
    }


}
