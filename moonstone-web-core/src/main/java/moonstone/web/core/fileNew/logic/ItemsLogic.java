package moonstone.web.core.fileNew.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.category.model.ShopCategory;
import moonstone.category.model.ShopCategoryItem;
import moonstone.category.service.ShopCategoryItemReadService;
import moonstone.category.service.ShopCategoryReadService;
import moonstone.common.api.ResultCode;
import moonstone.common.constants.CommonSqlConstant;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.vo.ComboBoxVo;
import moonstone.common.model.vo.PageVo;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.UserUtil;
import moonstone.item.emu.ItemSellOutStatusEnum;
import moonstone.item.emu.ItemStatusEnum;
import moonstone.item.emu.ItemTypeEnum;
import moonstone.item.emu.UseCashGiftEnum;
import moonstone.item.model.*;
import moonstone.item.service.*;
import moonstone.search.model.ItemsEsIndex;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import moonstone.web.core.events.item.ItemUpdateEvent;
import moonstone.web.core.fileNew.dto.ItemPageDto;
import moonstone.web.core.fileNew.dto.ItemUpdateReqDto;
import moonstone.web.core.fileNew.dto.ShopCategoryDto;
import moonstone.web.core.fileNew.dto.ShopCategoryItemsDto;
import moonstone.web.core.fileNew.enums.SourceTypeEnum;
import moonstone.web.core.fileNew.vo.ItemSortRequestVo;
import moonstone.web.core.fileNew.vo.ItemSortedVo;
import moonstone.web.core.fileNew.vo.ItemsVo;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ItemsLogic {

    @Resource
    private ItemWriteService itemWriteService;

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private SkuReadService skuReadService;

    @Resource
    private SkuWriteService skuWriteService;

    @Resource
    private ThirdPartySkuStockReadService thirdPartySkuStockReadService;

    @Resource
    private IntermediateInfoLogic intermediateInfoLogic;

    @Resource
    private ShopCategoryLogic shopCategoryLogic;

    @Resource
    private RestrictedSalesAreaTemplateReadService restrictedSalesAreaTemplateReadService;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Resource
    private ItemsSpecificationDetailService itemsSpecificationDetailService;

    @Resource
    private SkuComboRelationService skuComboRelationService;

    @Resource
    private GbActivityConfigSkuReadService gbActivityConfigSkuReadService;

    @Resource
    private ShopCategoryItemReadService shopCategoryItemReadService;

    @Resource
    private ShopCategoryReadService shopCategoryReadService;

    @Resource
    private IntermediateInfoReadService intermediateInfoReadService;

    @Value("${image.base.url:https://dante-img.oss-cn-hangzhou.aliyuncs.com}")
    private String ossImageUrl;

    public PageVo<ItemsVo> pages(ItemPageDto req) {
        if (req.getShopId() == null) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            if (currentUser == null) {
                throw new ApiException(ResultCode.UN_AUTHORIZED);
            }
            req.setShopId(currentUser.getShopId());
        }
        Map<String, Object> query = convertReqToMap(req);
        if (StringUtils.hasText(req.getIdOrName())) {
            query.put("itemName", req.getIdOrName());
            Long total = itemReadService.countByCondition(query);
            if (total == 0L) {
                query.remove("itemName");
                long id = -1L;
                try {
                    id = Long.parseLong(req.getIdOrName());
                } catch (NumberFormatException ignored) {}
                query.put("itemIdList", Lists.newArrayList(id));
            }
        }
        Long total = itemReadService.countByCondition(query);
        long pages = total / req.getSize() + (total % req.getSize() > 0 ? 1 : 0);
        if (total == 0L) {
            return PageVo.build(total, req.getSize(), req.getCurrent(), pages, Collections.emptyList());
        }
        long offset = (req.getCurrent() - 1) * req.getSize();
        query.put(CommonSqlConstant.OFFSET, offset);
        query.put(CommonSqlConstant.LIMIT, req.getSize());
        query.put(CommonSqlConstant.SORT_BY, "updatedAt");
        query.put(CommonSqlConstant.SORT_TYPE, 2);
        List<Item> itemList = itemReadService.pagesByCondition(query);
        return PageVo.build(total, req.getSize(), req.getCurrent(), pages, convertVo(itemList));
    }

    private List<ItemsVo> convertVo(List<Item> itemList) {
        if (CollUtil.isEmpty(itemList)) {
            return Collections.emptyList();
        }

        // 1. 收集所有需要查询的ID
        List<Long> itemIds = itemList.stream().map(Item::getId).toList();
        Map<Long, Item> itemMap = itemList.stream().collect(Collectors.toMap(Item::getId, Function.identity()));
        Set<Long> shopIds = itemList.stream().map(Item::getShopId).collect(Collectors.toSet());

        // 2. 批量查询SKU信息
        // 使用批量查询方法findSkusByItemIds
        List<Sku> allSkus = skuReadService.findSkusByItemIds(itemIds).getResult();

        // 按商品ID分组
        Map<Long, List<Sku>> itemSkuMap = new HashMap<>();
        Map<Long, List<String>> itemOuterSkuIdMap = new HashMap<>();
        Map<Long, Integer> itemStockQuantityMap = new HashMap<>();
        Set<String> allOuterSkuIds = new HashSet<>();
        Map<Long, Long> skuShopIdMap = new HashMap<>();
        Set<Long> skuIds = new HashSet<>();

        // 初始化商品SKU映射
        for (Long itemId : itemIds) {
            itemSkuMap.put(itemId, new ArrayList<>());
            itemOuterSkuIdMap.put(itemId, new ArrayList<>());
            itemStockQuantityMap.put(itemId, 0);
        }

        // 处理所有SKU
        if (CollUtil.isNotEmpty(allSkus)) {
            for (Sku sku : allSkus) {
                Long itemId = sku.getItemId();

                // 添加到对应商品的SKU列表
                itemSkuMap.get(itemId).add(sku);

                // 累加库存
                int currentStock = itemStockQuantityMap.getOrDefault(itemId, 0);
                itemStockQuantityMap.put(itemId, currentStock + sku.getStockQuantity());

                // 收集外部SKU ID
                if (StrUtil.isNotBlank(sku.getOuterSkuId())) {
                    itemOuterSkuIdMap.get(itemId).add(sku.getOuterSkuId());
                    allOuterSkuIds.add(sku.getOuterSkuId());
                }

                // 收集SKU ID和店铺ID
                skuIds.add(sku.getId());
                skuShopIdMap.put(sku.getId(), sku.getShopId());
            }
        }

        // 3. 批量查询第三方库存信息
        Map<String, List<ThirdPartySkuStock>> outerSkuStockMap = new HashMap<>();
        for (Long shopId : shopIds) {
            for (String outerSkuId : allOuterSkuIds) {
                List<ThirdPartySkuStock> stocks = thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(shopId, ThirdPartySystem.Y800_V3.Id(), outerSkuId).getResult();
                outerSkuStockMap.put(outerSkuId, stocks);
            }
        }
        // 注意: 这里可以优化为批量查询，但需要在ThirdPartySkuStockReadService中添加新的方法

        // 4. 批量查询佣金信息
        Map<Long, IntermediateInfo> skuIntermediateInfoMap = new HashMap<>();
        // 使用批量查询方法
        List<IntermediateInfo> intermediateInfoList = intermediateInfoReadService.findByThirdIdsAndType(new ArrayList<>(skuIds), ThirdIntermediateType.SKU).getResult();
        if (CollUtil.isNotEmpty(intermediateInfoList)) {
            for (IntermediateInfo info : intermediateInfoList) {
                skuIntermediateInfoMap.put(info.getThirdId(), info);
            }
        }

        // 对于没有直接配置的SKU，使用店铺默认配置
        for (Long skuId : skuIds) {
            if (!skuIntermediateInfoMap.containsKey(skuId)) {
                Long shopId = skuShopIdMap.get(skuId);
                IntermediateInfo info = intermediateInfoLogic.getShopDefaultIntermediateInfo(shopId);
                if (info != null) {
                    skuIntermediateInfoMap.put(skuId, info);
                }
            }
        }

        // 5. 批量查询店铺分类
        // 使用批量查询方法
        Long shopId = itemList.get(0).getShopId(); // 假设所有商品都属于同一个店铺

        // 批量查询所有商品关联的分类项
        List<ShopCategoryItem> allShopCategoryItems = shopCategoryItemReadService.findByItemIds(itemIds).getResult();

        // 按商品ID分组
        Map<Long, List<ShopCategoryItem>> itemCategoryMap = new HashMap<>();
        if (CollUtil.isNotEmpty(allShopCategoryItems)) {
            for (ShopCategoryItem item : allShopCategoryItems) {
                if (!itemCategoryMap.containsKey(item.getItemId())) {
                    itemCategoryMap.put(item.getItemId(), new ArrayList<>());
                }
                itemCategoryMap.get(item.getItemId()).add(item);
            }
        }

        // 收集所有分类ID
        Set<Long> allCategoryIds = allShopCategoryItems.stream()
                .map(ShopCategoryItem::getShopCategoryId)
                .collect(Collectors.toSet());

        // 批量查询所有分类信息
        List<ShopCategory> allCategories = CollUtil.isEmpty(allCategoryIds) ?
                Collections.emptyList() :
                shopCategoryReadService.findByIds(new ArrayList<>(allCategoryIds));

        // 6. 批量查询限售区域模板
        Set<Long> templateIds = itemList.stream()
                .map(Item::getRestrictedSalesAreaTemplateId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 使用批量查询方法
        Map<Long, RestrictedSalesAreaTemplate> templateMap = CollUtil.isEmpty(templateIds) ?
                Collections.emptyMap() :
                restrictedSalesAreaTemplateReadService.findMapByIds(new ArrayList<>(templateIds));

        // 7. 组装结果
        List<ItemsVo> resultList = new ArrayList<>(itemList.size());

        // 合并循环，同时完成分类树构建、商品规格查询和结果组装
        for (Long itemId : itemIds) {
            // 3. 组装结果
            Item item = itemMap.get(itemId);
            ItemsVo result = BeanUtil.copyProperties(item, ItemsVo.class);
            result.setSourceTypeName(SourceTypeEnum.getNameByCode(item.getSourceType()));
            result.setMainImage(item.getMainImage() != null && !item.getMainImage().startsWith("http") ? ossImageUrl + item.getMainImage() : item.getMainImage());

            // 1. 构建分类树
            ShopCategoryDto categoryDto;
            List<ShopCategoryItem> shopCategoryItemList = itemCategoryMap.getOrDefault(itemId, Collections.emptyList());
            if (CollUtil.isEmpty(shopCategoryItemList)) {
                // 如果没有分类，设置一个空的分类DTO
                categoryDto = new ShopCategoryDto();
            } else {
                // 默认根节点
                ShopCategoryDto root = new ShopCategoryDto();
                root.setId(0L);
                root.setName("全部");
                root.setLevel(0);

                // 商品对应的全部后台类目id
                List<Long> shopCategoryIds = shopCategoryItemList.stream().map(ShopCategoryItem::getShopCategoryId).toList();

                // 从已查询的分类中筛选出当前商品的分类
                List<ShopCategory> shopCategoryList = allCategories.stream()
                        .filter(category -> shopCategoryIds.contains(category.getId()))
                        .toList();

                if (CollUtil.isEmpty(shopCategoryList)) {
                    categoryDto = root;
                } else {
                    // 按父ID分组
                    Map<Long, List<ShopCategory>> shopCategoryMapOfPid = shopCategoryList.stream()
                            .collect(Collectors.groupingBy(ShopCategory::getPid));

                    // 获取根分类（父ID为0的分类）
                    List<ShopCategory> rootShopCategories = shopCategoryList.stream()
                            .filter(shopCategory -> shopCategory.getPid().equals(ShopCategory.ID_ROOT))
                            .toList();

                    if (CollUtil.isEmpty(rootShopCategories)) {
                        categoryDto = root;
                    } else {
                        // 获取第一个根分类
                        ShopCategory shopCategory = rootShopCategories.get(0);
                        ShopCategoryDto rootShopCategory = BeanUtil.copyProperties(shopCategory, ShopCategoryDto.class);

                        // 递归添加子分类
                        appendChildren(rootShopCategory, shopCategoryMapOfPid);

                        // 设置根节点的子分类
                        root.setChildren(Collections.singletonList(rootShopCategory));
                        categoryDto = root;
                    }
                }
            }

            // 直接设置分类树到结果对象
            result.setShopCategory(categoryDto);

            // 2. 查询商品规格
            List<ItemsSpecificationDetail> specs = itemsSpecificationDetailService.getItemSpecListByItemId(itemId);

            // 设置SKU相关信息
            List<Sku> skus = itemSkuMap.getOrDefault(itemId, Collections.emptyList());
            List<String> outSkuIds = itemOuterSkuIdMap.getOrDefault(itemId, Collections.emptyList());
            int stockQuantity = itemStockQuantityMap.getOrDefault(itemId, 0);

            // 计算第三方库存
            int thirdSkuStockQuantity = 0;
            String useCashGiftDesc = UseCashGiftEnum.NO.getDesc();

            for (Sku sku : skus) {
                // 计算第三方库存
                if (StrUtil.isNotBlank(sku.getOuterSkuId())) {
                    List<ThirdPartySkuStock> stocks = outerSkuStockMap.getOrDefault(sku.getOuterSkuId(), Collections.emptyList());
                    thirdSkuStockQuantity += stocks.stream().map(ThirdPartySkuStock::getAuthenticStock).reduce(0, Integer::sum);
                }

                // 设置佣金信息
                IntermediateInfo skuIntermediateInfo = skuIntermediateInfoMap.get(sku.getId());
                if (ObjUtil.isNotEmpty(skuIntermediateInfo)) {
                    appendCommissionInfo(result, skuIntermediateInfo);
                    if (ObjUtil.isNotEmpty(skuIntermediateInfo.getIsCashGift()) && skuIntermediateInfo.getIsCashGift().equals(UseCashGiftEnum.YES.getCode())) {
                        useCashGiftDesc = UseCashGiftEnum.YES.getDesc();
                    }
                }
            }

            result.setOuterSkuIds(outSkuIds);
            result.setStockQuantity(stockQuantity);
            result.setThirdSkuStockQuantity(thirdSkuStockQuantity);

            // 设置限售区域模板
            if (result.getRestrictedSalesAreaTemplateId() != null) {
                RestrictedSalesAreaTemplate template = templateMap.get(result.getRestrictedSalesAreaTemplateId());
                if (template != null) {
                    result.setRestrictedSalesAreaTemplateName(template.getName());
                }
            }

            // 设置规格信息
            result.setWhetherExistSpec(CollUtil.isNotEmpty(specs));
            result.setUseCashGiftDesc(useCashGiftDesc);

            resultList.add(result);
        }

        return resultList;
    }

    /**
     * 添加服务费相关信息
     *
     * @param item         商品信息
     * @param targetConfig 服务费配置
     */
    private void appendCommissionInfo(ItemsVo item, IntermediateInfo targetConfig) {
        targetConfig.checkFlag();
        var calculateType = IntermediateInfo.CalculateType.parse(targetConfig.getFlag());
        if (calculateType == null) {
            return;
        }
        switch (calculateType) {
            case FIXED_AMOUNT -> setByFixAmount(item, targetConfig);
            case BY_RATIO -> setByRatio(item, targetConfig);
        }
    }

    private void setByRatio(ItemsVo item, IntermediateInfo targetConfig) {
        Integer lowPrice = item.getLowPrice();
        Integer highPrice = item.getHighPrice();
        if (lowPrice == null || highPrice == null) {
            return;
        }

        item.setServiceProviderCommissionRate(targetConfig.getServiceProviderRate());
        item.setSubStoreCommissionRate(targetConfig.getFirstRate());
        item.setGuiderCommissionRate(targetConfig.getSecondRate());

        if (Objects.equals(lowPrice, highPrice)) {
            item.setServiceProviderCommission(calculateByRatio(targetConfig.getServiceProviderRate(), lowPrice, targetConfig.getVersion()));
            item.setSubStoreCommission(calculateByRatio(targetConfig.getFirstRate(), lowPrice, targetConfig.getVersion()));
            item.setGuiderCommission(calculateByRatio(targetConfig.getSecondRate(), lowPrice, targetConfig.getVersion()));
        } else {
            item.setServiceProviderCommission(calculateByRatio(targetConfig.getServiceProviderRate(), lowPrice, targetConfig.getVersion()) + "-" +
                    calculateByRatio(targetConfig.getServiceProviderRate(), highPrice, targetConfig.getVersion()));

            item.setSubStoreCommission(calculateByRatio(targetConfig.getFirstRate(), lowPrice, targetConfig.getVersion()) + "-" +
                    calculateByRatio(targetConfig.getFirstRate(), highPrice, targetConfig.getVersion()));

            item.setGuiderCommission(calculateByRatio(targetConfig.getSecondRate(), lowPrice, targetConfig.getVersion()) + "-" +
                    calculateByRatio(targetConfig.getSecondRate(), highPrice, targetConfig.getVersion()));
        }
    }

    private void setByFixAmount(ItemsVo item, IntermediateInfo targetConfig) {
        item.setServiceProviderCommission(calculateByFix(targetConfig.getServiceProviderFee()));

        item.setSubStoreCommission(calculateByFix(targetConfig.getFirstFee()));

        item.setGuiderCommission(calculateByFix(targetConfig.getSecondFee()));
    }

    private String calculateByFix(Long price) {
        if (price == null) {
            return "0.00";
        }

        var result = new BigDecimal(price)
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return result.toPlainString();
    }

    private String calculateByRatio(Long ratio, Integer price, Integer version) {
        if (price == null) {
            return "0.00";
        }

        if (ratio == null) {
            ratio = 0L;
        }

        int percent = 10000;
        if (version < 3) {
            percent = 100;
        }

        var result = new BigDecimal(price)
                .multiply(new BigDecimal(ratio))
                .divide(new BigDecimal(percent), 6, RoundingMode.HALF_UP)
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

        return result.toPlainString();
    }

    private Map<String, Object> convertReqToMap(ItemPageDto req) {
        List<Long> itemIdList = req.getItemIdList();
        if (CollUtil.isEmpty(itemIdList)) {
            itemIdList = new ArrayList<>();
        }
        if (req.getItemId() != null) {
            itemIdList.add(req.getItemId());
        }

        // 仅当入参不根据id查询时 才通过OuterSkuIds构建itemId集合
        if (CollUtil.isNotEmpty(req.getOuterSkuIds()) && CollUtil.isEmpty(req.getItemIdList())) {
            for (String outerSkuId : req.getOuterSkuIds()) {
                List<Sku> skus = skuReadService.findSkuByOuterSkuId(req.getShopId(), outerSkuId).getResult();
                if (CollUtil.isEmpty(skus)) {
                    continue;
                }
                List<Long> itemIds = skus.stream().map(Sku::getItemId).toList();
                itemIdList.addAll(itemIds);
            }
            // 填了sku但是没有找到对应的商品id, 设置一个-1进去让sql查询不到
            if (CollUtil.isEmpty(itemIdList)) {
                itemIdList.add(-1L);
            }
        }
        req.setItemIdList(itemIdList);
        return req.toMap();
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePrice(ItemUpdateReqDto req) {
        checkParam(req);
        Item item = new Item();
        item.setId(req.getId());
        item.setLowPrice(req.getPrice());
        item.setHighPrice(req.getPrice());
        itemWriteService.update(item).getResult();
        List<Sku> skus = skuReadService.findSkusByItemId(item.getId()).getResult();
        for (Sku sku : skus) {
            sku.setPrice(req.getPrice());
            skuWriteService.updateSku(sku);
        }
        publishItemUpdateEvent(Collections.singletonList(req.getId()));
        return true;
    }

    private void checkParam(ItemUpdateReqDto req) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null) {
            throw new ApiException(ResultCode.UN_AUTHORIZED);
        }
        if (ObjUtil.isEmpty(req.getShopId())) {
            req.setShopId(UserUtil.getCurrentShopId());
        }
        if (ObjUtil.isEmpty(req.getId())) {
            throw new ApiException("商品id不能为空");
        }
        Item item = itemReadService.findById(req.getId()).getResult();
        if (ObjUtil.isEmpty(item)) {
            throw new ApiException("未找到对应的商品信息");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStockQuantity(ItemUpdateReqDto req) {
        checkParam(req);
        if (req.getStockQuantity() < 0) {
            throw new ApiException("商品平台可售库存不能小于0");
        }

        Item item = new Item();
        item.setId(req.getId());
        item.setStockQuantity(req.getStockQuantity());
        if (req.getStockQuantity() == 0) {
            item.setSellOutStatus(ItemSellOutStatusEnum.SOLD_OUT.getCode());
        }
        itemWriteService.update(item);
        List<Sku> skus = skuReadService.findSkusByItemId(item.getId()).getResult();
        for (Sku sku : skus) {
            sku.setStockQuantity(req.getStockQuantity());
            skuWriteService.updateSku(sku);
        }
        publishItemUpdateEvent(Collections.singletonList(req.getId()));
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteShopCategory(ItemUpdateReqDto req) {
        checkParams(req);
        shopCategoryLogic.deleteItemShopCategoryByItemId(req.getShopId(), req.getIds());
        publishItemUpdateEvent(req.getIds());
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(ItemUpdateReqDto req) {
        List<Long> cloneByStream = ObjectUtil.cloneByStream(req.getIds());
        checkParams(req);
        for (Long itemId : req.getIds()) {
            Item item = itemReadService.findById(itemId).getResult();
            if (item.getStatus().equals(ItemStatusEnum.DELETED.getCode())) {
                throw new ApiException("已删除商品不允许更新状态");
            }
            // 库存不足则不允许上架
            if (item.getStockQuantity() <= 0 && req.getStatus() == 1) {
                throw new ApiException("库存不足 不允许上架");
            }
            if (req.getStatus().equals(ItemStatusEnum.DELETED.getCode())) {
                if (item.getStatus().equals(ItemStatusEnum.ON_SHELF.getCode())) {
                    throw new ApiException("请先下架商品");
                }
                // 删除逻辑
                Integer itemType = item.getType();
                if (itemType.equals(ItemTypeEnum.COMMON.getCode())) {
                    //如果是普通商品，则查询是否有关联到的组合商品，如果有则一并删除
                    log.info("删除的组合商品id {}", itemId);
                    List<SkuComboRelation> skuComboRelationList = skuComboRelationService.getSkuComboRelationListByComboItemId(itemId);
                    List<Long> skuIdList = skuComboRelationList.stream().map(SkuComboRelation::getSkuId).distinct().toList();
                    List<Sku> skuList = skuReadService.findSkusByIds(skuIdList).getResult();
                    List<Long> itemIdList = skuList.stream().map(Sku::getItemId).distinct().toList();
                    log.info("查询到关联的子商品id {}", JSONUtil.toJsonStr(itemIdList));
                    cloneByStream.addAll(itemIdList);
                }
            }
            List<GbActivityConfigSku> list = gbActivityConfigSkuReadService.findByItemId(itemId);
            if (CollUtil.isNotEmpty(list) && req.getStatus().equals(ItemStatusEnum.OFF_SHELF.getCode())) {
                log.info("存在参与活动的商品,不允许下架 {}", JSONObject.toJSONString(list));
                throw new RuntimeException("存在参与活动的商品,不允许下架");
            }
        }
        itemWriteService.batchUpdateStatus(cloneByStream, req.getStatus());
        publishItemUpdateEvent(cloneByStream);
        return true;
    }

    private void checkParams(ItemUpdateReqDto req) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null) {
            throw new ApiException(ResultCode.UN_AUTHORIZED);
        }
        if (ObjUtil.isEmpty(req.getShopId())) {
            req.setShopId(UserUtil.getCurrentShopId());
        }
        if (CollUtil.isEmpty(req.getIds())) {
            throw new ApiException("商品id列表不能为空");
        }
        List<Item> itemList = itemReadService.findByIds(req.getIds()).getResult();
        if (itemList.size() != req.getIds().size()) {
            throw new ApiException("需要修改的商品数量部分未查到");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSelloutStatus(ItemUpdateReqDto req) {
        checkParams(req);
        if (ObjUtil.isEmpty(req.getSellOutStatus())) {
            throw new ApiException("停售状态不能为空");
        }
        itemWriteService.batchUpdateSellOutStatusByIds(req.getIds(), req.getSellOutStatus());
        publishItemUpdateEvent(req.getIds());
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateShopCategory(ItemUpdateReqDto req) {
        checkParams(req);
        List<ShopCategoryItemsDto> shopCategoryItemsList = req.getShopCategoryItemsList();
        if (CollUtil.isEmpty(shopCategoryItemsList)) {
            throw new ApiException("需要修改的店铺分类列表不能为空");
        }
        // 删除已有的店铺商品分类
        deleteShopCategory(req);
        ShopCategoryItemsDto root = new ShopCategoryItemsDto();
        root.setShopCategoryId(0L);
        // 添加默认节点  全部
        shopCategoryItemsList.add(root);
        // 转换后的店铺商品分类列表
        List<ShopCategoryItem> shopCategoryItemList = shopCategoryItemsList.stream()
                .sorted(Comparator.comparing(ShopCategoryItemsDto::getShopCategoryId))
                .map(shopCategoryItemsDto -> {
                    ShopCategoryItem shopCategoryItem = BeanUtil.copyProperties(shopCategoryItemsDto, ShopCategoryItem.class);
                    shopCategoryItem.setShopId(req.getShopId());
                    return shopCategoryItem;
                }).toList();
        shopCategoryLogic.saveShopCategoryItems(req.getIds(), shopCategoryItemList);
        publishItemUpdateEvent(req.getIds());
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRestrictedSales(ItemUpdateReqDto req) {
        checkParams(req);
        if (ObjUtil.isEmpty(req.getRestrictedSalesAreaTemplateId())) {
            throw new ApiException("可售地区模板id不能为空");
        }
        itemWriteService.updateRestrictedSalesAreaTemplate(req.getShopId(), req.getIds(), req.getRestrictedSalesAreaTemplateId());
        publishItemUpdateEvent(req.getIds());
        return Boolean.TRUE;
    }

    public void publishItemUpdateEvent(List<Long> itemIds) {
        for (Long itemId : itemIds) {
            EventSender.publish(new ItemUpdateEvent(itemId));
        }
    }

    public List<ItemSortedVo> sortedList(ItemSortRequestVo itemSortRequestVo) {
        // 构建查询条件
        NativeSearchQueryBuilder query = new NativeSearchQueryBuilder();
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        if (shopId == null) {
            throw new ApiException("请先登录");
        }
        queryBuilder.must(QueryBuilders.termQuery("shopId", shopId));
        queryBuilder.must(QueryBuilders.termQuery("status", "1"));
        if (itemSortRequestVo.getCategoryId() != null) {
            queryBuilder.must(QueryBuilders.termsQuery("shopCategoryIds", Lists.newArrayList(itemSortRequestVo.getCategoryId())));
        }
        query.withQuery(queryBuilder);
        FieldSortBuilder cateIndexAscSort = SortBuilders.fieldSort("categoryIndex").order(SortOrder.ASC);
        FieldSortBuilder indexAscSort = SortBuilders.fieldSort("index").order(SortOrder.ASC);
        FieldSortBuilder updatedAtDescSort = SortBuilders.fieldSort("updatedAt").order(SortOrder.DESC);
        query.withSorts(cateIndexAscSort, indexAscSort, updatedAtDescSort);
        NativeSearchQuery build = query.build();
        log.info("查询的语句 {}", build.getQuery());
        SearchHits<ItemsEsIndex> searchHits = elasticsearchRestTemplate.search(build, ItemsEsIndex.class);
        long total = searchHits.getTotalHits();

        if (total == 0) {
            return new ArrayList<>();
        }
        List<ItemSortedVo> inl = new ArrayList<>();
        for (SearchHit<ItemsEsIndex> hit : searchHits.getSearchHits()) {
            ItemsEsIndex itemsEsIndex = hit.getContent();
            ItemSortedVo itemVo = new ItemSortedVo();
            itemVo.setId(itemsEsIndex.getId());
            itemVo.setName(itemsEsIndex.getName());
            itemVo.setPrice(itemsEsIndex.getPrice());
            itemVo.setStockQuantity(itemsEsIndex.getStockQuantity());
            itemVo.setMainImage(itemsEsIndex.getMainImage());
            inl.add(itemVo);
        }
        return inl;
    }

    @Transactional(rollbackFor = Exception.class)
    public void sortedListUpdate(ItemSortRequestVo itemSortRequestVo) {
        if (itemSortRequestVo.getCategoryId() == null) {
            throw new ApiException("分类id不能为空");
        }
        List<Long> itemIds = itemSortRequestVo.getItemIds();
        if (CollUtil.isEmpty(itemIds)) {
            throw new ApiException("商品id列表不能为空");
        }
        Response<List<Item>> items = itemReadService.findByIds(itemIds);
        if (!items.isSuccess()) {
            throw new ApiException(items.getError());
        }
        List<Item> itemList = items.getResult();
        if (CollUtil.isEmpty(itemList)) {
            throw new ApiException("商品不存在");
        }
        Map<Long, Item> map = itemList.stream().collect(Collectors.toMap(Item::getId, Function.identity()));
        Integer index = 0;
        for (Long itemId : itemIds) {
            index++;
            Item item = map.get(itemId);
            if (item == null) {
                continue;
            }
            Item updateItem = new Item();
            updateItem.setId(itemId);
            updateItem.setIndex(index);
            itemWriteService.update(updateItem);
        }
        for (Long itemId : itemIds) {
            EventSender.publish(new ItemUpdateEvent(itemId));
        }
    }

    public Long countItemByBrand(Long shopId, Long brandId) {
        return itemReadService.countByBrand(shopId, brandId);
    }

    /**
     * 递归添加子分类
     *
     * @param rootShopCategory 当前分类节点
     * @param shopCategoryMapOfPid 按父ID分组的分类映射
     */
    private void appendChildren(ShopCategoryDto rootShopCategory, Map<Long, List<ShopCategory>> shopCategoryMapOfPid) {
        List<ShopCategory> shopCategoryList = shopCategoryMapOfPid.get(rootShopCategory.getId());
        if (CollUtil.isEmpty(shopCategoryList)) {
            return;
        }
        List<ShopCategoryDto> shopCategoryDtoList = BeanUtil.copyToList(shopCategoryList, ShopCategoryDto.class);
        rootShopCategory.setChildren(shopCategoryDtoList);
        for (ShopCategoryDto shopCategoryDto : shopCategoryDtoList) {
            appendChildren(shopCategoryDto, shopCategoryMapOfPid);
        }
    }

    public List<ComboBoxVo> getCashGiftDropDownBox() {
        List<ComboBoxVo> comboBoxes = new ArrayList<>();
        for (UseCashGiftEnum value : UseCashGiftEnum.values()) {
            ComboBoxVo comboBoxVo = new ComboBoxVo();
            comboBoxVo.setId(value.getCode());
            comboBoxVo.setName(value.getDesc());
            comboBoxes.add(comboBoxVo);
        }
        return comboBoxes;
    }
}
