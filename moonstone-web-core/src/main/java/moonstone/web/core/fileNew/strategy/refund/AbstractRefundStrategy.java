package moonstone.web.core.fileNew.strategy.refund;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.ImmutableMap;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.api.TokenProvider;
import io.terminus.pay.model.RefundParams;
import io.terminus.pay.model.TradeRequest;
import io.terminus.pay.service.PayChannel;
import io.terminus.pay.service.RefundOperation;
import io.terminus.pay.wechatpay.app.component.AppWechatpayTokenProvider;
import io.terminus.pay.wechatpay.common.channel.WechatpayChannel;
import io.terminus.pay.wechatpay.common.model.token.AppWxToken;
import io.terminus.pay.wechatpay.common.model.token.JsapiWxToken;
import io.terminus.pay.wechatpay.common.model.token.QrWxToken;
import io.terminus.pay.wechatpay.common.model.token.WxToken;
import io.terminus.pay.wechatpay.jsapi.component.JsapiWechatpayTokenProvider;
import io.terminus.pay.wechatpay.qr.component.QrWechatpayTokenProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.*;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.event.OrderRefundEvent;
import moonstone.order.api.TradeBatchNoGenerator;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.model.*;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.fileNew.dto.*;
import moonstone.web.core.fileNew.logic.OrderRefundLogic;
import moonstone.web.core.fileNew.logic.RefundLogic;
import moonstone.web.core.fileNew.logic.RefundProcessRecordLogic;
import moonstone.web.core.fileNew.logic.RefundReturnDetailLogic;
import moonstone.web.core.fileNew.strategy.api.RefundStrategy;
import moonstone.web.core.order.api.RefundParamsMaker;
import moonstone.web.core.refund.event.RefundSuccessCallbackEvent;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.ParameterizedType;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractRefundStrategy implements RefundStrategy {

	@Resource
	private RefundLogic refundLogic;

	@Resource
	private OrderRefundLogic orderRefundLogic;

	@Resource
	private RefundReturnDetailLogic refundReturnDetailLogic;

	@Resource
	private RefundProcessRecordLogic refundProcessRecordLogic;

	@Resource
	private TradeBatchNoGenerator tradeBatchNoGenerator;

	@Resource
	private UserReadService<User> userReadService;

	@Resource
	private JsapiWechatpayTokenProvider jsapiWechatpayTokenProvider;

	@Resource
	private QrWechatpayTokenProvider qrWechatpayTokenProvider;

	@Resource
	private AppWechatpayTokenProvider appWechatpayTokenProvider;

	@Resource
	private ChannelRegistry channelRegistry;

	@Resource
	private RefundParamsMaker refundParamsMaker;

	@Resource
	private TokenShopPayInfo tokenShopPayInfo;


	@Override
	public Long apply(RefundApplyDto refundApplyDto) {
		ShopOrder shopOrder = refundApplyDto.getShopOrder();
		SkuOrder skuOrder = refundApplyDto.getSkuOrder();
		Payment payment = refundApplyDto.getPayment();
		Integer refundStatue = refundApplyDto.getRefundType() == Refund.RefundType.AFTER_SALE_RETURN.value() ? OrderStatus.RETURN_APPLY.getValue() : OrderStatus.REFUND_APPLY.getValue();
		Refund refund = new Refund();
		refund.setRefundType(refundApplyDto.getRefundType());
		refund.setFee(shopOrder.getFee());
		refund.setSourceType(skuOrder.getShippingWarehouseType());
		refund.setShopId(shopOrder.getShopId());
		refund.setShopName(shopOrder.getShopName());
		refund.setBuyerId(shopOrder.getBuyerId());
		refund.setBuyerName(shopOrder.getBuyerName());
		refund.setChannel(payment.getChannel());
		refund.setPaySerialNo(payment.getPaySerialNo());
		refund.setRefundAccountNo(payment.getPayAccountNo());
		// 余额退还金额 暂时没用 都是0
		refund.setBalance(0);
		refund.setStatus(refundStatue);
		refund.setPaymentId(payment.getId());
		refund.setReasonType(refundApplyDto.getReasonType());
		refund.setBuyerNote(refundApplyDto.getBuyerNote());
		refund.setBuyerReceivedStatus(refundApplyDto.getBuyerReceivedStatus());
		refund.setShipping(skuOrder.getShipping());
		refund.setImagesJson(refundApplyDto.getImagesJson());
		refund.setTradeNo(payment.getOutId());
		// 保存退款单信息
		refundLogic.save(refund);
		OrderRefund orderRefund = new OrderRefund();
		orderRefund.setRefundId(refund.getId());
		orderRefund.setStatus(refundStatue);
		orderRefund.setOrderType(refundApplyDto.getOrderType());
		orderRefund.setOrderId(refundApplyDto.getOrderId());
		// 保存订单和退款单关联信息
		orderRefundLogic.save(orderRefund);
		// 生成退款详情
		RefundReturnDetail refundReturnDetail = new RefundReturnDetail();
		refundReturnDetail.setShopId(refund.getShopId());
		refundReturnDetail.setRefundId(refund.getId());
		refundReturnDetail.setApplyReceiptType(RefundReturnDetailApplyReceiptTypeEnum.WAIT_RECEIPT.getCode());
		refundReturnDetail.setApplyReceiptMsg("");
		refundReturnDetailLogic.save(refundReturnDetail);
		refundApplyDto.setRefund(refund);
		doApplyRecord(refundApplyDto);
		return refund.getId();
	}

	public abstract void doApplyRecord(RefundApplyDto refundApplyDto);

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean applyAgree(RefundApplyAgreeDto refundApplyAgreeDto) {
		Long refundId = refundApplyAgreeDto.getRefundId();
		Refund refund = refundApplyAgreeDto.getRefund();
		if (refund.getStatus().equals(OrderStatus.REFUND_APPLY.getValue()) || refund.getStatus().equals(OrderStatus.RETURN_APPLY.getValue())) {
			if (StrUtil.isBlank(refund.getOutId()) || PaymentChannelEnum.ALLINPAY_YST.getCode().equals(refund.getChannel())) {
				String outId = tradeBatchNoGenerator.generateBatchNo(new Date(), refund.getId());
				refund.setOutId(outId);
			}
			if (refund.getRefundType().equals(Refund.RefundType.AFTER_SALE_RETURN.value())) {
				Map<String, Object> query = new HashMap<>();
				query.put("refundId", refundId);
				RefundReturnDetail refundReturnDetail = refundReturnDetailLogic.getOne(query);
				if (refundReturnDetail != null) {
					refundReturnDetail.setSellerAddress(refundApplyAgreeDto.getSellerAddress());
					refundReturnDetailLogic.update(refundReturnDetail);
				}
				refund.setStatus(OrderStatus.RETURN_APPLY_AGREED.getValue());
			} else {
				refund.setStatus(OrderStatus.REFUND_APPLY_AGREED.getValue());
			}
			refundLogic.update(refund);
			doApplyAgreeRecord(refundApplyAgreeDto);
			if (refund.getRefundType().equals(Refund.RefundType.AFTER_SALE_RETURN.value())) {
				EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.RETURN_APPLY_AGREE.toOrderOperation()));
			}else {
				EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.REFUND_APPLY_AGREE.toOrderOperation()));
			}
		} else {
			throw new ApiException("当前售后单状态不支持同意售后操作");
		}
		return true;
	}

	public void doApplyAgreeRecord(RefundApplyAgreeDto refundApplyAgreeDto) {
		// 商家同意退款申请
		Refund refund = refundApplyAgreeDto.getRefund();
		CommonUser currentUser = UserUtil.getCurrentUser();
		RefundProcessRecord refundProcessRecord = new RefundProcessRecord();
		refundProcessRecord.setShopId(refund.getShopId());
		refundProcessRecord.setRefundId(refund.getId());
		refundProcessRecord.setRefundRound(RefundProcessRecordRoundEnum.REFUND_APPLY_AGREE.getCode());
		if (refund.getRefundType() == Refund.RefundType.AFTER_SALE_RETURN.value()) {
			// 退货退款类型
			refundProcessRecord.setType(RefundProcessRecordTypeEnum.SELLER_RETURN_APPLY_AGREE.getCode());
		} else {
			refundProcessRecord.setType(RefundProcessRecordTypeEnum.SELLER_REFUND_APPLY_AGREE.getCode());
		}
		if (ObjUtil.isNotEmpty(currentUser)) {
			refundProcessRecord.setOperatorId(currentUser.getId());
			refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.SELLER.getCode());
			refundProcessRecord.setOperatorName(RefundProcessRecordOperatorTypeEnum.SELLER.getDesc());
		}else {
			refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.SYSTEM.getCode());
			refundProcessRecord.setOperatorName(RefundProcessRecordOperatorTypeEnum.SYSTEM.getDesc());
		}
		refundProcessRecord.setOperationTime(new Date());
		refundProcessRecord.setRefundFee(refund.getFee());
		refundProcessRecord.setRefundType(refund.getRefundType());
		refundProcessRecordLogic.save(refundProcessRecord);
	}

	@Override
	public Boolean applyReject(RefundApplyRejectDto refundApplyRejectDto) {
		Long refundId = refundApplyRejectDto.getRefundId();
		Refund refund = refundApplyRejectDto.getRefund();
		if (refund.getStatus().equals(OrderStatus.REFUND_APPLY.getValue()) || refund.getStatus().equals(OrderStatus.RETURN_APPLY.getValue()) || refund.getStatus().equals(OrderStatus.RETURN.getValue())) {
			OrderEvent event = null;
			if (refund.getRefundType().equals(Refund.RefundType.AFTER_SALE_RETURN.value())) {
				// 退货退款类型
				event = OrderEvent.RETURN_APPLY_REJECT;
			} else {
				event = OrderEvent.REFUND_APPLY_REJECT;
			}
			refund.setSellerNote(refundApplyRejectDto.getSellerNote());
			if (refund.getRefundType().equals(Refund.RefundType.AFTER_SALE_RETURN.value())) {
				refund.setStatus(OrderStatus.RETURN_APPLY_REJECTED.getValue());
			} else {
				refund.setStatus(OrderStatus.REFUND_APPLY_REJECTED.getValue());
			}
			refundLogic.update(refund);
			doApplyRejectRecord(refundApplyRejectDto);
			EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, event.toOrderOperation()));
		} else {
			throw new ApiException("当前售后单状态不支持拒绝售后操作");
		}
		return true;
	}

	public void doApplyRejectRecord(RefundApplyRejectDto refundApplyRejectDto) {
		// 商家拒绝退款申请
		Refund refund = refundApplyRejectDto.getRefund();
		Map<String, Object> content = new HashMap<>();
		CommonUser currentUser = UserUtil.getCurrentUser();
		RefundProcessRecord refundProcessRecord = new RefundProcessRecord();
		refundProcessRecord.setShopId(refund.getShopId());
		refundProcessRecord.setRefundId(refund.getId());
		refundProcessRecord.setRefundRound(RefundProcessRecordRoundEnum.REFUND_APPLY_REJECT.getCode());
		if (refund.getRefundType() == Refund.RefundType.AFTER_SALE_RETURN.value()) {
			// 退货退款类型
			refundProcessRecord.setType(RefundProcessRecordTypeEnum.SELLER_RETURN_APPLY_REJECT.getCode());
		} else {
			refundProcessRecord.setType(RefundProcessRecordTypeEnum.SELLER_REFUND_APPLY_REJECT.getCode());
		}
		content.put(RefundProcessRecordContentEnum.SELLER_NOTE.getCode(), refund.getSellerNote());
		refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.SELLER.getCode());
		refundProcessRecord.setOperatorId(currentUser.getId());
		refundProcessRecord.setOperatorName(RefundProcessRecordOperatorTypeEnum.SELLER.getDesc());
		refundProcessRecord.setOperationTime(new Date());
		refundProcessRecord.setRefundFee(refund.getFee());
		refundProcessRecord.setRefundType(refund.getRefundType());
		refundProcessRecord.setContent(JSONUtil.toJsonStr(content));
		refundProcessRecordLogic.save(refundProcessRecord);
	}

	@Override
	public Boolean uploadExpressInfo(RefundUploadExpressInfoDto refundUploadExpressInfoDto) {
		Long refundId = refundUploadExpressInfoDto.getRefundId();
		Refund refund = refundUploadExpressInfoDto.getRefund();
		Map<String, Object> query = new HashMap<>();
		query.put("refundId", refundId);
		RefundReturnDetail refundReturnDetail = refundReturnDetailLogic.getOne(query);
		if (refundReturnDetail != null) {
			refundReturnDetail.setExpressNo(refundUploadExpressInfoDto.getExpressNo());
			refundReturnDetail.setExpressCode(refundUploadExpressInfoDto.getExpressCode());
			refundReturnDetail.setExpressCompany(refundUploadExpressInfoDto.getExpressCompany());
			refundReturnDetailLogic.update(refundReturnDetail);
			refundUploadExpressInfoDto.setRefundReturnDetail(refundReturnDetail);
		}
		refund.setStatus(OrderStatus.RETURN.getValue());
		refundLogic.update(refund);
		EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.RETURN.toOrderOperation()));
		doUploadExpressInfoRecord(refundUploadExpressInfoDto);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean refundHandle(RefundHandleDto refundHandleDto) {
		Long refundId = refundHandleDto.getRefundId();
		Refund refund = refundHandleDto.getRefund();
		// 为了避开一些退款系统的限制, 将退款批次日期改为单日
		if (StrUtil.isEmpty(refund.getOutId()) || PaymentChannelEnum.ALLINPAY_YST.getCode().equals(refund.getChannel())) {
			refund.setOutId(tradeBatchNoGenerator.generateBatchNo(new Date(), refund.getId()));
			Refund updateOutId = new Refund();
			updateOutId.setId(refund.getId());
			updateOutId.setOutId(refund.getOutId());
			refundLogic.update(updateOutId);
		}
		// 非真实支付 无支付单
		if (Objects.isNull(refund.getPaymentId()) || refund.getPaymentId() <= 0) {
			log.info("非真实支付 无支付单");
			EventSender.sendApplicationEvent(new RefundSuccessCallbackEvent(refundId, UUID.randomUUID().toString(), LocalDateTime.now()));
			return true;
		}
		checkTheCerts(channelRegistry.findChannel(refund.getChannel()), refund.getShopId());
		PayChannel payChannel = channelRegistry.findChannel(refund.getChannel());
		log.info("发起退款的渠道为 {}", JSONUtil.toJsonStr(payChannel));
		RefundParams refundParams = refundParamsMaker.makeParams(refund);
		log.info("发起退款 请求参数 {}", JSONUtil.toJsonStr(refundParams));
		TradeRequest request = payChannel.refundRequest(refundParams);
		log.info("退款调用结果 {}", JSONUtil.toJsonStr(request));
		if (!request.isSuccess() || StrUtil.isNotBlank(request.getError())) {
			// 系统退款失败
			doRefundHandlerFailedRecord(refundHandleDto, request.getError());
			return false;
		}
		// 退款处理中
		refund.setStatus(OrderStatus.REFUND_PROCESSING.getValue());
		refundLogic.update(refund);
		EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.REFUND.toOrderOperation()));
		return true;
	}

	public void doRefundHandlerFailedRecord(RefundHandleDto refundHandleDto, String errorMsg) {
		// 退款失败
		Refund refund = refundHandleDto.getRefund();
		refund.setRefundAt(new Date());
		refund.setStatus(OrderStatus.REFUND_FAIL.getValue());
		refundLogic.update(refund);
		Map<String, Object> query = new HashMap<>();
		query.put("refundId", refund.getId());
		RefundReturnDetail refundReturnDetail = refundReturnDetailLogic.getOne(query);
		if (refundReturnDetail != null) {
			refundReturnDetail.setRefundReceiptType(RefundReturnDetailRefundReceiptTypeEnum.RECEIPT_FAIL.getCode());
			refundReturnDetailLogic.update(refundReturnDetail);
		}
		RefundProcessRecord refundProcessRecord = new RefundProcessRecord();
		refundProcessRecord.setShopId(refund.getShopId());
		refundProcessRecord.setRefundId(refund.getId());
		refundProcessRecord.setRefundRound(RefundProcessRecordRoundEnum.REFUND_HANDLE_FAIL.getCode());
		refundProcessRecord.setType(RefundProcessRecordTypeEnum.SYSTEM_REFUND_FAIL.getCode());
		refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.SYSTEM.getCode());
		refundProcessRecord.setOperationTime(new Date());
		refundProcessRecord.setRefundFee(refund.getFee());
		refundProcessRecord.setRefundType(refund.getRefundType());
		Map<String, Object> content = new HashMap<>();
		content.put(RefundProcessRecordContentEnum.REFUND_RESULT.getCode(), "退款失败");
		content.put(RefundProcessRecordContentEnum.REFUND_FAIL_REASON.getCode(), errorMsg);
		refundProcessRecord.setContent(JSONUtil.toJsonStr(content));
		refundProcessRecordLogic.save(refundProcessRecord);
	}

	/**
	 * check the refund certs, only wechat-pay require now
	 *
	 * @param refundOperation pay action
	 * @param shopId          shopId to gain the pay-token
	 */
	private void checkTheCerts(RefundOperation refundOperation, Long shopId) {
		if (!(refundOperation instanceof WechatpayChannel)) {
			return;
		}
		final Map<Class<?>, TokenProvider<? extends WxToken>> tokenProviderMap
				= ImmutableMap.of(JsapiWxToken.class, jsapiWechatpayTokenProvider
				, AppWxToken.class, appWechatpayTokenProvider
				, QrWxToken.class, qrWechatpayTokenProvider
		);
		// extract token type from refundOperation
		final ParameterizedType parameterizedType = (ParameterizedType) refundOperation.getClass().getGenericSuperclass();
		final Class<?> payTokenType = (Class<?>) parameterizedType.getActualTypeArguments()[0];
		try {
			// extract pay token from token type and provider
			//Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
			final WxToken wxToken = tokenProviderMap.get(payTokenType).findToken(shopId.toString());
			log.debug("{} refund check certs token [shopId => {}, type => {}, appId => {}, returnUrl => {}, refundGateway => {}, refundNotifyUrl => {}, certFilePath => {}]", LogUtil.getClassMethodName()
					, shopId
					, wxToken.getClass().getSimpleName()
					, wxToken.getAppId()
					, wxToken.getReturnUrl()
					, wxToken.getRefundGateway()
					, wxToken.getRefundNotifyUrl()
					, wxToken.getCertFilePath());
			// check the cert file
			final File certFile = new File(wxToken.getCertFilePath());
			if (!certFile.exists()) {
				throw new RuntimeException(Translate.of("支付证书未配备, 请上传支付证书[shopId=>%s]", shopId));
			}
		} catch (NullPointerException nullPointerException) {
			log.error("{} fail to check the pay-token cause the payToken[shopId => {}] invalidate", LogUtil.getClassMethodName(), shopId, nullPointerException);
			throw new RuntimeException(Translate.of("支付信息查找失败, 请检查支付信息[shopId=>%s]是否已经配备", shopId));
		}
	}

	public void doUploadExpressInfoRecord(RefundUploadExpressInfoDto refundUploadExpressInfoDto) {
		// 上传物流快递信息
		Refund refund = refundUploadExpressInfoDto.getRefund();
		Map<String, Object> content = new HashMap<>();
		CommonUser currentUser = UserUtil.getCurrentUser();
		RefundProcessRecord refundProcessRecord = new RefundProcessRecord();
		refundProcessRecord.setShopId(refund.getShopId());
		refundProcessRecord.setRefundId(refund.getId());
		refundProcessRecord.setRefundRound(RefundProcessRecordRoundEnum.UPLOAD_EXPRESS_NO.getCode());
		// 退货退款类型
		if (currentUser.getId().equals(refund.getBuyerId())) {
			User user = userReadService.findById(currentUser.getId()).getResult();
			// 买家上传物流单号
			refundProcessRecord.setType(RefundProcessRecordTypeEnum.BUYER_RETURN_EXPRESS.getCode());
			refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.BUYER.getCode());
			refundProcessRecord.setOperatorName(user.getName());
		} else {
			// 卖家上传物流信息
			refundProcessRecord.setType(RefundProcessRecordTypeEnum.SELLER_UPLOAD_EXPRESS_INFO.getCode());
			refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.SELLER.getCode());
			refundProcessRecord.setOperatorName(RefundProcessRecordOperatorTypeEnum.SELLER.getDesc());
		}
		content.put(RefundProcessRecordContentEnum.EXPRESS_COMPANY.getCode(), refundUploadExpressInfoDto.getExpressCompany());
		content.put(RefundProcessRecordContentEnum.EXPRESS_NO.getCode(), refundUploadExpressInfoDto.getExpressNo());
		content.put(RefundProcessRecordContentEnum.REFUND_ADDRESS.getCode(), refundUploadExpressInfoDto.getRefundReturnDetail().getSellerAddress());
		refundProcessRecord.setOperatorId(currentUser.getId());
		refundProcessRecord.setOperationTime(new Date());
		refundProcessRecord.setContent(JSONUtil.toJsonStr(content));
		refundProcessRecordLogic.save(refundProcessRecord);
	}
}
