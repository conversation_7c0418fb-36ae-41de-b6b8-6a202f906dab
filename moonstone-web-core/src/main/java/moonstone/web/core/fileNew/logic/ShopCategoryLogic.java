package moonstone.web.core.fileNew.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCategoryCacher;
import moonstone.category.model.ShopCategory;
import moonstone.category.model.ShopCategoryItem;
import moonstone.category.service.ShopCategoryItemReadService;
import moonstone.category.service.ShopCategoryItemWriteService;
import moonstone.category.service.ShopCategoryReadService;
import moonstone.category.service.ShopCategoryWriteService;
import moonstone.common.api.ResultCode;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.web.core.fileNew.dto.ShopCategoryDto;
import moonstone.web.core.fileNew.dto.ShopCategoryUpdateReqDto;
import moonstone.web.core.fileNew.vo.ShopCategoryTreeVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShopCategoryLogic {

    @Resource
    private ShopCategoryReadService shopCategoryReadService;

    @Resource
    private ShopCategoryWriteService shopCategoryWriteService;

    @Resource
    private ShopCategoryItemReadService shopCategoryItemReadService;

    @Resource
    private ShopCategoryItemWriteService shopCategoryItemWriteService;

    @Resource
    private ShopCategoryCacher shopCategoryCacher;


    public List<ShopCategoryTreeVo> getDefaultTree(Long shopId) {
        if (shopId == null) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            if (currentUser == null) {
                throw new ApiException(ResultCode.UN_AUTHORIZED);
            }
            shopId = currentUser.getShopId();
        }
        List<ShopCategory> rootCategories = shopCategoryReadService.findChildrenByShopId(shopId).getResult();
        if (CollUtil.isEmpty(rootCategories)) {
            return Collections.emptyList();
        }
        List<ShopCategoryTreeVo> shopCategoryTreeVos = BeanUtil.copyToList(rootCategories, ShopCategoryTreeVo.class);
        for (ShopCategoryTreeVo root : shopCategoryTreeVos) {
            setChildrenNode(root);
        }
        return shopCategoryTreeVos;
    }

    private void setChildrenNode(ShopCategoryTreeVo root) {
        List<ShopCategory> nodeCategories = shopCategoryReadService.findChildrenByShopIdAndPid(root.getShopId(), root.getId()).getResult();
        if (CollUtil.isEmpty(nodeCategories)) {
            return;
        }
        List<ShopCategoryTreeVo> childrenList = BeanUtil.copyToList(nodeCategories, ShopCategoryTreeVo.class);
        root.setChildren(childrenList);
        for (ShopCategoryTreeVo node : childrenList) {
            setChildrenNode(node);
        }
    }


    public ShopCategoryDto getItemShopCategoryList(Long itemId) {
        List<ShopCategoryItem> shopCategoryItemList = shopCategoryItemReadService.findByItemIds(Collections.singletonList(itemId)).getResult();
        if (CollUtil.isEmpty(shopCategoryItemList)) {
            return null;
        }
        // 默认根节点
        ShopCategoryDto root = new ShopCategoryDto();
        root.setId(0L);
        root.setName("全部");
        root.setLevel(0);
        // 商品对应的全部后台类目id
        List<Long> shopCategoryIds = shopCategoryItemList.stream().map(ShopCategoryItem::getShopCategoryId).toList();
        List<ShopCategory> shopCategoryList = shopCategoryReadService.findByIds(shopCategoryIds);
        if (CollUtil.isEmpty(shopCategoryList)) {
            return null;
        }
        Map<Long, List<ShopCategory>> shopCategoryMapOfPid = shopCategoryList.stream().collect(Collectors.groupingBy(ShopCategory::getPid));
        List<ShopCategory> rootShopCategories = shopCategoryList.stream().filter(shopCategory -> shopCategory.getPid().equals(ShopCategory.ID_ROOT)).toList();
        ShopCategory shopCategory = rootShopCategories.get(0);
        ShopCategoryDto rootShopCategory = BeanUtil.copyProperties(shopCategory, ShopCategoryDto.class);
        appendChildren(rootShopCategory, shopCategoryMapOfPid);
        root.setChildren(Collections.singletonList(rootShopCategory));
        return root;
    }

    private void appendChildren(ShopCategoryDto rootShopCategory, Map<Long, List<ShopCategory>> shopCategoryMapOfPid) {
        List<ShopCategory> shopCategoryList = shopCategoryMapOfPid.get(rootShopCategory.getId());
        if (CollUtil.isEmpty(shopCategoryList)) {
            return;
        }
        List<ShopCategoryDto> shopCategoryDtoList = BeanUtil.copyToList(shopCategoryList, ShopCategoryDto.class);
        rootShopCategory.setChildren(shopCategoryDtoList);
        for (ShopCategoryDto shopCategoryDto : shopCategoryDtoList) {
            appendChildren(shopCategoryDto, shopCategoryMapOfPid);
        }
    }


    public Boolean deleteItemShopCategoryByItemId(Long shopId, List<Long> itemIds) {
        return shopCategoryItemWriteService.deleteItemShopCategoryByItemId(shopId, itemIds);
    }

    public Boolean saveShopCategoryItems(List<Long> itemIds, List<ShopCategoryItem> shopCategoryItemList) {
        List<ShopCategoryItem> needSaveList = new ArrayList<>();
        for (Long itemId : itemIds) {
            for (ShopCategoryItem shopCategoryItem : shopCategoryItemList) {
                ShopCategoryItem entity = BeanUtil.copyProperties(shopCategoryItem, ShopCategoryItem.class);
                entity.setItemId(itemId);
                needSaveList.add(entity);
            }
        }
        return shopCategoryItemWriteService.saveBatch(needSaveList);
    }

    public ShopCategory save(ShopCategory shopCategory) {
        if (ObjUtil.isEmpty(shopCategory.getShopId())) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            if (currentUser == null) {
                throw new ApiException(ResultCode.UN_AUTHORIZED);
            }
            shopCategory.setShopId(currentUser.getShopId());
        }
        Long shopId = shopCategory.getShopId();
        int length = shopCategory.getName().length();
        if (length > 20) {
            throw new ApiException("类目名称不能超过20个字符");
        }
        Response<List<ShopCategory>> listResponse = shopCategoryReadService.findByShopIds(Collections.singletonList(shopId));
        if (listResponse.isSuccess() && !listResponse.getResult().isEmpty()
                && listResponse.getResult().size() > 50) {
            log.warn("ShopCategory more than 15 shopId(shopId={})", shopId);
            throw new ApiException("店铺类目最多50条");
        }
        Response<ShopCategory> r = shopCategoryWriteService.create(shopCategory);
        if (!r.isSuccess()) {
            log.warn("failed to create {}, error code:{}", shopCategory, r.getError());
            throw new ApiException("新增店铺商品分类信息失败");
        }
        shopCategoryCacher.invalidateByShopId(shopId);
        return r.getResult();
    }

    public Boolean update(ShopCategoryUpdateReqDto req) {
        if (ObjUtil.isEmpty(req.getShopId())) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            if (currentUser == null) {
                throw new ApiException(ResultCode.UN_AUTHORIZED);
            }
            req.setShopId(currentUser.getShopId());
        }
        Long id = req.getId();
        Long shopId = req.getShopId();
        String name = req.getName();
        Response<Boolean> r = shopCategoryWriteService.updateName(id, shopId, name);
        if (!r.isSuccess()) {
            log.warn("failed to update shop category(id={}) name to {} ,error code:{}", id, name, r.getError());
            throw new ApiException(r.getError());
        }
        shopCategoryCacher.invalidateByShopId(req.getShopId());
        return r.getResult();
    }

    public Boolean moveUp(ShopCategoryUpdateReqDto req) {
        if (ObjUtil.isEmpty(req.getShopId())) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            if (currentUser == null) {
                throw new ApiException(ResultCode.UN_AUTHORIZED);
            }
            req.setShopId(currentUser.getShopId());
        }
        Response<Boolean> booleanResponse = shopCategoryWriteService.move(req.getId(), -1);
        shopCategoryCacher.invalidateByShopId(req.getShopId());
        return booleanResponse.getResult();
    }

    public Boolean moveDown(ShopCategoryUpdateReqDto req) {
        if (ObjUtil.isEmpty(req.getShopId())) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            if (currentUser == null) {
                throw new ApiException(ResultCode.UN_AUTHORIZED);
            }
            req.setShopId(currentUser.getShopId());
        }
        Response<Boolean> booleanResponse = shopCategoryWriteService.move(req.getId(), 1);
        shopCategoryCacher.invalidateByShopId(req.getShopId());
        return booleanResponse.getResult();
    }

    public Boolean changeDisclosed(ShopCategoryUpdateReqDto req) {
        return shopCategoryWriteService.updateDisclosed(req.getId(), req.getDisabled()).getResult();
    }

    public Boolean changeVisibility(ShopCategoryUpdateReqDto req) {
        if (ObjUtil.isEmpty(req.getShopId())) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            if (currentUser == null) {
                throw new ApiException(ResultCode.UN_AUTHORIZED);
            }
            req.setShopId(currentUser.getShopId());
        }
        var response = shopCategoryWriteService.changeVisibility(req.getId(), req.getVisible());
        shopCategoryCacher.invalidateByShopId(req.getShopId());
        return response.getResult();
    }

    public Boolean deleteById(ShopCategoryUpdateReqDto req) {
        if (ObjUtil.isEmpty(req.getShopId())) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            if (currentUser == null) {
                throw new ApiException(ResultCode.UN_AUTHORIZED);
            }
            req.setShopId(currentUser.getShopId());
        }
        Long id = req.getId();
        Long shopId = req.getShopId();
        Response<List<ShopCategoryItem>> listResponse = shopCategoryItemReadService.findByShopIdsAndShopCategoryId(Collections.singletonList(shopId), id);
        if (listResponse.isSuccess() && !listResponse.getResult().isEmpty()) {
            log.warn("remove item shopId:{} shopCategoryId:{}", shopId, id);
            throw new ApiException("分类下有商品,请先移除商品,再删除");
        }

        Response<Boolean> r = shopCategoryWriteService.delete(id, shopId);
        if (!r.isSuccess()) {
            log.warn("failed to delete shop category(id={}) of shop(id={}) ,error code:{}",
                    id, shopId, r.getError());
            throw new ApiException(r.getError());
        }
        shopCategoryCacher.invalidateByShopId(shopId);
        return r.getResult();
    }
}
