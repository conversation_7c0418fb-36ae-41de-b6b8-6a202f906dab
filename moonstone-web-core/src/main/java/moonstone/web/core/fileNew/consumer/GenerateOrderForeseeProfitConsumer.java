package moonstone.web.core.fileNew.consumer;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.ApiException;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.web.core.events.trade.app.OrderProfitActionRecordApp;
import moonstone.common.constants.RedisKeyConstant;
import moonstone.common.constants.RocketMQConstant;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@RocketMQMessageListener(
        topic = RocketMQConstant.APP_MERCHANT_TOPIC,
        consumerGroup = RocketMQConstant.GENERATE_ORDER_FORESEE_PROFIT_CONSUMER_GROUP,
        selectorExpression = RocketMQConstant.GENERATE_ORDER_FORESEE_PROFIT_TAG
)
@Slf4j
public class GenerateOrderForeseeProfitConsumer implements RocketMQListener<String> {


    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    OrderProfitActionRecordApp orderProfitActionRecordApp;

    @Resource
    private BalanceDetailManager balanceDetailManager;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    @Override
    public void onMessage(String orderId) {
        try {
            log.info("收到生成订单利润消息 订单id {}", orderId);
            ShopOrder shopOrder = shopOrderReadService.findById(Long.valueOf(orderId)).getResult();
            orderProfitActionRecordApp.recordConfirmProfitOrder(Long.valueOf(orderId));
            balanceDetailManager.persistForeseeProfit(shopOrder);
        } catch (Exception e) {
            String key = RedisKeyConstant.GENERATE_ORDER_FORESEE_PROFIT_EXCEPTION_COUNT + orderId;
            Object count = redisTemplate.opsForValue().get(key);
            if (count != null) {
                redisTemplate.opsForValue().increment(key);
            }else {
                redisTemplate.opsForValue().set(key, 1,7, TimeUnit.DAYS);
            }
            log.error("生成订单真实利润失败 订单id {} 失败次数 {} 失败原因 {}", orderId, count == null ? 0 : count, e.getMessage(), e);
            throw new ApiException("生成订单真实利润失败");
        }
    }
}
