package moonstone.web.core.fileNew.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.common.model.Paging;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.api.Result;
import moonstone.common.constants.CommonSqlConstant;
import moonstone.common.enums.ExternalOrderState;
import moonstone.common.enums.OpBusinessType;
import moonstone.common.enums.ShipmentStatusEnum;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.SysOperLog;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.common.model.vo.PageVo;
import moonstone.common.mongo.SkuHistory;
import moonstone.common.mongo.SkuHistoryData;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuComboRelation;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuComboRelationService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.ExpressTrackInfo;
import moonstone.order.api.FlowPicker;
import moonstone.order.api.OrderAndOperation;
import moonstone.order.dto.OrderDetail;
import moonstone.order.dto.OrderGroup;
import moonstone.order.dto.SkuComboDTO;
import moonstone.order.dto.fsm.*;
import moonstone.order.enu.*;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.order.vo.PayInfoVO;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.thirdParty.service.ThirdPartyUserShopReadService;
import moonstone.user.bo.PayerInfoBO;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.user.model.DataExportTask;
import moonstone.user.model.User;
import moonstone.user.service.PayerInfoReadService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.express.service.OrderExpressDetailCenter;
import moonstone.web.core.fileNew.dto.*;
import moonstone.web.core.fileNew.dto.external.ExternalOrderDto;
import moonstone.web.core.fileNew.dto.external.ExternalOrderPageDto;
import moonstone.web.core.fileNew.enums.AdminShopOrderTableStatusEnum;
import moonstone.web.core.fileNew.export.AdminOrderExporter;
import moonstone.web.core.fileNew.vo.AdminShopOrderNumberVo;
import moonstone.web.core.fileNew.vo.AdminShopOrderVo;
import moonstone.web.core.fileNew.vo.ShopOrderVo;
import moonstone.web.core.fileNew.vo.external.*;
import moonstone.web.core.order.OrderReadLogic;
import moonstone.web.core.order.dto.OrderGroupViewObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * author：书生
 */
@Service
@Slf4j
public class ShopOrderLogic {

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private ShopOrderWriteService shopOrderWriteService;

    @Resource
    private SkuOrderLogic skuOrderLogic;

    @Resource
    private SkuReadService skuReadService;

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private SkuComboRelationService skuComboRelationService;

    @Resource
    private ShipmentReadService shipmentReadService;

    @Resource
    private OrderShipmentsService orderShipmentsService;

    @Resource
    private UserReadService<User> userReadService;

    @Resource
    private OrderReceiverInfoService orderReceiverInfoService;

    @Resource
    private ThirdPartyUserShopReadService thirdPartyUserShopReadService;

    @Resource
    private OrderRoleSnapshotLogic orderRoleSnapshotLogic;

    @Resource
    private PaymentV2Logic paymentV2Logic;

    @Resource
    private BalanceDetailLogic balanceDetailLogic;

    @Resource
    private FlowPicker flowPicker;

    @Resource
    private SkuCacheHolder skuCacheHolder;

    @Resource
    private ShopCacheHolder shopCacheHolder;

    @Resource
    private AgentPayOrderLogic agentPayOrderLogic;

    @Resource
    private AgentPayPaymentLogic agentPayPaymentLogic;

    @Resource
    private ReceiverInfoReadService receiverInfoReadService;

    @Resource
    private AdminOrderExporter adminOrderExporter;

    @Resource
    private ShopReadService shopReadService;

    @Resource
    private PayerInfoReadService payerInfoReadService;

    @Resource
    private SysOperLogWriteService sysOperLogWriteService;

    @Value("${image.base.url:https://dante-img.oss-cn-hangzhou.aliyuncs.com}")
    private String ossImageUrl;

	@Resource
	private OrderWriteService orderWriteService;

    @Resource
    private OrderReadLogic orderReadLogic;

    @Resource
    private RefundLogic refundLogic;

    @Resource
    private RefundReturnDetailLogic refundReturnDetailLogic;

    @Resource
    private OrderExpressDetailCenter orderExpressDetailCenter;

    @Resource
    private SkuHistoryLogic skuHistoryLogic;

	/**
     * 根据申报号列表查询对外提供的订单详情列表
     *
     * @param req 请求参数
     * @return 订单详情列表
     */
    public List<ExternalOrderVo> getExternalOrderByDeclaredId(ExternalOrderDto req) {
        Long shopId = thirdPartyUserShopReadService.getShopIdByAccessToken(req.getAccessCode());
        if (shopId == null) {
            throw new ApiException("accessCode无效，并未找到对应的店铺信息");
        }
        List<String> tradeNoList = req.getTradeNoList();
        if (tradeNoList.size() > 20) {
            throw new ApiException("查询订单详情，一次传入订单编号数量不能超过 20 条");
        }
        Map<String, Object> query = new HashMap<>();
        query.put("declaredIds", tradeNoList);
        query.put(CommonSqlConstant.SORT_BY, "id");
        query.put(CommonSqlConstant.SORT_TYPE, CommonSqlConstant.SORT_DESC);
        List<ShopOrder> shopOrderList = shopOrderReadService.list(query);
        if (CollUtil.isEmpty(shopOrderList)) {
            return Collections.emptyList();
        }
        return convertOrderExternalVo(shopOrderList);
    }

    /**
     * 将内部订单信息列表转为对外提供的订单信息列表
     *
     * @param shopOrderList 内部订单信息列表
     * @return 对外提供的订单信息列表
     */
    private List<ExternalOrderVo> convertOrderExternalVo(List<ShopOrder> shopOrderList) {
        List<ExternalOrderVo> result = new ArrayList<>();
        for (ShopOrder shopOrder : shopOrderList) {
            ExternalOrderVo entity = new ExternalOrderVo();
            entity.setTradeNo(shopOrder.getDeclaredId());
            int externalOrderState = ExternalOrderState.convertExternalOrderState(shopOrder.getStatus());
            entity.setExternalOrderState(externalOrderState);
            entity.setExternalOrderStateDesc(ExternalOrderState.getDesc(externalOrderState));
            Integer shipmentStatus = 0;
            String shipmentCompanyName = "";
            OrderShipment orderShipment = orderShipmentsService.findByOrderIdAndOrderType(shopOrder.getId(), shopOrder.getType());
            if (orderShipment != null) {
                Shipment shipment = shipmentReadService.findById(orderShipment.getShipmentId()).getResult();
                shipmentStatus = shipment.getStatus();
                shipmentCompanyName = shipment.getShipmentCorpName();
            }
            entity.setShipmentState(shipmentStatus);
            entity.setShipmentStateDesc(ShipmentStatusEnum.getDesc(shipmentStatus));
            entity.setCreateTime(DateUtil.format(shopOrder.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
            entity.setShipmentCompanyName(shipmentCompanyName);
            OrderDetailCollect orderDetailCollect = getOrderDetailCollect(shopOrder);
            // 所有SKU的单品优惠之和
            entity.setItemDiscountAmount(0L);
            entity.setDeductionAmount(orderDetailCollect.getItemDiscountAmount());
            // 总货款
            entity.setItemTotalAmount(orderDetailCollect.getItemTotalAmount());
            entity.setFeeAmount(shopOrder.getFee());
            int refundNum = 0;
            if (entity.getExternalOrderState().equals(ExternalOrderState.IN_AFTER_SALE.getCode()) || entity.getExternalOrderState().equals(ExternalOrderState.COMPLETED_AFTER_SALE.getCode())) {
                refundNum = orderDetailCollect.getItemQuantity();
            }
            OrderRoleSnapshot subStoreSnapshot = orderRoleSnapshotLogic.getOneByShopOrderIdAndRole(shopOrder.getId(), OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER, SubStoreUserIdentityEnum.SUB_STORE);
            if (subStoreSnapshot != null) {
                ExternalSubStoreVo externalOrderSubStore = BeanUtil.copyProperties(subStoreSnapshot, ExternalSubStoreVo.class);
                externalOrderSubStore.setId(shopOrder.getOutShopId());
                entity.setExternalOrderSubStore(externalOrderSubStore);
            }
            entity.setRefundNum(refundNum);
            entity.setRefundMode(1);
            entity.setShopName(shopOrder.getShopName());
            entity.setExternalOrderConsignee(getExternalOrderConsignee(shopOrder));
            entity.setOrderDetailList(orderDetailCollect.getOrderDetailList());
            result.add(entity);
        }
        return result;
    }

    /**
     * 汇总订单明细
     */
    private OrderDetailCollect getOrderDetailCollect(ShopOrder shopOrder) {
        OrderDetailCollect result = new OrderDetailCollect();
        long itemDiscountAmount = 0;
        long itemTotalAmount = 0;
        int itemQuantity = 0;
        List<ExternalOrderDetail> orderDetailList = new ArrayList<>();
        List<SkuOrder> skuOrderList = skuOrderLogic.listByShopOrderId(shopOrder.getId());
        for (SkuOrder skuOrder : skuOrderList) {
            ExternalOrderDetail orderDetail = new ExternalOrderDetail();
            orderDetail.setOrderDetailId(skuOrder.getId());
            orderDetail.setItemNo(skuOrder.getOuterSkuId());
            orderDetail.setItemName(skuOrder.getItemName());
            orderDetail.setQuantity(skuOrder.getQuantity());
            Sku sku = skuReadService.findSkuById(skuOrder.getSkuId()).getResult();
            orderDetail.setUnitCost(Long.valueOf(sku.getPrice()));
            orderDetail.setDiscountAmount(skuOrder.getDiscount());
            orderDetail.setFee(skuOrder.getFee());
            orderDetailList.add(orderDetail);
            itemDiscountAmount += skuOrder.getDiscount() == null ? 0 : skuOrder.getDiscount();
            itemQuantity += skuOrder.getQuantity();
            itemTotalAmount += orderDetail.getQuantity() * orderDetail.getUnitCost();
        }
        result.setItemDiscountAmount(itemDiscountAmount);
        result.setItemTotalAmount(itemTotalAmount);
        result.setItemQuantity(itemQuantity);
        result.setOrderDetailList(orderDetailList);
        return result;
    }

    /**
     * 获取订单收货人信息
     *
     * @param shopOrder 订单信息
     * @return 订单收货人信息
     */
    private ExternalOrderConsignee getExternalOrderConsignee(ShopOrder shopOrder) {
        ExternalOrderConsignee externalOrderConsignee = new ExternalOrderConsignee();
        User user = userReadService.findById(shopOrder.getBuyerId()).getResult();
        if (user == null) {
            return externalOrderConsignee;
        }
        externalOrderConsignee.setConsigneeName(user.getName());
        OrderReceiverInfo orderReceiverInfo = orderReceiverInfoService.findByOrderIdAndOrderType(shopOrder.getId(), shopOrder.getType());
        if (orderReceiverInfo != null) {
            ReceiverInfo receiverInfo = orderReceiverInfo.getReceiverInfo();
            externalOrderConsignee.setConsigneeProvince(receiverInfo.getProvince());
            externalOrderConsignee.setConsigneeCity(receiverInfo.getCity());
            externalOrderConsignee.setConsigneeRegion(receiverInfo.getRegion());
        }
        return externalOrderConsignee;
    }

    /**
     * 根据查询条件分页查询对外提供的订单详情
     *
     * @param req 请求参数
     * @return 对外提供的订单详情
     */
    public PageVo<ExternalOrderVo> pageExternalOrders(ExternalOrderPageDto req) {
        Long shopId = thirdPartyUserShopReadService.getShopIdByAccessToken(req.getAccessCode());
        if (shopId == null) {
            throw new ApiException("accessCode无效，并未找到对应的店铺信息");
        }
        long dayTime = DateUtil.betweenDay(req.getOrderTimeStart(), req.getOrderTimeEnd(), true);
        if (dayTime < 0 || dayTime > 10) {
            throw new ApiException("时间差小于0或大于10天，请修改传参");
        }
        Map<String, Object> query = new HashMap<>();
        long current = req.getCurrent();
        long size = req.getSize();
        long offset = (current - 1) * size;
        query.put(CommonSqlConstant.OFFSET, offset);
        query.put(CommonSqlConstant.LIMIT, size);
        query.put("shopId", shopId);
        query.put("startAt", req.getOrderTimeStart());
        query.put("endAt", req.getOrderTimeEnd());
        Paging<ShopOrder> paging = shopOrderReadService.pages(query);
        Long total = paging.getTotal();
        long pages = total / size + (total % size == 0 ? 0 : 1);
        if (total == 0) {
            return PageVo.build(total, size, current, pages, Collections.emptyList());
        }
        return PageVo.build(total, size, current, pages, convertOrderExternalVo(paging.getData()));

    }

    /**
     * 分页查询订单信息列表
     *
     * @param req 请求参数
     * @return 订单信息列表
     */
    public PageVo<ShopOrderVo> pagesWithSnapshot(ShopOrderPageDto req) {
        if (req.getShopId() == null) {
            Long currentShopId = UserUtil.getCurrentShopId();
            if (currentShopId == null) {
                throw new ApiException("当前用户没有绑定任何店铺");
            }
            req.setShopId(currentShopId);
        }
        Map<String, Object> query = convertReqToMap(req);
        log.info("转换后的map {}", JSONUtil.toJsonStr(query));
        long count = shopOrderReadService.countWithSnapshot(query);
        long pages = count / req.getSize() + (count % req.getSize() == 0 ? 0 : 1);
        if (count == 0L) {
            return PageVo.build(count, req.getSize(), req.getCurrent(), pages, Collections.emptyList());
        }
        query.put(CommonSqlConstant.OFFSET, (req.getCurrent() - 1) * req.getSize());
        query.put(CommonSqlConstant.LIMIT, req.getSize());
        query.put(CommonSqlConstant.SORT_BY, "created_at");
        query.put(CommonSqlConstant.SORT_TYPE, CommonSqlConstant.SORT_DESC);
        List<ShopOrder> shopOrderList = shopOrderReadService.pagesWithSnapshot(query);
        return PageVo.build(count, req.getSize(), req.getCurrent(), pages, convertShopOrderVo(shopOrderList));
    }

    private List<ShopOrderVo> convertShopOrderVo(List<ShopOrder> shopOrderList) {
        List<Long> orderIds = shopOrderList.stream().map(OrderBase::getId).toList();
        Long shopId = shopOrderList.get(0).getShopId();
        Map<Long, List<BalanceDetail>> balanceDetailMap = balanceDetailLogic.getBalanceDetailMap(orderIds, shopId);
        // key = shopOrderId
        Map<Long, AgentPayOrder> agentOrderMap = agentPayOrderLogic.findMapByShopOrderIds(orderIds);
        // key = agentPayOrderId
        var agentPaymentMap = agentPayPaymentLogic.findMapByAgentPayOrderIds(
                agentOrderMap.values().stream().map(AgentPayOrder::getId).toList());

        List<ShopOrderVo> result = new ArrayList<>();
        for (ShopOrder shopOrder : shopOrderList) {
            List<BalanceDetail> balanceDetailList = balanceDetailMap.get(shopOrder.getId());
            ShopOrderVo shopOrderVo = BeanUtil.copyProperties(shopOrder, ShopOrderVo.class);
            shopOrderVo.setExtra(JSONUtil.parseObj(shopOrderVo.getExtraJson()));
            List<SkuOrder> skuOrderList = skuOrderLogic.listByShopOrderId(shopOrder.getId());
            List<OrderGroup.SkuOrderAndOperation> skuOrderAndOperations
                    = Lists.newArrayListWithCapacity(skuOrderList.size());
            List<SkuOrderDto> skuOrderDtoList = new ArrayList<>();
            long shopTax = 0L;
            for (SkuOrder skuOrder : skuOrderList) {
                SkuOrderDto skuOrderDto = BeanUtil.copyProperties(skuOrder, SkuOrderDto.class);
                //组合品数据展示
//                根据item_id查询类型是否是组合品，然后查询相关子商品信息。
                Item item = itemReadService.findById(skuOrder.getItemId()).getResult();
                if(ObjectUtil.isNotEmpty(item) && item.isCombination()){
                    List<SkuComboDTO> skuComboList = Lists.newArrayList();
                    List<SkuComboRelation> skuComboRelationList = skuComboRelationService.getSkuComboRelationListBySkuId(skuOrder.getSkuId());
                    for (SkuComboRelation skuComboRelation : skuComboRelationList) {
                        SkuComboDTO entity = BeanUtil.copyProperties(skuComboRelation, SkuComboDTO.class);
                        Long comboSkuId = entity.getComboSkuId();
                        Sku sku = skuReadService.findSkuById(comboSkuId).getResult();
                        entity.setComboSkuPrice(sku.getPrice());
                        entity.setComboSkuStockQuantity(sku.getStockQuantity());
                        entity.setComboSkuSpecification(sku.getSpecification());
                        Item comboItem = itemReadService.findById(skuComboRelation.getComboItemId()).getResult();
                        entity.setComboItemMainImage(comboItem.getMainImage() != null && !comboItem.getMainImage().startsWith("http") ? ossImageUrl + comboItem.getMainImage() : comboItem.getMainImage());
                        skuComboList.add(entity);
                    }
                    skuOrderDto.setSkuComboList(skuComboList);
                }
                Sku sku1 = new Sku();
                // 查询 sku 历史数据
                SkuHistory skuHistory = skuHistoryLogic.getSkuHistory(skuOrder.getSkuId(), skuOrder.getSkuVersion());
                if (ObjUtil.isNotEmpty(skuHistory)) {
                    SkuHistoryData currentData = skuHistory.getCurrentData();
                    BeanUtil.copyProperties(currentData, sku1);
                    if (StrUtil.isNotBlank(currentData.getSpecification())) {
                        skuOrderDto.setSpecification(currentData.getSpecification());
                    }
                    if (StrUtil.isNotBlank(currentData.getImage())) {
                        skuOrderDto.setSkuImage(currentData.getImage());
                    }
                }
                skuOrderDto.setSkuImage(skuOrderDto.getSkuImage() != null && !skuOrderDto.getSkuImage().startsWith("http") ? ossImageUrl + skuOrderDto.getSkuImage() : skuOrderDto.getSkuImage());
                if (ObjUtil.isEmpty(sku1.getItemId())) {
                    sku1 = skuReadService.findSkuById(skuOrder.getSkuId()).getResult();
                }
                var op = flowPicker.pick(skuOrder, OrderLevel.SKU).availableOperations(skuOrder.getStatus());
                var orderOperation = new OrderGroup.SkuOrderAndOperation(skuOrder, op, sku1,
                        OrderFlagEnum.getFrontEndFlagCodes(skuOrder.getFlag()));
                skuOrderAndOperations.add(orderOperation);
                if (skuOrder.getTax() != null) {
                    shopTax += skuOrder.getTax();
                }
                skuOrderDtoList.add(skuOrderDto);
            }
            shopOrderVo.setShopTax(shopTax);
            boolean shouldWaitAuth = shopOrder.getStatus() != OrderStatus.NOT_PAID.getValue() && skuOrderList.stream().map(SkuOrder::getPushStatus)
                    .anyMatch(pushStatus -> pushStatus == SkuOrderPushStatus.WAITING_SELLER_AUTH.value());
            shopOrderVo.setWaitingSellerAuth(shouldWaitAuth);
            // 1. 判断shop是否开启审核
            Shop shop = shopCacheHolder.findShopById(shopId);
            // 2. 如果开启了审核则 设置订单显示状态
            if (ShopFunctionSlice.build(shop).isShopOpenOrderAuth()) {
                if (Objects.equals(OrderStatus.PAID.getValue(), shopOrder.getStatus())) {
                    shopOrderVo.setWaitingSellerAuthDesc(shopOrderVo.getWaitingSellerAuth() != null && shopOrderVo.getWaitingSellerAuth() ? "待审核" : "审核通过");
                }
            }
            OrderStatus status = OrderStatus.fromInt(shopOrder.getStatus());
            // 3. 此时如果订单状态为null 则再设置其本来的订单状态
            if (StrUtil.isBlank(shopOrderVo.getWaitingSellerAuthDesc())) {
                shopOrderVo.setWaitingSellerAuthDesc(status.intoString());
            }
            shopOrderVo.setSkuOrderDtoList(skuOrderDtoList);
            shopOrderVo.setSkuOrderAndOperations(skuOrderAndOperations);
            Flow flow = flowPicker.pick(shopOrder, OrderLevel.SHOP);
            if (!CollectionUtils.isEmpty(shopOrderVo.getSkuOrderAndOperations())) {
                List<OrderAndOperation> operations = new ArrayList<>(shopOrderVo.getSkuOrderAndOperations().size());
                for (OrderAndOperation skuOrderAndOperation : shopOrderVo.getSkuOrderAndOperations()) {
                    SkuOrder skuOrder = (SkuOrder) skuOrderAndOperation.order();
                    var sku = skuCacheHolder.findSkuById(skuOrder.getSkuId());
                    var op = flow.availableOperations(skuOrder.getStatus());
                    if (Optional.ofNullable(shopOrder.getExtra()).orElse(Collections.emptyMap())
                            .getOrDefault(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.FALSE.getCode())
                            .equals(ShopOrderIdentityErrorEnum.TRUE.getCode())) {
                        op = skuOrderAndOperation.operation().stream()
                                .filter(iop -> !iop.getText().equals("pay")).collect(Collectors.toSet());
                    }
                    operations.add(new OrderGroup.SkuOrderAndOperation(skuOrder, op, sku,
                            OrderFlagEnum.getFrontEndFlagCodes(skuOrder.getFlag())));
                }
                shopOrderVo.setSkuOrderAndOperations(operations);
            }
            //确定店铺订单可以执行的操作
            //如果是根据状态筛选,那归组出来的子订单可能不能构成一个总单,这个时候就要以数据库真实数据为准
            //如果不根据状态筛选, 由于订单列表查询的时候只会返回有限数量的子订单,所以也要重新找一把
            Set<OrderOperation> orderOperations = pickCommonSkuOperation(shopOrderVo.getSkuOrderAndOperations()
                            .stream().map(op -> (SkuOrder) op.order()).collect(Collectors.toList())
                    , flow);
            if (Objects.equals(OrderStatus.SHIPPED.getValue(), shopOrder.getStatus()) ||
                  Objects.equals(OrderStatus.CONFIRMED.getValue(), shopOrder.getStatus())) {
                // pc端已发货,确认收货后,去除退货申请的按钮
                orderOperations = orderOperations.stream()
                        .filter(op -> !op.getText().equals("refundApply")).collect(Collectors.toSet());
            }
            shopOrderVo.setShopOrderOperations(orderOperations);

            if (Optional.ofNullable(shopOrder.getExtra()).orElse(Collections.emptyMap())
                    .getOrDefault(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.FALSE.getCode())
                    .equals(ShopOrderIdentityErrorEnum.TRUE.getCode())) {
                shopOrderVo.setShopOrderOperations(shopOrderVo.getShopOrderOperations().stream()
                        .filter(op -> !op.getText().equals("pay")).collect(Collectors.toSet()));
            }

            List<Payment> paymentList = paymentV2Logic.listByOrderId(shopOrderVo.getId());
            if (CollUtil.isNotEmpty(paymentList)) {
                shopOrderVo.setPayOutId(paymentList.get(0).getOutId());
                shopOrderVo.setThirdPartyTransactionNo(paymentList.get(0).getThirdPartyTransactionNo());
                shopOrderVo.setThirdPartyTransactionNoSource(paymentList.get(0).getThirdPartyTransactionNoSource());
            }
            List<OrderRoleSnapshot> orderRoleSnapshots = orderRoleSnapshotLogic.listByShopOrderIdAndSnapshotType(shopOrder.getId(), OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER);
            Map<Integer, OrderRoleSnapshot> orderRoleSnapshotByUserRoleMap = orderRoleSnapshots.stream().collect(Collectors.toMap(OrderRoleSnapshot::getUserRole, Function.identity()));
            ServiceProviderDto serviceProviderDto = getServiceProviderInfo(orderRoleSnapshotByUserRoleMap, balanceDetailList);
            shopOrderVo.setServiceProvider(serviceProviderDto);
            SubStoreDto subStoreDto = getSubStoreInfo(orderRoleSnapshotByUserRoleMap, balanceDetailList);
            shopOrderVo.setSubStore(subStoreDto);
            SubStoreGuiderDto subStoreGuiderDto = getSubStoreGuiderInfo(orderRoleSnapshotByUserRoleMap, balanceDetailList);
            shopOrderVo.setSubStoreGuiderDto(subStoreGuiderDto);
            AgentPayOrder agentPayOrder = agentOrderMap.get(shopOrder.getId());
            List<AgentPayPayment> agentPayPayments = agentPayOrder != null ? agentPaymentMap.get(agentPayOrder.getId()) : null;
            OrderGroupViewObject.AgentPayStatus agentPayStatus = getAgentPayStatus(agentPayOrder, agentPayPayments);
            shopOrderVo.setAgentPayStatusCode(agentPayStatus.getCode());
            shopOrderVo.setAgentPayStatusDescription(agentPayStatus.getDescription());
            // 设置订购人信息
            List<ReceiverInfo> receiverInfoList = receiverInfoReadService.findByOrderId(shopOrder.getId(), OrderLevel.SHOP).getResult();
            if (CollUtil.isNotEmpty(receiverInfoList)) {
                ReceiverInfo receiverInfo = receiverInfoList.get(0);
                shopOrderVo.setReceiveUserName(receiverInfo.getReceiveUserName());
                shopOrderVo.setPaperNo(receiverInfo.getPaperNo());
            }
            if (Objects.nonNull(shopOrder.getExceptionType())) {
                shopOrderVo.setExceptionMessage(shopOrder.getExceptionReason());
            }
            result.add(shopOrderVo);
        }
        return result;
    }

    private OrderGroupViewObject.AgentPayStatus getAgentPayStatus(AgentPayOrder agentPayOrder, List<AgentPayPayment> agentPayPayments) {
        OrderGroupViewObject.AgentPayStatus result;
        if (!CollectionUtils.isEmpty(agentPayPayments)) {
            agentPayPayments.sort(Comparator.comparing(AgentPayPayment::getId).reversed());
            result = getByAgentPayPaymentStatus(agentPayPayments.get(0).getStatus());
        } else if (agentPayOrder != null) {
            result = getByAgentPayOrderStatus(agentPayOrder.getStatus());
        } else {
            result = OrderGroupViewObject.AgentPayStatus.NO_NEED;
        }
        return result;
    }

    private OrderGroupViewObject.AgentPayStatus getByAgentPayOrderStatus(Integer status) {
        var statusEnum = AgentPayOrderStatusEnum.parse(status);
        if (statusEnum == null) {
            return OrderGroupViewObject.AgentPayStatus.UNKNOWN;
        }

        return switch (statusEnum) {
            case NOT_PAID -> OrderGroupViewObject.AgentPayStatus.NOT_PAID;
            case PAID -> OrderGroupViewObject.AgentPayStatus.PAID;
        };
    }

    private OrderGroupViewObject.AgentPayStatus getByAgentPayPaymentStatus(Integer status) {
        var agentPayPaymentStatus = AgentPayPaymentStatusEnum.parse(status);
        if (agentPayPaymentStatus == null) {
            return OrderGroupViewObject.AgentPayStatus.UNKNOWN;
        }

        return switch (agentPayPaymentStatus) {
            case NOT_PAID -> OrderGroupViewObject.AgentPayStatus.NOT_PAID;
            case PAYING -> OrderGroupViewObject.AgentPayStatus.PAYING;
            case PAID -> OrderGroupViewObject.AgentPayStatus.PAID;
            case FAILED -> OrderGroupViewObject.AgentPayStatus.FAILED;
        };
    }


    private Set<OrderOperation> pickCommonSkuOperation(List<SkuOrder> skuOrders, Flow flow) {
        //查询店铺操作,所有子订单共有的操作才能在订单级别操作
        ArrayListMultimap<OrderOperation, Long> groupSkuOrderIdByOperation = ArrayListMultimap.create();
        for (SkuOrder skuOrder : skuOrders) {
            Set<OrderOperation> orderOperations = flow.availableOperations(skuOrder.getStatus());
            for (OrderOperation orderOperation : orderOperations) {
                groupSkuOrderIdByOperation.put(orderOperation, skuOrder.getId());
            }
        }
        Set<OrderOperation> shopOperation = Sets.newHashSet();
        for (OrderOperation operation : groupSkuOrderIdByOperation.keySet()) {
            if (groupSkuOrderIdByOperation.get(operation).size() == skuOrders.size()) {
                shopOperation.add(operation);
            }
        }
        return shopOperation;
    }

    private ServiceProviderDto getServiceProviderInfo(Map<Integer, OrderRoleSnapshot> orderRoleSnapshotByUserRoleMap, List<BalanceDetail> balanceDetailList) {
        OrderRoleSnapshot serviceProviderSnapshot = orderRoleSnapshotByUserRoleMap.get(SubStoreUserIdentityEnum.SERVICE_PROVIDER.getCode());
        ServiceProviderDto serviceProviderDto = BeanUtil.copyProperties(serviceProviderSnapshot, ServiceProviderDto.class);
        if (serviceProviderDto == null) {
            return null;
        }
        serviceProviderDto.setId(null);
        Long serviceProviderUserId = serviceProviderSnapshot.getUserId();
        if (CollUtil.isNotEmpty(balanceDetailList)) {
            Long profit = balanceDetailList.stream()
                    .filter(detail -> detail.getUserId().equals(serviceProviderUserId) && !detail.isPresent() && !detail.isLinked())
                    .map(BalanceDetail::getChangeFee)
                    .reduce(Long::sum)
                    .orElse(null);
            serviceProviderDto.setProfit(profit);
        }
        return serviceProviderDto;
    }

    private SubStoreDto getSubStoreInfo(Map<Integer, OrderRoleSnapshot> orderRoleSnapshotByUserRoleMap, List<BalanceDetail> balanceDetailList) {
        OrderRoleSnapshot subStoreSnapshot = orderRoleSnapshotByUserRoleMap.get(SubStoreUserIdentityEnum.SUB_STORE.getCode());
        if (ObjUtil.isEmpty(subStoreSnapshot)) {
            return null;
        }
        SubStoreDto subStoreDto = BeanUtil.copyProperties(subStoreSnapshot, SubStoreDto.class);
        subStoreDto.setId(null);
        Long subStoreUserId = subStoreSnapshot.getUserId();
        if (CollUtil.isNotEmpty(balanceDetailList)) {
            Long profit = balanceDetailList.stream()
                    .filter(detail -> detail.getUserId().equals(subStoreUserId) && !detail.isPresent())
                    .map(BalanceDetail::getChangeFee)
                    .reduce(Long::sum)
                    .orElse(null);
            subStoreDto.setProfit(profit);
        }
        return subStoreDto;
    }

    /**
     * 获取导购信息
     *
     * @param orderRoleSnapshotByUserRoleMap 订单角色快照
     * @param balanceDetailList              余额明细
     * @return 订单角色快照
     */
    private SubStoreGuiderDto getSubStoreGuiderInfo(Map<Integer, OrderRoleSnapshot> orderRoleSnapshotByUserRoleMap, List<BalanceDetail> balanceDetailList) {
        OrderRoleSnapshot subStoreGuiderSnapshot = orderRoleSnapshotByUserRoleMap.get(SubStoreUserIdentityEnum.STORE_GUIDER.getCode());
        if (subStoreGuiderSnapshot == null) {
            return null;
        }
        OrderRoleSnapshot subStoreSnapshot = orderRoleSnapshotByUserRoleMap.get(SubStoreUserIdentityEnum.SUB_STORE.getCode());
        Long subStoreUserId = subStoreSnapshot.getUserId();
        SubStoreGuiderDto subStoreGuiderDto = BeanUtil.copyProperties(subStoreGuiderSnapshot, SubStoreGuiderDto.class);
        if (CollUtil.isNotEmpty(balanceDetailList)) {
            Long guiderProfit = balanceDetailList.stream()
                    .filter(profit -> !profit.isPresent() && !subStoreUserId.equals(profit.getUserId()))
                    .filter(BalanceDetail::isLinked)
                    .map(BalanceDetail::getChangeFee)
                    .reduce(Long::sum)
                    .orElse(null);
            subStoreGuiderDto.setProfit(guiderProfit);
        }
        subStoreGuiderDto.setId(subStoreGuiderSnapshot.getUserId());
        return subStoreGuiderDto;
    }

    private Map<String, Object> convertReqToMap(Object obj) {
        if (obj instanceof ShopOrderPageDto req) {
            if (req.getOrderFlag() != null) {
                if (req.getOrderFlag() == 1) {
                    List<Integer> orderFlags = new ArrayList<>();
                    orderFlags.add(OrderFlagEnum.PROMOTION_LIMITED_ITEM.getValue());
                    req.setOrderFlags(orderFlags);
                } else if (req.getOrderFlag() == 2) {
                    req.setSkuOrderShippingWarehouseType(2);
                }
            }
            return req.toMap();
        }
        if (obj instanceof ShopOrderDto req) {
            if (req.getOrderFlag() != null) {
                if (req.getOrderFlag() == 1) {
                    List<Integer> orderFlags = new ArrayList<>();
                    orderFlags.add(OrderFlagEnum.PROMOTION_LIMITED_ITEM.getValue());
                    req.setOrderFlags(orderFlags);
                } else if (req.getOrderFlag() == 2) {
                    req.setSkuOrderShippingWarehouseType(2);
                }
            }
            return req.toMap();
        }
        return null;

    }

    public List<Long> listIdsWithSnapshot(ShopOrderDto req) {
        Map<String, Object> query = convertReqToMap(req);
        query.put(CommonSqlConstant.SORT_BY, "created_at");
        query.put(CommonSqlConstant.SORT_TYPE, CommonSqlConstant.SORT_ASC);
        return shopOrderReadService.listIdsWithSnapshot(query);
    }

    public List<DropDownBoxVo> getFlagDropDownBox() {
        List<DropDownBoxVo> result = new ArrayList<>();
        DropDownBoxVo dropDownBoxVo1 = new DropDownBoxVo();
        dropDownBoxVo1.setId("1");
        dropDownBoxVo1.setName("限定商品");
        result.add(dropDownBoxVo1);
        return result;
    }

    public List<DropDownBoxVo> getTradeStatusDropDownBox() {
        List<DropDownBoxVo> result = new ArrayList<>();
        DropDownBoxVo dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.NOT_PAID.getValue()));
        dropDownBoxVo.setName("待付款");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.PAID.getValue()));
        dropDownBoxVo.setName("待发货");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.SHIPPED.getValue()));
        dropDownBoxVo.setName("待收货");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.CONFIRMED.getValue()));
        dropDownBoxVo.setName("已收货");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.BUYER_CANCEL.getValue()));
        dropDownBoxVo.setName("买家关闭订单");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.SELLER_CANCEL.getValue()));
        dropDownBoxVo.setName("商家关闭订单");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.TIMEOUT_CANCEL.getValue()));
        dropDownBoxVo.setName("超时关闭");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.REFUND_APPLY.getValue()));
        dropDownBoxVo.setName("申请退款");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.REFUND_APPLY_AGREED.getValue()));
        dropDownBoxVo.setName("同意退款");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.REFUND_APPLY_REJECTED.getValue()));
        dropDownBoxVo.setName("拒绝退款");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.RETURN_APPLY.getValue()));
        dropDownBoxVo.setName("申请退货退款");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.RETURN_APPLY_AGREED.getValue()));
        dropDownBoxVo.setName("同意退货退款");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.RETURN_APPLY_REJECTED.getValue()));
        dropDownBoxVo.setName("拒绝退货退款");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.REFUND.getValue()));
        dropDownBoxVo.setName("退款成功");
        result.add(dropDownBoxVo);
        dropDownBoxVo = new DropDownBoxVo();
        dropDownBoxVo.setId(String.valueOf(OrderStatus.REFUND_FAIL.getValue()));
        dropDownBoxVo.setName("退款失败");
        result.add(dropDownBoxVo);


        return result;
    }

    public List<DropDownBoxVo> getExceptionTypeDropDownBox() {
        List<DropDownBoxVo> result = new ArrayList<>();
        for (OrderExceptionEnum value : OrderExceptionEnum.values()) {
            if (OrderExceptionEnum.CLEAR_ERROR.equals(value)) {
                continue;
            }
            DropDownBoxVo dropDownBoxVo = new DropDownBoxVo();
            dropDownBoxVo.setId(String.valueOf(value.getCode()));
            dropDownBoxVo.setName(value.getMessage());
            result.add(dropDownBoxVo);
        }
        return result;
    }

    public Result<Long> adminOrderExcelExport(AdminShopOrderDto req) {
        List<Long> shopOrderIdList = adminListIdsWithSnapshot(req);
        CommonUser currentUser = UserUtil.getCurrentUser();
        if (currentUser == null) {
            currentUser = new CommonUser();
        }
        currentUser.setId(1L);
        currentUser.setShopId(1L);
        try {
            String fileName = DataExportTaskTypeEnum.ORDER_MANAGEMENT.getDescription() + DatePattern.PURE_DATETIME_FORMAT.format(new Date()) + ".xlsx";
            DataExportTask task = adminOrderExporter.asyncExport(currentUser, fileName, new AdminOrderExporter.OrderExportParameter(shopOrderIdList));
            return Result.data(task.getId());
        } catch (Exception e) {
            log.error("导出失败 {}", e.getMessage(), e);
            throw new ApiException("导出失败");
        }
    }

    private List<Long> adminListIdsWithSnapshot(AdminShopOrderDto req) {
        Map<String, Object> query = req.toMap();
        if (StrUtil.isNotBlank(req.getTabName())) {
            query.remove("statusList");
            if (req.getTabName().equals(AdminShopOrderTableStatusEnum.HAS_EXCEPTION.getCode())) {
                query.put("exceptionTypes", List.of(OrderExceptionEnum.SYSTEM_ERROR.getCode(), OrderExceptionEnum.UPLOAD_PAYMENT_ORIGINAL_INFO_FAILED.getCode()));
            }else {
                query.put("statusList", AdminShopOrderTableStatusEnum.getStatusByCode(req.getTabName()));
            }
        }
        query.put(CommonSqlConstant.SORT_BY, "created_at");
        query.put(CommonSqlConstant.SORT_TYPE, CommonSqlConstant.SORT_ASC);
        return shopOrderReadService.adminListIdsWithSnapshot(query);
    }


    public PayInfoVO getPayInfoOfDecode(Long orderId) {
        PayerInfoBO payerInfoBO = payerInfoReadService.getPayInfoOfDecode(orderId);
        ShopOrder shopOrder = shopOrderReadService.findById(orderId).getResult();
        User user = userReadService.findById(shopOrder.getBuyerId()).getResult();
        PayInfoVO payInfoVO = new PayInfoVO();
        payInfoVO.setPhone(user.getMobile());
        payInfoVO.setPayName(payerInfoBO.getPayerName());
        payInfoVO.setPayIdCard(payerInfoBO.getPayerNo());
        SysOperLog logInfo = new SysOperLog();
        logInfo.setName("订单解密查看支付人信息:" + orderId);
        logInfo.setShopId(shopOrder.getShopId());
        logInfo.setTitle("订单管理->订单管理");
        logInfo.setUserId(UserUtil.getUserId());
        logInfo.setBusinessType(OpBusinessType.OTHER.getCode());
        logInfo.setStatus(1);
        logInfo.setCreateAt(System.currentTimeMillis());
        sysOperLogWriteService.insertSysOperLog(logInfo);
        return payInfoVO;
    }

    public List<DropDownBoxVo> getShippingWarehouseDropDownBox() {
        return Arrays.stream(SkuOrderShippingWarehouseTypeEnum.values()).map(item -> {
            DropDownBoxVo dropDownBoxVo = new DropDownBoxVo();
            dropDownBoxVo.setId(String.valueOf(item.getValue()));
            dropDownBoxVo.setName(item.getDescription());
            return dropDownBoxVo;
        }).collect(Collectors.toList());
    }

    public ShopOrder getOne(Map<String, Object> query) {
        return shopOrderReadService.getOne(query);
    }

    public PageVo<AdminShopOrderVo> adminOrderPage(AdminShopOrderPageDto req) {
        Map<String, Object> query = req.toMap();
        if (StrUtil.isNotBlank(req.getTabName())) {
            query.remove("statusList");
            if (req.getTabName().equals(AdminShopOrderTableStatusEnum.HAS_EXCEPTION.getCode())) {
                query.put("exceptionTypes", List.of(OrderExceptionEnum.SYSTEM_ERROR.getCode(), OrderExceptionEnum.UPLOAD_PAYMENT_ORIGINAL_INFO_FAILED.getCode()));
            }else {
                query.put("statusList", AdminShopOrderTableStatusEnum.getStatusByCode(req.getTabName()));
            }
        }
        log.info("转换后的map {}", JSONUtil.toJsonStr(query));
        long count = shopOrderReadService.countAdminWithSnapshot(query);
        long pages = count / req.getSize() + (count % req.getSize() == 0 ? 0 : 1);
        if (count == 0L) {
            return PageVo.build(count, req.getSize(), req.getCurrent(), pages, Collections.emptyList());
        }
        query.put(CommonSqlConstant.OFFSET, (req.getCurrent() - 1) * req.getSize());
        query.put(CommonSqlConstant.LIMIT, req.getSize());
        query.put(CommonSqlConstant.SORT_BY, "created_at");
        query.put(CommonSqlConstant.SORT_TYPE, CommonSqlConstant.SORT_DESC);
        List<ShopOrder> shopOrderList = shopOrderReadService.pagesAdminWithSnapshot(query);
        return PageVo.build(count, req.getSize(), req.getCurrent(), pages, convertAdminShopOrderVo(shopOrderList));
    }

    private List<AdminShopOrderVo> convertAdminShopOrderVo(List<ShopOrder> shopOrderList) {
        List<Long> orderIds = shopOrderList.stream().map(OrderBase::getId).toList();
        Long shopId = shopOrderList.get(0).getShopId();
        Map<Long, List<BalanceDetail>> balanceDetailMap = balanceDetailLogic.getBalanceDetailMap(orderIds, shopId);
        // key = shopOrderId
        Map<Long, AgentPayOrder> agentOrderMap = agentPayOrderLogic.findMapByShopOrderIds(orderIds);
        // key = agentPayOrderId
        var agentPaymentMap = agentPayPaymentLogic.findMapByAgentPayOrderIds(
                agentOrderMap.values().stream().map(AgentPayOrder::getId).toList());

        List<AdminShopOrderVo> result = new ArrayList<>();
        for (ShopOrder shopOrder : shopOrderList) {
            List<BalanceDetail> balanceDetailList = balanceDetailMap.get(shopOrder.getId());
            AdminShopOrderVo shopOrderVo = BeanUtil.copyProperties(shopOrder, AdminShopOrderVo.class);
            shopOrderVo.setExtra(JSONUtil.parseObj(shopOrderVo.getExtraJson()));
            List<SkuOrder> skuOrderList = skuOrderLogic.listByShopOrderId(shopOrder.getId());
            List<SkuOrderDto> skuOrderDtoList = new ArrayList<>();
            long shopTax = 0L;
            for (SkuOrder skuOrder : skuOrderList) {
                SkuOrderDto skuOrderDto = BeanUtil.copyProperties(skuOrder, SkuOrderDto.class);
                if (skuOrder.getTax() != null) {
                    shopTax += skuOrder.getTax();
                }
                skuOrderDto.setSkuImage(skuOrder.getSkuImage() != null && !skuOrder.getSkuImage().startsWith("http") ? ossImageUrl + skuOrder.getSkuImage() : skuOrder.getSkuImage());
                skuOrderDtoList.add(skuOrderDto);
            }
            shopOrderVo.setOrderPushStatus(skuOrderList.get(0).getPushStatus());
            shopOrderVo.setOrderPushStatusDesc(SkuOrderPushStatus.fromInt(shopOrderVo.getOrderPushStatus()).desc());
            shopOrderVo.setShopTax(shopTax);
            boolean shouldWaitAuth = shopOrder.getStatus() != OrderStatus.NOT_PAID.getValue() && skuOrderList.stream().map(SkuOrder::getPushStatus)
                    .anyMatch(pushStatus -> pushStatus == SkuOrderPushStatus.WAITING_SELLER_AUTH.value());
            shopOrderVo.setWaitingSellerAuth(shouldWaitAuth);
            // 1. 判断shop是否开启审核
            Shop shop = shopCacheHolder.findShopById(shopId);
            // 2. 如果开启了审核则 设置订单显示状态
            if (ShopFunctionSlice.build(shop).isShopOpenOrderAuth()) {
                if (Objects.equals(OrderStatus.PAID.getValue(), shopOrder.getStatus())) {
                    shopOrderVo.setWaitingSellerAuthDesc(shopOrderVo.getWaitingSellerAuth() != null && shopOrderVo.getWaitingSellerAuth() ? "待审核" : "审核通过");
                }
            }
            OrderStatus status = OrderStatus.fromInt(shopOrder.getStatus());
            // 3. 此时如果订单状态为null 则再设置其本来的订单状态
            if (StrUtil.isBlank(shopOrderVo.getWaitingSellerAuthDesc())) {
                shopOrderVo.setWaitingSellerAuthDesc(status.intoString());
            }
            shopOrderVo.setSkuOrderDtoList(skuOrderDtoList);
            List<Payment> paymentList = paymentV2Logic.listByOrderId(shopOrderVo.getId());
            if (CollUtil.isNotEmpty(paymentList)) {
                shopOrderVo.setPaymentId(paymentList.get(0).getId());
                shopOrderVo.setPayOutId(paymentList.get(0).getOutId());
                shopOrderVo.setPaymentPushStatus(paymentList.get(0).getPushStatus());
                shopOrderVo.setPaymentPushStatusDesc(PaymentPushStatus.getEnums(shopOrderVo.getPaymentPushStatus()).getDesc());
                shopOrderVo.setThirdPartyTransactionNo(paymentList.get(0).getThirdPartyTransactionNo());
                shopOrderVo.setThirdPartyTransactionNoSource(paymentList.get(0).getThirdPartyTransactionNoSource());
            }
            List<OrderRoleSnapshot> orderRoleSnapshots = orderRoleSnapshotLogic.listByShopOrderIdAndSnapshotType(shopOrder.getId(), OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER);
            Map<Integer, OrderRoleSnapshot> orderRoleSnapshotByUserRoleMap = orderRoleSnapshots.stream().collect(Collectors.toMap(OrderRoleSnapshot::getUserRole, Function.identity()));
            ServiceProviderDto serviceProviderDto = getServiceProviderInfo(orderRoleSnapshotByUserRoleMap, balanceDetailList);
            shopOrderVo.setServiceProvider(serviceProviderDto);
            SubStoreDto subStoreDto = getSubStoreInfo(orderRoleSnapshotByUserRoleMap, balanceDetailList);
            shopOrderVo.setSubStore(subStoreDto);
            SubStoreGuiderDto subStoreGuiderDto = getSubStoreGuiderInfo(orderRoleSnapshotByUserRoleMap, balanceDetailList);
            shopOrderVo.setSubStoreGuiderDto(subStoreGuiderDto);
            AgentPayOrder agentPayOrder = agentOrderMap.get(shopOrder.getId());
            List<AgentPayPayment> agentPayPayments = agentPayOrder != null ? agentPaymentMap.get(agentPayOrder.getId()) : null;
            OrderGroupViewObject.AgentPayStatus agentPayStatus = getAgentPayStatus(agentPayOrder, agentPayPayments);
            shopOrderVo.setAgentPayStatusCode(agentPayStatus.getCode());
            shopOrderVo.setAgentPayStatusDescription(agentPayStatus.getDescription());
            // 设置订购人信息
            List<ReceiverInfo> receiverInfoList = receiverInfoReadService.findByOrderId(shopOrder.getId(), OrderLevel.SHOP).getResult();
            if (CollUtil.isNotEmpty(receiverInfoList)) {
                ReceiverInfo receiverInfo = receiverInfoList.get(0);
                shopOrderVo.setReceiveUserName(receiverInfo.getReceiveUserName());
                shopOrderVo.setPaperNo(receiverInfo.getPaperNo());
            }
            if (Objects.nonNull(shopOrder.getExceptionType())) {
                shopOrderVo.setExceptionMessage(shopOrder.getExceptionReason());
            }
            result.add(shopOrderVo);
        }
        return result;
    }


    public List<DropDownBoxVo> pushStatusDropDown() {
        List<SkuOrderPushStatus> skuOrderPushStatusList = SkuOrderPushStatus.getValidStatus();
		return skuOrderPushStatusList.stream().map(skuOrderPushStatus -> {
			DropDownBoxVo dropDownBoxVo = new DropDownBoxVo();
			dropDownBoxVo.setId(String.valueOf(skuOrderPushStatus.value()));
			dropDownBoxVo.setName(skuOrderPushStatus.desc());
			return dropDownBoxVo;
		}).toList();
    }

	public void updateOrderPushException(Long orderId, Integer exceptionType, String exceptionMessage) {
		orderWriteService.updateOrderPushException(orderId, exceptionType, exceptionMessage);
	}

    public OrderDetail getOrderDetail(ShopOrderDto req) {
        if (ObjUtil.isEmpty(req.getId())) {
            throw new ApiException("订单id不能为空");
        }
        OrderDetail orderDetail = orderReadLogic.orderDetail(req.getId()).getResult();
        if (ObjUtil.isEmpty(orderDetail)) {
            throw new ApiException("订单详情不存在");
        }
        PayInfoVO payInfo = orderDetail.getPayInfo();
        if (Objects.nonNull(payInfo)) {
            payInfo.desensitization();
        }
        List<OrderReceiverInfo> orderReceiverInfos = orderDetail.getOrderReceiverInfos();
        if (!CollectionUtils.isEmpty(orderReceiverInfos)) {
            orderReceiverInfos.forEach(item -> {
                // 脱敏
                ReceiverInfo receiverInfo = item.getReceiverInfo();
                receiverInfo.desensitization();
            });
        }
        Refund refund = refundLogic.getExistFlowRefundByOrderId(req.getId());
        if (ObjUtil.isNotEmpty(refund)) {
            orderDetail.setRefundId(refund.getId());
            RefundReturnDetail refundReturnDetail = refundReturnDetailLogic.getByRefundId(refund.getId());
            if (refundReturnDetail!= null) {
                orderDetail.setSellerAddress(refundReturnDetail.getSellerAddress());
            }
        }
        return orderDetail;
    }

    public OrderDetail showReceiverInfo(ShopOrderDto req) {
        if (ObjUtil.isEmpty(req.getId())) {
            throw new ApiException("订单id不能为空");
        }
        return orderReadLogic.showReceiverInfo(req.getId()).getResult();
    }

    public List<? extends ExpressTrackInfo> express(ShopOrderDto req) {
        if (ObjUtil.isEmpty(req.getId())) {
            throw new ApiException("订单id不能为空");
        }
        return orderExpressDetailCenter.queryOrderExpressDetail(req.getId());
    }

    public AdminShopOrderNumberVo adminOrderNumberByStatus(AdminShopOrderPageDto req) {
		AdminShopOrderNumberVo result = new AdminShopOrderNumberVo();
        req.setStatusList(Collections.emptyList());
		Map<String, Object> query = req.toMap();
        query.put("exceptionTypes", List.of(OrderExceptionEnum.SYSTEM_ERROR.getCode(), OrderExceptionEnum.UPLOAD_PAYMENT_ORIGINAL_INFO_FAILED.getCode()));
		log.info("获取有异常数量查询条件 {}", JSONUtil.toJsonStr(query));
		long exceptionNum = shopOrderReadService.countAdminWithSnapshot(query);
		result.setExceptionNum(exceptionNum);
		return result;
	}

    public void updateOrderActualPurchaser(List<Long> orderIds, Long buyerId) {
        if (CollUtil.isEmpty(orderIds)) {
            return;
        }
        log.info("将订单原本的下单人 转变成实际购买人 需要修改的订单号 {} 实际购买人的id {}", JSONUtil.toJsonStr(orderIds),buyerId);
        shopOrderWriteService.updateOrderActualPurchaser(orderIds, buyerId);
    }

    public List<ShopOrder> list(Map<String, Object> query) {
        return shopOrderReadService.list(query);
    }
}
