package moonstone.web.core.fileNew.consumer;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.fileNew.chain.ShopMiniPublishHandlerChain;
import moonstone.common.constants.RocketMQConstant;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * author：书生
 */
@Component
@RocketMQMessageListener(
        topic = RocketMQConstant.APP_ADMIN_TOPIC,
        consumerGroup = RocketMQConstant.ADMIN_QUERY_BUILD_CONSUMER_GROUP,
        selectorExpression = RocketMQConstant.ADMIN_BUILD_QUERY_TAG
)
@Slf4j
public class AdminBuildQueryConsumer implements RocketMQListener<String> {

    @Resource
    private ShopMiniPublishHandlerChain shopMiniPublishHandlerChain;


    @Override
    public void onMessage(String msg) {
        // 传入了 shopWxaId  appType appVersion
        log.info("收到查询小程序构建状态的消息 传入的参数：{}",msg);
        JSONObject param = JSONUtil.parseObj(msg);
        shopMiniPublishHandlerChain.buildQuery(param);
    }
}
