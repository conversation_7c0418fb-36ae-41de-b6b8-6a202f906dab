package moonstone.web.core.fileNew.dto;

import lombok.Data;
import moonstone.order.api.OrderAndOperation;
import moonstone.order.dto.SkuComboDTO;

import java.util.Date;
import java.util.List;

/**
 * author：书生
 */
@Data
public class SkuOrderDto {

    /**
     * sku order id
     */
    private Long id;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * sku 版本号
     */
    private Integer skuVersion;

    /**
     * sku数量
     */
    private Long quantity;

    /**
     * 实付金额
     */
    private Long fee;

    /**
     * 子订单状态
     */
    private Integer status;

    /**
     * 是否保税(1:保税，0:完税)
     */
    private Integer isBonded;

    /**
     * 税费
     */
    private Long tax;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 买家id
     */
    private Long buyerId;

    /**
     * 外部自订单id
     */
    private String outId;

    /**
     * 买家名称
     */
    private String buyerName;

    /**
     * 买家外部id
     */
    private String outBuyerId;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 是否为第三方商品（0：为自建商品，1：第三方商品）
     */
    private Integer isThirdPartyItem;

    /**
     * sku主图
     */
    private String skuImage;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺外部id
     */
    private String outShopId;

    /**
     * 外部订单来源
     */
    private String outFrom;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 外部sku编号
     */
    private String outerSkuId;

    /**
     * 推送状态(0:不需要推送，1：待推送，2：完成推送)
     */
    private Integer pushStatus;

    /**
     * 推单失败原因
     */
    private String pushErrorMsg;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * sku属性, json表示
     */
    private String skuAttributes;

    /**
     * 订单渠道 1-pc, 2-手机
     */
    private Integer channel;

    /**
     * 支付类型 1-在线支付 2-货到付款
     */
    private Integer payType;

    /**
     * 配送方式
     */
    private Integer shipmentType;

    /**
     * 原价
     */
    private Long originFee;

    /**
     * 优惠后费用（不含税费）
     */
    private Long afterDiscountFee;

    /**
     * 折扣
     */
    private Long discount;

    /**
     * 运费
     */
    private Long shipFee;

    /**
     * 运费折扣
     */
    private Long shipFeeDiscount;

    /**
     * 积分减免金额
     */
    private Integer integral;

    /**
     * 余额减免金额
     */
    private Integer balance;

    /**
     * 单品级别的优惠id
     */
    private Long promotionId;

    /**
     * 商品快照id
     */
    private Long itemSnapshotId;

    /**
     * 是否申请过退款
     */
    private Integer hasRefund;

    /**
     * 是否已开具过发票
     */
    private Integer invoiced;

    /**
     * 是否已评价
     */
    private Integer commented;

    /**
     * 是否申请过售后
     */
    private Integer hasApplyAfterSale;

    /**
     * 电商平台佣金费率, 万分之一
     */
    private Integer commissionRate;

    /**
     * 分销抽佣费率, 万分之一
     */
    private Integer distributionRate;

    /**
     * 改价金额
     */
    private Integer diffFee;

    /**
     * 子订单额外信息,json表示
     */
    private String extraJson;

    /**
     * 子订单tag信息, json表示
     */
    private String tagsJson;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 发货时间
     */
    private Date shipmentAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 仓库名称
     */
    private String depotName;

    private String supplierName;

    private Long gatherOrderId;
    /**
     * 仓库编码
     */
    private String depotCode;

    /**
     * 仓库快照
     */
    private Long depotId;

    private Long feeId;

    private Long extraId;

    private Long profitId;

    /**
     * 订单来源类型
     */
    private Long outFromType;
    /**
     * sku图片快照
     */
    private Long skuImageId;

    /**
     * 订单上的各种标记(二进制位)
     */
    private Integer flag;
    /**
     * 子订单的分类快照
     */
    private String categoryNameSnapshot;

    /**
     * 发货仓类型(1:代塔仓自有,2：京东云交易)
     */
    private Integer shippingWarehouseType;

    /**
     * 组合品相关信息
     */
    private List<SkuComboDTO> skuComboList;


    /**
     * 订单上的sku操作列表
     */
    private List<? extends OrderAndOperation> skuOrderAndOperations;


    private String specification;


    private Long price;
    
}
