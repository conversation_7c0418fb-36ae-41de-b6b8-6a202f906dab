package moonstone.web.core.fileNew.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.logic.PaymentV2Logic;
import moonstone.web.core.fileNew.logic.ShopOrderLogic;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RocketMQMessageListener(
		topic = RocketMQConstant.APP_MERCHANT_TOPIC,
		consumerGroup = RocketMQConstant.ORDER_EXCEPTION_RECORD_CONSUMER_GROUP,
		selectorExpression = RocketMQConstant.ORDER_EXCEPTION_RECORD_TAG
)
public class OrderExceptionRecordConsumer implements RocketMQListener<String> {

	@Resource
	private ShopOrderLogic shopOrderLogic;

	@Resource
	private PaymentV2Logic paymentV2Logic;


	@Override
	public void onMessage(String msg) {
		log.info("接收到订单异常推送消息 {}",msg);
		JSONObject msgJson = JSONUtil.parseObj(msg);
		List<Long> recordOrderIdList = new ArrayList<>();
		Long orderId = msgJson.get("orderId", Long.class);
		if (ObjUtil.isNotEmpty(orderId)) {
			recordOrderIdList.add(orderId);
		}
		Long paymentId = msgJson.get("paymentId", Long.class);
		if (ObjUtil.isNotEmpty(paymentId)) {
			List<Long> orderIdList = paymentV2Logic.listOrderIdByPaymentId(paymentId);
			if (CollUtil.isNotEmpty(orderIdList)) {
				recordOrderIdList.addAll(orderIdList);
			}
		}
		Integer exceptionType = msgJson.get("exceptionType", Integer.class);
		String exceptionMessage = msgJson.get("exceptionMessage", String.class);
		if (CollUtil.isNotEmpty(recordOrderIdList)) {
			for (Long recordOrderId : recordOrderIdList) {
				shopOrderLogic.updateOrderPushException(recordOrderId, exceptionType, exceptionMessage);
			}
		}
	}
}
