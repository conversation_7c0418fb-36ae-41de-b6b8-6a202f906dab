package moonstone.web.core.fileNew.strategy.processor;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.ApiException;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.dto.*;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.fileNew.strategy.api.RefundStrategy;
import moonstone.web.core.fileNew.strategy.factory.RefundStrategyFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RefundProcessor {

	@Resource
	private RefundStrategyFactory refundStrategyFactory;

	@Resource
	private RocketMQProducer rocketMQProducer;


	/**
	 * 申请退款
	 *
	 * @param refundApplyDto 请求参数
	 * @return 退款单id
	 */
	@Transactional(rollbackFor = Exception.class)
	public Long apply(RefundApplyDto refundApplyDto) {
		RefundStrategy refundStrategy = refundStrategyFactory.getRefundStrategy(refundApplyDto.getSkuOrder().getShippingWarehouseType());
		if (ObjUtil.isEmpty(refundStrategy)) {
			throw new ApiException("未找到合适的申请退款策略");
		}
		Long refundId = refundStrategy.apply(refundApplyDto);
		// 发送MQ  异步消费申请退款与第三方系统联动
		JSONObject msg = new JSONObject();
		msg.set("orderId", refundApplyDto.getOrderId());
		msg.set("refundId", refundId);
		rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.REFUND_APPLY_THIRD_PARTY_TAG, msg.toString());
		return refundId;
	}

	/**
	 * 同意退款申请
	 *
	 * @param refundApplyAgreeDto 请求参数
	 * @return 是否同意
	 */
	public Boolean applyAgree(RefundApplyAgreeDto refundApplyAgreeDto) {
		RefundStrategy refundStrategy = refundStrategyFactory.getRefundStrategy(refundApplyAgreeDto.getRefund().getSourceType());
		if (ObjUtil.isEmpty(refundStrategy)) {
			throw new ApiException("未找到合适的同意退款申请策略");
		}
		return refundStrategy.applyAgree(refundApplyAgreeDto);
	}

	/**
	 * 拒绝退款申请
	 * @param refundApplyRejectDto 请求参数
	 * @return 是否成功
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean applyReject(RefundApplyRejectDto refundApplyRejectDto) {
		RefundStrategy refundStrategy = refundStrategyFactory.getRefundStrategy(refundApplyRejectDto.getRefund().getSourceType());
		if (ObjUtil.isEmpty(refundStrategy)) {
			throw new ApiException("未找到合适的拒绝退款申请策略");
		}
		Boolean success = refundStrategy.applyReject(refundApplyRejectDto);
		JSONObject msg = new JSONObject();
		msg.set("orderId", refundApplyRejectDto.getOrderId());
		msg.set("refundId", refundApplyRejectDto.getRefundId());
		rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.REFUND_REJECT_THIRD_PARTY_TAG, msg.toString());
		return success;
	}


	/**
	 * 上传退款快递单号
	 * @param refundUploadExpressInfoDto 请求参数
	 * @return 是否成功
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean uploadExpressInfo(RefundUploadExpressInfoDto refundUploadExpressInfoDto) {
		RefundStrategy refundStrategy = refundStrategyFactory.getRefundStrategy(refundUploadExpressInfoDto.getRefund().getSourceType());
		if (ObjUtil.isEmpty(refundStrategy)) {
			throw new ApiException("未找到合适的上传退款快递单号策略");
		}
		Boolean success =  refundStrategy.uploadExpressInfo(refundUploadExpressInfoDto);
		JSONObject msg = new JSONObject();
		msg.set("orderId", refundUploadExpressInfoDto.getOrderId());
		msg.set("refundId", refundUploadExpressInfoDto.getRefundId());
		rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.REFUND_AFTER_SALE_LOGISTICS_UPLOAD_THIRD_PARTY_TAG, msg.toString());
		return success;
	}

	public Boolean refundHandle(RefundHandleDto refundHandleDto) {
		RefundStrategy refundStrategy = refundStrategyFactory.getRefundStrategy(refundHandleDto.getRefund().getSourceType());
		if (ObjUtil.isEmpty(refundStrategy)) {
			throw new ApiException("未找到合适的上传退款快递单号策略");
		}
		return refundStrategy.refundHandle(refundHandleDto);
	}
}
