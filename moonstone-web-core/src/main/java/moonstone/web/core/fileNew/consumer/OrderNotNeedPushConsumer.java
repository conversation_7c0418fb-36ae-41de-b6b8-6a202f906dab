package moonstone.web.core.fileNew.consumer;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.model.Payment;
import moonstone.order.model.SkuOrder;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.logic.PaymentV2Logic;
import moonstone.web.core.fileNew.logic.SkuOrderLogic;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@RocketMQMessageListener(
		topic = RocketMQConstant.APP_MERCHANT_TOPIC,
		consumerGroup = RocketMQConstant.ORDER_NOT_NEED_PUSH_CONSUMER_GROUP,
		selectorExpression = RocketMQConstant.ORDER_NOT_NEED_PUSH_TAG
)
@Slf4j
@Component
public class OrderNotNeedPushConsumer implements RocketMQListener<String> {


	@Resource
	private PaymentV2Logic paymentV2Logic;

	@Resource
	private SkuOrderLogic skuOrderLogic;


	@Override
	public void onMessage(String msg) {
		log.info("接收到订单无需推送的消息 {}",msg);
		JSONObject message = JSONUtil.parseObj(msg);
		Long orderId = message.get("orderId", Long.class);
		List<SkuOrder> skuOrderList = skuOrderLogic.listByShopOrderId(orderId);
		log.info("关联的skuOrder列表 {}",JSONUtil.toJsonStr(skuOrderList));
		for (SkuOrder skuOrder : skuOrderList) {
			if (!skuOrder.getPushStatus().equals(SkuOrderPushStatus.FINISHED.value())) {
				skuOrder.setPushStatus(SkuOrderPushStatus.NOT_NEED.value());
				skuOrderLogic.update(skuOrder);
			}
		}
		List<Payment> payments = paymentV2Logic.listByOrderId(orderId);
		log.info("关联的支付单列表 {}",JSONUtil.toJsonStr(payments));
		for (Payment payment : payments) {
			if (!payment.getPushStatus().equals(PaymentPushStatus.PUSH_SUCCESS.getValue())) {
				payment.setPushStatus(PaymentPushStatus.NO_NEED_PUSH.getValue());
				paymentV2Logic.update(payment);
			}
		}
	}
}
