package moonstone.web.core.fileNew.logic;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.ApiException;
import moonstone.common.model.vo.ComboBoxVo;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.common.model.vo.PageVo;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.ItemWriteService;
import moonstone.itemBrand.dto.ItemBrandDto;
import moonstone.itemBrand.dto.ItemBrandPageDto;
import moonstone.itemBrand.model.ItemBrand;
import moonstone.itemBrand.service.ItemBrandService;

/**
 * @Author: yousx
 * @Date: 2025/02/24
 * @Description:
 */
@Service
@Slf4j
public class ItemBrandLogic {

    @Resource
    private ItemBrandService itemBrandService;
    @Resource
    private ItemWriteService itemWriteService;
    @Resource
    private ItemReadService itemReadService;

    public void save(ItemBrand itemBrand) {
        itemBrandService.save(itemBrand);
    }

    public void delete(Long id) {
        itemBrandService.delete(id);
    }

    public PageVo<ItemBrandDto> pages(ItemBrandPageDto itemBrandPageDto) {
        return itemBrandService.pages(itemBrandPageDto);
    }

    public List<ComboBoxVo> getDropDownBox(Long shopId) {
        return itemBrandService.getDropDownBox(shopId);
    }

    public ItemBrandDto findByName(String name, Long shopId) {
        return itemBrandService.findByName(name, shopId);
    }

    public ItemBrandDto findById(Long id) {
        return itemBrandService.findById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(ItemBrand itemBrand) {
        List<Item> items = itemReadService.findByBrandId(itemBrand.getId());
        if (!CollectionUtils.isEmpty(items)) {
            for (Item item : items) {
                Item updateItem = new Item();
                updateItem.setId(item.getId());
                updateItem.setBrandName(itemBrand.getName());
                itemWriteService.update(updateItem);
            }
        }
        itemBrandService.update(itemBrand);
    }
}
