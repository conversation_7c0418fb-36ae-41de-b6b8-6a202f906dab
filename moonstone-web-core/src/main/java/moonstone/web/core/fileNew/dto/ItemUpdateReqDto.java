package moonstone.web.core.fileNew.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * author：书生
 */
@Data
public class ItemUpdateReqDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商品id
     */
    private Long id;

    /**
     * 商品id列表
     */
    private List<Long> ids;

    /**
     * 最低价
     */
    private Integer lowPrice;

    /**
     * 最高价
     */
    private Integer highPrice;

    /**
     * 售价
     */
    private Integer price;

    /**
     * 平台可售库存
     */
    private Integer stockQuantity;

    /**
     * 商品状态
     */
    private Integer status;

    /**
     * 商品分类
     */
    private List<ShopCategoryItemsDto> shopCategoryItemsList;

    /**
     * 商品停售
     */
    private Integer sellOutStatus;


    /**
     * 可售地区模板id
     */
    private Long restrictedSalesAreaTemplateId;
}
