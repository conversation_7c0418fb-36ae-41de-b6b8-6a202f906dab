package moonstone.web.core.fileNew.consumer;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.service.ThirdPartyJobService;
import moonstone.thirdParty.service.ThirdPartyUserShopReadService;
import moonstone.common.constants.RocketMQConstant;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
@RocketMQMessageListener(
		topic = RocketMQConstant.APP_MERCHANT_TOPIC,
		consumerGroup = RocketMQConstant.PRODUCT_THIRD_PARTY_STOCK_SYNCHRONIZATION_CONSUMER_GROUP,
		selectorExpression = RocketMQConstant.PRODUCT_THIRD_PARTY_STOCK_SYNCHRONIZATION_TAG
)
public class ProductThirdPartyStockSyncConsumer implements RocketMQListener<String> {


	@Resource
	private ThirdPartyJobService thirdPartyJobService;

	@Resource
	private ThirdPartyUserShopReadService thirdPartyUserShopReadService;

	@Override
	public void onMessage(String msg) {
		log.info("接收到商品同步第三方库存的消息 {}",msg);
		JSONObject message = JSONUtil.parseObj(msg);
		Integer thirdPartyId = message.get("thirdPartyId", Integer.class);
		Long shopId = message.get("shopId", Long.class);
		ThirdPartyUserShop thirdPartyUserShop = thirdPartyUserShopReadService.findByThirdPartyIdAndShopId(thirdPartyId, shopId).getResult();
		if (ObjUtil.isEmpty(thirdPartyUserShop)) {
			log.error("店铺id {} 同步平台 {} 未找到对应的绑定信息",shopId,thirdPartyId);
			return;
		}
		log.info("查询到的绑定信息 {}",JSONUtil.toJsonStr(thirdPartyUserShop));
		try {
			thirdPartyJobService.synchronize(ThirdPartySystem.fromInt(thirdPartyUserShop.getThirdPartyId()), thirdPartyUserShop);
		} catch (Exception e) {
			log.error("同步失败 失败原因 {}",e.getMessage(),e);
			return;
		}
		log.info("店铺id {} 同步平台 {} 对应的商品库存同步完成",shopId,thirdPartyId);

	}
}
