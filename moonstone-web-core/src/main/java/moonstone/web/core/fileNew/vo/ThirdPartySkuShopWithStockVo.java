package moonstone.web.core.fileNew.vo;

import lombok.Data;
import moonstone.thirdParty.dto.DepotStock;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ThirdPartySkuShopWithStockVo {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 第三方标识+skuCode（只是做拼接，前端要求）
     */
    private String id;

    /**
     * 第三方平台标识
     */
    private Integer thirdPartyId;

    /**
     * 第三方平台名称
     */
    private String thirdPartyName;

    /**
     * 第三方skuCode
     */
    private String outerSkuId;

    /**
     * 第三方sku名称
     */
    private String outerSkuName;

    /**
     * 总正品库存
     */
    private Integer totalAuthenticStock;

    /**
     * 总次品库存
     */
    private Integer totalDefectiveStock;

    /**
     * 状态（true：已匹配，false：未匹配）
     */
    private Boolean isMatch;

    /**
     * 商品id列表
     */
    private String itemIds;

    /**
     * 贸易类型
     *
     * @see moonstone.item.emu.SkuTypeEnum
     */
    private Integer type;

    /**
     * 贸易类型描述
     */
    private String typeDesc;

    /**
     * 仓库库存信息
     */
    private List<DepotStock> depotStocks;

    /**
     * 商品来源(1：代塔仓自有，2：京东云交易)
     */
    private Integer sourceType;

    /**
     * 商品来源名称
     */
    private String sourceTypeName;


}
