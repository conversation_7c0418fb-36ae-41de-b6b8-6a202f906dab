package moonstone.web.core.fileNew.consumer;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.shopMini.model.ShopMiniVersion;
import moonstone.web.core.fileNew.chain.ShopMiniPublishHandlerChain;
import moonstone.common.constants.RocketMQConstant;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * author：书生
 */
@Component
@RocketMQMessageListener(
        topic = RocketMQConstant.APP_ADMIN_TOPIC,
        consumerGroup = RocketMQConstant.ADMIN_QUERY_EXPERIENCE_CONSUMER_GROUP,
        selectorExpression = RocketMQConstant.ADMIN_QUERY_EXPERIENCE_TAG
)
@Slf4j
public class AdminQueryExperienceConsumer implements RocketMQListener<String> {

    @Resource
    private ShopMiniPublishHandlerChain shopMiniPublishHandlerChain;

    @Override
    public void onMessage(String msg) {
        log.info("收到查询生成体验版状态的消息 传入的参数：{}",msg);
        ShopMiniVersion shopMiniVersion = JSONUtil.toBean(msg, ShopMiniVersion.class);
        shopMiniPublishHandlerChain.queryExperience(shopMiniVersion);
    }
}
