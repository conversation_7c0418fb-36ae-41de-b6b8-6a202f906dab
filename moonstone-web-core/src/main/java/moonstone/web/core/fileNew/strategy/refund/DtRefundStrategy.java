package moonstone.web.core.fileNew.strategy.refund;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.*;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.UserUtil;
import moonstone.order.model.Refund;
import moonstone.order.model.RefundProcessRecord;
import moonstone.order.model.RefundReturnDetail;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.component.order.OutSideRefundExecutor;
import moonstone.web.core.fileNew.dto.RefundApplyDto;
import moonstone.web.core.fileNew.dto.RefundApplyRejectDto;
import moonstone.web.core.fileNew.dto.RefundThirdPartyDto;
import moonstone.web.core.fileNew.dto.RefundUploadExpressInfoDto;
import moonstone.web.core.fileNew.logic.RefundProcessRecordLogic;
import moonstone.web.core.fileNew.logic.RefundReturnDetailLogic;
import moonstone.web.core.fileNew.strategy.api.RefundStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DtRefundStrategy extends AbstractRefundStrategy implements RefundStrategy {


	@Resource
	private OutSideRefundExecutor outSideRefundExecutor;

	@Resource
	private UserReadService<User> userReadService;

	@Resource
	private RefundProcessRecordLogic refundProcessRecordLogic;

	@Resource
	private RefundReturnDetailLogic refundReturnDetailLogic;


	@Override
	public boolean isSupport(int shippingWarehouseType) {
		return shippingWarehouseType == ThirdPartySkuSourceEnum.DAI_TA.getCode();
	}

	@Override
	public void refundApplyThirdPartyInteraction(RefundThirdPartyDto refundThirdPartyDto) {
		Refund refund = refundThirdPartyDto.getRefund();
		Map<String, Object> query = new HashMap<>();
		query.put("refundId", refund.getId());
		RefundReturnDetail refundReturnDetail = refundReturnDetailLogic.getOne(query);
		RefundProcessRecord refundProcessRecord = new RefundProcessRecord();
		refundProcessRecord.setShopId(refund.getShopId());
		refundProcessRecord.setRefundId(refund.getId());
		refundProcessRecord.setRefundRound(RefundProcessRecordRoundEnum.REFUND_APPLY_RECEIVE_RECEIPT.getCode());
		refundProcessRecord.setType(RefundProcessRecordTypeEnum.DT_APPLY_REFUND_CALLBACK.getCode());
		refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.SYSTEM.getCode());
		refundProcessRecord.setOperationTime(new Date());
		Map<String, Object> content = new HashMap<>();

		Either<Boolean> result = outSideRefundExecutor.executeRefund_OM(refundThirdPartyDto.getShopOrder());
		try {
			Boolean success = result.getResult();
			if (success) {
				// 退款申请成功
				log.info("退款申请成功");
				refundReturnDetail.setApplyReceiptType(RefundReturnDetailApplyReceiptTypeEnum.RECEIPT_SUCCESS.getCode());
				content.put(RefundProcessRecordContentEnum.SYSTEM_NOTE.getCode(), "返回取消成功");
				refundProcessRecord.setContent(JSONUtil.toJsonStr(content));
			}else{
				// 退款申请失败
				log.info("退款申请失败 失败原因 订单无法被取消");
				refundReturnDetail.setApplyReceiptType(RefundReturnDetailApplyReceiptTypeEnum.RECEIPT_FAIL.getCode());
				refundReturnDetail.setApplyReceiptMsg("订单无法被取消");
				content.put(RefundProcessRecordContentEnum.SYSTEM_NOTE.getCode(), "返回取消失败,失败原因:订单无法被取消");
				refundProcessRecord.setContent(JSONUtil.toJsonStr(content));
			}
		} catch (Exception e) {
			// 退款申请失败
			log.info("退款申请失败异常 失败原因 {}", e.getMessage());
			refundReturnDetail.setApplyReceiptType(RefundReturnDetailApplyReceiptTypeEnum.RECEIPT_FAIL.getCode());
			refundReturnDetail.setApplyReceiptMsg(e.getMessage());
			content.put(RefundProcessRecordContentEnum.SYSTEM_NOTE.getCode(), "返回取消失败,失败原因:订单无法被取消");
			refundProcessRecord.setContent(JSONUtil.toJsonStr(content));
		}
		refundReturnDetailLogic.update(refundReturnDetail);
		refundProcessRecordLogic.save(refundProcessRecord);
	}

	@Override
	public void refundRejectThirdPartyInteraction(RefundThirdPartyDto refundThirdPartyDto) {

	}

	@Override
	public void refundAfterSaleLogisticsUploadThirdPartyInteraction(RefundThirdPartyDto refundThirdPartyDto) {

	}


	@Override
	public void doApplyRecord(RefundApplyDto refundApplyDto) {
		CommonUser currentUser = UserUtil.getCurrentUser();
		Map<String, Object> content = new HashMap<>();
		Refund refund = refundApplyDto.getRefund();
		RefundProcessRecord refundProcessRecord = new RefundProcessRecord();
		refundProcessRecord.setShopId(refund.getShopId());
		refundProcessRecord.setRefundId(refund.getId());
		refundProcessRecord.setRefundRound(RefundProcessRecordRoundEnum.REFUND_APPLY.getCode());
		if (ObjUtil.isEmpty(currentUser)) {
			// 系统发起退款
			refundProcessRecord.setType(RefundProcessRecordTypeEnum.SYSTEM_APPLY.getCode());
			refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.SYSTEM.getCode());
			refundProcessRecord.setOperatorName(RefundProcessRecordOperatorTypeEnum.SYSTEM.getDesc());
			content.put(RefundProcessRecordContentEnum.SYSTEM_NOTE.getCode(), refundApplyDto.getSystemNote());
		}else{
			if (currentUser.getId().equals(refund.getBuyerId())) {
				// 买家发起退款
				refundProcessRecord.setType(RefundProcessRecordTypeEnum.BUYER_APPLY.getCode());
				refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.BUYER.getCode());
				refundProcessRecord.setOperatorId(currentUser.getId());
				User user = userReadService.findById(currentUser.getId()).getResult();
				refundProcessRecord.setOperatorName(user.getName());
				content.put(RefundProcessRecordContentEnum.BUYER_NOTE.getCode(), refund.getBuyerNote());
			} else {
				// 商家发起退款
				refundProcessRecord.setType(RefundProcessRecordTypeEnum.SELLER_APPLY.getCode());
				refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.SELLER.getCode());
				refundProcessRecord.setOperatorId(currentUser.getId());
				refundProcessRecord.setOperatorName(RefundProcessRecordOperatorTypeEnum.SELLER.getDesc());
				content.put(RefundProcessRecordContentEnum.SELLER_NOTE.getCode(), refund.getSellerNote());
			}
		}
		refundProcessRecord.setOperationTime(new Date());
		refundProcessRecord.setRefundFee(refund.getFee());
		refundProcessRecord.setRefundType(refund.getRefundType());
		content.put(RefundProcessRecordContentEnum.APPLY_REASON.getCode(), RefundReasonTypeEnum.getDesc(refund.getReasonType()));
		content.put(RefundProcessRecordContentEnum.APPLY_IMAGES.getCode(), refund.getImagesJson());
		content.put(RefundProcessRecordContentEnum.BUYER_RECEIVE.getCode(), refund.getBuyerReceivedStatus());
		refundProcessRecord.setContent(JSONUtil.toJsonStr(content));
		refundProcessRecordLogic.save(refundProcessRecord);
	}

	@Override
	public void doApplyRejectRecord(RefundApplyRejectDto refundApplyRejectDto) {
		super.doApplyRejectRecord(refundApplyRejectDto);
	}

	@Override
	public void doUploadExpressInfoRecord(RefundUploadExpressInfoDto refundUploadExpressInfoDto) {
		super.doUploadExpressInfoRecord(refundUploadExpressInfoDto);
	}
}
