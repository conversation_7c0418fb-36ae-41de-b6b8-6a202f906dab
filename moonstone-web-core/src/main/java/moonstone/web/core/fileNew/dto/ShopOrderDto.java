package moonstone.web.core.fileNew.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import moonstone.common.model.BaseQuery;

import java.util.Date;
import java.util.List;

/**
 * author：书生
 */
@Getter
@Setter
public class ShopOrderDto  extends BaseQuery {

    /**
     * 订单号
     */
    private Long id;

    /**
     * 店铺Id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 申报号
     */
    private String declareId;

    /**
     * 门店id
     */
    private String outShopId;

    /**
     * 类型 （默认为 1）
     */
    private Integer type;

    /**
     * 订单类型
     *
     * @see moonstone.common.enums.OrderOutFrom
     */
    private String outFrom;

    /**
     * 推送状态
     *
     * @see moonstone.order.dto.fsm.PaymentPushStatus
     */
    private Integer pushStatus;

    /**
     * 支付单号
     */
    private String paymentOutId;

    /**
     * 门店名称
     */
    private String subStoreName;

    /**
     * 服务商名称
     */
    private String serviceProviderName;

    /**
     * 导购员
     */
    private String guiderName;

    /**
     * 订单标记
     */
    private Integer orderFlag;

    /**
     * 订单标记列表
     */
    private List<Integer> orderFlags;

    /**
     * 订单状态
     *
     * @see moonstone.order.dto.fsm.OrderStatus
     */
    private Integer status;

    /**
     * 下单开始时间
     */
    private Date orderTimeStart;
    /**
     * 下单结束时间
     */
    private Date orderTimeEnd;

    /**
     * 发货开始时间
     */
    private Date shipTimeStart;

    /**
     * 发货结束时间
     */
    private Date shipTimeEnd;

    /**
     * 确认收货开始时间
     */
    private Date confirmTimeStart;

    /**
     * 确认收货结束时间
     */
    private Date confirmTimeEnd;

    /**
     * 小二端 下单开始日期
     */
    private Date startAt;

    /**
     * 小二端 下单结束日期
     */
    private Date endAt;

    /**
     * 小二端 发货开始时间
     */
    private Date start;

    /**
     * 小二端 发货结束时间
     */
    private Date end;

    private String statusStr;

    /**
     * 第三方交易单号
     */
    private List<String> thirdPartyTransactionNoList;
    /**
     * 发货仓类型(1:代塔仓自有,2：京东云交易)
     */
    private Integer skuOrderShippingWarehouseType;

    /**
     * 支付流水号
     */
    private String paySerialNo;

    @ApiModelProperty(value = "是否评价 1 否, 2 是")
    private Integer evaluateStatus;

}
