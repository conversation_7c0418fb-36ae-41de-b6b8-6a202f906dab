package moonstone.web.core.fileNew.consumer;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.refund.service.RefundCallBackService;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
@RocketMQMessageListener(
		topic = RocketMQConstant.APP_MERCHANT_TOPIC,
		consumerGroup = RocketMQConstant.REFUND_CALLBACK_SUCCESS_CONSUMER_GROUP,
		selectorExpression = RocketMQConstant.REFUND_CALLBACK_SUCCESS_TAG
)
@Slf4j
public class RefundCallbackSuccessConsumer  implements RocketMQListener<String> {

	@Resource
	private RefundCallBackService refundCallBackService;

	@Override
	public void onMessage(String msg) {
		log.info("接收到退款成功的消息 {}", msg);
		JSONObject message = JSONUtil.parseObj(msg);
		String outId = message.get("outId", String.class);
		Date refundAt = message.get("refundAt", Date.class);
		refundCallBackService.refundCallBack(outId, refundAt);
	}
}
