package moonstone.web.core.fileNew.logic;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.mongo.SkuHistory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * author：书生
 */
@Slf4j
@Service
public class SkuHistoryLogic {

	@Resource
	private MongoTemplate mongoTemplate;

	/**
	 * 根据skuId和版本号查询sku历史信息
	 *
	 * @param skuId   skuId
	 * @param version 版本号
	 * @return sku历史信息
	 */
	public SkuHistory getSkuHistory(Long skuId, Integer version) {
		Criteria criteria = Criteria.where("sku_id").is(skuId).and("version").is(version);
		return mongoTemplate.findOne(new Query(criteria), SkuHistory.class);
	}

}
