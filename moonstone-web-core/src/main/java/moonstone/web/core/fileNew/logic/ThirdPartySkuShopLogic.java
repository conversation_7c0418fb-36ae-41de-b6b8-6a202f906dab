package moonstone.web.core.fileNew.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.ResultCode;
import moonstone.common.constants.CommonSqlConstant;
import moonstone.common.constants.ShopExtra;
import moonstone.common.constants.Y800ServiceName;
import moonstone.common.exception.ApiException;
import moonstone.common.model.ApiV3Resp;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Y800OpenRequestV3;
import moonstone.common.model.rpcAPI.y800Storage.Y800JdInventoryQuery;
import moonstone.common.model.rpcAPI.y800Storage.Y800JdInventoryRes;
import moonstone.common.model.vo.PageVo;
import moonstone.common.utils.UserUtil;
import moonstone.item.emu.SkuTypeEnum;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.thirdParty.dto.DepotStock;
import moonstone.thirdParty.model.*;
import moonstone.thirdParty.service.*;
import moonstone.web.core.fileNew.dto.ItemCheckStockDto;
import moonstone.web.core.fileNew.dto.ThirdPartySkuDetailDto;
import moonstone.web.core.fileNew.dto.ThirdPartySkuShopDto;
import moonstone.web.core.fileNew.dto.ThirdPartySkuShopWithStockPageDto;
import moonstone.web.core.fileNew.enums.SourceTypeEnum;
import moonstone.web.core.fileNew.vo.ThirdPartySkuShopVo;
import moonstone.web.core.fileNew.vo.ThirdPartySkuShopWithStockVo;
import moonstone.web.core.fileNew.y800.client.Y800V3Client;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ThirdPartySkuShopLogic {


    @Resource
    private ThirdPartySkuShopReadService thirdPartySkuShopReadService;

    @Resource
    private ThirdPartySkuStockReadService thirdPartySkuStockReadService;

    @Resource
    private ThirdPartySkuReadService thirdPartySkuReadService;

    @Resource
    private ThirdPartySkuDetailReadService thirdPartySkuDetailReadService;

    @Resource
    private ThirdPartySkuDetailOssReadService thirdPartySkuDetailOssReadService;

    @Resource
    private SkuReadService skuReadService;

    @Resource
    private ThirdPartyUserShopReadService thirdPartyUserShopReadService;

    @Resource
    private Y800V3Client y800V3Client;

    @Resource
    private ShopReadService shopReadService;

    public PageVo<ThirdPartySkuShopWithStockVo> pagesThirdPartySkuShopWithStock(ThirdPartySkuShopWithStockPageDto req) {
        if (req.getShopId() == null) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            if (currentUser == null) {
                throw new ApiException(ResultCode.UN_AUTHORIZED);
            }
            req.setShopId(currentUser.getShopId());
        }
        if (ObjUtil.isEmpty(req.getThirdPartyId())) {
            // 默认查询的是 API V3 同步过来的商品
            req.setThirdPartyId(3);
        }
        Map<String, Object> query = req.toMap();
        Long total = thirdPartySkuShopReadService.countThirdPartySkuShopWithStock(query);
        long pages = total / req.getSize() + (total % req.getSize() == 0 ? 0 : 1);
        if (total == 0L) {
            return PageVo.build(total, req.getSize(), req.getCurrent(), pages, Collections.emptyList());
        }
        long offset = (req.getCurrent() - 1) * req.getSize();
        query.put(CommonSqlConstant.OFFSET, offset);
        query.put(CommonSqlConstant.LIMIT, req.getSize());
        List<ThirdPartySkuShop> thirdPartySkuShopList = thirdPartySkuShopReadService.pagesThirdPartySkuShopWithStock(query);
        return PageVo.build(total, req.getSize(), req.getCurrent(), pages, convert(thirdPartySkuShopList));
    }

    private List<ThirdPartySkuShopWithStockVo> convert(List<ThirdPartySkuShop> thirdPartySkuShopList) {
        return thirdPartySkuShopList.stream().map(thirdPartySkuShop -> {
            ThirdPartySkuShopWithStockVo entity = BeanUtil.copyProperties(thirdPartySkuShop, ThirdPartySkuShopWithStockVo.class);
            //第三方标识+skuCode（只是做拼接，前端要求）
            entity.setId(thirdPartySkuShop.getThirdPartyId() + thirdPartySkuShop.getOuterSkuId());
            List<ThirdPartySkuStock> thirdPartySkuStocks = thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(thirdPartySkuShop.getShopId(), thirdPartySkuShop.getThirdPartyId(), thirdPartySkuShop.getOuterSkuId()).getResult();
            log.info("查询到的第三方sku的库存信息 skuId {} {}", thirdPartySkuShop.getOuterSkuId(), JSONUtil.toJsonStr(thirdPartySkuStocks));
            Integer totalAuthenticStock = thirdPartySkuStocks.stream().map(ThirdPartySkuStock::getAuthenticStock).reduce(0, Integer::sum);
            entity.setTotalAuthenticStock(totalAuthenticStock);
            Integer totalDefectiveStock = thirdPartySkuStocks.stream().map(ThirdPartySkuStock::getDefectiveStock).reduce(0, Integer::sum);
            entity.setTotalDefectiveStock(totalDefectiveStock);
            List<DepotStock> depotStocks = getDepotStocksInfo(thirdPartySkuStocks);
            entity.setDepotStocks(depotStocks);
            entity.setSourceTypeName(SourceTypeEnum.getNameByCode(entity.getSourceType()));
            appendMatchInfo(entity);
            entity.setTypeDesc(SkuTypeEnum.getDesc(entity.getType()));
            return entity;

        }).toList();
    }

    private void appendMatchInfo(ThirdPartySkuShopWithStockVo entity) {
        if (ObjUtil.isEmpty(entity)) {
            return;
        }
        String outerSkuId = entity.getOuterSkuId();
        Long shopId = entity.getShopId();
        List<Sku> skus = skuReadService.findSkuByOuterSkuId(shopId, outerSkuId).getResult();
        Boolean isMatch = CollUtil.isNotEmpty(skus);
        entity.setIsMatch(isMatch);
        if (isMatch) {
            String itemIds = skus.stream().map(Sku::getItemId).map(String::valueOf).collect(Collectors.joining(","));
            entity.setItemIds(itemIds);
        }
    }

    /**
     * 获取第三方商品的库存信息列表
     * @param thirdPartySkuStocks 第三方商品库存信息
     * @return 第三方商品的库存信息列表
     */
    public List<DepotStock> getDepotStocksInfo(List<ThirdPartySkuStock> thirdPartySkuStocks) {
        if (CollUtil.isEmpty(thirdPartySkuStocks)) {
            return Collections.emptyList();
        }
        return thirdPartySkuStocks.stream().map(thirdPartySkuStock -> {
            DepotStock depotStock = new DepotStock();
            depotStock.setDepotCode(thirdPartySkuStock.getDepotCode());
            depotStock.setDepotName(thirdPartySkuStock.getDepotName());
            return depotStock;
        }).toList();

    }

    public ThirdPartySkuShopVo getInfo(ThirdPartySkuShopDto req) {
        checkParam(req);
        if (StrUtil.isBlank(req.getOuterSkuId())) {
            throw new ApiException("第三方sku编码不能为空");
        }
        Map<String, Object> query = req.toMap();
        ThirdPartySkuShop thirdPartySkuShop = thirdPartySkuShopReadService.getOne(query);
        Response<ThirdPartySku> thirdPartySkuRes = thirdPartySkuReadService.findByThirdPartyAndSkuId(thirdPartySkuShop.getThirdPartyId(), thirdPartySkuShop.getOuterSkuId());
        ThirdPartySkuShopVo thirdPartySkuShopVo = BeanUtil.copyProperties(thirdPartySkuShop, ThirdPartySkuShopVo.class);
        ThirdPartySku result = thirdPartySkuRes.getResult();
        if (result != null) {
            thirdPartySkuShopVo.setUnit(result.getUnit());
        }
        thirdPartySkuShopVo.setSourceTypeName(SourceTypeEnum.getNameByCode(thirdPartySkuShop.getSourceType()));

        optimizeThirdPartySkuDetails(thirdPartySkuShop, thirdPartySkuShopVo);
        return thirdPartySkuShopVo;
    }

    public void optimizeThirdPartySkuDetails(ThirdPartySkuShop thirdPartySkuShop, ThirdPartySkuShopVo thirdPartySkuShopVo) {
        // 处理类型为 1 的 SKU 图片
        List<ThirdPartySkuDetail> skuDetails1 = thirdPartySkuDetailReadService.getDetailsByOuterSkuId(thirdPartySkuShop.getOuterSkuId(), 1);
        List<ThirdPartySkuDetailDto> skuDetails = processThirdPartySkuDetails(skuDetails1, thirdPartySkuShop);
        thirdPartySkuShopVo.setSkuDetails(skuDetails);

        // 处理类型为 2 的 SKU 图片
        List<ThirdPartySkuDetail> skuDetails2 = thirdPartySkuDetailReadService.getDetailsByOuterSkuId(thirdPartySkuShop.getOuterSkuId(), 2);
        List<ThirdPartySkuDetailDto> skuDetailImages = processThirdPartySkuDetails(skuDetails2, thirdPartySkuShop);
        thirdPartySkuShopVo.setSkuDetailImages(skuDetailImages);
    }


    // 通用方法处理第三方 SKU 详情
    private List<ThirdPartySkuDetailDto> processThirdPartySkuDetails(List<ThirdPartySkuDetail> skuDetails,
                                                                     ThirdPartySkuShop thirdPartySkuShop) {
        List<ThirdPartySkuDetailDto> list = new ArrayList<>();

        if (CollUtil.isNotEmpty(skuDetails)) {
            // 获取所有路径
            List<String> paths = skuDetails.stream()
                    .map(ThirdPartySkuDetail::getPath)
                    .collect(Collectors.toList());
            // 根据路径获取 OSS 数据
            List<ThirdPartySkuDetailOss> ossList = thirdPartySkuDetailOssReadService.findByPaths(thirdPartySkuShop.getOuterSkuId(), paths);
            Map<String, ThirdPartySkuDetailOss> ossMap = ossList.stream()
                    .collect(Collectors.toMap(ThirdPartySkuDetailOss::getPath, Function.identity()));

            // 遍历 SKU 详情
            for (ThirdPartySkuDetail skuDetail : skuDetails) {
                ThirdPartySkuDetailDto dto = BeanUtil.copyProperties(skuDetail, ThirdPartySkuDetailDto.class);
                ThirdPartySkuDetailOss oss = ossMap.get(dto.getPath());
                dto.setPath(oss != null ? oss.getOssPath() : dto.getPath());

                list.add(dto);
            }
        }
        return list;
    }

    private void checkParam(ThirdPartySkuShopDto req) {
        if (ObjUtil.isEmpty(req.getShopId())) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            if (currentUser == null) {
                throw new ApiException(ResultCode.UN_AUTHORIZED);
            }
            req.setShopId(currentUser.getShopId());
        }
        if (ObjUtil.isEmpty(req.getThirdPartyId())) {
            req.setThirdPartyId(3);
        }
    }

    public Boolean checkStockInfo(ItemCheckStockDto itemCheckStockDto) {
        Response<ThirdPartyUserShop> shopRes = thirdPartyUserShopReadService.findByThirdPartyIdAndShopId(3, itemCheckStockDto.getShopId());
        ThirdPartyUserShop shop = shopRes.getResult();
        if (shop == null) {
            throw new RuntimeException("店铺信息不存在");
        }
        Response<Shop> shopResponse = shopReadService.findById(shop.getShopId());
        Shop shopResult = shopResponse.getResult();
        if (shopResult == null) {
            throw new RuntimeException("店铺信息不存在");
        }
        if (!Boolean.TRUE.equals(shopResult.getJdStockCheck())) {
            return Boolean.TRUE;
        }


        List<ItemCheckStockDto.SkuQuantity> skuQuantityList = itemCheckStockDto.getSkuQuantityList();
        if (CollUtil.isEmpty(skuQuantityList)) {
            throw new RuntimeException("商品信息不能为空");
        }
        List<String> outerSkuIds = skuQuantityList.stream().map(ItemCheckStockDto.SkuQuantity::getOuterSkuId).collect(Collectors.toList());
        List<ThirdPartySkuShop> thirdPartySkuShops = thirdPartySkuShopReadService.listByOuterSkuIds(outerSkuIds);

        List<ThirdPartySkuShop> collect = thirdPartySkuShops.stream().filter(sku -> Objects.equals(SourceTypeEnum.JD.getCode(), sku.getSourceType())).toList();
        if (CollUtil.isEmpty(collect)) {
            return Boolean.TRUE;
        }
        List<String> jdSkuCode = collect.stream().map(ThirdPartySkuShop::getOuterSkuId).collect(Collectors.toList());

        String accessCode = shop.getExtra().get(ShopExtra.Y800StorageAccessCode.getCode());
        Y800OpenRequestV3 y800OpenRequest = new Y800OpenRequestV3();
        y800OpenRequest.setServiceName(Y800ServiceName.JDCtpInventoryQuery.getServiceName());
        Y800JdInventoryQuery y800JdInventoryQuery = new Y800JdInventoryQuery();
        y800JdInventoryQuery.setAccessCode(accessCode);

        List<Y800JdInventoryQuery.SkuQuantity> skuQuantities = new ArrayList<>();
        y800JdInventoryQuery.setSkuQuantityList(skuQuantities);
        for (ItemCheckStockDto.SkuQuantity skuQuantity : skuQuantityList) {
            if (jdSkuCode.contains(skuQuantity.getOuterSkuId())) {
                Y800JdInventoryQuery.SkuQuantity query = new Y800JdInventoryQuery.SkuQuantity();
                query.setSkuId(skuQuantity.getOuterSkuId());
                query.setQuantity(skuQuantity.getQuantity());
                skuQuantities.add(query);
            }
        }
        List<String> querySkuId = skuQuantities.stream().map(Y800JdInventoryQuery.SkuQuantity::getSkuId).toList();
        Map<String, Y800JdInventoryQuery.SkuQuantity> skuMap = skuQuantities.stream().collect(Collectors.toMap(Y800JdInventoryQuery.SkuQuantity::getSkuId, Function.identity()));
        ItemCheckStockDto.Address dtoAddress = itemCheckStockDto.getAddress();
        Y800JdInventoryQuery.Address address = new Y800JdInventoryQuery.Address();
        address.setProvinceId(0);
        address.setCityId(0);
        address.setCountyId(0);
        address.setTownId(0);
        address.setFullAddress(dtoAddress.getProvince() + dtoAddress.getCity() + dtoAddress.getRegion() + dtoAddress.getDetail());
        y800JdInventoryQuery.setAddress(address);
        y800OpenRequest.setBizData(y800JdInventoryQuery);
        y800OpenRequest.setAppId(shop.getThirdPartyCode());
        y800OpenRequest.sign(shop.getThirdPartyKey());

        ApiV3Resp apiV3Resp = y800V3Client.execute(y800OpenRequest);
        if (!apiV3Resp.isSuccess()) {
            throw new RuntimeException("库存交验失败: " + apiV3Resp.getError());
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonResponse = objectMapper.writeValueAsString(apiV3Resp.getResult());
            JsonNode resJsonNode = objectMapper.readTree(jsonResponse);
            JsonNode stockStateJsonArr = resJsonNode.get("stockStateList");
            List<Y800JdInventoryRes> stockStateList = objectMapper.readValue(
                    stockStateJsonArr.toString(),
                    new TypeReference<List<Y800JdInventoryRes>>() {}
            );
            for (Y800JdInventoryRes y800JdInventoryRes : stockStateList) {
                Y800JdInventoryRes.SkuQuantity skuQuantity = y800JdInventoryRes.getSkuQuantity();
                if (skuQuantity == null) {
                    throw new RuntimeException("库存信息异常");
                }
                log.info("check stock querySkuId: {}, resSkuId: {}", querySkuId, skuQuantity.getSkuId());
                if (querySkuId.contains(skuQuantity.getSkuId()) && !Objects.equals(y800JdInventoryRes.getAreaStockState(), 1)) {
                    log.warn("库存不足, sku: {}", skuQuantity.getSkuId());
                    return Boolean.FALSE;
                }
                Y800JdInventoryQuery.SkuQuantity reqSkuQty = skuMap.get(skuQuantity.getSkuId());
                if (reqSkuQty != null && reqSkuQty.getQuantity() > skuQuantity.getQuantity()) {
                    return Boolean.FALSE;
                }
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return true;
    }
}
