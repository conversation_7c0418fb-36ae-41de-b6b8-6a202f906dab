package moonstone.web.core.fileNew.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import io.terminus.common.model.Paging;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.DefaultShowEnum;
import moonstone.common.enums.ShopMiniAuthorizationStateEnum;
import moonstone.common.enums.ShopMiniVersionStateEnum;
import moonstone.common.exception.ApiException;
import moonstone.common.model.vo.PageVo;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.shopMini.model.ShopMiniVersion;
import moonstone.shopMini.service.ShopMiniVersionService;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaProjectWriteService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.shopWxa.service.ShopWxaWriteService;
import moonstone.web.core.fileNew.chain.ShopMiniPublishHandlerChain;
import moonstone.common.constants.LockKeyConstant;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.dto.*;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.fileNew.vo.MiniVersionVo;
import moonstone.web.core.fileNew.vo.ShopWxaVo;
import moonstone.wxa.model.WxaTemplate;
import moonstone.wxa.service.WxaTemplateReadService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * author：书生
 */
@Service
@Slf4j
public class ShopWxaLogic {

    @Resource
    private ShopWxaReadService shopWxaReadService;

    @Resource
    private ShopWxaWriteService shopWxaWriteService;

    @Resource
    private ShopReadService shopReadService;

    @Resource
    private ShopWxaProjectWriteService shopWxaProjectWriteService;

    @Resource
    private WxaTemplateReadService wxaTemplateReadService;

    @Resource
    private ShopWxaProjectReadService shopWxaProjectReadService;

    @Resource
    private ShopMiniPublishHandlerChain shopMiniPublishHandlerChain;

    @Resource
    private ShopMiniVersionService shopMiniVersionService;

    @Resource
    public RedissonClient redissonClient;

    @Resource
    private RocketMQProducer rocketMQProducer;


    /**
     * 创建店铺小程序基本信息并生成授权链接
     *
     * @param req 请求参数
     * @return 店铺小程序创建后的基本信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ShopWxaVo save(ShopWxaCreateDto req) {
        checkSaveParam(req);
        ShopWxa shopWxa = new ShopWxa();
        BeanUtil.copyProperties(req, shopWxa);
        shopWxa.setStatus(DefaultShowEnum.SHOW.getCode());
        shopWxa.setIsAuthorized(0);
        shopWxa.setPublicationType(1);
        shopWxa.setWhetherOnline(false);
        shopWxa.setAuthorizationState(ShopMiniAuthorizationStateEnum.UNAUTHORIZED.getCode());
        shopWxa = shopWxaWriteService.save(shopWxa);
        req.setShopWxaId(shopWxa.getId());
        log.info("生成店铺小程序id {}", shopWxa.getId());
        ShopAuthInvitationLinkDto shopAuthInvitationLinkDto = shopMiniPublishHandlerChain.generalInvitationLink(req);
        shopWxa.setAppInviteUrl(shopAuthInvitationLinkDto.getAppInviteUrl());
        shopWxa.setPcInviteUrl(shopAuthInvitationLinkDto.getPcInviteUrl());
        shopWxaWriteService.updateInfo(shopWxa);
        ShopWxaProject shopWxaProject = new ShopWxaProject();
        shopWxaProject.setShopWxaId(shopWxa.getId());
        shopWxaProject.setStatus(DefaultShowEnum.SHOW.getCode());
        shopWxaProjectWriteService.save(shopWxaProject);
        ShopWxaVo result = new ShopWxaVo();
        BeanUtil.copyProperties(shopWxa, result);
        return result;
    }


    /**
     * 参数校验
     *
     * @param req 请求参数
     */
    private void checkSaveParam(ShopWxaCreateDto req) {
        Integer appType = req.getAppType();
        Long shopId = req.getShopId();
        Shop shop = shopReadService.findById(shopId).getResult();
        if (shop == null) {
            throw new ApiException("指定商家不存在");
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("appType", appType);
        queryMap.put("shopId", shopId);
        List<ShopWxa> shopWxaList = shopWxaReadService.list(queryMap);
        if (shopWxaList != null && !shopWxaList.isEmpty()) {
            for (ShopWxa shopWxa : shopWxaList) {
                if (StrUtil.isBlank(shopWxa.getAuthorizerAccessToken())) {
                    throw new ApiException("该id为："+ shopWxa.getId() +"的店铺小程序已生成邀请链接，请及时授权，才可创建该店铺其他小程序");
                }
            }
        }

    }

    /**
     * 分页查询店铺小程序信息列表
     *
     * @param req 请求参数
     * @return 店铺小程序信息列表
     */
    public PageVo<ShopWxaVo> pages(ShopWxaPageDto req) {
        long current = req.getCurrent() == 0 ? 1 : req.getCurrent();
        long size = req.getSize() == 0 ? 10 : req.getSize();
        req.setCurrent(current);
        req.setSize(size);
        long offset = (current - 1) * size;
        Map<String, Object> query = new HashMap<>();
        query.put("publicationType", 1);
        if (req.getWxaTemplatesId() != null) {
            query.put("wxaTemplatesId", req.getWxaTemplatesId());
        }
        if (StrUtil.isNotBlank(req.getShopName())) {
            query.put("shopName", req.getShopName());
        }
        if (req.getAppType() != null) {
            query.put("appType", req.getAppType());
        }
        query.put("offset", offset);
        query.put("limit", size);
        query.put("sortBy", "id");
        query.put("sortType", "2");
        Paging<ShopWxa> paging = shopWxaReadService.pages(query);
        Long total = paging.getTotal();
        long pages = total / size + (total % size > 0 ? 1 : 0);
        if (total == 0) {
            return PageVo.build(total, size, current, pages, List.of());
        }
        List<ShopWxaVo> dataList = paging.getData().stream().map(shopWxa -> {
            ShopWxaVo entity = new ShopWxaVo();
            BeanUtil.copyProperties(shopWxa, entity);
            if (entity.getWxaTemplatesId() != null) {
                WxaTemplate wxaTemplate = wxaTemplateReadService.findById(entity.getWxaTemplatesId()).getResult();
                entity.setWxaTemplatesName(wxaTemplate.getName());
            }
            return entity;
        }).toList();
        return PageVo.build(total, size, current, pages, dataList);
    }

    /**
     * 修改店铺小程序信息
     *
     * @param req 请求参数
     * @return 是否修改成功
     */
    public Boolean updateInfo(ShopWxaUpdateDto req) {
        ShopWxa update = new ShopWxa();
        BeanUtil.copyProperties(req, update);
        if (req.getWxaTemplatesId() != null) {
            ShopWxa shopWxa = shopWxaReadService.findById(req.getId()).getResult();
            if (shopWxa.getWhetherOnline()) {
                // 当前店铺小程序 已经上架了
                if (!shopWxa.getWxaTemplatesId().equals(req.getWxaTemplatesId())) {
                    // 模板id不同  下架现在的小程序
                    offline(shopWxa.getId());
                }
            }
        }
        return shopWxaWriteService.updateInfo(update);
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean upload(ShopWxaUploadDto req) {
        Map<String, Object> query = new HashMap<>();
        query.put("id", req.getTemplateId());
        WxaTemplate wxaTemplate = wxaTemplateReadService.getOne(query);
        if (wxaTemplate == null) {
            throw new ApiException("未找到对应的模板信息");
        }
        Long shopWxaId = req.getShopWxaId();
        query = new HashMap<>();
        query.put("id", shopWxaId);
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        if (shopWxa == null) {
            throw new ApiException("未找到对应店铺小程序信息");
        }
        if (!shopWxa.getWxaTemplatesId().equals(req.getTemplateId())) {
            throw new ApiException("传入模板与设置的模板不一致");
        }
        if (StrUtil.isBlank(shopWxa.getAuthorizerAccessToken()) || shopWxa.getIsAuthorized() == 0) {
            throw new ApiException("该商家并未授权成功，不能进行构建操作");
        }

        query = new HashMap<>();
        query.put("shopWxaId", shopWxaId);
        query.put("templateId", req.getTemplateId());
        query.put("version", req.getAppVersion());
        ShopMiniVersion exist = shopMiniVersionService.getOne(query);
        if (exist != null) {
            throw new ApiException("该版本已构建，请勿重复构建");
        }
        query = new HashMap<>();
        query.put("shopWxaId", shopWxaId);
        query.put("templateId", req.getTemplateId());
        query.put("versionState", ShopMiniVersionStateEnum.IN_BUILD.getCode());
        exist = shopMiniVersionService.getOne(query);
        if (exist != null) {
            throw new ApiException("现存在正在构建的版本，请勿发起构建");
        }
        query = new HashMap<>();
        query.put("shopWxaId", shopWxaId);
        ShopWxaProject shopWxaProject = shopWxaProjectReadService.getOne(query);
        // 构建
        JSONObject param = new JSONObject();
        param.put("appType", shopWxa.getAppType());
        param.put("token", shopWxa.getAuthorizerAccessToken());
        param.put("shopId", shopWxa.getShopId());
        param.put("appId", shopWxa.getAppId());
        param.put("shopWxaProjectId", shopWxaProject.getId());
        param.put("templateId", wxaTemplate.getTemplateId());
        param.put("appVersion", req.getAppVersion());
        param.put("templateVersion", req.getTemplateVersion());
        String lockKey = LockKeyConstant.DECORATION_SHOP_MINI_APP_UPLOAD_PREFIX + shopWxaId;
        // 防止一下并发 以及重复操作
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean flag = lock.tryLock(0, 5, TimeUnit.SECONDS);
            if (!flag) {
                throw new ApiException("已经发起构建操作，请勿再次进行构建");
            }
            // 构建小程序
            shopMiniPublishHandlerChain.uploadMiniApp(param);
            // 构建成功 之后
            // 生成构建版本 构建的版本号 只能比之前的版本号都要高 所以只会生成 不存在修改操作
            ShopMiniVersion shopMiniVersion = new ShopMiniVersion();
            shopMiniVersion.setShopId(shopWxa.getShopId());
            shopMiniVersion.setShopWxaId(shopWxaId);
            shopMiniVersion.setTemplateId(shopWxa.getWxaTemplatesId());
            shopMiniVersion.setVersion(req.getAppVersion());
            shopMiniVersion.setVersionType(shopWxa.getAppType());
            shopMiniVersion.setTemplateVersion(req.getTemplateVersion());
            shopMiniVersion.setTemplateVersionDesc(req.getTemplateVersionDesc());
            shopMiniVersion.setBuildStartTime(new Date());
            shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.IN_BUILD.getCode());
            shopMiniVersion.setWhetherExperience(false);
            shopMiniVersion.setWhetherOnline(false);
            shopMiniVersion.setFailedReason("");
            shopMiniVersionService.save(shopMiniVersion);
            JSONObject msg = new JSONObject();
            msg.put("shopWxaId", shopWxaId);
            msg.put("appType", shopWxa.getAppType());
            msg.put("appVersion", req.getAppVersion());
            // 发送延迟队列 轮训查询一下
            rocketMQProducer.sendDelayMessage(
                    RocketMQConstant.APP_ADMIN_TOPIC,
                    RocketMQConstant.ADMIN_BUILD_QUERY_TAG,
                    msg.toString(),
                    1
            );
            return true;
        } catch (Exception e) {
            log.error("店铺小程序构建失败 失败原因 {}", e.getMessage(),e);
            throw new ApiException("构建失败 失败原因 " + e.getMessage());
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    public Boolean submitCreateExperience(Long id) {
        Map<String, Object> query = new HashMap<>();
        query.put("id", id);
        ShopMiniVersion shopMiniVersion = shopMiniVersionService.getOne(query);
        if (shopMiniVersion == null) {
            throw new ApiException("未找到相应的店铺小程序迭代版本信息");
        }
        // 向第三方发起生成体验版之前  先查询当下是否有体验版存在 若有 则先修改为不是体验版
        query = new HashMap<>();
        query.put("shopWxaId", shopMiniVersion.getShopWxaId());
        query.put("templateId", shopMiniVersion.getTemplateId());
        query.put("versionType", shopMiniVersion.getVersionType());
        query.put("whetherExperience", true);
        ShopMiniVersion currentExperience = shopMiniVersionService.getOne(query);
        if (currentExperience != null) {
            // 当下有体验版存在
            currentExperience.setWhetherExperience(false);
            shopMiniVersionService.update(currentExperience);
        }
        shopMiniPublishHandlerChain.createExperience(shopMiniVersion);
        return true;
    }

    /**
     * 提交审核
     *
     * @param req 请求参数
     * @return 是否提交成功
     */
    public Boolean submitAudit(ShopMiniSubmitAuditDto req) {
        Long id = req.getId();
        String version = req.getVersion();
        String versionDesc = req.getVersionDesc();
        Map<String, Object> query = new HashMap<>();
        query.put("id", id);
        ShopMiniVersion shopMiniVersion = shopMiniVersionService.getOne(query);
        if (shopMiniVersion == null) {
            throw new ApiException("未找到店铺小程序迭代信息");
        }
        if (!shopMiniVersion.getId().equals(id)) {
            throw new ApiException("未找到店铺小程序迭代信息");
        }
        if (!shopMiniVersion.getVersion().equals(version)) {
            throw new ApiException("选择的版本与需要审核的版本不符合");
        }
        Integer versionState = shopMiniVersion.getVersionState();
        if (versionState.equals(ShopMiniVersionStateEnum.OFFLINE.getCode())) {
            throw new ApiException("下架的版本不允许提交审核");
        }
        shopMiniVersion.setVersionDesc(versionDesc);
        //  提交审核
        shopMiniPublishHandlerChain.submitAudit(shopMiniVersion);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean callback(Long shopWxaId) {
        //  回滚
        Map<String, Object> query = new HashMap<>();
        query.put("id", shopWxaId);
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        if (shopWxa == null) {
            throw new ApiException("未找到需要下架的店铺小程序");
        }
        if (!shopWxa.getWhetherOnline()) {
            throw new ApiException("该店铺小程序未上架，无需进行下架操作");
        }

        query = new HashMap<>();
        query.put("shopWxaId", shopWxaId);
        query.put("templateId", shopWxa.getWxaTemplatesId());
        query.put("appType", shopWxa.getAppType());
        query.put("versionState", ShopMiniVersionStateEnum.ONLINE.getCode());
        ShopMiniVersion currentShopMiniVersion = shopMiniVersionService.getOne(query);
        if (currentShopMiniVersion == null) {
            throw new ApiException("未查到当前上架的店铺小程序迭代信息");
        }
        currentShopMiniVersion.setVersionState(ShopMiniVersionStateEnum.OFFLINE.getCode());
        currentShopMiniVersion.setOfflineTime(new Date());
        currentShopMiniVersion.setFailedReason("");
        currentShopMiniVersion.setWhetherOnline(false);
        // 版本回滚
        shopMiniPublishHandlerChain.rollback(shopWxa);
        JSONObject params = new JSONObject();
        params.put("appType", shopWxa.getAppType());
        params.put("token", shopWxa.getAuthorizerAccessToken());
        params.put("versionStatus", 7);
        List<MiniVersionVo> miniVersionList = shopMiniPublishHandlerChain.queryVersionList(params);
        MiniVersionVo miniVersionVo = miniVersionList.get(0);
        String appVersion = miniVersionVo.getAppVersion();
        query = new HashMap<>();
        query.put("shopWxaId", shopWxaId);
        query.put("templateId", shopWxa.getWxaTemplatesId());
        query.put("appType", shopWxa.getAppType());
        query.put("version", appVersion);
        ShopMiniVersion callbackShopMiniVersion = shopMiniVersionService.getOne(query);
        if (callbackShopMiniVersion != null) {
            callbackShopMiniVersion.setFailedReason("");
            callbackShopMiniVersion.setOnlineTime(new Date());
            callbackShopMiniVersion.setWhetherOnline(true);
            callbackShopMiniVersion.setVersionState(ShopMiniVersionStateEnum.ONLINE.getCode());
            shopMiniVersionService.update(callbackShopMiniVersion);
        }
        shopWxa.setOnlineVersion(appVersion);
        shopWxaWriteService.updateInfo(shopWxa);
        shopMiniVersionService.update(currentShopMiniVersion);
        return true;
    }

    public Boolean offline(Long shopWxaId) {
        // 下架
        Map<String, Object> query = new HashMap<>();
        query.put("id", shopWxaId);
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        if (shopWxa == null) {
            throw new ApiException("未找到需要下架的店铺小程序");
        }
        if (!shopWxa.getWhetherOnline()) {
            throw new ApiException("该店铺小程序未上架，无需进行下架操作");
        }
        shopMiniPublishHandlerChain.offline(shopWxa);
        shopWxa.setWhetherOnline(false);
        shopWxa.setOnlineVersion("");
        shopWxaWriteService.updateInfo(shopWxa);
        query = new HashMap<>();
        query.put("shopWxaId", shopWxaId);
        query.put("templateId", shopWxa.getWxaTemplatesId());
        query.put("appType", shopWxa.getAppType());
        query.put("versionState", ShopMiniVersionStateEnum.ONLINE.getCode());
        ShopMiniVersion shopMiniVersion = shopMiniVersionService.getOne(query);
        shopMiniVersion.setFailedReason("");
        shopMiniVersion.setWhetherOnline(false);
        shopMiniVersion.setAuditPassedState(3);
        shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.OFFLINE.getCode());
        shopMiniVersion.setOfflineTime(new Date());
        shopMiniVersion.setFailedReason("");
        shopMiniVersionService.update(shopMiniVersion);
        return true;
    }


    public ShopAuthInvitationLinkDto getInvitationLink(Long id) {
        Map<String, Object> query = new HashMap<>();
        query.put("id", id);
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        if (shopWxa == null) {
            throw new ApiException("未找到对应店铺小程序信息");
        }
        if (!shopWxa.getId().equals(id)) {
            throw new ApiException("未找到对应店铺小程序信息");
        }
        ShopAuthInvitationLinkDto shopAuthInvitationLinkDto = new ShopAuthInvitationLinkDto();
        shopAuthInvitationLinkDto.setAppInviteUrl(shopWxa.getAppInviteUrl());
        shopAuthInvitationLinkDto.setPcInviteUrl(shopWxa.getPcInviteUrl());
        return shopAuthInvitationLinkDto;
    }
}
