package moonstone.web.core.shop.application;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.shop.enums.ShopPayInfoExtraIndexEnum;
import moonstone.shop.enums.ShopPayInfoPayChannelEnum;
import moonstone.shop.enums.ShopPayInfoStatusEnum;
import moonstone.shop.enums.ShopPayInfoUsageChannelEnum;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.ShopPayInfoReadService;
import moonstone.shop.service.ShopPayInfoWriteService;
import moonstone.user.model.User;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserReadService;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.web.core.files.utils.FileUtil;
import moonstone.web.core.files.utils.ZipUtil;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ShopPayInfoComponent {

    @Resource
    private ShopPayInfoWriteService shopPayInfoWriteService;

    @Resource
    private ShopPayInfoReadService shopPayInfoReadService;

    @Resource
    private UserRelationEntityReadService userRelationEntityReadService;

    @Resource
    private UserReadService<User> userReadService;

    @Value("${pay.certFilePath}")
    private String certFilePath;

    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    /**
     * 获取支付单使用的支付渠道
     *
     * @param shopId       商家id
     * @param usageChannel 使用渠道（pc/wechat app）
     * @param payerUserId  当前的支付人的用户id
     * @return
     */
    @Deprecated
    public PaymentChannelEnum getPaymentChannel(Long shopId, ShopPayInfoUsageChannelEnum usageChannel, Long payerUserId) {
        var list = shopPayInfoReadService.find(shopId, usageChannel,
                List.of(ShopPayInfoStatusEnum.ACTIVE, ShopPayInfoStatusEnum.GRAY_RELEASE)).getResult();
        if (CollectionUtils.isEmpty(list)) {
            log.error("ShopPayInfoComponent.getPaymentChannel, shopId={}, usageChannel={} has no valid shopPayInfo",
                    shopId, usageChannel.getCode());
            return null;
        }

        // 优先使用适用的灰度配置
        var greyOne = list.stream()
                .filter(entity -> ShopPayInfoStatusEnum.GRAY_RELEASE.getCode().equals(entity.getStatus())).findAny();
        if (payerUserId != null && greyOne.isPresent() && isValidGreyMember(greyOne.get(), payerUserId)) {
            return PaymentChannelEnum.convert(ShopPayInfoPayChannelEnum.parse(greyOne.get().getPayChannel()));
        }

        // "使用中"的配置
        var activeOne = list.stream()
                .filter(entity -> ShopPayInfoStatusEnum.ACTIVE.getCode().equals(entity.getStatus()))
                .findAny()
                .orElseThrow(() -> new RuntimeException("没有使用中的支付配置"));
        return PaymentChannelEnum.convert(ShopPayInfoPayChannelEnum.parse(activeOne.getPayChannel()));
    }


    /**
     * 获取支付单使用的支付渠道
     *
     *  TODO 参考老方法 getPaymentChannel
     *
     * @param shopOrderId  订单id
     * @param payerUserId  当前的支付人的用户id
     * @return
     */
    public PaymentChannelEnum getPaymentChannelNew(Long shopOrderId, Long payerUserId) {
        List<ShopPayInfo> payInfoList = tokenShopPayInfo.listShopPayInfo(shopOrderId);
        log.info("根据订单查询支付配置信息 payInfoList={}", JSON.toJSONString(payInfoList));
        if (CollectionUtils.isEmpty(payInfoList)) {
            log.error("ShopPayInfoComponent.getPaymentChannel, shopOrderId={}, payerUserId={} has no valid shopPayInfo",
                    shopOrderId, payerUserId);
            return null;
        }

        // 优先使用适用的灰度配置
        var greyOne = payInfoList.stream()
                .filter(entity -> Objects.equals(1, entity.getRelationType())).findAny();
        if (payerUserId != null && greyOne.isPresent() && isValidGreyMember(greyOne.get(), payerUserId)) {
            return PaymentChannelEnum.convert(ShopPayInfoPayChannelEnum.parse(greyOne.get().getPayChannel()));
        }

        // "使用中"的配置
        var activeOne = payInfoList.stream()
                .filter(entity -> Objects.equals(2, entity.getRelationType()))
                .findAny()
                .orElseThrow(() -> new RuntimeException("没有使用中的支付配置"));
        return PaymentChannelEnum.convert(ShopPayInfoPayChannelEnum.parse(activeOne.getPayChannel()));
    }

    /**
     * 新增配置
     *
     * @param shopPayInfo
     * @param file
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean create(ShopPayInfo shopPayInfo, MultipartFile file, MultipartFile file2) throws Exception {
        var response = shopPayInfoWriteService.create(shopPayInfo);
        if (response.getResult() == null) {
            throw new RuntimeException(response.getError());
        }

        if (ShopPayInfoPayChannelEnum.WECHATPAY.getCode().equals(shopPayInfo.getPayChannel())) {
            // 微信支付要这个证书文件(退款时使用)
            uploadWechatPayCertFile(shopPayInfo.getShopId(), response.getResult(), file);
        } else if (ShopPayInfoPayChannelEnum.ALLINPAY_YST.getCode().equals(shopPayInfo.getPayChannel())) {
            // 通联支付 - 云商通，需要 公钥证书文件 与 私钥证书文件
            if (file == null || file.isEmpty() || file2 == null || file2.isEmpty()) {
                throw new RuntimeException("公/私钥证书文件皆不能为空");
            }
            String publicKey = uploadFile(shopPayInfo.getShopId(), response.getResult(), file);
            String secretKey = uploadFile(shopPayInfo.getShopId(), response.getResult(), file2);

            updateKeyFilePathForAllInPayYST(response.getResult(), publicKey, secretKey);
        }

        return true;
    }

    /**
     * 更新配置
     *
     * @param updateObject
     * @param file
     * @return
     * @throws Exception
     */
    public boolean update(ShopPayInfo updateObject, MultipartFile file, MultipartFile file2) throws Exception {
        var response = shopPayInfoWriteService.update(updateObject);
        if (!Boolean.TRUE.equals(response.getResult())) {
            throw new RuntimeException(response.getError());
        }

        if (ShopPayInfoPayChannelEnum.WECHATPAY.getCode().equals(updateObject.getPayChannel()) &&
                (file != null && !file.isEmpty())) {
            // 微信支付要这个证书文件(退款时使用)
            uploadWechatPayCertFile(updateObject.getShopId(), updateObject.getId(), file);
        }
        if (ShopPayInfoPayChannelEnum.ALLINPAY_YST.getCode().equals(updateObject.getPayChannel()) &&
                ((file != null && !file.isEmpty()) || (file2 != null && !file2.isEmpty()))) {
            // 通联支付 - 云商通，需要 公钥证书文件 与 私钥证书文件
            String publicKey = uploadFile(updateObject.getShopId(), updateObject.getId(), file);
            String secretKey = uploadFile(updateObject.getShopId(), updateObject.getId(), file2);

            updateKeyFilePathForAllInPayYST(updateObject.getId(), publicKey, secretKey);
        }

        return true;
    }

    /**
     * 上传支付证书
     *
     * @param shopId
     * @param shopPayInfoId
     * @param file
     * @throws Exception
     */
    public void uploadWechatPayCertFile(Long shopId, Long shopPayInfoId, MultipartFile file) throws Exception {
        // 文件类型
        String contentType = file.getContentType();
        if (!(Objects.equals(contentType, "application/zip") || Objects.requireNonNull(contentType).equals("application/x-pkcs12"))) {
            log.warn("certFile type is illegal, contentType={}, shopPayInfoId={}", contentType, shopPayInfoId);
            throw new RuntimeException("上传证书文件类型不合法");
        }

        // 名字
        String fileName = file.getOriginalFilename();
        if (contentType.equals("application/x-pkcs12") && !Objects.equals(fileName, "apiclient_cert.p12")) {
            log.warn("certFile name is illegal, fileName fileName={}, shopPayInfoId={}", fileName, shopPayInfoId);
            throw new RuntimeException("上传证书文件名称不合法");
        }

        // 文件存放路径
        String filePath = certFilePath + "/" + shopId + "/";

        if (!FileUtil.delAllFile(filePath)) {
            log.error("[op:clearFilePath]fail to clear filePath({})", filePath);
            throw new RuntimeException("f文件目录清空失败");
        }
        FileUtil.uploadFile(file.getBytes(), filePath, fileName);

        if (contentType.equals("application/zip")) {
            //是zip压缩包则解压
            ZipUtil.unZipFile(filePath + fileName, filePath);
            if (!(new File(filePath + "apiclient_cert.p12").exists())) {
                throw new RuntimeException("上传的压缩包中未找到证书文件");
            }
        }
    }

    /**
     * 全量发布
     *
     * @param target
     * @return 被停用的配置
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ShopPayInfo> fullRelease(ShopPayInfo target) {
        // 当前使用中的
        var deleteList = shopPayInfoReadService.find(target.getShopId(),
                ShopPayInfoUsageChannelEnum.parse(target.getUsageChannel()), List.of(ShopPayInfoStatusEnum.ACTIVE)).getResult();


        // “使用中”的配置变为“已停用”
        var revoked = shopPayInfoWriteService.revokeActive(target.getShopId(), target.getUsageChannel());
        if (StringUtils.isNotBlank(revoked.getError())) {
            throw new RuntimeException(revoked.getError());
        }

        // 当前配置变为“使用中”
        var updateObject = new ShopPayInfo();
        updateObject.setId(target.getId());
        updateObject.setStatus(ShopPayInfoStatusEnum.ACTIVE.getCode());
        var updated = shopPayInfoWriteService.update(updateObject);
        if (!Boolean.TRUE.equals(updated.getResult())) {
            throw new RuntimeException(updated.getError());
        }

        return deleteList;
    }

    /**
     * 判断指定用户是否为商家的会员
     *
     * @param shopId
     * @param userId
     * @return
     */
    public boolean judgeMemberUser(Long shopId, Long userId) {
        var response = userRelationEntityReadService.findByUserIdAndRelationIdAndType(userId,
                shopId, UserRelationEntity.UserRelationType.Member);

        return response.getResult() != null;
    }

    /**
     * 灰度发布
     *
     * @param target
     * @param members
     */
    public void greyRelease(ShopPayInfo target, List<String> members) {
        // 当前是否有其他“灰度中”的配置
        var list = shopPayInfoReadService.find(target.getShopId(), ShopPayInfoStatusEnum.GRAY_RELEASE).getResult();
        if (!CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("当前已有其它支付配置处于灰度中");
        }

        // 更新
        var response = shopPayInfoWriteService.update(buildGreyReleaseUpdate(target, members));
        if (!Boolean.TRUE.equals(response.getResult())) {
            throw new RuntimeException(response.getError());
        }
    }

    private ShopPayInfo buildGreyReleaseUpdate(ShopPayInfo target, List<String> members) {
        var updateObject = new ShopPayInfo();
        updateObject.setId(target.getId());
        updateObject.setStatus(ShopPayInfoStatusEnum.GRAY_RELEASE.getCode());

        var extra = target.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        extra.put(ShopPayInfoExtraIndexEnum.GREY_RELEASE_MEMBERS.getCode(), JSON.toJSONString(members));
        updateObject.setExtra(extra);

        return updateObject;
    }

    /**
     * 停止灰度发布
     *
     * @param shopPayInfoId
     */
    public void stopGreyRelease(Long shopPayInfoId) {
        var response = shopPayInfoWriteService.updateStatus(shopPayInfoId, ShopPayInfoStatusEnum.INACTIVE,
                ShopPayInfoStatusEnum.GRAY_RELEASE);
        if (!Boolean.TRUE.equals(response.getResult())) {
            throw new RuntimeException(response.getError());
        }
    }

    /**
     * 停用
     *
     * @param shopPayInfoId
     */
    public void stopUsage(Long shopPayInfoId) {
        var response = shopPayInfoWriteService.updateStatus(shopPayInfoId, ShopPayInfoStatusEnum.INACTIVE,
                ShopPayInfoStatusEnum.ACTIVE);
        if (!Boolean.TRUE.equals(response.getResult())) {
            throw new RuntimeException(response.getError());
        }
    }

    /**
     * 判断用户是否是支付配置里的灰度成员
     *
     * @param shopPayInfo
     * @param payerUserId
     * @return
     */
    private boolean isValidGreyMember(ShopPayInfo shopPayInfo, Long payerUserId) {
        var user = userReadService.findById(payerUserId).getResult();
        if (user == null) {
            log.warn("ShopPayInfoComponent.isValidGreyMember, payerUserId={}, 查无此人", payerUserId);
            return false;
        }
        if (StringUtils.isBlank(user.getMobile())) {
            return false;
        }

        var extra = shopPayInfo.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            return false;
        }

        var memberString = extra.get(ShopPayInfoExtraIndexEnum.GREY_RELEASE_MEMBERS.getCode() + shopPayInfo.getTradeType());
        if (StringUtils.isBlank(memberString)) {
            return false;
        }

        try {
            var members = JSON.parseArray(memberString, String.class);
            return !CollectionUtils.isEmpty(members) && members.contains(user.getMobile());
        } catch (Exception ex) {
            log.error("ShopPayInfoComponent.isValidGreyMember error, memberString={}", memberString, ex);
            return false;
        }
    }

    private String uploadFile(Long shopId, Long shopPayInfoId, MultipartFile file) throws Exception {
        if (shopId == null || shopPayInfoId == null || file == null || file.isEmpty()) {
            return null;
        }

        // 文件名
        String fileName = file.getOriginalFilename();
        // 文件存放路径
        String filePath = certFilePath + "/" + shopId + "/";

        String fullPath = filePath + fileName;
        // 删除同名文件
        FileUtil.deleteSingleFile(fullPath);
        // 上传
        FileUtil.uploadFile(file.getBytes(), filePath, fileName);

        return fullPath;
    }

    private void updateKeyFilePathForAllInPayYST(Long shopPayInfoId, String publicKeyFilePath, String secretKeyFilePath) {
        var existed = shopPayInfoReadService.getById(shopPayInfoId).getResult();
        if (existed == null) {
            throw new RuntimeException(String.format("shopPayInfo查询为空[id=%s]", shopPayInfoId));
        }

        var extra = existed.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }

        if (StringUtils.isNotBlank(publicKeyFilePath)) {
            extra.put(ShopPayInfoExtraIndexEnum.TL_CERT_PATH.getCode(), publicKeyFilePath);
        }
        if (StringUtils.isNotBlank(secretKeyFilePath)) {
            extra.put(ShopPayInfoExtraIndexEnum.CERT_PATH.getCode(), secretKeyFilePath);
        }

        // 更新
        var updateObject = new ShopPayInfo();
        updateObject.setId(existed.getId());
        updateObject.setExtra(extra);
        var result = shopPayInfoWriteService.update(updateObject);
        if (StringUtils.isNotBlank(result.getError()) || !Boolean.TRUE.equals(result.getResult())) {
            throw new RuntimeException(String.format("shopPayInfo更新失败[id=%s, %s]", shopPayInfoId, result.getError()));
        }
    }

    /**
     * 更新启用禁用状态
     *
     * @param shopPayInfoId
     * @param statusEnum
     */
    public void shopPayChannelStatus(Long shopPayInfoId, ShopPayInfoStatusEnum statusEnum) {
        if (shopPayInfoId == null || statusEnum == null) {
            throw new RuntimeException("参数不能为空");
        }
        ShopPayInfo payInfo = new ShopPayInfo();
        payInfo.setId(shopPayInfoId);
        payInfo.setStatus(statusEnum.getCode());
        if (Objects.equals(ShopPayInfoStatusEnum.ACTIVE, statusEnum)) {
            payInfo.setStatusTime(0L);
        } else {
            payInfo.setStatusTime(System.currentTimeMillis());
        }
        try {
            var response = shopPayInfoWriteService.update(payInfo);
            if (!Boolean.TRUE.equals(response.getResult())) {
                throw new RuntimeException(response.getError());
            }
        } catch (DuplicateKeyException e) {
            throw new RuntimeException("同一支付账号配置只能存在一个有效状态");
        }
    }


}
