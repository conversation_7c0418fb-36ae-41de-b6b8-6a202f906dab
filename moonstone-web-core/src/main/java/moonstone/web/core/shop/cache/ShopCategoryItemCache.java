package moonstone.web.core.shop.cache;

import lombok.extern.slf4j.Slf4j;
import moonstone.category.model.ShopCategory;
import moonstone.category.model.ShopCategoryItem;
import moonstone.category.service.ShopCategoryItemReadService;
import moonstone.category.service.ShopCategoryReadService;
import moonstone.common.api.HashCache;
import moonstone.item.service.ItemReadService;
import moonstone.showcase.mq.config.anno.MQEventConsumerMethod;
import moonstone.web.core.events.item.ItemUpdateEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

import java.util.*;

@Slf4j
@Component
public class ShopCategoryItemCache extends HashCache.JedisCache {
    ShopCategoryItemReadService service;
    ShopCategoryReadService categoryReadService;
    ItemReadService itemService;

    public ShopCategoryItemCache(Pool<Jedis> pool, ShopCategoryItemReadService service, ShopCategoryReadService categoryReadService, ItemReadService itemReadService) {
        super(pool);
        this.service = service;
        this.categoryReadService = categoryReadService;
        this.itemService = itemReadService;
    }

    @EventListener(ItemUpdateEvent.class)
    @MQEventConsumerMethod(ItemUpdateEvent.class)
    public void updateItemCategory(ItemUpdateEvent event){
        log.info("ItemUpdateEvent LOG : ShopCategoryItemCache {}", event.getItemId());
        invalidateByItem(event.getItemId());
    }

    public void invalidateByItem(Long itemId) {
        invalid(Namespace.CategoryByItem.toString() + itemId);
        invalidateByShop(itemService.findById(itemId).getResult().getShopId());
    }

    public void invalidateByShop(Long shopId) {
        invalid(Namespace.CategoryByShop.toString() + shopId);
    }

    public List<Category> findByItemId(List<Long> itemId) {
        Map<Long, Category> categoryMap = new TreeMap<>();
        for (Long id : itemId) {
            Category category = findByItemId(id);
            if (categoryMap.containsKey(category.category().getId())) {
                List<ShopCategoryItem> itemList = new ArrayList<>(category.categoryItemList());
                itemList.addAll(categoryMap.get(category.category().getId()).categoryItemList());
                categoryMap.put(category.category().getId(), new Category(category.category(),
                        itemList));
            } else {
                categoryMap.put(category.category().getId(), category);
            }
        }
        return new ArrayList<>(categoryMap.values());
    }

    @Override
    public long ttl() {
        return 30 * 60 * 1000;
    }

    public Category findByItemId(Long itemId) {
        return getCache(Namespace.CategoryByItem.toString(), itemId, id -> {
            var itemList = service.findByItemIds(Collections.singletonList(id)).getResult();
            if (itemList.isEmpty()) {
                return null;
            }
            var cid = itemList.stream().map(ShopCategoryItem::getShopCategoryId).filter(i -> i > 0)
                    .max(Long::compareTo).orElse(0L);
            var ca = categoryReadService.findById(cid).getResult();
            return new Category(ca, itemList);
        }, Category.class);
    }

    public List<Category> findByShopId(Long shopId) {
        return getCache(Namespace.CategoryByShop.toString(), shopId, id -> {
            List<ShopCategoryItem> itemList = service.findByShopId(shopId).take();

            Map<Long, Category> categoryMap = new TreeMap<>();
            for (ShopCategoryItem shopCategoryItem : itemList) {
                if (categoryMap.containsKey(shopCategoryItem.getShopCategoryId())) {
                    Category category = categoryMap.get(shopCategoryItem.getShopCategoryId());
                    category.categoryItemList().add(shopCategoryItem);
                } else {
                    categoryMap.put(shopCategoryItem.getShopCategoryId(), new Category(
                            categoryReadService.findById(shopCategoryItem.getShopCategoryId()).getResult()
                            , new ArrayList<>(List.of(shopCategoryItem))));
                }
            }
            return new CategoryList(new ArrayList<>(categoryMap.values()));
        }, CategoryList.class).categories();
    }

    enum Namespace {
        CategoryByItem, CategoryByShop;

        @Override
        public String toString() {
            return super.toString() + "#";
        }
    }

    public static record CategoryList(List<Category> categories) {

    }

    public static record Category(ShopCategory category, List<ShopCategoryItem> categoryItemList) {
    }


}
