package moonstone.web.core.events.trade.listener;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.model.BaseUser;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.UserUtil;
import moonstone.event.PaymentPaidEvent;
import moonstone.order.model.OrderPayment;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.user.model.StoreProxy;
import moonstone.web.core.component.WxSubscribeMsgSender;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.component.wx.WxSubscribeMsg;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.user.StoreProxyManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class OrderPaidWechatNotifierListener {

    @Autowired
    private PaymentReadService paymentReadService;

    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private SkuOrderReadService skuOrderReadService;

    @Autowired
    private StoreProxyManager storeProxyManager;
    @Autowired
    private ShopWxaCacheHolder shopWxaCacheHolder;
    @Autowired
    private WxSubscribeMsgSender wxSubscribeMsgSender;

    @Autowired
    private RocketMQProducer rocketMQProducer;



    /**
     * 提醒用户 订单已经被支付了
     *
     * @param paymentPaidEvent 支付单被支付事件
     */
    @EventListener(PaymentPaidEvent.class)
    public void notifyWechat(PaymentPaidEvent paymentPaidEvent) {
        List<ShopOrder> paidShopOrderList = new ArrayList<>();
        for (OrderPayment orderPayment : paymentReadService.findOrderIdsByPaymentId(paymentPaidEvent.getPaymentId()).getResult()) {
            Long orderId;
            switch (orderPayment.getOrderLevel()) {
                case SKU:
                    orderId = skuOrderReadService.findById(orderPayment.getOrderId()).getResult().getOrderId();
                    break;
                case SHOP:
                    orderId = orderPayment.getOrderId();
                    break;
                default:
                    continue;
            }
            ShopOrder shopOrder = shopOrderReadService.findById(orderId).getResult();
            if (shopWxaCacheHolder.findReleaseOneForShopId(shopOrder.getShopId()) == null) return;
            if (wxSubscribeMsgSender.getSupportedAppId().contains(shopWxaCacheHolder.findReleaseOneForShopId(shopOrder.getShopId()).getAppId())) {
                paidShopOrderList.add(shopOrder);
            }
        }
        if (paidShopOrderList.isEmpty()) return;
        List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderId(paidShopOrderList.get(0).getId()).getResult();

        String orderContent = skuOrderList.get(0).getItemName();

        if (skuOrderList.size() > 1 || paidShopOrderList.size() > 1)
            orderContent = orderContent + ",...";
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < 3 && i < paidShopOrderList.size(); i++) {
            builder.append(paidShopOrderList.get(i).getId());
            builder.append(",");
        }
        builder.deleteCharAt(builder.length() - 1);

        String idList = builder.toString();

        String price = new BigDecimal(paidShopOrderList.get(0).getFee()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN).toString();


        String shopName = storeProxyManager.getStoreProxyByShopIdAndUserId(paidShopOrderList.get(0).getShopId(), paidShopOrderList.get(0).getReferenceId())
                .map(StoreProxy::getProxyShopName).orElse(paidShopOrderList.get(0).getShopName());

        WxSubscribeMsg wxSubscribeMsg = new WxSubscribeMsg();
        wxSubscribeMsg.setUserId(paidShopOrderList.get(0).getBuyerId());
        wxSubscribeMsg.setShopId(paidShopOrderList.get(0).getShopId());
        wxSubscribeMsg.setData(ImmutableMap.of("thing3", limitString(orderContent)
                , "amount6", price
                , "character_string5", limitString(idList), "thing1", limitString(shopName)));
        wxSubscribeMsg.setTemplate_id("bV6wtUr6jFlW8NBF2ucZvJc5HRYjo8sLwDVvCUUyO9M");
        wxSubscribeMsg.setPage(String.format("pages/order_detail?id=%s", skuOrderList.get(0).getOrderId()));
        wxSubscribeMsgSender.send(wxSubscribeMsg);

        // 发送优惠券
        paidShopOrderList.forEach(shopOrder -> {
            Map<String, Long> msgMap = new HashMap<>();
            BaseUser currentUser = UserUtil.getCurrentUser();
            msgMap.put("userId", currentUser.getId());
            msgMap.put("shopId", shopOrder.getShopId());
            msgMap.put("orderId", shopOrder.getId());
            rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC,
                    RocketMQConstant.ORDER_SEND_INVENTED_GIFT_COUPON_TAG_NEW, JSONUtil.toJsonStr(msgMap));
        });

    }

    private String limitString(String str) {
        if (str.length() > 20) return str.substring(0, 18) + "..";
        return str;
    }
}
