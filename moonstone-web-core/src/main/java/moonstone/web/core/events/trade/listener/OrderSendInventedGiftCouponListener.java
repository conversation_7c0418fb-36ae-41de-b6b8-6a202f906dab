package moonstone.web.core.events.trade.listener;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.event.PaymentPaidEvent;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class OrderSendInventedGiftCouponListener {

    @Autowired
    private ShopOrderReadService shopOrderReadService;

    @Autowired
    private RocketMQProducer rocketMQProducer;



    /**
     * 订单支付成功后，发送优惠券
     *
     * @param paymentPaidEvent
     */
    @EventListener(PaymentPaidEvent.class)
    public void notifyWechat(PaymentPaidEvent paymentPaidEvent) {
        List<ShopOrder> shopOrderList = shopOrderReadService.findByIds(paymentPaidEvent.getOrderId()).getResult();
        // 发送优惠券
        for (ShopOrder shopOrder : shopOrderList) {
            Map<String, Long> msgMap = new HashMap<>();
            msgMap.put("userId", shopOrder.getBuyerId());
            msgMap.put("shopId", shopOrder.getShopId());
            msgMap.put("orderId", shopOrder.getId());
            rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC,
                    RocketMQConstant.ORDER_SEND_INVENTED_GIFT_COUPON_TAG_NEW, JSONUtil.toJsonStr(msgMap));
        }
    }
}
