package moonstone.web.core.events.trade.listener;

import cn.hutool.json.JSONUtil;
import com.google.common.base.Objects;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.RocketMQConstant;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.event.OrderCancelEvent;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.storage.service.StorageService;
import moonstone.web.core.component.cache.SkuStockCacheAtRedisManager;
import moonstone.web.core.component.order.PayFactory;
import moonstone.web.core.events.item.handler.OrderInventoryValidateUtil;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * 买家取消,卖家取消监听事件
 * Mail: <EMAIL>
 * Data: 16/7/26
 * Author: yangzefeng
 */
@Slf4j
public class OrderCancelListener {

    @RpcConsumer
    private StorageService storageService;

    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;

    @RpcConsumer
    private PaymentReadService paymentReadService;

    @Autowired
    private PayFactory payFactory;

    @Autowired
    private SkuStockCacheAtRedisManager skuStockCacheAtRedisManager;

    @Resource
    private OrderInventoryValidateUtil orderInventoryValidateUtil;

    @Autowired
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private RocketMQProducer rocketMQProducer;

    @EventListener(OrderCancelEvent.class)
    public void closePayment(OrderCancelEvent orderCancelEvent) {
        List<Payment> payments = paymentReadService.findByOrderIdAndOrderLevel(orderCancelEvent.getOrderId(), OrderLevel.fromInt(orderCancelEvent.getOrderLevel())).getResult();
        if (payments == null) {
            log.error("[OrderCancelListener](closePayment) fail to query payments by event:{}", orderCancelEvent);
            return;
        }
        Function<Long, Either<Boolean>> closePayment = (id) -> {
            try {
                if (payFactory.closePayment(id)) {
                    return Either.ok(true);
                }
                return Either.error("error in close payment:" + id);
            } catch (Exception ex) {
                log.error("[OrderCancelListener](closePayment) lambda close paymentId:{}", id);
                ex.printStackTrace();
                return Either.error("error in close payment:" + id + " error reason:" + ex.getMessage());
            }
        };
        payments.parallelStream()
                .filter(paid -> paid.getStatus() == OrderStatus.PAID.getValue())
                .map(Payment::getId).map(closePayment).filter(r -> !r.isSuccess()).forEach(r -> log.error("payment close failed,error:{}", r.getErrorMsg()));

    }

    @EventListener(OrderCancelEvent.class)
    public void RollbackStockAndSaleQuantity(OrderCancelEvent orderCancelEvent) {
        Long orderId = orderCancelEvent.getOrderId();
        Integer orderLevel = orderCancelEvent.getOrderLevel();

        List<SkuOrder> skuOrders = new ArrayList<>();
        if (Objects.equal(orderLevel, OrderLevel.SHOP.getValue())) {
            Response<List<SkuOrder>> skuOrdersR = skuOrderReadService.findByShopOrderId(orderId);
            if (!skuOrdersR.isSuccess()) {
                log.error("fail to find skuOrder by shopOrder id {}, error code:{}",
                        orderId, skuOrdersR.getError());
                return;
            }
            skuOrders.addAll(skuOrdersR.getResult());
        } else if (Objects.equal(orderLevel, OrderLevel.SKU.getValue())) {
            Response<SkuOrder> skuOrderR = skuOrderReadService.findById(orderId);
            if (!skuOrderR.isSuccess()) {
                log.error("fail to find sku order by id {}, error code:{}",
                        orderId, skuOrderR.getError());
                return;
            }
            SkuOrder skuOrder = skuOrderR.getResult();
            skuOrders.add(skuOrder);
        } else {
            log.error("unknown order level, order id {}, order level {}",
                    orderId, orderLevel);
        }

        ShopOrder shopOrder = shopOrderReadService.findById(orderId).getResult();
        log.info("订单取消事件 {} 子单数量 {}", orderId, skuOrders.size());
        List<Long> skuIdList = new ArrayList<>();
        for (SkuOrder skuOrder : skuOrders) {
            log.debug("{} return stock [{}] for orderId[orderId => {}, id => {}]", LogUtil.getClassMethodName(),
                    skuOrder.getQuantity(), orderId, skuOrder.getId());

            // 拥有独立库存的订单 库存不返还到老库存上
            if(orderInventoryValidateUtil.isIndependentStockAvailable(shopOrder, skuOrder)){
                continue;
            }
            Item item = itemReadService.findById(skuOrder.getItemId()).getResult();
            Response<Boolean> storageR = storageService.increaseBy(skuOrder.getSkuId(), null, null, skuOrder.getQuantity(), item.getType());
            if (!storageR.isSuccess()) {
                log.error("fail to increase storage and decrease sale quantity by skuOrder id {}, skip",
                        skuOrder.getSkuId());
            }
            skuIdList.add(skuOrder.getSkuId());
        }
        rocketMQProducer.sendMessage(RocketMQConstant.APP_ADMIN_TOPIC, RocketMQConstant.SKU_STOCK_CHANGE_TAG, JSONUtil.toJsonStr(skuIdList));
        log.info("订单取消 发送商品库存变化的消息 {}", JSONUtil.toJsonStr(skuIdList));
        log.debug("{} rollback order(Id:{} orderLevel:{}) stock", LogUtil.getClassMethodName(), orderId, orderLevel);
        skuStockCacheAtRedisManager.rollBackSkuOrder(skuOrders);
    }
}
