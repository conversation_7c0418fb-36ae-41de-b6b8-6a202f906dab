package moonstone.web.core.events.item.handler;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.service.MerchantCommodityInventoryReadService;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.common.enums.OrderSourceEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class OrderInventoryValidateUtil {


    @Resource
    private MerchantCommodityInventoryReadService merchantCommodityInventoryReadService;


    public boolean isHasIndependentStock(ShopOrder shopOrder) {
        Integer orderSource = shopOrder.getOrderSource();
        return OrderSourceEnum.isHasIndependentStock(orderSource);
    }

    public boolean isIndependentStockAvailable(ShopOrder shopOrder, SkuOrder skuOrder) {
        Integer orderSource = shopOrder.getOrderSource();
        Long shopOrderId = shopOrder.getId();
        Long skuId = skuOrder.getSkuId();

        if(OrderSourceEnum.isHasIndependentStock(orderSource) && isIndependentStockAvailable(skuId)){
            // 当具有独立库存 && 独立库存不为空时 不触发上下架及原库存逻辑归零时的动作
            log.info("订单拥有独立库存 不扣减原库存 {} {} {}", shopOrderId, skuOrder.getId(), orderSource);
            return true;
        }else{
            log.info("常规订单 扣减原库存 {} {} {}", shopOrderId, skuOrder.getId(), orderSource);
            return false;
        }
    }


    /**
     * 一个sku的任意独立库存大于0时 返回true
     * @param skuId
     * @return
     */
    private boolean isIndependentStockAvailable(Long skuId) {
        Long value = merchantCommodityInventoryReadService.isIndependentStockAvailable(skuId);
        if(value > 0){
            return true;
        }
        return false;
    }

}
