package moonstone.web.core.events.trade.listener;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import io.terminus.common.model.Response;
import io.vertx.core.Context;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonArray;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopItemCacher;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.utils.EventSender;
import moonstone.event.OrderCreatedEvent;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.ItemWriteService;
import moonstone.item.service.MerchantCommodityInventoryReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.storage.service.StorageService;
import moonstone.weShop.model.WeShopItem;
import moonstone.web.core.component.cache.SkuStockCacheAtRedisManager;
import moonstone.web.core.events.api.RefreshItemStockByTimePeriod;
import moonstone.web.core.events.item.ItemUpdateEvent;
import moonstone.web.core.events.weShopItem.WeShopItemDumpEvent;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.common.enums.OrderSourceEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Slf4j
public class StorageDecreaseListener {
    @Autowired
    Vertx vertx;
    @Autowired
    private StorageService storageService;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private SkuReadService skuReadService;
    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private ItemWriteService itemWriteService;
    @Autowired
    private WeShopItemCacher weShopItemCacher;
    @Autowired
    private SkuStockCacheAtRedisManager skuStockCacheAtRedisManager;

    @Resource
    private MerchantCommodityInventoryReadService merchantCommodityInventoryReadService;

    @Resource
    private RocketMQProducer rocketMQProducer;

    @Resource
    private ItemReadService itemReadService;

    @PostConstruct
    public void initVertx() {
        vertx.eventBus().<JsonArray>localConsumer(RefreshItemStockByTimePeriod.ADDRESS, msg -> {
            Context context = vertx.getOrCreateContext();
            if (context.get("queue") == null) {
                context.put("queue", new HashSet<>());
            }
            Set<Long> queue = context.get("queue");
            msg.body().forEach(itemId -> queue.add((Long) itemId));
            msg.reply(null);
        });

        vertx.periodicStream(100L)
                .handler(turn -> {
                    Set<Long> queue = vertx.getOrCreateContext().get("queue");
                    if (queue == null) {
                        return;
                    }
                    for (Long id : queue) {
                        EventSender.publish(new ItemUpdateEvent(id));
                    }
                    queue.clear();
                });
    }

    @EventListener(OrderCreatedEvent.class)
    public void decrStorageAndIncrSale(OrderCreatedEvent orderCreatedEvent) {
        Long shopOrderId = orderCreatedEvent.getOrderId();

        Response<List<SkuOrder>> findSkuOrders = skuOrderReadService.findByShopOrderId(shopOrderId);
        if (!findSkuOrders.isSuccess()) {
            log.error("fail to find sku orders by shopId={},cause:{}",
                    shopOrderId, findSkuOrders.getError());
            return;
        }
        ShopOrder shopOrder = shopOrderReadService.findById(shopOrderId).getResult();
        log.info("扣减库存 订单对象 {}", JSONObject.toJSONString(shopOrder));
        Integer orderSource = shopOrder.getOrderSource();
        List<SkuOrder> skuOrders = findSkuOrders.getResult();
        Set<Long> itemIdSet = new HashSet<>(8);
        List<Long> skuIdList = new ArrayList<>();
        try {
            for (SkuOrder skuOrder : skuOrders) {
                Long skuId = skuOrder.getSkuId();
                if(OrderSourceEnum.isHasIndependentStock(orderSource) && isIndependentStockAvailable(skuId)){
                    // 当具有独立库存 && 独立库存不为空时 不触发上下架及原库存逻辑归零时的动作
                    log.info("订单拥有独立库存 不扣减原库存 {} {} {}", shopOrderId, skuOrder.getId(), orderSource);
                    continue;
                }else{
                    log.info("常规订单 扣减原库存 {} {} {}", shopOrderId, skuOrder.getId(), orderSource);
                }
                Long itemId = skuOrder.getItemId();
                Item item = itemReadService.findById(itemId).getResult();
                Integer itemType = item.getType();
                Response<Boolean> deltaR = storageService.decreaseBy(skuId, null, null, skuOrder.getQuantity(), itemType);
                if (!deltaR.isSuccess()) {
                    log.error("fail to decrease sku(id={})'s storage, delta={}, error code:{}",
                            skuId, skuOrder.getQuantity(), deltaR.getError());
                }
                Response<Sku> rSku = skuReadService.findSkuById(skuId);
                if (rSku.isSuccess() && rSku.getResult().getStockQuantity() <= 0) {
                    clearSkuQuantity(skuId);
                    itemIdSet.add(rSku.getResult().getItemId());
                }
                skuIdList.add(skuId);
                if (Objects.nonNull(shopOrder.getOutShopId()) && Objects.equals(shopOrder.getOutFrom(), OrderOutFrom.WE_SHOP.Code())) {
                    long weShopId = Long.parseLong(shopOrder.getOutShopId());
                    WeShopItem weShopItem = weShopItemCacher.findWeShopItemByWeShopIdAndItemId(weShopId, skuOrder.getItemId());
                    weShopItemCacher.invalidateWeShopItemById(weShopItem.getId());
                    // weShopItemWriteService.increaseSellQuantity(skuOrder.getQuantity(), skuOrder.getItemId(), weShopId);
                    EventSender.send(new WeShopItemDumpEvent(weShopItem.getId()));
                    weShopItemCacher.invalidateWeShopItemById(weShopItem.getId());
                }
            }
        } catch (Exception e) {
            log.error("Fail to decrease Item Quantity By Order[{}]", orderCreatedEvent.getOrderId(), e);
        }
        rocketMQProducer.sendMessage(RocketMQConstant.APP_ADMIN_TOPIC, RocketMQConstant.SKU_STOCK_CHANGE_TAG, JSONUtil.toJsonStr(skuIdList));
        log.info("订单创建 发送商品库存变化的消息 {}", JSONUtil.toJsonStr(skuIdList));

        JsonArray array = new JsonArray();
        itemIdSet.forEach(array::add);
        vertx.eventBus().request(RefreshItemStockByTimePeriod.ADDRESS, array);
    }

    /**
     * 一个sku的任意独立库存大于0时 返回true
     * @param skuId
     * @return
     */
    private boolean isIndependentStockAvailable(Long skuId) {
        Long value = merchantCommodityInventoryReadService.isIndependentStockAvailable(skuId);
        if(value > 0){
            return true;
        }
        return false;
    }

    /**
     * 清楚缓存迫使它重新获取单品数量
     *
     * @param skuId 单品Id
     */
    private void clearSkuQuantity(Long skuId) {
        skuStockCacheAtRedisManager.removeCache(Collections.singletonList(skuId));
    }
}
