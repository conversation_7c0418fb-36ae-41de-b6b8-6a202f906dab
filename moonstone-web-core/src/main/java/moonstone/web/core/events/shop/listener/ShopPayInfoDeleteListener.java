package moonstone.web.core.events.shop.listener;

import io.terminus.pay.alipay.app.component.AppAlipayTokenProvider;
import io.terminus.pay.alipay.common.model.token.AppAlipayToken;
import io.terminus.pay.alipay.common.model.token.PcAlipayToken;
import io.terminus.pay.alipay.common.model.token.WapAlipayToken;
import io.terminus.pay.alipay.pc.component.PcAlipayTokenProvider;
import io.terminus.pay.alipay.wap.component.WapAlipayTokenProvider;
import io.terminus.pay.wechatpay.app.component.AppWechatpayTokenProvider;
import io.terminus.pay.wechatpay.common.model.token.AppWxToken;
import io.terminus.pay.wechatpay.common.model.token.JsapiWxToken;
import io.terminus.pay.wechatpay.common.model.token.QrWxToken;
import io.terminus.pay.wechatpay.jsapi.component.JsapiWechatpayTokenProvider;
import io.terminus.pay.wechatpay.qr.component.QrWechatpayTokenProvider;
import io.vertx.core.AbstractVerticle;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.enums.ShopPayInfoPayChannelEnum;
import moonstone.shop.model.ShopPayInfo;
import moonstone.web.core.component.pay.allinpay.AllInPayToken;
import moonstone.web.core.component.pay.allinpay.AllInPayTokenProvider;
import moonstone.web.core.component.pay.allinpayyst.AllInPayYSTToken;
import moonstone.web.core.component.pay.allinpayyst.AllInPayYSTTokenProvider;
import moonstone.web.core.component.pay.umf.UMFTokenProvider;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.shop.ShopPayInfoDeleteEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
public class ShopPayInfoDeleteListener extends AbstractVerticle {

    @Autowired
    private PcAlipayTokenProvider pcAlipayTokenProvider;
    @Autowired
    private AppAlipayTokenProvider appAlipayTokenProvider;
    @Autowired
    private WapAlipayTokenProvider wapAlipayTokenProvider;

    @Autowired
    private AppWechatpayTokenProvider appWechatpayTokenProvider;
    @Autowired
    private JsapiWechatpayTokenProvider jsapiWechatpayTokenProvider;
    @Autowired
    private QrWechatpayTokenProvider qrWechatpayTokenProvider;
    @Resource
    private AllInPayTokenProvider allInPayTokenProvider;
    @Resource
    private UMFTokenProvider umfTokenProvider;
    @Resource
    private AllInPayYSTTokenProvider allInPayYSTTokenProvider;

    @VertxEventBusListener(ShopPayInfoDeleteEvent.class)
    public void shopPayInfoDelete(ShopPayInfoDeleteEvent event) {
        final ShopPayInfo shopPayInfo = new ShopPayInfo();
        shopPayInfo.setShopId(event.getShopId());
        shopPayInfo.setPayChannel(event.getChannel());
        shopPayInfo.setTradeType(event.getTradeType());
        if (Objects.nonNull(event.getTradeType())) {
            deleteTradeType(shopPayInfo);
        } else {
            deleteShopId(shopPayInfo);
        }
    }

    /**
     * 老的支付注册删除
     *
     * @param shopPayInfo
     */
    private void deleteShopId(ShopPayInfo shopPayInfo) {
        if (ShopPayInfoPayChannelEnum.ALIPAY.getCode().equals(shopPayInfo.getPayChannel())) {
            /**
             * PC方式注册
             */
            PcAlipayToken pcAlipayToken = new PcAlipayToken();
            pcAlipayTokenProvider.register(shopPayInfo.getShopId().toString(), pcAlipayToken);
            /**
             *  APP方式注册
             */
            AppAlipayToken appAlipayToken = new AppAlipayToken();
            appAlipayTokenProvider.register(shopPayInfo.getShopId().toString(), appAlipayToken);
            /**
             * wap方式注册
             */
            WapAlipayToken wapAlipayToken = new WapAlipayToken();
            wapAlipayTokenProvider.register(shopPayInfo.getShopId().toString(), wapAlipayToken);
        }
        if (ShopPayInfoPayChannelEnum.WECHATPAY.getCode().equals(shopPayInfo.getPayChannel())) {
            /**
             * APP方式注册
             */
            AppWxToken appWxToken = new AppWxToken();
            appWechatpayTokenProvider.register(shopPayInfo.getShopId().toString(), appWxToken);
            /**
             * Jsapi方式注册
             */
            JsapiWxToken jsapiWxToken = new JsapiWxToken();
            jsapiWechatpayTokenProvider.register(shopPayInfo.getShopId().toString(), jsapiWxToken);
            /**
             *Qr方式注册
             */
            QrWxToken qrWxToken = new QrWxToken();
            qrWechatpayTokenProvider.register(shopPayInfo.getShopId().toString(), qrWxToken);
        }
        if (ShopPayInfoPayChannelEnum.ALLINPAY.getCode().equals(shopPayInfo.getPayChannel())) {
            var token = new AllInPayToken();
            allInPayTokenProvider.register(shopPayInfo.getShopId().toString(), token);
        }
        if (ShopPayInfoPayChannelEnum.UMF.getCode().equals(shopPayInfo.getPayChannel())) {
            umfTokenProvider.unregister(shopPayInfo.getShopId().toString());
        }
        if (ShopPayInfoPayChannelEnum.ALLINPAY_YST.getCode().equals(shopPayInfo.getPayChannel())) {
            allInPayYSTTokenProvider.register(shopPayInfo.getShopId().toString(), new AllInPayYSTToken());
        }
    }

    /**
     * 多支付方式删除
     *
     * @param shopPayInfo
     */
    private void deleteTradeType(ShopPayInfo shopPayInfo) {
        if (ShopPayInfoPayChannelEnum.ALIPAY.getCode().equals(shopPayInfo.getPayChannel())) {
            /**
             * PC方式注册
             */
            PcAlipayToken pcAlipayToken = new PcAlipayToken();
            pcAlipayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), pcAlipayToken);
            /**
             *  APP方式注册
             */
            AppAlipayToken appAlipayToken = new AppAlipayToken();
            appAlipayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), appAlipayToken);
            /**
             * wap方式注册
             */
            WapAlipayToken wapAlipayToken = new WapAlipayToken();
            wapAlipayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), wapAlipayToken);
        }
        if (ShopPayInfoPayChannelEnum.WECHATPAY.getCode().equals(shopPayInfo.getPayChannel())) {
            /**
             * APP方式注册
             */
            AppWxToken appWxToken = new AppWxToken();
            appWechatpayTokenProvider.register(shopPayInfo.getShopId().toString(), appWxToken);
            /**
             * Jsapi方式注册
             */
            JsapiWxToken jsapiWxToken = new JsapiWxToken();
            jsapiWechatpayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), jsapiWxToken);
            /**
             *Qr方式注册
             */
            QrWxToken qrWxToken = new QrWxToken();
            qrWechatpayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), qrWxToken);
        }
        if (ShopPayInfoPayChannelEnum.ALLINPAY.getCode().equals(shopPayInfo.getPayChannel())) {
            var token = new AllInPayToken();
            allInPayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), token);
        }
        if (ShopPayInfoPayChannelEnum.UMF.getCode().equals(shopPayInfo.getPayChannel())) {
            umfTokenProvider.unregister(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType());
        }
        if (ShopPayInfoPayChannelEnum.ALLINPAY_YST.getCode().equals(shopPayInfo.getPayChannel())) {
            allInPayYSTTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), new AllInPayYSTToken());
        }
    }

}