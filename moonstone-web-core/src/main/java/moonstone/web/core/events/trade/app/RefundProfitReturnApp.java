package moonstone.web.core.events.trade.app;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Paging;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.model.EntityBase;
import moonstone.common.model.IsPersistAble;
import moonstone.common.model.WithExtraMap;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.InComeDetail;
import moonstone.order.dto.OutComeDetail;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.OrderRoleSnapshot;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.*;
import moonstone.web.core.component.order.BirthProfitRecorder;
import moonstone.web.core.shop.cache.SubStoreCache;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对退款订单进行 清退利润
 */
@Slf4j
@AllArgsConstructor
@Component
public class RefundProfitReturnApp {
    RefundReadService refundReadService;
    BalanceDetailManager balanceDetailManager;
    OrderProfitActionRecordApp orderProfitActionRecordApp;
    OrderRefundMarkFirstCompeteApp orderRefundMarkFirstCompeteApp;
    ShopOrderTradeRecordApp shopOrderTradeRecordApp;
    BalanceDetailReadService balanceDetailReadService;
    BirthProfitRecorder birthProfitRecorder;
    ShopCacheHolder shopCacheHolder;

    OrderRoleSnapshotReadService orderRoleSnapshotReadService;

    ShopOrderReadService shopOrderReadService;

    SubStoreCache subStoreCache;

    AccountStatementDetailReadService accountStatementDetailReadService;

    private static final int MAX_LIMIT_BALANCE_QUANTITY = 3;


    /**
     * 对退款订单进行利润退款
     *
     * @param refundId  退款单号
     * @param shopOrder 主订单
     * @param outFrom   订单类型
     */
    public void refundProfit(Long refundId, ShopOrder shopOrder, OrderOutFrom outFrom) {
        log.info("{} working on ShopOrder:{},refundId:{},outShopId:{},referenceId:{} outFrom:{}"
                , LogUtil.getClassMethodName("refund")
                , shopOrder.getId()
                , refundId
                , shopOrder.getOutShopId()
                , shopOrder.getReferenceId()
                , outFrom
        );
        if (outFrom == OrderOutFrom.COMMUNITY_OPERATION) {
            log.info("当前订单是社群模式，不做利润相关的计算 {}", shopOrder.getId());
            return;
        }
        //  检测是否已经被退款过 (如果可以希望能在外层完成,不要在内层完成,会影响测试)
        Long sourceId = shopOrder.getShopId();
        if (outFrom == OrderOutFrom.WE_SHOP) {
            // 设计错误的BUG
            sourceId = 0L;
        }

        checkIfProfitAlreadyRefund(sourceId, refundId, shopOrder.getId());
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        /// 下面的不是子订单查询,准备采取直接的主订单查询 代替子订单 因为是全体退款
        criteria.setRelatedId(shopOrder.getId());
        criteria.setType(ProfitType.InCome.getValue());
        criteria.setNotStatusBitMarks(Arrays.asList(InComeDetail.orderRelatedMask.SkuOrder.getValue()
                , BalanceDetail.maskBit.WithDrawRelated.getValue()
                , BalanceDetail.maskBit.RefundRelated.getValue()
        ));
        criteria.setStatusBitMarks(Arrays.asList(InComeDetail.orderRelatedMask.ShopOrder.getValue()
                , BalanceDetail.maskBit.OrderRelated.getValue()
                , IsPersistAble.maskBit.PersistAble.getValue()));
        List<InComeDetail> inComeList = Optional.ofNullable(balanceDetailReadService.paging(criteria.toMap())
                        .getResult()).orElseThrow(() -> new RuntimeException("[ProfitMakerWithComplexListener](refund) paging refund skuList failed")).getData()
                .stream().filter(Objects::nonNull).map(InComeDetail::new).collect(Collectors.toList());

        //如果有可提现收益，则取可提现收益，否则取预计收益
        if (hasPresent(inComeList)) {
            inComeList = inComeList.stream().filter(BalanceDetail::isPresent).collect(Collectors.toList());
            inComeList = filterByBadDebts(inComeList);
        }

        /// 因为阶级分销只支持两级获取 加上一个拉新佣金 一共是三个 如果超过3个 则是有问题
        if (inComeList.size() > MAX_LIMIT_BALANCE_QUANTITY || inComeList.isEmpty()) {
            log.warn("[ProfitMakerWithComplexListener](refund) confused about list sub:{}", JSON.toJSONString(inComeList));
        }
        log.debug("{} criteria:{} inComeList:{}", LogUtil.getClassMethodName("get-related-balance"), criteria, JSON.toJSONString(inComeList.toArray()));

        //退款减去拉新佣金统计-mongodb
        Long exLinkedFee = inComeList.stream().filter(BalanceDetail::isExLinked).map(BalanceDetail::getChangeFee).reduce(Long::sum).orElse(0L);
        inComeList.stream().filter(BalanceDetail::isExLinked).map(BalanceDetail::getUserId).findFirst()
                .ifPresent(referenceId -> birthProfitRecorder.decreaseRefundFakeProfit(shopOrder.getBuyerId(), referenceId, shopOrder.getShopId(), exLinkedFee));

        List<BalanceDetail> balanceList = inComeList.stream().map(IncomeReverse::reverseInCome)
                .peek(outCome -> outCome.setExtra(Optional.ofNullable(outCome.getExtra()).orElseGet(HashMap::new)))
                .peek(outCome -> outCome.getExtra().put("orderId", shopOrder.getId().toString()))
                .peek(outCome -> outCome.setRelatedId(refundId))
                .peek(outCome -> outCome.setStatus(
                        outCome.getStatus()
                                & (~BalanceDetail.orderRelatedMask.ShopOrder.getValue())
                                & (~BalanceDetail.maskBit.OrderRelated.getValue())
                                | OutComeDetail.maskBit.RefundRelated.getValue()
                                | IsPersistAble.maskBit.PersistAble.getValue()))
                .peek(balanceDetailManager::changeRealProfit)
                .collect(Collectors.toList());

        markIncomeAsIgnorable(inComeList);
        log.debug("{} shopOrderId:{} balanceList-Size:{} outFrom:{}", LogUtil.getClassMethodName(), shopOrder.getId(), balanceList.size(), outFrom);
        balanceList.forEach(balanceDetailManager.generateRecordExecutor(shopOrder));
        orderProfitActionRecordApp.refundProfitComplete(shopOrder.getId());
        orderRefundMarkFirstCompeteApp.markOrderRefundFirst(shopOrder.getId())
                .ifSuccess(ok ->
                        shopOrderTradeRecordApp.recordShopOrderTrade(shopOrder, true, !shopOrder.getCreatedAt().before(DateUtil.withTimeAtStartOfDay(new Date())), shopCacheHolder.findShopById(shopOrder.getShopId()), outFrom));
    }

    /**
     * 订单已发起提现或已生成账单，此时消费者再完成退款，则产生坏账，<br/>
     * 被退款的订单在发起提现或生成账单时，已默认对应佣金给到对应的角色手上，故退款时不再扣减其可提现余额，由商家线下自行处理
     *
     * @param inComeList
     * @return
     */
    private List<InComeDetail> filterByBadDebts(List<InComeDetail> inComeList) {
        if (CollectionUtils.isEmpty(inComeList)) {
            return inComeList;
        }
        var shopOrder = shopOrderReadService.findById(inComeList.get(0).getRelatedId()).getResult();
        var roleSnapshots = findRoleSnapshots(inComeList.get(0).getRelatedId());

        List<InComeDetail> result = new ArrayList<>();
        for (var incomeDetail : inComeList) {
            if (isSubStore(shopOrder, roleSnapshots, incomeDetail.getUserId())) {
                // 门店
                // 已发起提现，则退款时不再扣减可提现余额
                if (alreadyInitiateWithdraw(shopOrder, incomeDetail.getUserId())) {
                    continue;
                }
            } else if (isServiceProvider(shopOrder, roleSnapshots, incomeDetail)) {
                // 服务商
                // 已生成账单，则退款时不再扣减可提现余额
                if (existedInAccountStatement(incomeDetail)) {
                    continue;
                }
            } else if (isStoreGuider(shopOrder, roleSnapshots, incomeDetail)) {
                // 导购
                // 已生成账单，则退款时不再扣减可提现余额
                if (existedInAccountStatement(incomeDetail)) {
                    continue;
                }
            }

            result.add(incomeDetail);
        }

        return result;
    }

    /**
     * 指定用户的该订单佣金是否已生成了账单
     *
     * @param inComeDetail
     * @return
     */
    private boolean existedInAccountStatement(InComeDetail inComeDetail) {
        var response = accountStatementDetailReadService.findValidByBalanceDetailId(inComeDetail.getId());
        if (!response.isSuccess()) {
            throw new RuntimeException(String.format("账单明细查询失败[balanceDetailId=%s]", inComeDetail.getId()));
        }

        return response.getResult() != null;
    }

    /**
     * 指定用户是否已对该订单发起了提现申请
     *
     * @param shopOrder
     * @param userId
     * @return
     */
    private boolean alreadyInitiateWithdraw(ShopOrder shopOrder, Long userId) {
        return alreadyInitiateWithdraw(shopOrder.getShopId(), shopOrder.getId(), userId);
    }

    /**
     * 指定用户是否已对该订单发起了提现申请
     *
     * @param shopId
     * @param shopOrderId
     * @param userId
     * @return
     */
    public boolean alreadyInitiateWithdraw(Long shopId, Long shopOrderId, Long userId) {
        var statusList = balanceDetailReadService.findShopOrderWithdrawStatus(shopId,
                List.of(shopOrderId)).getResult();
        if (CollectionUtils.isEmpty(statusList)) {
            return false;
        }

        return statusList.stream()
                .anyMatch(e -> e.getWithdrawStatus() != null && shopOrderId.equals(e.getShopOrderId()) &&
                        userId.equals(e.getUserId()));
    }

    private List<OrderRoleSnapshot> findRoleSnapshots(Long shopOrderId) {
        var response = orderRoleSnapshotReadService.findByShopOrderIds(List.of(shopOrderId),
                OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER);
        if (!response.isSuccess()) {
            throw new RuntimeException(String.format("订单[shopOrderId=%s]角色快照查询失败", shopOrderId));
        }

        return response.getResult();
    }

    /**
     * 判断是否为导购的佣金
     *
     * @param shopOrder
     * @param snapshots
     * @param inComeDetail
     * @return
     */
    private boolean isStoreGuider(ShopOrder shopOrder, List<OrderRoleSnapshot> snapshots, InComeDetail inComeDetail) {
        return inComeDetail.isLinked();
    }

    /**
     * 判断 userId 是否为 订单的服务商
     *
     * @param shopOrder
     * @param snapshots
     * @param inComeDetail
     * @return
     */
    private boolean isServiceProvider(ShopOrder shopOrder, List<OrderRoleSnapshot> snapshots, InComeDetail inComeDetail) {
        if (inComeDetail.isLinked()) {
            return false;
        }

        if (CollectionUtils.isEmpty(snapshots)) {
            log.error("订单[shopOrderId={}]没有可用角色快照信息，无法判明服务商", shopOrder.getId());
            return false;
        }

        return snapshots.stream().anyMatch(snapshot -> snapshot.getUserId().equals(inComeDetail.getUserId()) &&
                SubStoreUserIdentityEnum.SERVICE_PROVIDER.getCode().equals(snapshot.getUserRole()));
    }

    /**
     * 判断 userId 是否为 订单的门店
     *
     * @param shopOrder
     * @param snapshots
     * @param userId
     * @return
     */
    private boolean isSubStore(ShopOrder shopOrder, List<OrderRoleSnapshot> snapshots, Long userId) {
        if (!CollectionUtils.isEmpty(snapshots)) {
            return snapshots.stream().anyMatch(snapshot -> snapshot.getUserId().equals(userId) &&
                    SubStoreUserIdentityEnum.SUB_STORE.getCode().equals(snapshot.getUserRole()));
        } else {
            long subStoreId = Long.parseLong(shopOrder.getOutShopId());
            var subStore = subStoreCache.findById(subStoreId);
            if (subStore.isEmpty()) {
                return false;
            }

            return subStore.get().getUserId().equals(userId);
        }
    }

    private boolean hasPresent(List<InComeDetail> inComeList) {
        if (CollectionUtils.isEmpty(inComeList)) {
            return false;
        }

        return inComeList.stream().anyMatch(BalanceDetail::isPresent);
    }


    /**
     * 检测退款单利润触发是否已经触发过一次
     *
     * @param refundId 退款单号
     */
    private void checkIfProfitAlreadyRefund(Long sourceId, Long refundId, Long orderId) {
        Refund refund = refundReadService.findById(refundId).getResult();
        if (refund == null) {
            throw new RuntimeException(new Translate("退款单不存在或者不可用").toString());
        }
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setSourceId(sourceId);
        criteria.setRelatedId(refundId);
        criteria.setType(ProfitType.OutCome.getValue());
        criteria.setStatusBitMarks(Collections.singletonList(BalanceDetail.maskBit.RefundRelated.getValue()));
        Paging<BalanceDetail> pBalance = balanceDetailReadService.paging(criteria.toMap()).getResult();
        if (pBalance == null) {
            throw new RuntimeException(new Translate("查询数据库错误").toString());
        }
        boolean alreadyTrigger = !pBalance.isEmpty() && pBalance.getData().stream().map(WithExtraMap::getExtra).anyMatch(map -> map != null && map.getOrDefault("orderId", "").equals(orderId.toString()));
        if (alreadyTrigger) {
            throw new RuntimeException(new Translate("已有相关收支[%s],所以不再触发", pBalance.getData().stream()
                    .filter(outCome -> Optional.ofNullable(outCome.getExtra()).orElseGet(HashMap::new).getOrDefault("orderId", "").equals(orderId.toString()))
                    .map(EntityBase::getId).map(Objects::toString).findFirst().orElse("不存在???")).toString());
        }
    }

    private void markIncomeAsIgnorable(List<InComeDetail> inComeList) {
        inComeList.forEach(balanceDetailManager::markWithIgnoreAble);
    }
}
