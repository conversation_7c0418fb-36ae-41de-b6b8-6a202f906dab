/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.events.trade.listener;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderSourceEnum;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.event.OrderIntegralCountEvent;
import moonstone.event.OrderRefundEvent;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.OrderRefund;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.web.core.component.cache.SkuStockCacheAtRedisManager;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.locks.Lock;

/**
 * 订单退款相关listener
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-23
 *
 * <AUTHOR>
 * edit at 2020-11-05
 */
@Slf4j
public class OrderRefundStockRollbackListener {
    @Autowired
    private RefundReadService refundReadService;
    @Autowired
    private SkuOrderReadService skuOrderReadService;

    @Autowired
    private SkuStockCacheAtRedisManager skuStockCacheAtRedisManager;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 退款单状态联动订单状态变化
     * 1. 修正订单状态
     * 2. 回滚Redis中的库存订单
     * > BUG: 订单流程被卡死, 如果拒绝退款后, 订单就会进入到无法正常流转的状态
     *
     * @param orderRefundEvent 退款事件
     * @apiNote 重构订单状态
     */
    @EventListener(OrderRefundEvent.class)
    public void rollbackRefundOrderStockAtRedisCache(OrderRefundEvent orderRefundEvent) {
        Long refundId = orderRefundEvent.getRefundId();
        Lock lock = redissonClient.getLock(Refund.class.getSimpleName() + "#" + refundId);
        lock.lock();
        try {
            ShopOrder shopOrder = refundReadService.findShopOrderByRefundId(refundId);
            if (shopOrder.getOrderSource().equals(OrderSourceEnum.DEFAULT.getCode())) {
                // redis 库存回滚
                rollbackRefundOrderStockAction(refundId, orderRefundEvent);
            }
        } finally {
            lock.unlock();
        }
    }

    private void rollbackRefundOrderStockAction(Long refundId, OrderRefundEvent orderRefundEvent) {
        List<OrderRefund> orderRefunds = Optional.ofNullable(refundReadService.findOrderIdsByRefundId(refundId).getResult())
                .orElseThrow(() -> Translate.exceptionOf("查找退款相关订单失败"));
        Refund refund = Optional.ofNullable(refundReadService.findById(refundId).getResult()).orElseThrow(() -> Translate.exceptionOf("退款单查找失败"));
        if (!orderRefundEvent.getOrderOperation().equals(OrderEvent.REFUND_SUCCESS.toOrderOperation())) {
            return;
        }
        // > todo 需要保证以下事件只触发一次
        EventSender.sendApplicationEvent(new OrderIntegralCountEvent(0L, orderRefundEvent.getRefundId()));
        //如果是售后退款单,不用更新店铺订单或者sku订单信息
        if (!Objects.equals(refund.getRefundType(), Refund.RefundType.ON_SALE_REFUND.value())) {
            return;
        }
        List<SkuOrder> skuOrders = new ArrayList<>();
        for (OrderRefund orderRefund : orderRefunds) {
            if (OrderStatus.fromInt(orderRefund.getStatus()) == OrderStatus.REFUND) {
                continue;
            }
            switch (orderRefund.getOrderLevel()) {
                case SKU:
                    skuOrders.add(skuOrderReadService.findById(orderRefund.getOrderId()).getResult());
                    break;
                case SHOP:
                    skuOrders.addAll(skuOrderReadService.findByShopOrderId(orderRefund.getOrderId()).getResult());
                    break;
                default:
            }
        }
        log.debug("{} rollback stock of refund(id:{})", LogUtil.getClassMethodName(), refundId);
        skuStockCacheAtRedisManager.rollBackSkuOrder(skuOrders);
    }
}
