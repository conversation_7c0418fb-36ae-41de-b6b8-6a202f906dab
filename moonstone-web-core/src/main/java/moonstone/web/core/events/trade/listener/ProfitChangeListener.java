package moonstone.web.core.events.trade.listener;

import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.event.OrderConfirmEvent;
import moonstone.event.OrderRefundEvent;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderPayment;
import moonstone.order.model.OrderRefund;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.web.core.component.cache.DeveloperEmailAddressCache;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.events.msg.MsgSendRequestEvent;
import moonstone.web.core.events.settle.PaymentSettleEvent;
import moonstone.web.core.events.trade.app.*;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 利润变化的监听器
 * 当订单支付, 收货, 退款时变动相应利润
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProfitChangeListener {
    @Resource
    ShopOrderReadService shopOrderReadService;
    @Resource
    PaymentReadService paymentReadService;
    @Autowired
    BalanceDetailManager balanceDetailManager;
    @Resource
    RefundReadService refundReadService;
    @Autowired
    DeveloperEmailAddressCache developerEmailAddressCache;
    @Autowired
    EnvironmentConfig environmentConfig;
    @Resource
    ShopCacheHolder shopCacheHolder;
    @Autowired
    ForeseenProfitForOrderTakerApp foreseenProfitForOrderTakerApp;
    @Autowired
    SkuOrderProfitReturnApp skuOrderProfitReturnApp;
    @Autowired
    ShopOrderTradeRecordApp shopOrderTradeRecordApp;
    @Autowired
    OrderProfitActionRecordApp orderProfitActionRecordApp;
    @Autowired
    RefundProfitReturnApp refundProfitReturnApp;

    @Resource
    private RocketMQProducer rocketMQProducer;

    /**
     * 订单确定收货时触发, 预收入转化为可提现收入
     *
     * @param orderConfirmEvent 订单确定收货事件
     */
    @EventListener(OrderConfirmEvent.class)
    public void onConfirmedShipment(OrderConfirmEvent orderConfirmEvent) {
        OrderLevel orderLevel;
        try {
            orderLevel = OrderLevel.fromInt(orderConfirmEvent.getOrderType());
        } catch (Exception ex) {
            log.error("{} shit orderLevel are u serious? orderType:{}", LogUtil.getClassMethodName(), orderConfirmEvent.getOrderType());
            /// 发送邮件未测试过
            String toes = developerEmailAddressCache.getAllInJson();
            String data = "orderId=" + orderConfirmEvent.getOrderId();
            EventSender.sendApplicationEvent(new MsgSendRequestEvent(toes, "email.error.notice", ImmutableMap.of("location", "ProfitMakerWithComplexListener（创建收益）",
                    "env", environmentConfig.getEnv(), "errorCode", ex.getMessage(),
                    "data", data, "errorMsg", Throwables.getStackTraceAsString(ex))));
            log.info("sendSms email={}, errorCode={}", toes, ex.getMessage());
            return;
        }
        switch (orderLevel) {
            case SHOP: {
                /// 创建真实收益啦    使用 rocketMQ 去消费消息
                log.info("收到店铺级别的订单确认收货消息 订单id {} 发送生成订单利润消息",orderConfirmEvent.getOrderId());
                rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.GENERATE_ORDER_FORESEE_PROFIT_TAG, String.valueOf(orderConfirmEvent.getOrderId()));
                log.info("收到店铺级别的订单确认收货消息 订单id {} 发送完成生成订单利润消息",orderConfirmEvent.getOrderId());
                break;
            }
            case SKU: {
                log.info("{} no work for skuOrder now skuOrderId:{}", LogUtil.getClassMethodName(), orderConfirmEvent.getOrderId());
                break;
            }
            default: {
                log.error("{} strange orderLevel are u serious? orderType:{}", LogUtil.getClassMethodName(), orderConfirmEvent.getOrderType());
            }
        }
    }

    private List<OrderPayment> readOrderPayment(PaymentSettleEvent paymentSettleEvent) throws InterruptedException {
        int retriedTimes = 0;
        List<OrderPayment> orderPayments = null;
        while (orderPayments == null) {
            if (retriedTimes > 300) {
                throw new RuntimeException(new Translate("支付单[%s] 内部订单业务单号[%s] 无法查找到关联订单", paymentSettleEvent.getPaymentId(), paymentSettleEvent.getTradeNo()).toString());
            }
            orderPayments = paymentReadService.findOrderIdsByPaymentId(paymentSettleEvent.getPaymentId()).getResult();
            if (orderPayments == null) {
                retriedTimes++;
                Thread.sleep(10);
                log.warn("{} payment[{}] outId[{}] seems still hidden", LogUtil.getClassMethodName(), paymentSettleEvent.getPaymentId(), paymentSettleEvent.getTradeNo());
            }
        }
        return orderPayments;
    }

    private Optional<ShopOrder> readShopOrder(OrderPayment op) {
        /// 如果不是shopOrder则无视它,因为目前不支持直接下skuOrder订单,门店内也不支持
        Long shopOrderId = op.getOrderLevel().equals(OrderLevel.SHOP) ? op.getOrderId() : null;
        /// 错误的订单?
        if (shopOrderId == null) {
            log.warn("{} orderPayment[{}] orderLevel[{}] orderId[{}]", LogUtil.getClassMethodName("wrong-payment"), op.getId(), op.getOrderLevel(), op.getOrderId());
            return Optional.empty();
        }
        // IO操作
        val rShopOrder = shopOrderReadService.findById(shopOrderId);
        if (!rShopOrder.isSuccess() || rShopOrder.getResult() == null) {
            log.error("{} find ShopOrder failed by shopOrderId:{}", LogUtil.getClassMethodName(), shopOrderId);
            throw new RuntimeException(new Translate("查找主订单[%s]失败", shopOrderId).toString());
        }
        ShopOrder shopOrder = rShopOrder.getResult();
        /// 仅仅是门店订单才进行创建支付
        if (ObjectUtils.isEmpty(shopOrder.getOutFrom())) {
            log.info("[ProfitMakerWithComplexListener](profit) ignore outFrom:{} for no design or already done", shopOrder.getOutFrom());
            return Optional.empty();
        }
        return Optional.ofNullable(rShopOrder.getResult());
    }

    /**
     * 订单支付时触发
     *
     * @param paymentSettleEvent 支付单统计事件
     */
    @EventListener(PaymentSettleEvent.class)
    public void onPaymentPaid(PaymentSettleEvent paymentSettleEvent) {
        /// 以下错误应该发送邮件 以避免利润未结算
        try {
            if (paymentSettleEvent.getPaymentId() == null) {
                return;
            }
            /// 一堆IO巴拉巴拉 获取支付单到主订单信息
            List<OrderPayment> orderPayments = readOrderPayment(paymentSettleEvent);
            log.info("{} payment[{}} paid num [{}] order", LogUtil.getClassMethodName(), paymentSettleEvent.getPaymentId(), orderPayments.size());
            /// 一个支付单可能支付了多个订单,将其所有订单全部判断一遍
            for (OrderPayment op : orderPayments) {
                try {
                    Optional<ShopOrder> shopOrderOpt = readShopOrder(op);
                    if (!shopOrderOpt.isPresent()) {
                        log.debug("{} no order found for payment[{}]", LogUtil.getClassMethodName(), op.getPaymentId());
                        return;
                    }
                    ShopOrder shopOrder = shopOrderOpt.get();
                    OrderOutFrom outFrom = OrderOutFrom.fromCode(shopOrder.getOutFrom());
                    Shop shop = shopCacheHolder.findShopById(shopOrder.getShopId());

                    // 记录今日销售额
                    shopOrderTradeRecordApp.recordShopOrderTrade(shopOrder, false, true, shop, outFrom);
                    if (ShopFunctionSlice.build(shop).isProfitClosed()) {
                        log.warn("{} order[{}] profit is reject because shop is closed", LogUtil.getClassMethodName(), shopOrder.getId());
                        return;
                    }
                    // 记录利润操作
                    orderProfitActionRecordApp.recordForeseeProfitOrder(shopOrder.getId());
                    foreseenProfitForOrderTakerApp.earnForeseenProfitForOrderPayment(shopOrder, outFrom);
                } catch (Exception e) {
                    log.error("{} fail to calculate the profit for order[Id => {}], payment[Id => {}]", LogUtil.getClassMethodName(),
                            op.getOrderId(), op.getPaymentId(), e);
                }
            }
        } catch (Exception ex) {
            log.error("{} payment[{}] outId[{}] fail to trigger the profit-system,NullException[{}]", LogUtil.getClassMethodName(), paymentSettleEvent.getPaymentId(), paymentSettleEvent.getTradeNo(), ex instanceof NullPointerException, ex);
            String data = "paymentId=" + paymentSettleEvent.getPaymentId();
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("ProfitMakerWithComplexListener(创建收益)", data, ex, EmailReceiverGroup.DEVELOPER));
        }
    }


    @EventListener(OrderRefundEvent.class)
    public void onRefund(OrderRefundEvent orderRefundEvent) {
        try {
            /// 仅仅当退款成功时
            if (orderRefundEvent.getOrderOperation().getValue() == OrderEvent.REFUND_SUCCESS.getValue()) {
                List<OrderRefund> orderRefundList = Optional.ofNullable(refundReadService.findOrderIdsByRefundId(orderRefundEvent.getRefundId()).getResult()).orElseThrow(() -> new RuntimeException("[ProfitMakerWithComplexListener](refund) failed to find related Order"));
                orderRefundList.forEach(orderRefund -> {
                    if (orderRefund.getOrderLevel() == OrderLevel.SHOP) {
                        log.warn("{} decrease the order mark for the Shop by refundId[{}] OrderId[{}]", LogUtil.getClassMethodName(), orderRefundEvent.getRefundId(), orderRefund.getOrderId());
                        ShopOrder shopOrder = Optional.ofNullable(shopOrderReadService.findById(orderRefund.getOrderId()).getResult()).orElseThrow(() -> new RuntimeException("[ProfitMakerWithComplexListener](refund) fail to find shopOrder by Id:" + orderRefund.getOrderId()));
                        if (ObjectUtils.isEmpty(shopOrder.getOutFrom())) {
                            log.info("{} shopOrderId:{} outFrom:{}", LogUtil.getClassMethodName("normal-shop-order"), shopOrder.getId(), shopOrder.getOutFrom());
                            return;
                        }
                        OrderOutFrom outFrom = OrderOutFrom.fromCode(shopOrder.getOutFrom());
                        // check if has refund
                        orderProfitActionRecordApp.recordRefundProfitOrder(shopOrder.getId());
                        refundProfitReturnApp.refundProfit(orderRefund.getRefundId(), shopOrder, outFrom);
                    } else {
                        skuOrderProfitReturnApp.refundSkuOrderProfit(orderRefund);
                    }
                });
            }
        } catch (Exception ex) {
            log.error("{} msg:{} method:{} line:{}", LogUtil.getClassMethodName(), ex.getMessage(), ex.getStackTrace()[0].getMethodName(), ex.getStackTrace()[0].getLineNumber(), ex);
            String toes = developerEmailAddressCache.getAllInJson();
            String data = "refundId=" + orderRefundEvent.getRefundId();
            EventSender.sendApplicationEvent(new MsgSendRequestEvent(toes, "email.error.notice", ImmutableMap.of("location", "ProfitMakerWithComplexListener（退款扣减收益）",
                    "env", environmentConfig.getEnv(), "errorCode", ex.getMessage(),
                    "data", data, "errorMsg", Throwables.getStackTraceAsString(ex))));
            log.info("sendSms email={}, errorCode={}", toes, ex.getMessage());
        }
    }

}
