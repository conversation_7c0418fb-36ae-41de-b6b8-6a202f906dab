/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.events.trade.listener;

import cn.hutool.json.JSONUtil;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.event.OrderShipmentEvent;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.settle.model.Settlement;
import moonstone.settle.service.SettlementReadService;
import moonstone.settle.service.SettlementWriteService;
import moonstone.web.core.refund.application.RefundForSellerApplication;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR>
 * Date: 2016-05-23
 */
@Slf4j
public class OrderShipmentListener {

    @Autowired
    private ShipmentReadService shipmentReadService;

    @Autowired
    private ShopOrderReadService shopOrderReadService;

    @Autowired
    private SkuOrderReadService skuOrderReadService;

    @Autowired
    private OrderWriteService orderWriteService;

    @Autowired
    private OrderStatusUpdater orderStatusUpdater;

    @Autowired
    private RefundReadService refundReadService;

    @Autowired
    private RefundWriteService refundWriteService;

    @Autowired
    private SettlementReadService settlementReadService;

    @Autowired
    private SettlementWriteService settlementWriteService;

    @Autowired
    private PaymentReadService paymentReadService;

    @Autowired
    private RefundForSellerApplication refundForSellerApplication;

    @Autowired
    private MongoTemplate mongoTemplate;

    @EventListener(OrderShipmentEvent.class)
    public void onShipment(OrderShipmentEvent orderShipmentEvent) {
        log.info("收到订单已发货的通知 {}", JSONUtil.toJsonStr(orderShipmentEvent));
        Long shipmentId = orderShipmentEvent.getShipmentId();
        Response<List<OrderShipment>> rOrderShipments = shipmentReadService.findOrderIdsByShipmentId(shipmentId);
        if (!rOrderShipments.isSuccess()) {
            log.error("failed to find orderIds for shipment(id={}), error code:{}", shipmentId, rOrderShipments.getError());
            return;
        }
        List<OrderShipment> orderShipments = rOrderShipments.getResult();

        if (CollectionUtils.isEmpty(orderShipments)) {
            return;
        }
        orderStatusUpdater.update(orderShipments, OrderEvent.SHIP.toOrderOperation());

        //修改订单的最早、最晚发货时间
        Response<Shipment> rShipment = shipmentReadService.findById(shipmentId);
        if (!rShipment.isSuccess()) {
            log.error("failed to find shipment by id={}, error code: {}", shipmentId, rShipment.getError());
            return;
        }
        Date shipmentTime = Date.from(orderShipmentEvent.getShipmentAt().atZone(ZoneId.systemDefault()).toInstant());
        if (Objects.nonNull(rShipment.getResult().getShipmentAt())) {
            shipmentTime = rShipment.getResult().getShipmentAt();
        }
        // protect shipmentAt
        Date protectTime = Date.from(LocalDateTime.of(2000, 1, 1, 0, 0).atZone(ZoneId.systemDefault()).toInstant());
        if (shipmentTime.before(protectTime)) {
            shipmentTime = rShipment.getResult().getCreatedAt().after(new DateTime(orderShipmentEvent.getShipmentAt()).toDate()) ?
                    rShipment.getResult().getCreatedAt() : new DateTime(orderShipmentEvent.getShipmentAt()).toDate();
        }
        Map<Long, ShopOrder> shopOrderMap = new HashMap<>(3);
        for (OrderShipment orderShipment : orderShipments) {
            switch (orderShipment.getOrderLevel()) {
                case SHOP: {
                    Response<ShopOrder> rShopOrder = shopOrderReadService.findById(orderShipment.getOrderId());
                    if (!rShopOrder.isSuccess()) {
                        log.error("failed to find shop order by id={}, error code: {}", orderShipment.getOrderId(), rShopOrder.getError());
                        return;
                    }
                    ShopOrder shopOrder = rShopOrder.getResult();
                    shopOrderMap.put(shopOrder.getId(), shopOrder);
                    break;
                }
                case SKU: {
                    Response<SkuOrder> rSkuOrder = skuOrderReadService.findById(orderShipment.getOrderId());
                    if (!rSkuOrder.isSuccess()) {
                        log.error("failed to find sku order by id={}, error code: {}", orderShipment.getOrderId(), rSkuOrder.getError());
                        return;
                    }
                    Response<ShopOrder> rShopOrder = shopOrderReadService.findById(rSkuOrder.getResult().getOrderId());
                    if (!rShopOrder.isSuccess()) {
                        log.error("failed to find shop order by id={} for sku order(id={}), error code: {}",
                                rSkuOrder.getResult().getOrderId(), rSkuOrder.getResult().getId(), rShopOrder.getError());
                        return;
                    }
                    ShopOrder shopOrder = rShopOrder.getResult();
                    shopOrderMap.put(shopOrder.getId(), shopOrder);
                    break;
                }
                default: {

                }
            }
        }
        for (Long shopOrderId : shopOrderMap.keySet()) {
            ShopOrder shopOrder = shopOrderMap.get(shopOrderId);
            if (shopOrder.getHasRefund() != null && shopOrder.getHasRefund()) {
                rejectRefundByShipmentActionOfShopOrder(shopOrderId);
            }
            Date firstShipmentAt = shopOrder.getFirstShipmentAt();
            Date lastShipmentAt = shopOrder.getLastShipmentAt();
            boolean changeFlag = false;
            if (firstShipmentAt == null || shipmentTime.before(firstShipmentAt)) {
                firstShipmentAt = shipmentTime;
                changeFlag = true;
            }
            if (lastShipmentAt == null || shipmentTime.after(lastShipmentAt)) {
                lastShipmentAt = shipmentTime;
                changeFlag = true;
            }
            if (changeFlag) {
                Response<Boolean> response = orderWriteService.updateShopOrderShipmentTime(shopOrderId, firstShipmentAt, lastShipmentAt);
                if (!response.isSuccess()) {
                    log.error("fail to update shop order(id={}) to firstShipmentAt={}, lastShipmentAt={}, error code: {}",
                            shopOrderId, firstShipmentAt, lastShipmentAt, response.getError());
                }
            }
        }
    }

    @EventListener(OrderShipmentEvent.class)
    public void setShipmentForSettlement(OrderShipmentEvent orderShipmentEvent) {
        List<OrderShipment> orderShipments = shipmentReadService.findOrderIdsByShipmentId(orderShipmentEvent.getShipmentId()).getResult();
        if (orderShipments == null) {
            return;
        }
        List<Payment> payments = new ArrayList<>();
        for (OrderShipment orderShipment : orderShipments) {
            Date shipAt = Date.from(orderShipmentEvent.getShipmentAt().atZone(ZoneId.systemDefault()).toInstant());
            // 更新mongoDB中fullOrder的发货时间shipmentAt
            Query fullOrderQuery = Query.query(Criteria.where("orderId").is(orderShipment.getOrderId())).addCriteria(Criteria.where("shipmentAt").exists(false));
            if (mongoTemplate.exists(fullOrderQuery, FullOrder.class)) {
                Update fullOrderUpdate = new Update().set("shipmentAt", shipAt);
                // 正常情况一个orderId对应一条FullOrder数据，故此处指修改第一条
                mongoTemplate.updateFirst(fullOrderQuery, fullOrderUpdate, FullOrder.class);
                log.debug("{} update fullOrder shipmentAt [{}] where orderId [{}]", LogUtil.getClassMethodName(), shipAt, orderShipment.getOrderId());
            }
            // 更新结算表发货时间ShipmentAt
            List<Payment> orderPayments = paymentReadService.findByOrderIdAndOrderLevel(orderShipment.getOrderId(), orderShipment.getOrderLevel()).getResult();
            if (!CollectionUtils.isEmpty(orderPayments)) {
                payments.addAll(orderPayments);
            } else {
                orderPayments = new ArrayList<>();
                switch (orderShipment.getOrderLevel()) {
                    case SHOP:
                        Optional.ofNullable(skuOrderReadService.findByShopOrderId(orderShipment.getOrderId()).getResult())
                                .orElse(new ArrayList<>())
                                .stream()
                                .map(OrderBase::getId)
                                .map(skuOrderId -> paymentReadService.findByOrderIdAndOrderLevel(skuOrderId, OrderLevel.SKU).getResult())
                                .filter(Objects::nonNull)
                                .forEach(orderPayments::addAll);
                        break;
                    case SKU:
                        Optional.ofNullable(skuOrderReadService.findById(orderShipment.getOrderId()).getResult())
                                .map(SkuOrder::getOrderId)
                                .map(orderId -> paymentReadService.findByOrderIdAndOrderLevel(orderId, OrderLevel.SHOP))
                                .map(Response::getResult)
                                .ifPresent(orderPayments::addAll);
                        break;
                    default:
                }
                if (!CollectionUtils.isEmpty(orderPayments)) {
                    payments.addAll(orderPayments);
                }
            }
            for (Payment payment : payments) {
                Settlement settlement = settlementReadService.findSettlementByTradeNo(payment.getOutId()).getResult();
                if (settlement == null || settlement.getShipmentAt() != null) {
                    log.debug("{} skip-update payment[{}] shop(maybe) [{}] shipmentAt [{}]", LogUtil.getClassMethodName(), payment.getId(), payment.getPayAccountNo(), shipAt);
                    return;
                }
                log.debug("{} update payment[{}] shop(maybe) [{}] shipmentAt [{}]", LogUtil.getClassMethodName(), payment.getId(), payment.getPayAccountNo(), shipAt);
                Settlement update = new Settlement();
                update.setId(settlement.getId());
                // add default shipmentAt
                update.setShipmentAt(shipAt);
                settlementWriteService.updateSettlement(update);
            }
            payments.clear();
        }
    }

    private void rejectRefundByShipmentActionOfShopOrder(Long shopOrderId) {
        List<Refund> refunds = refundReadService.findAllByOrderIdAndOrderLevel(shopOrderId, OrderLevel.SHOP).getResult();
        for (Refund refund : refunds) {
            refundWriteService.updateSellerNote(refund.getId(), new Translate("已发货，自动拒绝退款").toString());

            CommonUser fakeUser = new CommonUser();
            fakeUser.setShopId(refund.getShopId());
            fakeUser.setName("自动退款处理人");
            try {
                switch (OrderStatus.fromInt(refund.getStatus())) {
                    case REFUND_APPLY:
                    case REFUND_APPLY_AGREED:
                    case RETURN_APPLY:
                    case RETURN_APPLY_AGREED:
                        refundForSellerApplication.rejectRefund(refund.getId(), fakeUser, "已发货自动拒绝退款");
                        break;
                    default:
                        break;
                }
            } catch (Exception ex) {
                log.error("{} fail to reject refund of shopOrderId:{}", LogUtil.getClassMethodName("auto-refund-reject"), shopOrderId, ex);
            }
        }
    }
}
