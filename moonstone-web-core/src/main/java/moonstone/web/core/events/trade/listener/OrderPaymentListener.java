/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.events.trade.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import io.vertx.core.Vertx;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.enums.BondedType;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.event.OrderIntegralCountEvent;
import moonstone.event.OrderPaymentEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.model.OrderPayment;
import moonstone.order.model.Payment;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.PaymentWriteService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.web.core.component.order.OrderPushStateMachine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Slf4j
public class OrderPaymentListener {

    @Resource
    PaymentReadService paymentReadService;

    @Resource
    PaymentWriteService paymentWriteService;

    @Autowired
    OrderStatusUpdater orderStatusUpdater;

    @Resource
    SkuOrderReadService skuOrderReadService;

    @Autowired
    OrderPushStateMachine orderPushStateMachine;

    @Resource
    ShopCacheHolder shopCacheHolder;

    @Autowired
    Vertx vertx;

    /**
     * 支付成功，修改支付单推送状态
     *
     * @param event
     */
    @EventListener(OrderPaymentEvent.class)
    public void onPaymentCover(OrderPaymentEvent event) {
        log.debug("OrderPaymentListener.onPaymentCover receive paymentId={}", event.paymentId());
        vertx.createSharedWorkerExecutor("OrderPaid").executeBlocking(p -> onPayment(event));
    }

    public void onPayment(OrderPaymentEvent orderPaymentEvent) {
        Long paymentId = orderPaymentEvent.getPaymentId();
        log.debug("OrderPaymentListener.onPayment begin to process paymentId={}", paymentId);

        Response<Payment> rPayment = paymentReadService.findById(paymentId);
        if (!rPayment.isSuccess()) {
            log.error("failed to find Payment(id={}), error code:{}", paymentId, rPayment.getError());
            return;
        }
        final Payment payment = rPayment.getResult();

        if (!Objects.equal(payment.getStatus(), OrderStatus.PAID.getValue())) { //不是支付成功事件, 直接返回吧
            log.info("OrderPaymentListener.onPayment, 支付单paymentId={}不是支付成功状态，不处理", paymentId);
            return;
        }

        Response<List<OrderPayment>> rOrderPayments = paymentReadService.findOrderIdsByPaymentId(paymentId);
        if (!rOrderPayments.isSuccess()) {
            log.error("OrderPaymentListener.onPayment, failed to find orderIds for payment(id={}), error code:{}", paymentId, rOrderPayments.getError());
            return;
        }
        List<OrderPayment> orderPayments = rOrderPayments.getResult();

        // 修改支付单推送状态
        var newPaymentPushStatus = judgePaymentPushStatusFromOrderRelation(orderPayments);
        updatePaymentPushStatus(payment, newPaymentPushStatus);

        if (CollectionUtils.isEmpty(orderPayments)) {
            return;
        }

        //add 2019-07-15
        EventSender.sendApplicationEvent(new OrderIntegralCountEvent(orderPaymentEvent.getPaymentId(), 0L));
        log.debug("OrderPaymentListener.onPayment paymentId={}, process end", paymentId);
    }

    /**
     * 视情况更新支付单的推送状态
     *
     * @param payment
     * @param newPushStatus
     */
    private void updatePaymentPushStatus(Payment payment, PaymentPushStatus newPushStatus) {
        if (paymentNeedUpdate(payment)) {
            log.info("OrderPaymentListener.onPayment, 支付单paymentId={}, 当前pushStatus={}, 更新pushStatus={}",
                    payment.getId(), payment.getPushStatus(), newPushStatus.getValue());
            payment.setPushStatus(newPushStatus.getValue());
            paymentWriteService.update(payment);
        } else {
            log.info("OrderPaymentListener.onPayment, 支付单paymentId={}, 当前pushStatus={}, 不再更新为pushStatus={}",
                    payment.getId(), payment.getPushStatus(), newPushStatus.getValue());
        }
    }

    private boolean paymentNeedUpdate(Payment payment) {
        if (payment.getPushStatus() == null || PaymentPushStatus.getEnums(payment.getPushStatus()) == null) {
            return true;
        }

        var alreadyPushed = Lists.newArrayList(PaymentPushStatus.PUSH_SUCCESS, PaymentPushStatus.DECLARE_SUCCESS,
                PaymentPushStatus.WAIT_CALLBACK);

        return !alreadyPushed.contains(PaymentPushStatus.getEnums(payment.getPushStatus()));
    }

    /**
     * 判断是否需要等待采购
     *
     * @param orderPayments 支付单对应订单
     * @return 支付单推送状态
     */
    private PaymentPushStatus judgePaymentPushStatusFromOrderRelation(List<OrderPayment> orderPayments) {
        for (OrderPayment orderPayment : orderPayments) {
            List<SkuOrder> skuOrderList;
            switch (orderPayment.getOrderLevel()) {
                case GATHER -> skuOrderList = skuOrderReadService.findByGatherOrderId(orderPayment.getOrderId()).take();
                case SHOP ->
                        skuOrderList = skuOrderReadService.findByShopOrderId(orderPayment.getOrderId()).getResult();
                case SKU ->
                        skuOrderList = Collections.singletonList(skuOrderReadService.findById(orderPayment.getOrderId()).getResult());
                default -> {
                    log.error("{} can't find order from [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(orderPayment));
                    EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("JudgePaymentPushStatusFail"
                            , String.format("OrderId[%s] OrderLevel[%s] fail to match OrderLevel", orderPayment.getOrderId(), orderPayment.getOrderLevel())
                            , EmailReceiverGroup.DEVELOPER));
                    throw new IllegalStateException(String.format("unknown order level[%s] for order[%s]", orderPayment.getOrderLevel(), orderPayment.getOrderId()));
                }
            }
            if (orderPushStateMachine.needGather(skuOrderList)) {
                return PaymentPushStatus.WAIT_GATHER;
            }
            var shop = shopCacheHolder.findShopById(skuOrderList.get(0).getShopId());
            var f = ShopFunctionSlice.build(shop);
            if (f.isShopOpenOrderAuth()) {
                return PaymentPushStatus.NEED_AUTH;
            }
            boolean isbonded = false;
//            boolean fromThirdParty = false;
            for (SkuOrder skuOrder : skuOrderList) {
                if (BondedType.fromInt(skuOrder.getIsBonded()).isBonded()) {
                    isbonded = true;
                }
            }
            if (isbonded) {
                return PaymentPushStatus.WAIT_PUSH;
            }
        }
//                if (skuOrder.getIsThirdPartyItem() == 1) {
//                    fromThirdParty = true;
//                }
//                if (isbonded || fromThirdParty) {
//                    break;
//                }
//            }
//            if (isbonded && fromThirdParty) {
//                return PaymentPushStatus.WAIT_PUSH;
//            }

        return PaymentPushStatus.NO_NEED_PUSH;
    }

}
