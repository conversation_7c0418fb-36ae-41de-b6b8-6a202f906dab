package moonstone.web.core.user.application;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import io.terminus.pay.model.TradeRequest;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.utils.UserUtil;
import moonstone.web.core.component.pay.allinpay.user.AllinPayAccountBindService;
import moonstone.web.core.component.pay.allinpay.user.ParanaRegisterByPayHandler;
import moonstone.web.core.component.pay.allinpay.user.dto.AllinPayLoginUserRegisterAccountDTO;
import moonstone.web.core.component.pay.allinpay.user.req.*;
import moonstone.web.core.component.pay.allinpay.user.res.*;
import moonstone.web.op.allinpay.enums.AllinPayRoleTypeEnum;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("allinPay")
@ApiModel(value = "通联支付")
public class AllinPayController {

	@Resource
	private AllinPayAccountBindService allinPayAccountBindService;

	@Resource
	private ParanaRegisterByPayHandler paranaRegisterByPayHandler;

	@PostMapping("initAllinPayUser")
	@ApiOperation(value = "初始化通联会员", notes = "初始化通联会员")
	public Result<Boolean> initAllinPayUser(@RequestBody AllinPayLoginUserRegisterAccountDTO dto){
		return allinPayAccountBindService.initAllinPayUser(dto);
	}

	/**
	 * 【支付设置-发送验证码】发送验证码
	 * @param sendPhoneDTO 通联-发送验证码
	 * @return 成功与否
	 */
	@PostMapping("sendPhoneCode")
	@ApiOperation(value = "手机号:发送验证码（绑定or解绑）", notes = "发送验证码（场景1：绑定手机号）")
	public Result<Boolean> sendPhoneCode(@RequestBody @Valid AllinPaySendPhoneCodeDTO sendPhoneDTO){
		return allinPayAccountBindService.sendPhoneCode(sendPhoneDTO);
	}

	/**
	 * 【支付设置-绑定手机号】绑定 用于 租客、房东、个人组织、企业组织
	 * @param bindingPhoneDTO 通联绑定手机
	 * @return 成功与否
	 */
	@PostMapping("bindingPhone")
	@ApiOperation(value = "手机号:绑定", notes = "【绑定手机号】绑定")
	public Result<Boolean> bindingPhone(@RequestBody @Valid AllinPayBindingPhoneDTO bindingPhoneDTO){
		return allinPayAccountBindService.bindingPhone(bindingPhoneDTO);
	}

	/**
	 * 【支付设置-解绑手机号】绑定 用于 租客、房东、个人组织、企业组织
	 * @param bindingPhoneDTO 通联绑定手机
	 * @return 成功与否
	 */
	@PostMapping("unbindingPhone")
	@ApiOperation(value = "手机号:解绑", notes = "解绑手机号")
	public Result<Boolean> unbindingPhone(@RequestBody @Valid AllinPayUnBindingPhoneDTO bindingPhoneDTO){
		return allinPayAccountBindService.unbindingPhone(bindingPhoneDTO);
	}

	/**
	 * 【支付设置-登陆人实名认证】用于 租客、房东、个人组织
	 * @param authenticationDTO 实名认证
	 * @return 成功与否
	 */
	@PostMapping("authentication")
	@ApiOperation(value = "实名认证", notes = "登陆人实名认证（自动创建通联会员）")
	public Result<Boolean> authentication(@RequestBody @Valid AllinPayAuthenticationDTO authenticationDTO){
		return allinPayAccountBindService.authentication(authenticationDTO);
	}


	/**
	 * 【支付设置-设置企业信息】用于 企业组织
	 * @param companyInfoDTO 设置企业信息
	 * @return 成功与否
	 */
	@PostMapping("setCompanyInfo")
	@ApiOperation(value = "设置企业信息", notes = "设置企业信息")
	public Result<Boolean> setCompanyInfo(@RequestBody @Valid AllinPaySetCompanyInfoDTO companyInfoDTO){
		return allinPayAccountBindService.setCompanyInfo(companyInfoDTO);
	}
	@PostMapping("updateCompanyInfo")
	@ApiOperation(value = "修改企业信息", notes = "修改企业信息")
	public Result<Boolean> updateCompanyInfo(@RequestBody @Valid AllinPaySetCompanyInfoDTO companyInfoDTO){
		return allinPayAccountBindService.updateCompanyInfo(companyInfoDTO);
	}

	/**
	 * 【支付设置-请求绑定银行卡】绑定 用于 租客、房东、个人组织、企业组织（企业可以绑定个人卡）
	 * @param applyBindBankCardDTO 请求绑卡
	 * @return 成功与否
	 */
	@PostMapping("applyBindBankCard")
	@ApiOperation(value = "银行卡:静默绑定", notes = "【绑定银行卡】绑定")
	public Result<Boolean> applyBindBankCard(@RequestBody @Valid AllinPayApplyBindBankCardDTO applyBindBankCardDTO){
		if(applyBindBankCardDTO != null){
			// 该接口仅作用在企业会员上
			applyBindBankCardDTO.setAllinpayRoleType(AllinPayRoleTypeEnum.PERSON.getType());
			applyBindBankCardDTO.setUserId(UserUtil.getCurrentUser().getId());
		}
		return allinPayAccountBindService.applyBindBankCard(applyBindBankCardDTO);
	}
	@PostMapping("unBindBankCard")
	@ApiOperation(value = "银行卡:解绑", notes = "解绑银行卡")
	public Result<Boolean> unBindBankCard(@RequestBody AllinPayUnBindBankCardDTO applyBindBankCardDTO){
		if(applyBindBankCardDTO != null){
			// 该接口仅作用在企业会员上
			applyBindBankCardDTO.setAllinpayRoleType(AllinPayRoleTypeEnum.PERSON.getType());
			applyBindBankCardDTO.setUserId(UserUtil.getCurrentUser().getId());
		}
		return allinPayAccountBindService.unBindBankCard(applyBindBankCardDTO);
	}


	@PostMapping("cardBin")
	@ApiOperation(value = "银行卡:查询卡bin", notes = "查询卡bin")
	public Result<CardBinVo> cardBin(@RequestBody @Valid AllinPayCardBinQueryDTO cardBinQueryDTO){
		return allinPayAccountBindService.cardBin(cardBinQueryDTO);
	}

	/**
	 * step:
	 * 		绑定步骤：
	 * 		auth: 实名认证
	 * 		wait: 未注册
	 * 		register: 注册完成
	 *		over: 绑卡or企业认证流程结束
	 * 		phone: 绑定手机号
	 * 		【取消】acct: 门店等角色 绑定了支付标识后需要进行解绑手机号操作才能继续绑定手机号
	 * 		company_info: 设置公司信息
	 * 		company_info_wait_back: 设置企业信息等待回调
	 * 		company_info_fail: 设置企业信息失败
	 * @param dto
	 * @return
	 */
	@PostMapping("bindStepInfo")
	@ApiOperation(value = "查询绑定流程回显数据", notes = "查询绑定流程回显数据")
	public Result<AccountAllinPayQueryVO> queryBindInfo(@RequestBody QueryBindInfoDTO dto){
		return allinPayAccountBindService.queryBindInfoByLoginUser(dto);
	}


	@PostMapping("bizUserId")
	@ApiOperation(value = "【直连通商云】Debug-查询bizUserId", notes = "查询bizUserId")
	public Result<String> bizUserId(@RequestParam String bizUserId){
		return allinPayAccountBindService.getMemberInfo(bizUserId);
	}
	@PostMapping("queryBankCard")
	@ApiOperation(value = "【直连通商云】Debug-查询bizUserId绑卡信息", notes = "查询bizUserId")
	public Result<String> queryBankCard(@RequestParam String bizUserId){
		return allinPayAccountBindService.queryBankCard(bizUserId);
	}
	@PostMapping("decryptBankCard")
	@ApiOperation(value = "【直连通商云】解密卡号", notes = "解密卡号")
	public Result<String> decryptBankCard(@RequestParam String cardNo, @RequestParam String bizUserId){
		return allinPayAccountBindService.decryptBankCard(cardNo, bizUserId);
	}

//	@PostMapping("subStoreInfo")
//	@ApiOperation(value = "门店开户信息", notes = "门店开户信息")
//	public Result<SubStoreInfoVO> subStoreInfo(@RequestBody SubStoreInfoDTO dto){
//		return allinPayAccountBindService.subStoreInfo(dto);
//	}


	@PostMapping("bindOpenId")
	@ApiOperation(value = "绑定openId、appId", notes = "绑定openId、appId")
	public Result<Boolean> bindOpenId(@RequestBody @Valid AllinPayBindOpenIdDTO bindOpenIdDTO){
		return allinPayAccountBindService.bindOpenId(bindOpenIdDTO);
	}

	@PostMapping("/signContract")
	@ApiOperation(value = "签约:获取签约地址", notes = "会员电子协议签约")
	public Result<String> signContract(@RequestBody SignContractReq dto){
		return allinPayAccountBindService.signContract(dto);
	}

	/**
	 * 0未签约1签约中2已签约
	 * @param dto
	 * @return
	 */
	@PostMapping("querySignContract")
	@ApiOperation(value = "签约:查询本地签约状态", notes = "查询会员电子协议签约")
	public Result<Integer> querySignContract(@RequestBody SignContractReq dto){
		Result<Integer> result = allinPayAccountBindService.getSignContractStatus(dto);
		return result;
	}

	@PostMapping("registerCompanyMember")
	@ApiOperation(value = "企业会员开户H5", notes = "企业会员开户H5")
	public Result<String> registerCompanyMember(@RequestBody AllinPayRegisterCompanyMemberDTO dto){
		return allinPayAccountBindService.registerCompanyMember(dto);
	}

	@PostMapping("switchAvailability")
	@ApiOperation(value = "切换启用的通联账户", notes = "切换启用的通联账户")
	public Result<SubStoreInfoVO> switchAvailability(@RequestBody SwitchAvailabilityDTO dto){
		return allinPayAccountBindService.switchAvailability(dto);
	}

	@PostMapping("allinPayAccountDetail")
	@ApiOperation(value = "门店通联账户详情", notes = "门店通联账户详情")
	public Result<AllinPayAccountDetailVO> allinPayAccountDetail(@RequestBody SubStoreInfoDTO dto){
		return allinPayAccountBindService.allinPayAccountDetail(dto);
	}

	@PostMapping("/api/family/registerByPay")
	@ApiOperation(value = "全家福-支付注册", notes = "全家福-支付注册")
	public Result<TradeRequest> registerByPay(@RequestBody FamilyRegisterByPayRequest request){
		return paranaRegisterByPayHandler.registerByPay(request);
	}
}
