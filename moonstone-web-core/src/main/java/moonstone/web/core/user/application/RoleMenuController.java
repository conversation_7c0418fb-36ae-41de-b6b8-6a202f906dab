package moonstone.web.core.user.application;

import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.api.APIResp;
import moonstone.common.api.Result;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.UserRole;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.APIRespWrapper;
import moonstone.common.utils.UserUtil;
import moonstone.user.auth.Role;
import moonstone.user.auth.UserRoleLoader;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.RoleMenu;
import moonstone.user.model.view.RoleMenuView;
import moonstone.user.service.RoleMenuManager;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 角色菜单相关接口
 */
@RestController
@AllArgsConstructor
@RequestMapping("/api/role/menu")
@Slf4j
public class RoleMenuController {
    ShopCacheHolder shopCacheHolder;
    RoleMenuManager roleMenuManager;
    UserTypeBean userTypeBean;
    UserRoleLoader userRoleLoader;

    @PostMapping("/create")
    public APIResp<Boolean> createByTree(@RequestBody RoleMenuView view) {
        if (!userTypeBean.isAdmin(UserUtil.getCurrentUser())) {
            return APIResp.notLogin();
        }
        return APIRespWrapper.wrap(roleMenuManager.createByView(view));
    }

    @PostMapping("/user")
    public APIResp<Boolean> groupMenuForRole(String role, Long userId, @RequestBody List<String> menu) {
        return APIRespWrapper.wrap(roleMenuManager.createUserRoleMenu(userId, role, menu));
    }

    @PostMapping("/group")
    public APIResp<Boolean> groupMenuForRole(String role, @RequestBody List<String> menu) {
        return APIRespWrapper.wrap(roleMenuManager.createDefaultRoleMenu(role, menu));
    }

    @GetMapping("/{userId}")
    public APIResp<List<String>> userMenu(String role, @PathVariable Long userId) {
        return APIRespWrapper.wrap(roleMenuManager.defaultAllowMenuForRole(role, userId));
    }

    @GetMapping
    public APIResp<RoleMenuView> viewByRole(String role) {
        return APIRespWrapper.wrap(roleMenuManager.findByRole(role));
    }

    @GetMapping("/allowMenu")
    public Set<String> allowMenu(){
        CommonUser user = UserUtil.getCurrentUser();
        if (userTypeBean.isSeller(user) || userTypeBean.isSubAccount(user)) {
            String shopMode = Optional.ofNullable(shopCacheHolder.findShopByUserId(user.getId()).getExtra())
                    .map(extra -> extra.getOrDefault(ShopExtra.SalesPattern.getCode(), ShopExtra.commonShop.getCode()))
                    .orElse(ShopExtra.commonShop.getCode());
            Set<String> allowMenuSet = new HashSet<>(roleMenuManager.defaultAllowMenuForRole(SalePattern.from(shopMode).getRole().name(), user.getId()).orElse(new ArrayList<>()));
            for (Role role : userRoleLoader.hardLoadRoles(user.getId()).getResult().getRoles()) {
                if (role.getBase().equals(UserRole.SELLER.name())) {
                    if (role.getTreeNodeSelection() != null) {
                        allowMenuSet.addAll(role.getTreeNodeSelection());
                    }
                }
            }
            return allowMenuSet;
        }
        return null;
    }

    @GetMapping("/current")
    public RoleMenuView current() {
        log.info("获取当前用户菜单数据");
        CommonUser user = UserUtil.getCurrentUser();
        log.info("当前用户 {}", JSONUtil.toJsonStr(user));
        if (userTypeBean.isSeller(user) || userTypeBean.isSubAccount(user)) {
            RoleMenuView roleMenuView = roleMenuManager.findByRole("SELLER").orElse(null);
            if (roleMenuView == null) {
                return null;
            }
            String shopMode = Optional.ofNullable(shopCacheHolder.findShopByUserId(user.getId()).getExtra())
                    .map(extra -> extra.getOrDefault(ShopExtra.SalesPattern.getCode(), ShopExtra.commonShop.getCode()))
                    .orElse(ShopExtra.commonShop.getCode());
            Set<String> allowMenuSet = new HashSet<>(roleMenuManager.defaultAllowMenuForRole(SalePattern.from(shopMode).getRole().name(), user.getId()).orElse(new ArrayList<>()));
            for (Role role : userRoleLoader.hardLoadRoles(user.getId()).getResult().getRoles()) {
                if (role.getBase().equals(UserRole.SELLER.name())) {
                    if (role.getTreeNodeSelection() != null) {
                        allowMenuSet.addAll(role.getTreeNodeSelection());
                    }
                }
            }
            filterMenu(roleMenuView.getChildren(), allowMenuSet);
            return roleMenuView;
        }
        return null;
    }

    /**
     * 获取当前用户菜单数据
     */
    @GetMapping("/current/v2")
    public Result<RoleMenuView> currentV2() {
        log.info("获取当前用户菜单数据");
        CommonUser user = UserUtil.getCurrentUser();
        log.info("当前用户 {}", JSONUtil.toJsonStr(user));
        if (userTypeBean.isSeller(user) || userTypeBean.isSubAccount(user)) {
            RoleMenuView roleMenuView = roleMenuManager.findByRole("SELLER").orElse(null);
            if (roleMenuView == null) {
                return null;
            }
            String shopMode = Optional.ofNullable(shopCacheHolder.findShopByUserId(user.getId()).getExtra())
                    .map(extra -> extra.getOrDefault(ShopExtra.SalesPattern.getCode(), ShopExtra.commonShop.getCode()))
                    .orElse(ShopExtra.commonShop.getCode());
            Set<String> allowMenuSet = new HashSet<>(roleMenuManager.defaultAllowMenuForRole(SalePattern.from(shopMode).getRole().name(), user.getId()).orElse(new ArrayList<>()));
            log.info("当前模式 {} 允许的路由 {}",shopMode,JSONUtil.toJsonStr(allowMenuSet));
            for (Role role : userRoleLoader.hardLoadRoles(user.getId()).getResult().getRoles()) {
                if (role.getBase().equals(UserRole.SELLER.name())) {
                    if (role.getTreeNodeSelection() != null) {
                        allowMenuSet.addAll(role.getTreeNodeSelection());
                    }
                }
            }
            filterMenu(roleMenuView.getChildren(), allowMenuSet);
            return Result.data(roleMenuView);
        }
        throw new ApiException("当前用户角色不支持");
    }

    private void filterMenu(List<RoleMenuView.Children> children, Set<String> allowMenu) {
        if (children == null || children.isEmpty()) {
            return;
        }
        LinkedList<RoleMenuView.Children> removed = new LinkedList<>();
        for (RoleMenuView.Children child : children) {
            if (!allowMenu.contains(child.getKey())) {
                removed.add(child);
            }
        }
        removed.forEach(children::remove);
        children.forEach(c -> filterMenu(c.getChildren(), allowMenu));
    }

    @PostMapping("/update")
    public APIResp<Boolean> update(@RequestBody RoleMenu roleMenu) {
        if (!userTypeBean.isAdmin(UserUtil.getCurrentUser())) {
            return APIResp.notLogin();
        }
        return APIRespWrapper.wrap(roleMenuManager.updateRoleMenu(roleMenu));
    }
}
