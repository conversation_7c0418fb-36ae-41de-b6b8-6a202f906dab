package moonstone.web.core.msg.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.Json;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.msg.model.WxMsgData;
import moonstone.web.core.user.WxAccessTokenByProjectCacheHolder;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * 向微信发起请求的信息总线
 */
@Slf4j
@Component
@AllArgsConstructor
public class WeChatMsgEventBus {
    WxAccessTokenByProjectCacheHolder wxAccessTokenByProjectCacheHolder;
    WxOpenParanaComponentService wxOpenParanaComponentService;
    EnvironmentConfig environmentConfig;

    /**
     * send the msg
     *
     * @param shopWxa    店铺信息配置
     * @param userOpenId 用户openId
     * @param wxMsgData  信息参数
     * @return 结果
     */
    public Either<Boolean> send(Long projectId, ShopWxa shopWxa, String userOpenId, String templateId, WxMsgData wxMsgData) {
        String accessCode;
        if (ObjectUtils.isEmpty(shopWxa.getAppSecret())) {
            try {
                accessCode = wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxa.getAppId(), false);
            } catch (Exception e) {
                log.error("{} fail to send WeChat Msg by Gain AccessToken for Project[{}] App[{}] user[OpenId => {}] msg [{}]", LogUtil.getClassMethodName(), projectId, shopWxa.getAppId(), userOpenId, Json.toJson(wxMsgData), e);
                return Either.error(e);
            }
        } else {
            accessCode = wxAccessTokenByProjectCacheHolder.getAccessToken(projectId).getAccessToken();
        }
        String url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + accessCode;
        try {
            HttpRequest request = HttpRequest.post(url);
            String data = Json.toJson(ImmutableMap.of("touser", userOpenId,
                    "template_id", templateId,
                    "page", Optional.ofNullable(wxMsgData.getPage()).orElse(""),
                    "data", Objects.requireNonNull(convertParameter(wxMsgData.getObject())),
                    "miniprogram_state", ImmutableMap.of("online", "formal"
                            , "pre", "trial"
                            , "test", "trial"
                            , "dev", "developer").get(environmentConfig.getEnv())));
            log.debug("{} trying send msg [{}]", LogUtil.getClassMethodName(), data);
            request.contentType(HttpRequest.CONTENT_TYPE_JSON)
                    .send(data);
            if (request.ok()) {
                String body = request.body();
                log.debug("{} msg send result [{}]", LogUtil.getClassMethodName(), body);
                Map<String, Object> result = Objects.requireNonNull(Json.parseObject(body, new TypeReference<Map<String, Object>>() {
                }));
                if (Optional.ofNullable(result.get("errcode")).map(Object::toString).filter(Predicate.isEqual("0")).isPresent()) {
                    return Either.ok(true);
                }
                return Either.error(Translate.exceptionOf("发送失败 %s", result.get("errmsg")));
            }
            return Either.error(Translate.exceptionOf("连接微信信息总线失败"));
        } catch (Exception e) {
            log.error("{} fail to send WeChat Msg for Project[{}] App[{}] user[OpenId => {}] msg [{}]", LogUtil.getClassMethodName(), projectId, shopWxa.getAppId(), userOpenId, Json.toJson(wxMsgData), e);
            return Either.error(e);
        }
    }

    private Map<String, Map<String, Object>> convertParameter(Map<String, Object> object) {
        Map<String, Map<String, Object>> wxParameter = new HashMap<>();
        for (Map.Entry<String, Object> pair : object.entrySet()) {
            wxParameter.put(pair.getKey().split(".DATA")[0], ImmutableMap.of("value", pair.getValue()));
        }
        return wxParameter;
    }
}
