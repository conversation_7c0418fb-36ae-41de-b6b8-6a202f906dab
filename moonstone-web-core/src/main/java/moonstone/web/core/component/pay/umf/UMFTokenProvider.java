package moonstone.web.core.component.pay.umf;

import com.alibaba.fastjson.JSON;
import io.terminus.pay.api.TokenProvider;
import io.terminus.pay.exception.PayException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class UMFTokenProvider implements TokenProvider<UMFToken> {
    Map<String, UMFToken> tokenMap = new ConcurrentHashMap<>();

    @Override
    public UMFToken findToken(String s) throws PayException {
        log.info("UMFTokenProvider tokenMap = {}", JSON.toJSONString(tokenMap));
        return tokenMap.get(s);
    }

    @Override
    public List<String> listAllAccounts() {
        return new ArrayList<>(tokenMap.keySet());
    }

    @Override
    public void register(String s, UMFToken umfToken) throws PayException {
        tokenMap.put(s, umfToken);
    }

    public void unregister(String account) {
        tokenMap.remove(account);
    }
}
