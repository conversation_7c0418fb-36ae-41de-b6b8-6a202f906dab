package moonstone.web.core.component.pay.bhecard.app;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.RefundCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.Refund;
import moonstone.order.service.RefundReadService;
import moonstone.web.core.component.pay.PayChannelsConstants;
import moonstone.web.core.component.pay.bhecard.domain.EasyPayToken;
import moonstone.web.core.component.pay.bhecard.domain.QueryRequest;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.refund.service.RefundCallBackService;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.concurrent.locks.Lock;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RefundingAutoQueryApp {

    @Resource
    private RefundReadService refundReadService;
    @Autowired
    private EasyPayTokenProvider easyPayTokenProvider;
    @Autowired
    private RefundCallBackService refundCallBackService;
    @Autowired
    private EnvironmentConfig environmentConfig;

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    @Scheduled(cron = "0 0/5 * * * ?")
    public void autoQueryRefundStatus() {
        RefundCriteria refundCriteria = new RefundCriteria();
        refundCriteria.setChannels(Collections.singletonList(PayChannelsConstants.EASY_PAY));
        refundCriteria.setStatus(Collections.singletonList(OrderStatus.REFUND_PROCESSING.getValue()));
        for (Refund refund : refundReadService.findRefundBy(1, Integer.MAX_VALUE, refundCriteria).getResult().getData()) {
            Lock lock = redissonClient.getLock(String.format("[%s](%s)", Refund.class, refund.getId()));
            try {
                if (!lock.tryLock()) {
                    continue;
                }
            } catch (Exception ignore) {
                continue;
            }
            try {
                //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
                EasyPayToken token = easyPayTokenProvider.findToken(refund.getShopId().toString());
                QueryRequest queryRequest = new QueryRequest();
                queryRequest.setOutTradeNo(refund.getTradeNo());
                queryRequest.setMerchantId(token.getMerchantId());
                queryRequest.setIgnoreDomainCheck(!environmentConfig.isOnline());
                queryRequest.request(token.getGateway(), token.getPartnerId(), token.getSecretKey())
                        .intoOptional()
                        .filter(result -> QueryRequest.QueryResult.Type.REFUND.equals(result.getTradeType()))
                        .ifPresent(result -> refundCallBackService.refundCallBack(refund.getOutId(), Date.from(result.getSuccessTime().atZone(ZoneId.systemDefault()).toInstant())));
            } catch (Exception e) {
                log.error("{} fail to query[{}]", LogUtil.getClassMethodName(), refund.getId(), e);
            } finally {
                lock.unlock();
            }
        }
    }
}
