package moonstone.web.core.component.pay.bhecard.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.api.TokenProvider;
import io.terminus.pay.enums.TradeType;
import io.terminus.pay.model.*;
import io.terminus.pay.util.URLUtil;
import io.terminus.pay.wechatpay.common.model.params.WxJsapiPayParams;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.web.core.component.pay.PayChannelsConstants;
import moonstone.web.core.component.pay.bhecard.domain.*;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class EasyPayChannel implements io.terminus.pay.service.PayChannel {
    private final static String TEST_PUBLIC_KEY_AT_ONLINE = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2WTfvas1JvvaRuJWIKmKlBLmkRvr2O7Fu3k/zvhJs+X1JQorPWq/yZduY6HKu0up7Qi3T6ULHWyKBS1nRqhhHpmLHnI3sIO8E/RzNXJiTd9/bpXMv+H8F8DW5ElLxCIVuwHBROkBLWS9fIpslkFPt+r13oKFnuWhXgRr+K/YkJQIDAQAB";
    private static final String TEST_SELLER_EMAIL = "<EMAIL>";

    @Value("${pay.easy-pay.publicKey:MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2WTfvas1JvvaRuJWIKmKlBLmkRvr2O7Fu3k/zvhJs+X1JQorPWq/yZduY6HKu0up7Qi3T6ULHWyKBS1nRqhhHpmLHnI3sIO8E/RzNXJiTd9/bpXMv+H8F8DW5ElLxCIVuwHBROkBLWS9fIpslkFPt+r13oKFnuWhXgRr+K/YkJQIDAQAB}")
    String publicKey;
    @Autowired
    ChannelRegistry registry;
    @Autowired
    TokenProvider<EasyPayToken> easyPayTokenTokenProvider;
    @Autowired
    private EnvironmentConfig environmentConfig;
    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    @PostConstruct
    public void register() {
        registry.register(PayChannelsConstants.EASY_PAY, this);
    }

    @Override
    public TradeResult businessPay(BusinessPayParams businessPayParams) {
        throw new RuntimeException("not impl");
    }

    @Override
    public TradeResult paymentClose(PaymentCloseParams paymentCloseParams) {
        throw new RuntimeException("not support");
    }

    @Override
    public TradeRequest paymentRequest(PaymentParams paymentParams) {
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        EasyPayToken easyPayToken = easyPayTokenTokenProvider.findToken(paymentParams.getSellerNo());
        JsPayRequest payRequest = new JsPayRequest();
        payRequest.setSubject(paymentParams.getSubject());
        payRequest.setAmount(paymentParams.getFee());
        payRequest.setOutTradeNo(paymentParams.getTradeNo());
        payRequest.setOpenId(paymentParams.getOpenId());
        payRequest.setBusinessTime(new Date());
        payRequest.setNotifyUrl(URLUtil.backNofityUrl(easyPayToken.getNotifyUrl(), paymentParams.getChannel(), paymentParams.getSellerNo()));
        payRequest.setMerchantId(easyPayToken.getMerchantId());
        if (!environmentConfig.isOnline()) {
            payRequest.setIgnoreDomainCheck(true);
        }
        try {
            WxJsapiPayParams payParams = new WxJsapiPayParams();
            H5Request h5Request = new H5Request();
            BeanUtils.copyProperties(payRequest, h5Request);
            h5Request.setBody("预先设置支付单超时时间");
            h5Request.request(easyPayToken.getGateway(), easyPayToken.getPartnerId(), easyPayToken.getSecretKey()).take();
            BeanUtils.copyProperties(payRequest.request(easyPayToken.getGateway(), easyPayToken.getPartnerId(), easyPayToken.getSecretKey()).take().getBody().toWxResp(), payParams);
            return TradeRequest.ok(new Redirect(paymentParams.getChannel(), false, null, payParams.toJson(), easyPayToken.getGateway()));
        } catch (Exception exception) {
            log.error("{} fail to prepay [{}]", LogUtil.getClassMethodName(), payRequest, exception);
            return TradeRequest.fail(exception instanceof NullPointerException ? Translate.of("支付失败") : exception.getMessage());
        }
    }

    @Override
    public TradeResult paymentCallback(HttpServletRequest httpServletRequest) {
        String json = null;
        try {
            json = JSON.toJSONString(JSON.parseObject(httpServletRequest.getInputStream(), CommonRequest.class));
            CommonRequest<PayNotify> request = JSON.parseObject(json, new TypeReference<CommonRequest<PayNotify>>() {
            });
            PayNotify payNotify = request.getBizContent();
            if (payNotify.getSellerEmail().equals(TEST_SELLER_EMAIL)) {
                request.verify(TEST_PUBLIC_KEY_AT_ONLINE);
            } else {
                request.verify(publicKey);
            }
            TradeResult result = new TradeResult();
            result.setPaySuccess(payNotify.success() && PayNotify.TradeStatus.TRADE_FINISHED.equals(payNotify.getTradeStatus()));
            result.setMerchantSerialNo(payNotify.getRefNo());
            result.setGatewaySerialNo(payNotify.getTradeNo());
            result.setTradeAt(Date.from(LocalDateTime.from(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").parse(payNotify.getLogisticsModify())).atZone(ZoneId.systemDefault()).toInstant()));
            result.setCallbackResponse("SUCCESS");
            return result;
        } catch (Exception exception) {
            log.error("{} fail to action with callback [{}]", LogUtil.getClassMethodName(), json, exception);
            return TradeResult.fail(TradeType.PAYMENT, PayChannelsConstants.EASY_PAY, exception.getMessage());
        }
    }

    @Override
    public TradeRequest refundRequest(RefundParams refundParams) {
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        EasyPayToken easyPayToken = easyPayTokenTokenProvider.findToken(refundParams.getSellerNo());
        RefundRequest refundRequest = new RefundRequest();
        refundRequest.setMerchantId(easyPayToken.getMerchantId());
        refundRequest.setOriginTradeNo(refundParams.getTradeNo());
        refundRequest.setOutTradeNo(refundParams.getRefundNo());
        if (!environmentConfig.isOnline()) {
            refundRequest.setIgnoreDomainCheck(true);
        }
        try {
            RefundRequest.Response.Payload payload = refundRequest.request(easyPayToken.getGateway(), easyPayToken.getPartnerId(), easyPayToken.getSecretKey()).take().getPayload();
            if (payload.success()) {
                return TradeRequest.ok(null);
            }
            return TradeRequest.fail(payload.getMsg());
        } catch (Exception e) {
            log.error("{} fail to refundRequest for EasyPay [{}]", LogUtil.getClassMethodName(), refundRequest, e);
            return TradeRequest.fail(e.getMessage());
        }
    }

    @Override
    public TradeResult refundCallback(HttpServletRequest httpServletRequest) {
        throw new IllegalStateException("no need");
    }
}
