package moonstone.web.core.component.pay.allinpay.user;

import com.alibaba.fastjson.JSONObject;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.constants.Channels;
import io.terminus.pay.model.PaymentParams;
import io.terminus.pay.model.Token;
import io.terminus.pay.model.TradeRequest;
import io.terminus.pay.service.PayChannel;
import io.terminus.pay.wechatpay.app.AppWechatpayChannel;
import io.terminus.pay.wechatpay.common.model.token.JsapiWxToken;
import io.terminus.pay.wechatpay.jsapi.component.JsapiWechatpayTokenProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.utils.UserOpenIdUtil;
import moonstone.web.core.component.pay.allinpay.user.req.FamilyRegisterByPayRequest;
import moonstone.web.op.allinpay.util.NacosAutoConfiguration;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class ParanaRegisterByPayHandler {

    @Autowired
    private ChannelRegistry channelRegistry;

    @Resource
    private NacosAutoConfiguration nacosAutoConfiguration;

    @Value("${order.auto.cancel.in.minutes}")
    private Integer expireMinutes;

    @Autowired
    private JsapiWechatpayTokenProvider jsapiWechatpayTokenProvider;

//    @Value("${pay.notifyUrlByRegisterPay}")
//    private String notifyUrlByRegisterPay;

    /**
     * 仅提供一个发起微信支付的工具型方式
     * @param payParams
     * @return
     */
    public Result<TradeRequest> registerByPay(FamilyRegisterByPayRequest payParams) {
        log.info("付费注册入参 {}", JSONObject.toJSONString(payParams));

        String subject = payParams.getSubject();
        Long shopId = payParams.getShopId();
        String channel = payParams.getChannel();
        String tradeNo = payParams.getTradeNo();
        String systemNo = payParams.getSystemNo();
        Long fee = payParams.getFee();
//        String notifyUrl = notifyUrlByRegisterPay;
        String openId = payParams.getOpenId();

        // 固定仅支持微信APP支付
        PayChannel paymentChannel = channelRegistry.findChannel(channel);

        String accountNo = String.valueOf(shopId);
//        String newAccountNo = accountNo + "_pay_register";

//        JsapiWxToken token = jsapiWechatpayTokenProvider.findToken(accountNo);
//        if(token != null){
//            token.setNotifyUrl(notifyUrl);
//            // 另存一份：每次请求都覆盖（商家会变更支付信息 需要同步）
//            jsapiWechatpayTokenProvider.register(newAccountNo, token);
//        }

        PaymentParams params = new PaymentParams();
        params.setChannel(channel);
        params.setSubject(subject);
        params.setContent(subject);
        params.setExpiredAt(new DateTime(System.currentTimeMillis()).plusMinutes(expireMinutes).toDate());
        params.setFee(fee);
        params.setTradeNo(tradeNo);
        params.setSystemNo(systemNo);
        // 会根据该字段取token
        params.setSellerNo(accountNo);
        params.setOpenId(openId);
        params.setAppId(payParams.getAppId());

        TradeRequest result = paymentChannel.paymentRequest(params);

        return Result.data(result);
    }
}
