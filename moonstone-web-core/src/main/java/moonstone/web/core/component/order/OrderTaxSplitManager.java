package moonstone.web.core.component.order;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.component.item.component.TaxChecker;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuComboRelation;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.order.dto.PushOrderItem;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.SkuOrderWriteService;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Slf4j
@Component
public record OrderTaxSplitManager(SkuCustomReadService skuCustomReadService
        , SkuOrderWriteService skuOrderWriteService
        , TaxChecker taxChecker) {
    /**
     * 获取拆税后的数据 如果拆税数据不存在则进行拆税
     * 会更新数据
     *
     * @param skuOrder  单品订单数据
     * @param skuCustom 单品海关信息
     * @return 拆税后的数据
     */
    public Optional<TaxChecker.TaxSplitResult> getRealTax_M(SkuOrder skuOrder, SkuCustom skuCustom) {
        // 1. 拒绝Null错误
        if (skuCustom == null || skuOrder == null) {
            log.error("{} skuCustom:{} skuOrder:{}", LogUtil.getClassMethodName("fail-get-Tax"), skuOrder, skuCustom);
            return Optional.empty();
        }
        Map<String, String> skuOrderExtra = Optional.ofNullable(skuOrder.getExtra()).orElseGet(HashMap::new);
        // 2. 拒绝非保税商品与非商家承担的商品继续
        if (!BondedType.fromInt(skuOrder.getIsBonded()).isBonded() && skuCustom.getCustomTaxHolder() == 2) {
            return Optional.of(new TaxChecker.TaxSplitResult(new BigDecimal(skuOrder.getTax()), new BigDecimal(skuOrder.getOriginFee())));
        }
        // 3. 如果已经拆过了就使用之前的数据
        if (skuOrderExtra.get("realFee") != null) {
            return Optional.of(new TaxChecker.TaxSplitResult(new BigDecimal(skuOrderExtra.get("realTax")).setScale(6, RoundingMode.HALF_UP), new BigDecimal(skuOrderExtra.get("realFee")).setScale(3, RoundingMode.HALF_DOWN)));
        }
        // 4. 如果不存在税来源 pushSystem则拒绝拆税
        if (ObjectUtils.isEmpty(skuOrder.getTags().get("pushSystem"))) {
            log.error("{} skuOrder:{} orderId:{} wrong bound and pushSystem", LogUtil.getClassMethodName(), skuOrder, skuOrder.getOrderId());
            return Optional.empty();
        }
        //ThirdPartySystem pushSystem = ThirdPartySystem.fromInt(Integer.parseInt(skuOrder.getTags().get("pushSystem")));
        // 5. 获取每个sku映射洋800的数量
        int unitQuantity = 1;
        try {
            unitQuantity = Integer.parseInt(skuOrder.getExtra().get("unitQuantity"));
        } catch (Exception ex) {
            log.warn("{} skuOrderId:{} skuId:{} orderId:{} sku:Extra:{} unitQuantity:{}", LogUtil.getClassMethodName(), skuOrder.getId(), skuOrder.getSkuId(), skuOrder.getOrderId(), skuOrder.getExtra(), skuOrder.getExtra().get("unitQuantity"), ex);
        }
        log.info("{} skuOrderId:{} orderId:{} unitQuantity:{}", LogUtil.getClassMethodName(), skuOrder.getId(), skuOrder.getOrderId(), unitQuantity);
        // 6. 调用外部接口获取拆税 [必须使用真实的洋800 Sku的价格]
        long skuOriginFee = skuOrder.getOriginFee() / skuOrder.getQuantity();
        Optional<TaxChecker.TaxSplitResult> taxSplitResult = taxChecker.splitTax(skuOrder.getShopId(), skuOrder.getSkuId(), skuOrder.getOuterSkuId()
                ///  除与商品数量 获得每件本地Sku的价格
                , new BigDecimal(skuOriginFee)
                        /// 除与每Sku映射数量,获取洋800的真实单品价格
                        .divide(new BigDecimal(unitQuantity * 100), 6, RoundingMode.HALF_UP));
        // 7. 处理成功获取拆税数值后的数据 储存与平价
        if (taxSplitResult.isPresent()) {
            BigDecimal realFee = taxSplitResult.get().getFee().setScale(6, RoundingMode.DOWN);
            BigDecimal realTax = new BigDecimal(skuOriginFee).divide(new BigDecimal(unitQuantity * 100), 6, RoundingMode.DOWN).subtract(realFee);
            /// 更新进入SkuOrder
            Map<String, String> extra = skuOrder.getExtra() == null ? new HashMap<>() : skuOrder.getExtra();
            extra.put("realFee", realFee.setScale(3, RoundingMode.HALF_UP).toString());
            extra.put("realTax", realTax.toString());
            /// 真实更新
            SkuOrder update = new SkuOrder();
            update.setId(skuOrder.getId());
            update.setExtra(extra);
            skuOrderWriteService.update(update);

            return Optional.of(new TaxChecker.TaxSplitResult(realTax, realFee));
        }
        return taxSplitResult;
    }

    /**
     * 根据skuOrder生成推送用的商品信息
     * WARNING: WRITE ON {@link OrderTaxSplitManager#getRealTax_M(SkuOrder, SkuCustom)}
     *
     * @param skuOrder          原推送订单
     * @param shareDiscount     优惠
     * @param allShipFee        运费
     * @param realShopOriginFee 原始价格
     * @param outFrom
     * @return 包装完毕的数据
     */
    public Either<PushOrderItem> makePushOrderItem_M(SkuOrder skuOrder, BigDecimal shareDiscount, Long allShipFee, Long realShopOriginFee, String outFrom) {
        Map<String, String> skuOrderExtra = Optional.ofNullable(skuOrder.getExtra()).orElseGet(HashMap::new);

        // 设置推送的订单商品数据
        PushOrderItem pushOrderItem = new PushOrderItem();

        // 0. 设置商品信息
        pushOrderItem.setSkuCode(skuOrder.getOuterSkuId());
        pushOrderItem.setOutCode(skuOrder.getSkuId().toString());

        // 1. 获取本地Sku单品的映射数量
        int unitQuantity = 1;
        if (!ObjectUtils.isEmpty(skuOrder.getExtra().get("unitQuantity"))) {
            unitQuantity = Integer.parseInt(skuOrder.getExtra().get("unitQuantity"));
        }

        // 2. 设置其真实数量为SkuOrder.quantity * unitQuantity [订单内数量x影射数量=订单内真实单品的数量]
        pushOrderItem.setNum(skuOrder.getQuantity() * unitQuantity);

        // 3. 设置折扣
        BigDecimal discount = skuOrder.getDiscount() == null ? BigDecimal.ZERO : BigDecimal.valueOf(skuOrder.getDiscount());
        if (Objects.equals(outFrom, OrderOutFrom.COMMUNITY_OPERATION.getOrderOutFormCode())) {
            pushOrderItem.setDiscount(discount.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
        } else {
            pushOrderItem.setDiscount(discount.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP).add(shareDiscount));
        }

        // 4. 设置运费
        long shipFee;
        if (realShopOriginFee == 0) {
            shipFee = allShipFee;
        } else {
            shipFee = new BigDecimal(allShipFee)
                    .multiply(new BigDecimal(skuOrder.getOriginFee()))
                    .divide(new BigDecimal(realShopOriginFee), RoundingMode.DOWN).longValue();
        }

        pushOrderItem.setShipFee(BigDecimal.valueOf(shipFee).divide(BigDecimal.valueOf(100), 4, RoundingMode.DOWN));

        // 4.5 重置真实价格
        skuOrder.setOriginFee(new BigDecimal(skuOrder.getOriginFee()).add(new BigDecimal(skuOrderExtra.getOrDefault("enhancedFee", "0"))).longValue());
        skuOrder.setFee(new BigDecimal(skuOrder.getFee()).add(new BigDecimal(skuOrderExtra.getOrDefault("enhancedFee", "0"))).longValue());

        // 5. 判断是否保税商品,如果并不是保税商品则直接设置价格
        if (BondedType.fromInt(skuOrder.getIsBonded()).isBonded()) {
            SkuCustom custom = skuCustomReadService.getBySkuId(skuOrder.getSkuId());
            if (custom == null) {
                log.error("{} shopOrderId:{} skuOrderId:{} skuId:{} isBound:{}", LogUtil.getClassMethodName(), skuOrder.getOrderId(), skuOrder.getId(), skuOrder.getSkuId(), skuOrder.getIsBonded());
                return Either.error("fail-find-skuCustom");
            }
            // 仅仅商家承担的时候进行拆税,其他情况使用订单内的税值
            if (custom.getCustomTaxHolder() == 2 && (skuOrder.getTax() == null || skuOrder.getTax() == 0)) {
                /// 获取拆税的数据
                try {
                    Optional<TaxChecker.TaxSplitResult> taxOptional = getRealTax_M(skuOrder, custom);
                    if (taxOptional.isEmpty()) {
                        log.error("{} orderId:{} skuOrderId:{} split tax failed", LogUtil.getClassMethodName(), skuOrder.getOrderId(), skuOrder.getId());
                        return Either.error("tax-split-failed");
                    }
                    BigDecimal realFee = taxOptional.get().getFee();
                    BigDecimal realTax = taxOptional.get().getTax();
                    /// 设置真实单品的价格 [已经处理过的,realPrice=SplitPrice(skuPrice/skuOrderQuantity/unitQuantity)]
                    pushOrderItem.setPrice(new BigDecimal(realFee.toString()).setScale(3, RoundingMode.DOWN));
                    /// WARNING!!!
                    /// tax是所有商品的税 不需要拆到最小单元
                    pushOrderItem.setTax(new BigDecimal(realTax.multiply(new BigDecimal(skuOrder.getQuantity() * unitQuantity)).toString()).setScale(3, RoundingMode.DOWN));
                    /// 平价
                    balanceThePrice(pushOrderItem, skuOrder, unitQuantity);
                    return Either.ok(pushOrderItem);
                } catch (Exception e) {
                    return Either.error(e);
                }
            }
        }

        pushOrderItem.setPrice(BigDecimal.valueOf(skuOrder.getOriginFee())
                .divide(BigDecimal.valueOf((long) skuOrder.getQuantity() * unitQuantity), 3, RoundingMode.DOWN)
                .divide(BigDecimal.valueOf(100), 3, RoundingMode.DOWN));

        /// WARNING!!!
        /// tax是所有商品的税 不需要拆到最小单元
        pushOrderItem.setTax(BigDecimal.valueOf(skuOrder.getTax())
                .divide(BigDecimal.valueOf(100), 3, RoundingMode.DOWN));
        balanceThePrice(pushOrderItem, skuOrder, unitQuantity);
        if (Optional.ofNullable(skuOrder.getShipFee()).filter(fee -> fee > 0).isPresent()) {
            BigDecimal taxWithShipFeeTax = pushOrderItem.getTax().divide(new BigDecimal(skuOrder.getOriginFee().toString()), 4, RoundingMode.DOWN).multiply(new BigDecimal(skuOrder.getShipFee().toString()));
            log.debug("{} tax of order[{}](skuOrder[{}]) is {} shipFee[{}] shipFeeTax[{}]", LogUtil.getClassMethodName(), skuOrder.getOrderId(), skuOrder.getId(), pushOrderItem.getTax(), skuOrder.getShipFee(), taxWithShipFeeTax);
            pushOrderItem.setTax(taxWithShipFeeTax);
        }
        return Either.ok(pushOrderItem);
    }

    /**
     * 构建组合商品推送用的商品信息
     *
     * @param skuOrder         原推送订单
     * @param outFrom          外部订单来源
     * @param sku              子商品信息
     * @param skuComboRelation 该子商品的关联关系
     * @param discount         折扣
     * @param shipFee          运费
     * @param tax              税费
     * @param price            实际售价
     * @return PushOrderItem  推送用的商品信息
     */
    public PushOrderItem makePushOrderComboItem_M(SkuOrder skuOrder, String outFrom, Sku sku,
                                                  SkuComboRelation skuComboRelation, BigDecimal discount,BigDecimal shipFee,
                                                  Long tax,BigDecimal price) {

        // 分摊比例
        Map<String, String> skuOrderExtra = Optional.ofNullable(skuOrder.getExtra()).orElseGet(HashMap::new);

        // 设置推送的订单商品数据
        PushOrderItem pushOrderItem = new PushOrderItem();

        // 0. 设置商品信息
        pushOrderItem.setSkuCode(sku.getOuterSkuId());
        pushOrderItem.setOutCode(sku.getId().toString());


        // 2. 设置其真实数量
        pushOrderItem.setNum(skuComboRelation.getComboSkuQuantity());

        // 3. 设置折扣
        pushOrderItem.setDiscount(discount);

        // 4. 设置运费
        pushOrderItem.setShipFee(shipFee);


        // 设置税费
        pushOrderItem.setTax(BigDecimal.valueOf(tax).divide(BigDecimal.valueOf(100), 4, RoundingMode.DOWN));

        // 设置价格
        pushOrderItem.setPrice(price);
//        // 5. 判断是否保税商品,如果并不是保税商品则直接设置价格
//        if (BondedType.fromInt(skuOrder.getIsBonded()).isBonded()) {
//            SkuCustom custom = skuCustomReadService.getBySkuId(skuOrder.getSkuId());
//            if (custom == null) {
//                log.error("{} shopOrderId:{} skuOrderId:{} skuId:{} isBound:{}", LogUtil.getClassMethodName(), skuOrder.getOrderId(), skuOrder.getId(), skuOrder.getSkuId(), skuOrder.getIsBonded());
//                return null;
//            }
//            // 仅仅商家承担的时候进行拆税,其他情况使用订单内的税值
//            // n2：商家承担税费 进行拆睡  目前是按照原价税费来缴
//            if (custom.getCustomTaxHolder() == 2 && (skuOrder.getTax() == null || skuOrder.getTax() == 0)) {
//                pushOrderItem.setTax(BigDecimalskuOrder.getTax());
//
//
//            }
//        }

        return pushOrderItem;


    }

    /**
     * 平价 因为精度导致的小数点丢失情况由该函数平衡
     *
     * @param pushOrderItem 推送物品
     * @param skuOrder      订单来源
     * @param unitQuantity  相对数量
     */
    private void balanceThePrice(PushOrderItem pushOrderItem, SkuOrder skuOrder, int unitQuantity) {
        Map<String, String> skuOrderExtra = Optional.ofNullable(skuOrder.getExtra()).orElseGet(HashMap::new);
        if (skuOrderExtra.getOrDefault("freeGift", "false").equals("true")) {
            log.debug("{} skuOrder[{}] not balance pushOrderItem[{}]", LogUtil.getClassMethodName(), skuOrder.getId(), pushOrderItem.toString());
            return;
        }
        BigDecimal diff = new BigDecimal(skuOrder.getFee()).divide(new BigDecimal("100"), 6, RoundingMode.HALF_UP).subtract(pushOrderItem.getPrice().multiply(new BigDecimal(skuOrder.getQuantity() * unitQuantity)).add(pushOrderItem.getTax()));
        log.debug("{} orderId:{} push-price:{} push-tax:{} diff:{} order:{}", LogUtil.getClassMethodName(), skuOrder.getOrderId(), pushOrderItem.getPrice(), pushOrderItem.getTax(), diff, skuOrder);
        if (Objects.equals(diff.abs().compareTo(new BigDecimal("0.02")), -1)) {
            switch (diff.compareTo(new BigDecimal("0"))) {
                case 1: {
                    pushOrderItem.setTax(pushOrderItem.getTax().add(diff));
                }
                case -1: {
                    // 0.01来自精度限制0.01，精度导致的0.01浮动
                    pushOrderItem.setPrice(pushOrderItem.getPrice().subtract(new BigDecimal("0.001")));
                    pushOrderItem.setTax(pushOrderItem.getTax().add(new BigDecimal("0.001").multiply(new BigDecimal(skuOrder.getQuantity() * unitQuantity))).subtract(diff));
                }
                default: {
                    // 正常的值
                }
            }
        } else {
            log.debug("{} order not need balance because can't or else", LogUtil.getClassMethodName());
        }
    }


}
