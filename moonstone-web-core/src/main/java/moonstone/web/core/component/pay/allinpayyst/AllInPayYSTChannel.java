package moonstone.web.core.component.pay.allinpayyst;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.allinpay.sdk.bean.BizParameter;
import com.allinpay.sdk.bean.OpenResponse;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.constants.RequestParams;
import io.terminus.pay.enums.TradeType;
import io.terminus.pay.exception.PayException;
import io.terminus.pay.model.*;
import io.terminus.pay.service.PayChannel;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.UserUtil;
import moonstone.order.api.payoperation.agentpay.AgentPayCallbackResult;
import moonstone.order.api.payoperation.agentpay.AgentPayOperation;
import moonstone.order.api.payoperation.agentpay.AgentPayRequestResult;
import moonstone.order.api.payoperation.fundstransfer.FundsTransferCallbackResult;
import moonstone.order.api.payoperation.fundstransfer.FundsTransferOperation;
import moonstone.order.api.payoperation.fundstransfer.FundsTransferRequestResult;
import moonstone.order.api.payoperation.paymentrequest.PaymentRequestOperation;
import moonstone.order.api.payoperation.withdraw.WithdrawCallbackResult;
import moonstone.order.api.payoperation.withdraw.WithdrawOperation;
import moonstone.order.api.payoperation.withdraw.WithdrawRequestResult;
import moonstone.order.enu.*;
import moonstone.order.model.*;
import moonstone.order.model.mongo.AgentPayOrderRecord;
import moonstone.order.service.*;
import moonstone.web.core.component.pay.allinpayyst.bo.AgentPayOrderBO;
import moonstone.web.core.component.pay.allinpayyst.convert.AllInPayYSTConvertor;
import moonstone.web.core.component.pay.allinpayyst.listener.event.TransferRefundFundEvent;
import moonstone.web.core.component.pay.allinpayyst.util.MethodName;
import moonstone.web.core.component.pay.allinpayyst.util.OpenClientProvider;
import moonstone.web.core.events.trade.app.RefundProfitReturnApp;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import moonstone.web.op.allinpay.util.AllinPayUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static moonstone.web.core.component.pay.allinpayyst.convert.AllInPayYSTConvertor.BUSINESS_OK;
import static moonstone.web.core.component.pay.allinpayyst.convert.AllInPayYSTConvertor.GATEWAY_OK;

/**
 * 通联支付 - 云商通
 * https://cloud.allinpay.com/ts-cloud-dev-web/#/apiCenter/index?params=y&key=247
 */
@Slf4j
@Component
public class AllInPayYSTChannel implements PayChannel, AgentPayOperation, FundsTransferOperation, WithdrawOperation,
        PaymentRequestOperation {

    @Resource
    private ChannelRegistry channelRegistry;

    @Resource
    private AllInPayYSTTokenProvider allInPayYSTTokenProvider;

    @Resource
    private AllInPayYSTConvertor allInPayYSTConvertor;

    @Resource
    private OpenClientProvider openClientProvider;

    @Resource
    private RefundReadService refundReadService;

    @Resource
    private AgentPayOrderReadService agentPayOrderReadService;

    @Resource
    private AgentPayOrderDetailReadService agentPayOrderDetailReadService;

    @Resource
    private AgentPayPaymentReadService agentPayPaymentReadService;

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private PaymentWriteService paymentWriteService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private OrderRoleSnapshotReadService orderRoleSnapshotReadService;

    @Resource
    private RefundProfitReturnApp refundProfitReturnApp;

    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    @PostConstruct
    public void register() {
        channelRegistry.register(PaymentChannelEnum.ALLINPAY_YST.getCode(), this);
    }

    @SneakyThrows
    @Override
    public void verify(HttpServletRequest request) throws PayException {
        request.setCharacterEncoding("UTF-8");
        var parameterMap = getParameterMap(request);
        log.info("AllInPayYSTChannel.verify, parameterMap={}", JSON.toJSONString(parameterMap));

        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        String account = request.getAttribute(RequestParams.ACCOUNT).toString();
        var payToken = allInPayYSTTokenProvider.findToken(account);
        var openClient = openClientProvider.getOpenClient(account, payToken);

        String sign = parameterMap.get("sign");

        parameterMap.remove("sign");
        parameterMap.remove("signType");
        String bizContent = parameterMap.get("bizContent");
        // 针对特殊符号的处理
        if (StrUtil.isNotBlank(bizContent)) {
            bizContent = URLDecoder.decode(bizContent, StandardCharsets.UTF_8);
        }
        parameterMap.put("bizContent", bizContent);
        if (!openClient.checkSign(AllinPayUtil.createSign(parameterMap), sign)) {
            throw new RuntimeException("签名验证失败");
        }
    }

    @Override
    public TradeRequest paymentRequest(PaymentParams paymentParams) {
        // 查询必要的业务对象
        Payment payment = paymentReadService.findByOutId(paymentParams.getTradeNo()).getResult();
        if (!paymentRequestRepeatable() && paymentRequestSuccess(payment.getPayRequest())) {
            return TradeRequest.ok(allInPayYSTConvertor.convertPayRedirect(payment.getPayRequest()));
        }

        // 获取支付token
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        var payToken = allInPayYSTTokenProvider.findToken(paymentParams.getSellerNo());
        checkToken(payToken);

        // 构造入参
        BizParameter parameter = allInPayYSTConvertor.convertPay(paymentParams, payToken, UserUtil.getUserId());

        // 发起请求
        var response = sendRequest(paymentParams.getSellerNo(), payToken, MethodName.agentCollectApply, parameter);

        // 解析结果
        return allInPayYSTConvertor.convertPayResult(response);
    }

    @Override
    public TradeResult paymentCallback(HttpServletRequest request) {
        var parameterMap = getParameterMap(request);
        log.debug("AllInPayYSTChannel.paymentCallback, parameterMap={}", JSON.toJSONString(parameterMap));

        var bizContent = getBizContent(parameterMap);
        if (!BUSINESS_OK.equals(bizContent.get("status"))) {
            return TradeResult.fail(TradeType.PAYMENT, PaymentChannelEnum.ALLINPAY_YST.getCode(), JSON.toJSONString(parameterMap));
        }

        TradeResult result = new TradeResult();
        result.setChannel(PaymentChannelEnum.ALLINPAY_YST.getCode());
        result.setTradeAt(DateUtil.parseDate(bizContent.getString("payDatetime")));
        result.setMerchantSerialNo(bizContent.getString("bizOrderNo"));
        result.setGatewaySerialNo(bizContent.getString("payInterfaceOutTradeNo"));
        result.setPaySuccess(true);
        result.setCallbackResponse("success");

        // 记录使用的商户号快照
        paymentWriteService.updateAllInPayCusId(result.getMerchantSerialNo(), bizContent.getString("cusid"));
        return result;
    }

    /**
     * 消费者发起退款，<br/>
     * 1、未完成代付时，调用 退款申请；<br/>
     * <p>
     * 2、已完成代付时，调用 非标接口-订单分账退款申请，
     * 当报资金不足时，调用 非标接口-电子账户资金调拨收银宝 来补足资金，再重试非标退款接口（即使 门店小c 账户下没钱可退了，这部分钱的退款让显志大B承受损失）
     * <br/>
     * （PS: 需调拨资金的收银宝商户号，必须通过【会员收银宝渠道商户信息及终端信息绑定】接口与会员绑定。）
     *
     * @param refundParams
     * @return
     */
    @Override
    public TradeRequest refundRequest(RefundParams refundParams) {
        // 获取支付token
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        var token = allInPayYSTTokenProvider.findToken(refundParams.getSellerNo());
        checkToken(token);

        // 是否完成了代付
        var pair = findAgentPayOrders(refundParams.getRefundNo());
        if (agentPayFinished(pair.getShopOrderIds(), pair.getAgentPayOrders()) &&
                personalBalanceSufficient(pair.getAgentPayOrderDetailMap()) &&
                !alreadyInitiateWithdraw(pair.getShopOrderIds())) {
            // 已完成代付 且 小c的通联账户余额充足 且 门店未发起提现，调用非标接口[订单分账退款申请]
            var result = nonstandardRefund(refundParams, pair.getAgentPayOrders(), pair.getAgentPayOrderDetailMap(), token);
            if (insufficientBalance(result)) {
                // 余额不足，触发资金调拨
                EventSender.sendApplicationEvent(new TransferRefundFundEvent(refundParams.getRefundNo()));
            }

            return result;
        } else {
            // 未完成代付时，调用[退款申请]接口;
            return standardRefund(refundParams, token);
        }
    }

    private boolean alreadyInitiateWithdraw(List<Long> shopOrderIds) {
        if (CollectionUtils.isEmpty(shopOrderIds)) {
            return false;
        }

        // key = shop_order_id
        var map = orderRoleSnapshotReadService.findMapByShopOrderIds(shopOrderIds,
                OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER);
        if (CollectionUtils.isEmpty(map)) {
            throw new RuntimeException("订单快照查询为空");
        }

        return map.entrySet().stream().anyMatch(entry -> {
            var shopOrderId = entry.getKey();
            var subStoreSnapshot = entry.getValue().stream()
                    .filter(o -> SubStoreUserIdentityEnum.SUB_STORE.getCode().equals(o.getUserRole()))
                    .findAny()
                    .orElseThrow(() -> new RuntimeException(String.format("shopOrderId=%s没有门店角色快照", shopOrderId)));

            return refundProfitReturnApp.alreadyInitiateWithdraw(subStoreSnapshot.getShopId(), shopOrderId, subStoreSnapshot.getUserId());
        });
    }

    @Override
    public TradeResult refundCallback(HttpServletRequest request) {
        var parameterMap = getParameterMap(request);
        log.debug("AllInPayYSTChannel.refundCallback, parameterMap={}", JSON.toJSONString(parameterMap));

        var bizContent = getBizContent(parameterMap);
        if (!BUSINESS_OK.equals(bizContent.get("status"))) {
            return TradeResult.fail(TradeType.REFUND, PaymentChannelEnum.ALLINPAY_YST.getCode(), JSON.toJSONString(parameterMap));
        }

        return allInPayYSTConvertor.convertRefundCallback(bizContent);
    }

    @Override
    public TradeResult refundStats(RefundQueryParams queryParams) {
        // 获取支付token
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        var token = allInPayYSTTokenProvider.findToken(queryParams.getSellerNo());
        checkToken(token);

        // 构造入参
        BizParameter bizParameter = allInPayYSTConvertor.convertRefundQuery(queryParams);

        // 发起请求
        var response = sendRequest(queryParams.getSellerNo(), token, MethodName.getOrderStatus, bizParameter);

        // 解析结果
        return allInPayYSTConvertor.convertRefundQueryResult(response);
    }

    @Override
    public boolean supportedChannel(String payChannel) {
        return PaymentChannelEnum.ALLINPAY_YST.getCode().equals(payChannel);
    }

    @Override
    public AgentPayRequestResult agentPayRequest(AgentPayPayment payment) {
        // 获取支付token
        var token = allInPayYSTTokenProvider.findToken(payment.getShopId().toString());
        checkToken(token);

        // 构造入参
        BizParameter bizParameter = allInPayYSTConvertor.convertAgentPayRequest(payment, token);

        // 发起请求
        var response = sendRequest(payment.getShopId().toString(), token, MethodName.signalAgentPay, bizParameter);

        // 解析结果
        return allInPayYSTConvertor.convertAgentPayRequestResult(bizParameter, response);
    }

    @Override
    public AgentPayCallbackResult agentPayCallback(AgentPayPayment agentPayPayment, HttpServletRequest request) {
        // 验签
        verify(request);

        var parameterMap = getParameterMap(request);
        String raw = JSON.toJSONString(parameterMap);
        log.debug("AllInPayYSTChannel.agentPayCallback, parameterMap={}", raw);

        var bizContent = getBizContent(parameterMap);
        if (!BUSINESS_OK.equals(bizContent.get("status"))) {
            return new AgentPayCallbackResult(raw, false, null, agentPayPayment.getId());
        }

        return new AgentPayCallbackResult(raw, true, bizContent.getString("orderNo"), agentPayPayment.getId());
    }

    @Override
    public FundsTransferRequestResult fundsTransferRequest(FundsTransferPayment payment) {
        // 获取支付token
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        var token = allInPayYSTTokenProvider.findToken(payment.getShopId().toString());
        checkToken(token);

        // 会员收银宝渠道商户信息及终端信息绑定
        bind(payment.getShopId().toString(), token);

        // 构造入参
        BizParameter bizParameter = allInPayYSTConvertor.convertFundsTransferRequest(payment, token);
        log.info("发送调拨资金请求前 请求参数 {}", JSONUtil.toJsonStr(bizParameter));
        // 发起请求
        var response = sendRequest(payment.getShopId().toString(), token, MethodName.transferTeaRefundFund,
                bizParameter);
        log.info("发送调拨资金请求后 响应结果 {}", JSONUtil.toJsonStr(response));
        // 解析结果
        return allInPayYSTConvertor.convertFundsTransferRequestResult(bizParameter, response);
    }

    @Override
    public FundsTransferCallbackResult fundsTransferCallback(FundsTransferPayment payment, HttpServletRequest request) {
        // 验签
        verify(request);

        var parameterMap = getParameterMap(request);
        String raw = JSON.toJSONString(parameterMap);
        log.debug("AllInPayYSTChannel.fundsTransferCallback, parameterMap={}", raw);

        var bizContent = getBizContent(parameterMap);
        if (!BUSINESS_OK.equals(bizContent.get("status"))) {
            return new FundsTransferCallbackResult(raw, false, null, payment.getId());
        }

        return new FundsTransferCallbackResult(raw, true, bizContent.getString("orderNo"), payment.getId());
    }

    @Override
    public WithdrawRequestResult withdrawRequest(WithdrawPayment payment) {
        // 获取支付token
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        String accountNo = payment.getShopId().toString();
        var token = allInPayYSTTokenProvider.findToken(accountNo);
        checkToken(token);

        // 构造入参
        var openClient = openClientProvider.getOpenClient(accountNo, token);
        BizParameter bizParameter = allInPayYSTConvertor.convertWithdrawRequest(payment, token, openClient);

        // 发起请求
        var response = sendRequest(accountNo, token, MethodName.withdrawApply, bizParameter);

        // 解析结果
        return allInPayYSTConvertor.convertWithdrawRequestResult(bizParameter, response);
    }

    @Override
    public WithdrawCallbackResult withdrawCallback(WithdrawPayment payment, HttpServletRequest request) {
        // 验签
        request.setAttribute(RequestParams.ACCOUNT, payment.getShopId().toString());
        verify(request);

        var parameterMap = getParameterMap(request);
        String raw = JSON.toJSONString(parameterMap);
        log.debug("AllInPayYSTChannel.withdrawCallback, parameterMap={}", raw);

        var bizContent = getBizContent(parameterMap);
        if (!BUSINESS_OK.equals(bizContent.get("status"))) {
            // 尼马失败原因都没有
            var orderStatus = queryOrder(payment.getShopId().toString(), payment.getOrderNo());
            return new WithdrawCallbackResult(raw, false, null, payment.getId(),
                    orderStatus.getString("errorMessage"));
        }

        return new WithdrawCallbackResult(raw, true, bizContent.getString("orderNo"), payment.getId());
    }

    @Override
    public WithdrawRequestResult withdrawRequest(EnterpriseWithdraw withdraw) {
        // 获取支付token
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        String accountNo = withdraw.getShopId().toString();
        var token = allInPayYSTTokenProvider.findToken(accountNo);
        checkToken(token);

        // 构造入参
        var openClient = openClientProvider.getOpenClient(accountNo, token);
        BizParameter bizParameter = allInPayYSTConvertor.convertWithdrawRequest(withdraw, token, openClient);

        // 发起请求
        var response = sendRequest(accountNo, token, MethodName.withdrawApply, bizParameter);

        // 解析结果
        return allInPayYSTConvertor.convertWithdrawRequestResult(bizParameter, response);
    }

    @Override
    public WithdrawCallbackResult withdrawCallback(EnterpriseWithdraw withdraw, HttpServletRequest request) {
        // 验签
        request.setAttribute(RequestParams.ACCOUNT, withdraw.getShopId().toString());
        verify(request);

        var parameterMap = getParameterMap(request);
        String raw = JSON.toJSONString(parameterMap);
        log.debug("AllInPayYSTChannel.EnterpriseWithdrawCallback, withdrawId={}, parameterMap={}", withdraw.getId(), raw);

        var bizContent = getBizContent(parameterMap);
        if (!BUSINESS_OK.equals(bizContent.get("status"))) {
            var orderStatus = queryOrder(withdraw.getShopId().toString(), withdraw.getOrderNo());
            return new WithdrawCallbackResult(raw, false, null, withdraw.getId(),
                    orderStatus.getString("errorMessage"));
        }

        return new WithdrawCallbackResult(raw, true, bizContent.getString("orderNo"), withdraw.getId());
    }

    @Override
    public boolean paymentRequestRepeatable() {
        return false;
    }

    @Override
    public boolean paymentRequestSuccess(String rawPayRequest) {
        if (StringUtils.isBlank(rawPayRequest)) {
            return false;
        }

        var resultMap = JSON.parseObject(rawPayRequest);

        return StringUtils.isNotBlank(resultMap.getString("orderNo")) &&
                (StringUtils.isNotBlank(resultMap.getString("payInfo")) || StringUtils.isNotBlank(resultMap.getString("miniprogramPayInfo_VSP")));
    }

    @Override
    public TradeResult businessPay(BusinessPayParams params) {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult customDeclare(CustomDeclareParams customDeclareParams) throws PayException {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult customDeclareRest(CustomDeclareParams customDeclareParams) throws PayException {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult paymentStats(PaymentQueryParams params) {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult customStats(RefundQueryParams params) {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult paymentClose(PaymentCloseParams params) {
        throw new RuntimeException("not supported");
    }

    public OpenResponse sendRequest(String accountNo, AllInPayYSTToken payToken, String method, BizParameter parameter) {
        var openClient = openClientProvider.getOpenClient(accountNo, payToken);
        var response = openClient.execute(method, parameter);

        log.info("AllInPayYSTChannel.sendRequest, method={}, parameter={}, response={}", method, JSON.toJSONString(parameter),
                JSON.toJSONString(response));
        return response;
    }

    public void checkToken(AllInPayYSTToken payToken) {
        if (payToken == null) {
            throw new RuntimeException("支付配置为空");
        }

        if (StringUtils.isBlank(payToken.getAppId()) || StringUtils.isBlank(payToken.getSecretKey()) ||
                StringUtils.isBlank(payToken.getCertPath()) || StringUtils.isBlank(payToken.getCertPassword()) ||
                StringUtils.isBlank(payToken.getTlCertPath())) {
            throw new RuntimeException("支付配置信息不完整");
        }
    }

    /**
     * 动态遍历获取所有收到的参数
     *
     * @param request
     * @return
     */
    private TreeMap<String, String> getParameterMap(HttpServletRequest request) {
        TreeMap<String, String> map = new TreeMap<>();

        var reqMap = request.getParameterMap();
        for (var key : reqMap.keySet()) {
            String value = reqMap.get(key)[0];
            map.put(key, value);
        }

        return map;
    }

    private JSONObject getBizContent(TreeMap<String, String> parameterMap) {
        String bizContentString = parameterMap.get("bizContent");
        if (StringUtils.isBlank(bizContentString)) {
            throw new RuntimeException("bizContent为空");
        }

        return JSON.parseObject(bizContentString);
    }

    /**
     * 是否余额不足
     *
     * @param result
     * @return
     */
    private boolean insufficientBalance(TradeRequest result) {
        // 是真尼马的儿戏
        return StringUtils.isNotBlank(result.getError()) && result.getError().contains("贷方余额不足");
    }

    /**
     * 退款单关联的订单是否已经完成代付
     * <br/> PS: 一个退款单关联多个主订单，貌似除开发人员有入口外，消费者和商家都没有，先不管这种情况
     *
     * @param agentPayOrders
     * @return
     */
    public boolean agentPayFinished(List<Long> shopOrderIds, List<AgentPayOrder> agentPayOrders) {
        var records = mongoTemplate.find(Query.query(Criteria.where("shopOrderId").in(shopOrderIds)),
                AgentPayOrderRecord.class);
        if (CollectionUtils.isEmpty(records)) {
            return false;
        }

        var map = CollectionUtils.isEmpty(agentPayOrders) ? new HashMap<Long, AgentPayOrder>() :
                agentPayOrders.stream().collect(Collectors.toMap(AgentPayOrder::getRelatedOrderId, o -> o, (k1, k2) -> k1));

        // 存在进行中的代付，直接中断退款，等代付完成后再发起退款
        for (var record : records) {
            var agentPayOrder = map.get(record.getShopOrderId());
            if (agentPayOrder == null || !AgentPayOrderStatusEnum.PAID.getCode().equals(agentPayOrder.getStatus())) {
                throw new RuntimeException("须等待对应订单完成托管代付流程，才能发起退款");
            }
        }
        if (!CollectionUtils.isEmpty(agentPayOrders) &&
                !agentPayOrders.stream().allMatch(entity -> AgentPayOrderStatusEnum.PAID.getCode().equals(entity.getStatus()))) {
            throw new RuntimeException("须等待对应订单完成托管代付流程，才能发起退款");
        }

        return true;
    }

    /**
     * 查询退款单对应的代付订单
     *
     * @param refundOutId
     * @return
     */
    private AgentPayOrderBO findAgentPayOrders(String refundOutId) {
        var refund = refundReadService.findByOutId(refundOutId).getResult();
        if (refund == null) {
            throw new RuntimeException("退款单据不存在");
        }

        var map = refundReadService.findShopOrder(List.of(refund.getId())).getResult();
        if (CollectionUtils.isEmpty(map)) {
            throw new RuntimeException("关联的主订单信息不存在");
        }

        var shopOrderIds = map.values().stream().map(ShopOrder::getId).distinct().toList();
        var response = agentPayOrderReadService.findByRelatedOrderIds(shopOrderIds);
        if (StringUtils.isNotBlank(response.getError())) {
            throw new RuntimeException(String.format("代付订单查询失败[%s]", response.getError()));
        }

        Map<Long, List<AgentPayOrderDetail>> detailMap = CollectionUtils.isEmpty(response.getResult()) ? Collections.emptyMap() :
                agentPayOrderDetailReadService.findMap(response.getResult().stream().map(AgentPayOrder::getId).toList());

        return new AgentPayOrderBO(shopOrderIds, response.getResult(), detailMap);
    }

    /**
     * 是否存在支付中、等待回调的代付订单
     *
     * @param list
     * @return
     */
    private boolean existPaying(List<AgentPayOrder> list) {
        var notPaid = list.stream()
                .filter(entity -> AgentPayOrderStatusEnum.NOT_PAID.getCode().equals(entity.getStatus()))
                .map(AgentPayOrder::getId)
                .toList();
        if (CollectionUtils.isEmpty(notPaid)) {
            return false;
        }

        var payments = agentPayPaymentReadService.findByAgentPayOrderIds(notPaid).getResult();
        if (CollectionUtils.isEmpty(payments)) {
            return false;
        }

        return payments.stream().anyMatch(entity -> AgentPayPaymentStatusEnum.PAYING.getCode().equals(entity.getStatus()));
    }

    /**
     * 未代付时的退款
     *
     * @param refundParams
     * @param token
     * @return
     */
    private TradeRequest standardRefund(RefundParams refundParams, AllInPayYSTToken token) {
        // 构造入参
        BizParameter bizParameter = allInPayYSTConvertor.convertStandardRefund(refundParams, token);

        // 发起请求
        var response = sendRequest(refundParams.getSellerNo(), token, MethodName.refund, bizParameter);

        // 解析结果
        return allInPayYSTConvertor.convertRefundResult(response);
    }

    /**
     * 已代付时的退款
     *
     * @param refundParams
     * @param token
     * @return
     */
    private TradeRequest nonstandardRefund(RefundParams refundParams, List<AgentPayOrder> list,
                                           Map<Long, List<AgentPayOrderDetail>> detailMap, AllInPayYSTToken token) {
        // 构造入参
        BizParameter bizParameter = allInPayYSTConvertor.convertNonstandardRefund(refundParams, list, detailMap, token);

        // 发起请求
        var response = sendRequest(refundParams.getSellerNo(), token, MethodName.orderSplitRefund, bizParameter);

        // 解析结果
        return allInPayYSTConvertor.convertRefundResult(response);
    }

    /**
     * 需调拨资金的收银宝商户号，必须通过【会员收银宝渠道商户信息及终端信息绑定】接口与会员绑定
     *
     * @param token
     */
    private void bind(String shopIdString, AllInPayYSTToken token) {
        boolean bindSuccess = vspTermidService(shopIdString, "query", token);
        if (bindSuccess) {
            return;
        }

        bindSuccess = vspTermidService(shopIdString, "set", token);
        if (!bindSuccess) {
            throw new RuntimeException("会员收银宝渠道商户信息及终端信息绑定失败");
        }
    }

    public boolean bindSet(String shopIdString, String vspTermid) {
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        var token = allInPayYSTTokenProvider.findToken(shopIdString);
        checkToken(token);

        token.setVspTermid(vspTermid);
        return vspTermidService(shopIdString, "set", token);
    }

    private boolean vspTermidService(String shopIdString, String operation, AllInPayYSTToken token) {
        // 入参
        BizParameter bizParameter = new BizParameter();

        if (StringUtils.isNotBlank(token.getVspMerchantid())) {
            bizParameter.put("bizUserId", token.getEnterpriseUserId());
            bizParameter.put("vspMerchantid", token.getVspMerchantid());
        } else {
            bizParameter.put("bizUserId", token.getBizUserId());
        }

        bizParameter.put("operationType", operation);
        bizParameter.put("vspCusid", token.getVspCusid());
        bizParameter.put("appid", token.getVspAppId());

        if (StringUtils.isNotBlank(token.getVspTermid())) {
            bizParameter.put("vspTermid", token.getVspTermid());
        }

        // 发起请求
        var response = sendRequest(shopIdString, token, MethodName.vspTermidService, bizParameter);

        // 解析结果，是否成功绑定
        return allInPayYSTConvertor.convertVspTermidServiceResult(response, token);
    }

    public JSONObject queryOrder(String shopIdString, String orderNo) {
        // 获取支付token
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        var token = allInPayYSTTokenProvider.findToken(shopIdString);
        checkToken(token);

        // 构造入参
        BizParameter bizParameter = new BizParameter();
        bizParameter.put("bizOrderNo", orderNo);

        // 发起请求
        var response = sendRequest(shopIdString, token, MethodName.getOrderStatus, bizParameter);
        if (response == null || StringUtils.isBlank(response.getData())) {
            return new JSONObject();
        }

        return JSON.parseObject(response.getData());
    }

    /**
     * 订单对应的线上提现用户的账户余额是否充足，能从自身账户余额里退款给消费者
     *
     * @param detailMap key = AgentPayOrder.id
     * @return
     */
    private boolean personalBalanceSufficient(Map<Long, List<AgentPayOrderDetail>> detailMap) {
        if (CollectionUtils.isEmpty(detailMap)) {
            return true;
        }

        var map = detailMap.values().stream()
                .flatMap(Collection::stream)
                .filter(entity -> AgentPayOrderReceiverTypeEnum.SUB_STORE.getCode().equals(entity.getReceiverType()))
                .collect(Collectors.groupingBy(AgentPayOrderDetail::getReceiverId));
        for (var receiverBizUserId : map.keySet()) {
            var list = map.get(receiverBizUserId);

            Long shopId = list.get(0).getShopId();
            long totalAmount = list.stream().mapToLong(AgentPayOrderDetail::getAmount).sum();

            if (totalAmount > queryBalance(shopId.toString(), receiverBizUserId)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 查询用户在通联里的余额
     *
     * @param shopIdString
     * @param bizUserId
     * @return
     */
    public Long queryBalance(String shopIdString, String bizUserId) {
        // 获取支付token
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        var payToken = allInPayYSTTokenProvider.findToken(shopIdString);
        checkToken(payToken);

        // 构造入参
        BizParameter parameter = new BizParameter();
        parameter.put("bizUserId", bizUserId);
        parameter.put("accountSetNo", payToken.getAccountSetNo());

        // 发起请求
        var response = sendRequest(shopIdString, payToken, MethodName.queryBalance, parameter);
        if (!GATEWAY_OK.equals(response.getCode())) {
            throw new RuntimeException(String.format("[%s]%s", response.getCode(), response.getMsg()));
        }
        if (!BUSINESS_OK.equals(response.getSubCode())) {
            throw new RuntimeException((String.format("[%s]%s", response.getSubCode(), response.getSubMsg())));
        }

        var bizContent = JSON.parseObject(response.getData());
        return bizContent.getLong("allAmount") - bizContent.getLong("freezenAmount");
    }
}
