package moonstone.web.core.component.pay.allinpay;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.constants.RequestParams;
import io.terminus.pay.enums.TradeType;
import io.terminus.pay.exception.PayException;
import io.terminus.pay.model.*;
import io.terminus.pay.service.PayChannel;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.UserUtil;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.model.Payment;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.PaymentWriteService;
import moonstone.shop.enums.ShopPayInfoExtraIndexEnum;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.ShopPayInfoReadService;
import moonstone.web.core.component.pay.allinpay.constants.TrxStatusCode;
import moonstone.web.core.component.pay.allinpay.convert.AllInPayConvertor;
import moonstone.web.core.component.pay.allinpay.util.HttpConnectionUtil;
import moonstone.web.core.component.pay.allinpay.util.SybUtil;
import moonstone.web.core.component.pay.allinpayyst.bo.RedirectBO;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * 通联支付 - 普通支付
 * https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=86
 */
@Slf4j
@Component
public class AllInPayChannel implements PayChannel{

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private PaymentWriteService paymentWriteService;

    @Resource
    private AllInPayTokenProvider allInPayTokenProvider;

    @Resource
    private ChannelRegistry channelRegistry;

    @Resource
    private AllInPayConvertor allInPayConvertor;

    @Resource
    private ShopPayInfoReadService shopPayInfoReadService;

    @Value("${pay.allinpay.pay}")
    private String payUrl;

    @Value("${pay.allinpay.refund}")
    private String refundUrl;

    @Value("${pay.allinpay.query}")
    private String queryUrl;

    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    @PostConstruct
    public void register() {
        channelRegistry.register(PaymentChannelEnum.ALLINPAY.getCode(), this);
    }

    @SneakyThrows
    @Override
    public void verify(HttpServletRequest request) throws PayException {
        request.setCharacterEncoding("UTF-8");
        TreeMap<String, String> parameterMap = getParameterMap(request);
        log.info("AllInPayChannel.verify, parameterMap={}", JSON.toJSONString(parameterMap));

        String account = request.getAttribute(RequestParams.ACCOUNT).toString();

        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        AllInPayToken payToken = allInPayTokenProvider.findToken(account);

        boolean isSign = SybUtil.validSign(parameterMap, payToken.getPublicKey(), parameterMap.get("signtype"));
        if (!isSign) {
            throw new RuntimeException("验证签名失败");
        }
    }

    @Override
    public TradeRequest paymentRequest(PaymentParams paymentParams) {
        // 查询必要的业务对象
        Payment payment = paymentReadService.findByOutId(paymentParams.getTradeNo()).getResult();
        // 获取支付token

        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        AllInPayToken payToken = allInPayTokenProvider.findToken(paymentParams.getSellerNo());
        checkToken(payToken);

        // 查询一下对应的支付配置
        String channel = payment.getChannel();
        String payAccountNo = payment.getPayAccountNo();
        ShopPayInfo shopPayInfo = shopPayInfoReadService.findByShopIdAndPayChannel(Long.valueOf(payAccountNo), channel).getResult();
        log.info("获取到该shopId {} 支付渠道为 {} 的支付配置 {}", payAccountNo, channel, JSONUtil.toJsonStr(shopPayInfo));
        // 跳转开关
        String vspRedirectSwitch = shopPayInfo.getExtra().get(ShopPayInfoExtraIndexEnum.VSP_REDIRECT.getCode());
        log.info("跳转开关的值 {}", vspRedirectSwitch);
        if (StrUtil.isNotBlank(vspRedirectSwitch)) {
            if (vspRedirectSwitch.equals("on")) {
                TreeMap<String, String> redirectInfo = allInPayConvertor.convertPaySwitchOn(paymentParams, payToken ,payment);
                return TradeRequest.ok(new RedirectBO(
                        channel,
                        true,
                        null,
                        redirectInfo,
                        JSONUtil.toJsonStr(redirectInfo),
                        false,
                        true
                ));
            }
        }

        // 构造入参
        var parameters = allInPayConvertor.convertPay(paymentParams, payToken, UserUtil.getUserId(),
                payment);


        // 发起请求
        String resultString = doPost(payUrl, parameters);

        // 解析结果
        return allInPayConvertor.convertPayResult(resultString, payToken);
    }



    @Override
    public TradeResult paymentCallback(HttpServletRequest request) {
        TreeMap<String, String> parameterMap = getParameterMap(request);
        if (!TrxStatusCode.SUCCESS.equals(parameterMap.get("trxstatus"))) {
            return TradeResult.fail(TradeType.PAYMENT, PaymentChannelEnum.ALLINPAY.getCode(), JSON.toJSONString(parameterMap));
        }

        TradeResult result = new TradeResult();
        result.setChannel(PaymentChannelEnum.ALLINPAY.getCode());
        result.setTradeAt(getPayTime(parameterMap.get("paytime")));
        result.setMerchantSerialNo(parameterMap.get("cusorderid"));
        result.setGatewaySerialNo(parameterMap.get("trxid"));
        result.setPaySuccess(true);
        result.setCallbackResponse("success");

        // 记录使用的商户号快照
        paymentWriteService.updateAllInPayCusId(result.getMerchantSerialNo(), parameterMap.get("cusid"));
        return result;
    }

    @Override
    public TradeRequest refundRequest(RefundParams refundParams) {
        // 获取支付token
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        AllInPayToken token = allInPayTokenProvider.findToken(refundParams.getSellerNo());
        checkToken(token);

        // 构造入参
        var parameters = allInPayConvertor.convertRefund(refundParams, token);

        // 发起请求
        String resultString = doPost(refundUrl, parameters);

        // 解析结果
        return allInPayConvertor.convertRefundResult(resultString, token);
    }

    @Override
    public TradeResult refundStats(RefundQueryParams queryParams) {
        // 获取支付token
        AllInPayToken token = allInPayTokenProvider.findToken(queryParams.getSellerNo());
        checkToken(token);

        // 构造入参
        var parameters = allInPayConvertor.convertQuery(queryParams, token);

        // 发起请求
        String resultString = doPost(queryUrl, parameters);

        // 解析结果
        return allInPayConvertor.convertRefundQueryResult(resultString, token);
    }

    @Override
    public TradeResult businessPay(BusinessPayParams params) {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult customDeclare(CustomDeclareParams customDeclareParams) throws PayException {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult customDeclareRest(CustomDeclareParams customDeclareParams) throws PayException {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult paymentStats(PaymentQueryParams params) {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult customStats(RefundQueryParams params) {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult paymentClose(PaymentCloseParams params) {
        throw new RuntimeException("not supported");
    }

    @Override
    public TradeResult refundCallback(HttpServletRequest request) {
        // 通联没有退款结果回调
        throw new RuntimeException("not supported");
    }

    private void checkToken(AllInPayToken payToken) {
        if (payToken == null) {
            throw new RuntimeException("支付配置为空");
        }

        if (StringUtils.isBlank(payToken.getCusid()) || StringUtils.isBlank(payToken.getAppid()) ||
                StringUtils.isBlank(payToken.getPublicKey()) || StringUtils.isBlank(payToken.getPrivateKey()) ||
                StringUtils.isBlank(payToken.getSignType())) {
            throw new RuntimeException("支付配置信息不完整");
        }
    }

    /**
     * 动态遍历获取所有收到的参数,此步非常关键,因为收银宝以后可能会加字段,动态获取可以兼容由于收银宝加字段而引起的签名异常
     *
     * @param request
     * @return
     */
    private TreeMap<String, String> getParameterMap(HttpServletRequest request) {
        TreeMap<String, String> map = new TreeMap<>();

        var reqMap = request.getParameterMap();
        for (var key : reqMap.keySet()) {
            String value = reqMap.get(key)[0];
            map.put(key, value);
        }

        return map;
    }

    private Date getPayTime(String payTime) {
        if (StringUtils.isBlank(payTime)) {
            return new Date();
        }

        try {
            return DateUtil.parseDate(payTime);
        } catch (Exception ex) {
            log.error("AllInPayChannel.getPayTime error, payTime={}", payTime, ex);
            return new Date();
        }
    }

    @SneakyThrows
    private String doPost(String url, Map<String, String> parameters) {
        HttpConnectionUtil http = new HttpConnectionUtil(url);
        http.init();
        byte[] bytes = http.postParams(parameters, true);

        String resultString = new String(bytes, StandardCharsets.UTF_8);
        log.info("AllInPayChannel.doPost, url={}, parameters={}, resultString={}", url, JSON.toJSONString(parameters), resultString);

        return resultString;
    }
}
