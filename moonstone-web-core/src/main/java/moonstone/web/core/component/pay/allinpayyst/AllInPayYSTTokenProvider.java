package moonstone.web.core.component.pay.allinpayyst;

import com.alibaba.fastjson.JSON;
import io.terminus.pay.api.TokenProvider;
import io.terminus.pay.exception.PayException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class AllInPayYSTTokenProvider implements TokenProvider<AllInPayYSTToken> {

    private final Map<String, AllInPayYSTToken> tokenMap = new ConcurrentHashMap<>();

    @Override
    public AllInPayYSTToken findToken(String accountNo) throws PayException {
        log.info("AllInPayYSTTokenProvider tokenMap = {}", JSON.toJSONString(tokenMap));
        return tokenMap.get(accountNo);
    }

    @Override
    public List<String> listAllAccounts() {
        return new ArrayList<>(tokenMap.keySet());
    }

    @Override
    public void register(String accountNo, AllInPayYSTToken token) throws PayException {
        tokenMap.put(accountNo, token);
    }
}
