package moonstone.web.core.component.pay.domain;

import io.terminus.pay.constants.Channels;
import io.terminus.pay.constants.Tokens;
import io.terminus.pay.model.Token;
import io.terminus.pay.wechatpay.jsapi.component.JsapiWechatpayTokenProvider;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.OrderBase;
import moonstone.shop.model.Shop;
import moonstone.web.core.component.pay.PayChannelsConstants;
import moonstone.web.core.component.pay.bhecard.app.EasyPayTokenProvider;
import moonstone.web.core.order.cache.OrderStaticPartCache;
import moonstone.web.core.order.dto.PayParams;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class PayChannelConverter {
    private final OrderStaticPartCache orderStaticPartCache;
    private final ShopCacheHolder shopCacheHolder;
    private final JsapiWechatpayTokenProvider jsapiWechatpayTokenProvider;
    private final EasyPayTokenProvider easyPayTokenProvider;

    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    /**
     * 重指向支付渠道
     *
     * @param payParams 支付参数
     */
    public void rewindPayChannel(PayParams payParams) {
        // rewind the alipay-wap into alipay-pc
        if (Objects.equals(payParams.getChannel(), "alipay-wap")) {
            boolean wap2pc = orderStaticPartCache.getOrderStaticPartByOrderId(payParams.getOrderIds().get(0))
                    .map(OrderBase::getShopId)
                    .map(shopCacheHolder::findShopById)
                    .map(Shop::getExtra)
                    .getOrElse(HashMap::new)
                    .getOrDefault("wap2pc", "false")
                    .equals(Boolean.TRUE.toString());
            if (wap2pc) {
                log.debug("{} rewind the pay[{}] into Wap2Pc pay Channel", LogUtil.getClassMethodName(), payParams);
                payParams.setChannel("alipay-pc");
            }
        }
        // rewind the jswechat-pay into easy-pay
        if (Objects.equals(payParams.getChannel(), Channels.Wechatpay.JSAPI)) {
            Long shopId = orderStaticPartCache.getOrderStaticPartByOrderId(payParams.getOrderIds().get(0)).map(OrderBase::getShopId).get();

            //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
            Token originToken = jsapiWechatpayTokenProvider.findToken(shopId.toString());
            Token easyPayToken = easyPayTokenProvider.findToken(shopId.toString());
            if (!easyPayToken.equals(EasyPayTokenProvider.DEFAULT_TOKEN) && originToken.equals(jsapiWechatpayTokenProvider.findToken(Tokens.DEFAULT_ACCOUNT))) {
                log.debug("{} rewind pay[{}] into Easy-Pay Channel", LogUtil.getClassMethodName(), payParams);
                payParams.setChannel(PayChannelsConstants.EASY_PAY);
            }
        }
    }
}
