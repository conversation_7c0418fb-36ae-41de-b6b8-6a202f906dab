package moonstone.web.core.component.pay.allinpay.user.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FamilyRegisterByPayRequest implements Serializable {

    @ApiModelProperty(value = "商家")
    private Long shopId;

    @ApiModelProperty(value = "支付渠道")
    private String channel;

    @ApiModelProperty(value = "支付人")
    private Long userId;

    @ApiModelProperty(value = "商品标题")
    private String subject;

    @ApiModelProperty(value = "交易单号 商户生成的交易流水号")
    private String tradeNo;
    @ApiModelProperty(value = "系统单号 系统内部流水号, 用于前台回调通知时能找到对应的支付")
    private String systemNo;

    @ApiModelProperty(value = "支付金额(分)")
    private Long fee;

    @ApiModelProperty(value = "回调地址")
    private String notifyUrl;

    private String openId;

    private String appId;
}
