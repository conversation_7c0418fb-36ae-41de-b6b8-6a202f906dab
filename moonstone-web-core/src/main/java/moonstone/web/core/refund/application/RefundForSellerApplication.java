package moonstone.web.core.refund.application;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.model.BaseUser;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.api.TokenProvider;
import io.terminus.pay.model.TradeRequest;
import io.terminus.pay.service.RefundOperation;
import io.terminus.pay.wechatpay.app.component.AppWechatpayTokenProvider;
import io.terminus.pay.wechatpay.common.channel.WechatpayChannel;
import io.terminus.pay.wechatpay.common.model.token.AppWxToken;
import io.terminus.pay.wechatpay.common.model.token.JsapiWxToken;
import io.terminus.pay.wechatpay.common.model.token.QrWxToken;
import io.terminus.pay.wechatpay.common.model.token.WxToken;
import io.terminus.pay.wechatpay.jsapi.component.JsapiWechatpayTokenProvider;
import io.terminus.pay.wechatpay.qr.component.QrWechatpayTokenProvider;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.ShopExtra;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UUID;
import moonstone.event.OrderRefundEvent;
import moonstone.order.api.AfterSaleTimeRecordWrapper;
import moonstone.order.api.RefundExpressService;
import moonstone.order.dto.RefundDetail;
import moonstone.order.dto.ReturnInfo;
import moonstone.order.dto.fsm.Flow;
import moonstone.order.dto.fsm.FlowBook;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.model.domain.RefundDomain;
import moonstone.shop.model.Shop;
import moonstone.user.ext.UserTypeBean;
import moonstone.web.core.component.pay.app.Json;
import moonstone.web.core.order.api.RefundParamsMaker;
import moonstone.web.core.refund.event.RefundSuccessCallbackEvent;
import moonstone.web.core.refund.service.RefundDomainService;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.ParameterizedType;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.locks.Lock;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * RefundForSellerApplication Api, just construct the logical and code.
 * Code should auth the operation outer of this application, that is determined by different logical.
 * @see RefundDomain actually execute action
 * @see io.terminus.pay.service.PayChannel return the money
 * @see OrderRefundEvent event is send after action
 * @see RefundSuccessCallbackEvent  when refund success callback is notified
 */
@Slf4j
@Component
@AllArgsConstructor
public class RefundForSellerApplication {
    private final RefundDomainService refundDomainService;
    private final RefundExpressService refundExpressService;
    private final ShopCacheHolder shopCacheHolder;
    private final RefundDetailExpressDecorator refundDetailExpressDecorator;
    /**
     * payment execute dependency
     */
    private final RefundParamsMaker refundParamsMaker;
    private final ChannelRegistry channelRegistry;
    /**
     * verify the cert exist if necessary, but it can't validate the cert
     */
    private final JsapiWechatpayTokenProvider jsapiWechatpayTokenProvider;
    private final QrWechatpayTokenProvider qrWechatpayTokenProvider;
    private final AppWechatpayTokenProvider appWechatpayTokenProvider;

    /**
     * verify the user type for admin operation
     */
    private final UserTypeBean userTypeBean;

    private final RedissonClient redissonClient;

    private static final Flow REFUND_FLOW = FlowBook.REFUND_FLOW.getFlow();

    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    /**
     * actually refund the money
     * if payment isn't existed, automatic mark refund success(not execute payChannel#Refund)
     *
     * @param refundId refundId
     * @param operator operator that has been authed out-side
     * @return execute success
     */
    public Either<Boolean> refund(Long refundId, BaseUser operator) {
        Lock lock = takeLock(refundId);
        lock.lock();
        try {
            RefundDomain domain = refundDomainService.buildDomain(refundId);
            domain.setOperator(operator);
            domain.refund(refund -> {
                // 非真实支付 无支付单
                if (Objects.isNull(refund.getPaymentId()) || refund.getPaymentId() <= 0) {
                    return "";
                }
                checkTheCerts(channelRegistry.findChannel(refund.getChannel()), refund.getShopId());
                TradeRequest request = channelRegistry.findChannel(refund.getChannel())
                        .refundRequest(refundParamsMaker.makeParams(refund));
                log.debug("{} refund[Id => {}], request [{}]", LogUtil.getClassMethodName(), refundId, Json.toJson(request));
                if (!request.isSuccess() || !ObjectUtils.isEmpty(request.getError())) {
                    throw Translate.exceptionOf("退款失败[%s]", request.getError());
                }
                // wrap the some error design return serial
                return Optional.ofNullable(Json.toJson(request.getResult())).orElse("");
            }).take();
            EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.REFUND.toOrderOperation()));
            // auto refund suppress by agree
            if (Optional.ofNullable(domain.getRefund().getPaymentId()).filter(validId -> validId > 0).isEmpty()) {
                log.warn("{} should be suppress by agree", LogUtil.getClassMethodName());
                EventSender.sendApplicationEvent(new RefundSuccessCallbackEvent(refundId, UUID.randomUUID().toString(), LocalDateTime.now()));
            }
            return Either.ok(true);
        } catch (Exception e) {
            log.error("{} fail to refund money for refund[Id => {}]", LogUtil.getClassMethodName(), refundId, e);
            return Either.error(e);
        } finally {
            lock.unlock();
        }
    }

    public Either<Boolean> acceptRefund(Long refundId, ReturnInfo returnInfo, BaseUser operator) {
        return acceptRefund(refundId, returnInfo, operator, OrderEvent.REFUND);
    }

    /**
     * accept the refund, execute the order cancel if necessary. if fail to cancel the order, reject the accept.
     * behave success if order is not existed at outer system.
     *
     * @param refundId refund Id
     * @param operator operator that has been authed
     * @return result of accept refund
     */
    public Either<Boolean> acceptRefund(Long refundId, ReturnInfo returnInfo, BaseUser operator, OrderEvent sourceOrderEvent) {
        Lock lock = takeLock(refundId);
        lock.lock();
        try {
            RefundDomain refundDomain = refundDomainService.buildDomain(refundId);
            refundDomain.setOperator(operator);
            OrderEvent event = Refund.RefundType.from(refundDomain.getRefund().getRefundType()) == Refund.RefundType.AFTER_SALE_RETURN ? OrderEvent.RETURN_APPLY_AGREE : OrderEvent.REFUND_APPLY_AGREE;
            if (event == OrderEvent.RETURN_APPLY_AGREE) {
                // record the return info
                if (returnInfo == null) {
                    returnInfo = new ReturnInfo();
                    returnInfo.setReturnName("仓库小二");
                    Shop shop = shopCacheHolder.findShopById(refundDomain.getRefund().getShopId());
                    returnInfo.setReturnMobile(shop.getPhone());
                    returnInfo.setReturnAddress("浙江省杭州市江干区九盛路9号25幢A座505室");
                    Optional.ofNullable(shop.getExtra())
                            .map(shopExtra -> shopExtra.get(ShopExtra.ExpressReturnAddress.getCode()))
                            .ifPresent(returnInfo::setReturnAddress);
                }
                refundExpressService.setReturnExpressInfo(refundId, returnInfo.getReturnAddress(), returnInfo.getReturnName(), returnInfo.getReturnMobile()).take();
            }
            return refundDomain.accept()
                    .ifSuccess(ok -> EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, event.toOrderOperation())))
                    // auto refund all for no payment order[conflict with refund?]
                    .ifSuccess(ok -> Optional.of(refundDomain.getRefund().getPaymentId())
                            .filter(Predicate.isEqual(0L))
                            // auto trigger refund action
                            .ifPresent(nonPayment -> refundDomain.refund(refund -> UUID.randomUUID().toString(), sourceOrderEvent)
                                    // log action
                                    .ifSuccess(uuid -> EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.REFUND.toOrderOperation())))
                                    // auto confirm refund success
                                    .ifSuccess(uuid -> EventSender.sendApplicationEvent(new RefundSuccessCallbackEvent(refundId, UUID.randomUUID().toString(), LocalDateTime.now())))));
        } catch (Exception e) {
            log.error("{} fail to accept refund[Id => {}]", LogUtil.getClassMethodName(), refundId, e);
            throw new ApiException(e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    /**
     * display the detail of refund
     * will judge the owner of refund with user
     *
     * @param user     who wanna display the refund detail
     * @param refundId refund id
     * @return refundDetail
     */
    public Either<RefundDetail> detail(BaseUser user, Long refundId) {
        try {
            RefundDomain domain = refundDomainService.buildDomain(refundId);
            Long shopId = -999L;
            if (user instanceof CommonUser) {
                shopId = ((CommonUser) user).getShopId();
            }
            if (!domain.getApplierId().equals(user.getId()) && !domain.getRefund().getShopId().equals(shopId)) {
                return Either.error(Translate.of("该退款单不属于你, 无法查看"));
            }
            RefundDetail refundDetail = new RefundDetail();
            refundDetail.setRefund(domain.getRefund());
            refundDetail.setShopOrder((ShopOrder) domain.getRelatedOrderList().stream().filter(order -> order instanceof ShopOrder)
                    .findFirst().orElseThrow(() -> Translate.exceptionOf("查找退款单[%s]关联主订单失败", refundId)));
            refundDetail.setSkuOrderList(domain.getRelatedOrderList().stream().filter(skuOrder -> skuOrder instanceof SkuOrder)
                    .map(skuOrder -> (SkuOrder) skuOrder).collect(Collectors.toList()));
            refundDetail.setAfterSaleTimeRecord(AfterSaleTimeRecordWrapper.wrap(domain.getRefund()));
            refundDetail.setOperations(REFUND_FLOW.availableOperations(domain.getRefund().getStatus())
                    .stream().filter(op -> op.getOperator().contains("seller")).collect(Collectors.toSet())
            );
            refundDetailExpressDecorator.decorateReturnExpressTrack(refundDetail);
            return Either.ok(refundDetail);
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    /**
     * reject the refund, it's just simple action that won't execute any outer side code.
     * should remind you of order push sequence
     *
     * @param refundId refund
     * @param operator operator that has been authed
     * @param reason   the reason why reject this refund
     * @return result of reject refund
     * @see OrderRefundEvent Reject Event should take the order back into order push flow, if necessary.
     */
    public Either<Boolean> rejectRefund(Long refundId, BaseUser operator, String reason) {
        Lock lock = takeLock(refundId);
        lock.lock();
        try {
            RefundDomain refundDomain = refundDomainService.buildDomain(refundId);
            refundDomain.setOperator(operator);
            OrderEvent event = Refund.RefundType.from(refundDomain.getRefund().getRefundType()) == Refund.RefundType.AFTER_SALE_RETURN ? OrderEvent.RETURN_APPLY_REJECT : OrderEvent.REFUND_APPLY_REJECT;
            return refundDomain.reject(reason)
                    .ifSuccess(ok -> EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, event.toOrderOperation())));
        } catch (Exception e) {
            log.error("{} fail to reject refund[Id => {}] by user[Id => {}] with reason[{}]", LogUtil.getClassMethodName(),
                    reason, operator.getId(), reason, e);
            return Either.error(e);
        } finally {
            lock.unlock();
        }
    }

    /**
     * rewind the refund that has flow into error status.
     * Check This Refund Real status at finance system. operate this after confirm every thought.
     *
     * @param refundId refund that has flow into error
     * @param operator operator that has authed
     * @param success  if success flow refund into SUCCESS, or flow it into reject, let the customer apply it again.
     * @param reason   the reason why operate this with $success
     * @return result of rewind refund error
     */
    public Either<Boolean> rewindErrorRefund(Long refundId, BaseUser operator, boolean success, String reason) {
        Lock lock = takeLock(refundId);
        lock.lock();
        try {
            RefundDomain refundDomain = refundDomainService.buildDomain(refundId);
            refundDomain.setOperator(operator);
            return refundDomain.rewindError(success, reason)
                    .ifSuccess(ok -> EventSender.sendApplicationEvent(new OrderRefundEvent(refundId
                            , success ? OrderEvent.REFUND_SUCCESS.toOrderOperation()
                            : OrderEvent.RETURN_REJECT.toOrderOperation()
                    )))
                    ;
        } catch (Exception e) {
            log.error("{} fail to rewind refund[Id => {}] into refund[success => {}] with reason[{}]", LogUtil.getClassMethodName(),
                    refundId, success, reason, e);
            return Either.error(e);
        } finally {
            lock.unlock();
        }
    }

    /**
     * check the refund certs, only wechat-pay require now
     *
     * @param refundOperation pay action
     * @param shopId          shopId to gain the pay-token
     */
    private void checkTheCerts(RefundOperation refundOperation, Long shopId) {
        if (!(refundOperation instanceof WechatpayChannel)) {
            return;
        }
        final Map<Class<?>, TokenProvider<? extends WxToken>> tokenProviderMap
                = ImmutableMap.of(JsapiWxToken.class, jsapiWechatpayTokenProvider
                , AppWxToken.class, appWechatpayTokenProvider
                , QrWxToken.class, qrWechatpayTokenProvider
        );
        // extract token type from refundOperation
        final ParameterizedType parameterizedType = (ParameterizedType) refundOperation.getClass().getGenericSuperclass();
        final Class<?> payTokenType = (Class<?>) parameterizedType.getActualTypeArguments()[0];
        try {
            // extract pay token from token type and provider
            //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
            final WxToken wxToken = tokenProviderMap.get(payTokenType).findToken(shopId.toString());
            log.debug("{} refund check certs token [shopId => {}, type => {}, appId => {}, returnUrl => {}, refundGateway => {}, refundNotifyUrl => {}, certFilePath => {}]", LogUtil.getClassMethodName()
                    , shopId
                    , wxToken.getClass().getSimpleName()
                    , wxToken.getAppId()
                    , wxToken.getReturnUrl()
                    , wxToken.getRefundGateway()
                    , wxToken.getRefundNotifyUrl()
                    , wxToken.getCertFilePath());
            // check the cert file
            final File certFile = new File(wxToken.getCertFilePath());
            if (!certFile.exists()) {
                throw new RuntimeException(Translate.of("支付证书未配备, 请上传支付证书[shopId=>%s]", shopId));
            }
        } catch (NullPointerException nullPointerException) {
            log.error("{} fail to check the pay-token cause the payToken[shopId => {}] invalidate", LogUtil.getClassMethodName(), shopId, nullPointerException);
            throw new RuntimeException(Translate.of("支付信息查找失败, 请检查支付信息[shopId=>%s]是否已经配备", shopId));
        }
    }

    /**
     * get the lock for domain operation
     *
     * @param refundId refundId to specify the refund Entity
     * @return Lock
     */
    private Lock takeLock(Long refundId) {
        return redissonClient.getLock(RefundDomain.class.getSimpleName() + "#" + refundId);
    }

    /**
     * confirm the return stock, user should invoke this api after confirm the express no and make sure that express has been receive by Stock Master
     *
     * @param operator operator should be the store owner or admin
     * @param refundId the refund
     * @return confirm success
     */
    public Either<Boolean> confirmReturn(BaseUser operator, Long refundId) {
        Lock lock = takeLock(refundId);
        if (!lock.tryLock()) {
            return Either.error("系统繁忙请稍后再试");
        }
        try {
            RefundDomain refundDomain = refundDomainService.buildDomain(refundId);
            refundDomain.setOperator(operator);
            if (!(operator instanceof CommonUser)) {
                return Either.error(Translate.of("用户类型不正确"));
            }
            Long shopId = ((CommonUser) operator).getShopId();
            if (!Objects.equals(refundDomain.getRefund().getShopId(), shopId)
                    && !userTypeBean.isAdmin(operator)) {
                return Either.error(Translate.of("该订单不属于你"));
            }
            return refundDomain.confirmReturn()
                    .ifSuccess(confirm -> EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.RETURN_CONFIRM.toOrderOperation())));
        } catch (Exception e) {
            return Either.error(e);
        } finally {
            lock.unlock();
        }
    }
}
