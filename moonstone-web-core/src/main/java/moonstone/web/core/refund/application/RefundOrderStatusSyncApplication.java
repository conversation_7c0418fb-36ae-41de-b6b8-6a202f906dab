package moonstone.web.core.refund.application;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.terminus.common.model.Response;
import io.vertx.core.AbstractVerticle;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.ShopExtra;
import moonstone.common.utils.LogUtil;
import moonstone.event.OrderRefundEvent;
import moonstone.order.api.FlowPicker;
import moonstone.order.dto.ShopOrderFeeDetailDTO;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.*;
import moonstone.order.model.domain.RefundDomain;
import moonstone.order.service.RefundReadService;
import moonstone.web.core.component.pay.app.Json;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.logic.ShopOrderLogic;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.order.OrderReadLogic;
import moonstone.web.core.order.OrderWriteLogic;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Stream;

/**
 * Refund status change will sync the order status
 *
 * <AUTHOR>
 * @apiNote watch at OrderRefundEvent
 * @see moonstone.order.dto.fsm.OrderStatus same status for refund and order
 * @see moonstone.order.dto.fsm.FlowBook define the status flow
 */
@Slf4j
@Component
@AllArgsConstructor
public class RefundOrderStatusSyncApplication extends AbstractVerticle {
  private final OrderReadLogic orderReadLogic;
  private final OrderWriteLogic orderWriteLogic;
  private final RefundReadService refundReadService;
  private final FlowPicker flowPicker;
  private final RefundOrderStatusRecordApp refundOrderStatusRecordApp;
  private final RocketMQProducer rocketMQProducer;
  private final ShopOrderLogic shopOrderLogic;



  private static final Set<OrderStatus> ORDER_STATUS_AT_REFUND_REJECT_SET =
    new HashSet<>(Arrays.asList(OrderStatus.REFUND_APPLY, OrderStatus.RETURN_APPLY,
      OrderStatus.RETURN_REJECTED, OrderStatus.REFUND_APPLY_REJECTED, OrderStatus.RETURN_APPLY_REJECTED,OrderStatus.RETURN));

  @EventListener(OrderRefundEvent.class)
  @VertxEventBusListener(OrderRefundEvent.class)
  public void onRefundOperation(OrderRefundEvent orderRefundEvent) {
    log.info("接收到退款相关事件 {}", JSONUtil.toJsonStr(orderRefundEvent));
    Long orderId = null;
    try {
      boolean isRefundCancel = Stream.of(OrderEvent.REFUND_APPLY_REJECT, OrderEvent.RETURN_APPLY_REJECT,
          OrderEvent.REFUND_APPLY_CANCEL, OrderEvent.RETURN_APPLY_CANCEL)
        .map(OrderEvent::toOrderOperation).anyMatch(op -> op.equals(orderRefundEvent.getOrderOperation()));
      if (isRefundCancel) {
        refundOrderStatusRecordApp.rollBackOrderStatusForRefund(orderRefundEvent.getRefundId(), ORDER_STATUS_AT_REFUND_REJECT_SET).take();
        return;
      }
      for (OrderRefund orderRefund : refundReadService.findOrderIdsByRefundId(orderRefundEvent.getRefundId()).getResult()) {
        OrderBase orderBase = orderReadLogic.findOrder(orderRefund.getOrderId(), orderRefund.getOrderLevel());
        orderId = orderBase instanceof SkuOrder skuOrder ? skuOrder.getOrderId() : orderBase.getId();
        if (flowPicker.pick(orderBase, orderRefund.getOrderLevel())
          .reachStatus(orderRefundEvent.getOrderOperation(), orderBase.getStatus())) {
          log.debug("{} refund[Id => {}] order[Id => {}, Level => {}] has reach status[{}] for operation[{}]",
            LogUtil.getClassMethodName(), orderRefund.getRefundId(), orderRefund.getOrderId(), orderRefund.getOrderLevel()
            , OrderStatus.fromInt(orderBase.getStatus()), Json.toJson(orderRefundEvent.getOrderOperation()));
          return;
        }
        // mark order has refund if has not refund
        if (!hasMarkRefund(orderBase)) {
          orderWriteLogic.markHasRefund(orderBase);
        }
        orderWriteLogic.updateOrderStatusByOrderEvent(orderBase, orderRefund.getOrderLevel()
          , OrderEvent.fromInt(orderRefundEvent.getOrderOperation().getValue()));
        // 针对退款成功的事件
        if (orderRefundEvent.getOrderOperation().equals(OrderEvent.REFUND_SUCCESS.toOrderOperation())) {
          JSONObject msg = new JSONObject();
          msg.set("orderId", orderId);
          rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_NOT_NEED_PUSH_TAG, msg.toString());
          if (orderBase.getOutFrom().equals(ShopExtra.communityOperation.getCode())) {
            // 社群运营模式下
            log.info("社群运营模式 当前订单信息 {}", JSONUtil.toJsonStr(orderBase));

            Map<String, Object> query = new HashMap<>();
            Refund refund = refundReadService.findById(orderRefund.getRefundId()).getResult();
            Date refundAt = refund.getRefundAt();
            if (Objects.isNull(refundAt)) {
              refundAt = refund.getUpdatedAt();
            }
            if (Objects.nonNull(refundAt)) {
              msg.set("refundTime", refundAt.getTime());
            }
            query.put("id", orderId);
            ShopOrder shopOrder = shopOrderLogic.getOne(query);
            msg.set("shopId", shopOrder.getShopId());
            msg.set("userId", shopOrder.getBuyerId());
            String feeDetailJson = shopOrder.getFeeDetailJson();
            ShopOrderFeeDetailDTO shopOrderFeeDetail = JSONUtil.toBean(feeDetailJson, ShopOrderFeeDetailDTO.class);
            msg.set("whetherUsedCoupon", !shopOrderFeeDetail.getCouponPrice().equals(BigDecimal.ZERO));
            msg.set("whetherUsedGiftMoney", !shopOrderFeeDetail.getGiftPrice().equals(BigDecimal.ZERO));
            msg.set("whetherUsedCash", !shopOrderFeeDetail.getCashPrice().equals(BigDecimal.ZERO));
            log.info("发送退款成功消息 {}", msg);
            rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_REFUND_SUCCESS_TAG, msg.toString());
            log.info("已发送退款成功消息");
          }
        }
      }
    } catch (Exception e) {
      log.error("{} fail to operate at refund[Id => {}] operation[{}]", LogUtil.getClassMethodName(),
        orderRefundEvent.getRefundId(), Json.toJson(orderRefundEvent.getOrderOperation()), e);
    } finally {
    }
  }

  /**
   * check if the order has refund
   *
   * @param orderBase order base
   * @return has refund
   */
  private boolean hasMarkRefund(OrderBase orderBase) {
    switch (OrderLevel.ofOrderBase(orderBase)) {
      case GATHER: {
        return Optional.ofNullable(orderBase.getExtra())
          .map(extra -> extra.get(RefundDomain.Action.apply.name()))
          .map(Boolean::parseBoolean)
          .orElse(false);

      }
      case SKU:
        return Objects.equals(Boolean.TRUE, ((SkuOrder) (orderBase)).getHasRefund());
      case SHOP:
        return Objects.equals(Boolean.TRUE, ((ShopOrder) (orderBase)).getHasRefund());
      default:
        return false;
    }
  }
}
