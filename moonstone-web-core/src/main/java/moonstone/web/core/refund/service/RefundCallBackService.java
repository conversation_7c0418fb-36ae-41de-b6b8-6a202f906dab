package moonstone.web.core.refund.service;

import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.*;
import moonstone.common.exception.ApiException;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.event.OrderRefundEvent;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.model.*;
import moonstone.order.model.domain.RefundDomain;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.RefundReadService;
import moonstone.web.core.events.item.ReturnStockEvent;
import moonstone.web.core.fileNew.logic.RefundLogic;
import moonstone.web.core.fileNew.logic.RefundProcessRecordLogic;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
@Slf4j
public class RefundCallBackService {
	RefundReadService refundReadService;
	RefundDomainService refundDomainService;
	PaymentReadService paymentReadService;

	RedissonClient redissonClient;

	RefundProcessRecordLogic refundProcessRecordLogic;

	RefundLogic refundLogic;


	/**
	 * get the lock for domain operation
	 *
	 * @param refundId refundId to specify the refund Entity
	 * @return Lock
	 */
	private Lock takeLock(Long refundId) {
		return redissonClient.getLock(RefundDomain.class.getSimpleName() + "#" + refundId);
	}

	/**
	 * 退款回调, 更新退款单,(子)订单,如果出现失败,只记录日志,还是返回成功
	 *
	 * @param outId    内部退款流水号
	 * @param refundAt 退款时间
	 */
	public void refundCallBack(String outId, Date refundAt) {
		//根据outId查询退款单
		Refund refund = refundReadService.findByOutId(outId).getResult();
		if (refund == null) {
			log.error("通过外部单号查找退款单失败 外部单号 {}", outId);
			throw new ApiException("外部单号查找退款单失败");
		}
        if (refund.getStatus().equals(OrderStatus.REFUND_FAIL.getValue())) {
            // 针对由于系统原因 退款失败的 最终退款成功的单子
			refund.setStatus(OrderStatus.REFUND_PROCESSING.getValue());
			refundLogic.update(refund);
        }
		Long refundId = refund.getId();
		Lock lock = takeLock(refundId);
		lock.lock();
		try {
			RefundDomain refundDomain = refundDomainService.buildDomain(refundId);
			refundDomain.clearOrderPushError();
			doRefundSuccessProcessRecord(refund);
			Either<Boolean> refundSuccessResult = refundDomain.refundSuccess(outId, refundAt);
			log.info("调用退款结果 {}", JSONUtil.toJsonStr(refundSuccessResult));
			// 已经退款完成, 不进行以下回调
			if (!refundSuccessResult.take()) {
				log.warn("退款单退款可能失败 退款单id {} 对外单号 {} 状态 {}", refundDomain.getRefund().getId(), outId, refundDomain.getRefund().getStatus());
				return;
			}

			// 若发货前退款，归还库存
			// > 判断是否全额退款, 如果是全额退款则认为是需要回归库存
			try {
				ShopOrder shopOrder = refundReadService.findShopOrderByRefundId(refundId);
				if (shopOrder.getOrderSource().equals(OrderSourceEnum.DEFAULT.getCode())) {
					// 原订单退款成功, 则进行库存归还
					returnStockIfRefundComplete(refundDomain);
				}
			} finally {
				// 异步更新订单状态
				// > 发送订单修正事件, 让订单自己判断是否需要成为完成退款模式
				EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.REFUND_SUCCESS.toOrderOperation()));
			}
		} finally {
			lock.unlock();
		}
	}

	private void doRefundSuccessProcessRecord(Refund refund) {
		Map<String, Object> query = new HashMap<>();
		query.put("refundId", refund.getId());
		query.put("refundRound", RefundProcessRecordRoundEnum.REFUND_HANDLE_SUCCESS.getCode());
		RefundProcessRecord refundProcessRecord = refundProcessRecordLogic.getOne(query);
		if (refundProcessRecord != null) {
			log.info("退款成功的记录已存在 无需再次保存");
			return;
		}
		refundProcessRecord = new RefundProcessRecord();
		refundProcessRecord.setShopId(refund.getShopId());
		refundProcessRecord.setRefundId(refund.getId());
		refundProcessRecord.setRefundRound(RefundProcessRecordRoundEnum.REFUND_HANDLE_SUCCESS.getCode());
		refundProcessRecord.setType(RefundProcessRecordTypeEnum.SYSTEM_REFUND_SUCCESS.getCode());
		refundProcessRecord.setOperatorType(RefundProcessRecordOperatorTypeEnum.SYSTEM.getCode());
		refundProcessRecord.setOperationTime(new Date());
		refundProcessRecord.setRefundFee(refund.getFee());
		refundProcessRecord.setRefundType(refund.getRefundType());
		Map<String, Object> content = new HashMap<>();
		content.put(RefundProcessRecordContentEnum.REFUND_TRADE_NO.getCode(), refund.getOutId());
		content.put(RefundProcessRecordContentEnum.REFUND_PAYMENT_WAY.getCode(), PaymentChannelEnum.getDesc(refund.getChannel()));
		refundProcessRecord.setContent(JSONUtil.toJsonStr(content));
		refundProcessRecordLogic.save(refundProcessRecord);
	}

	private void returnStockIfRefundComplete(RefundDomain refundDomain) {
		if (refundDomain.getRefund().getRefundType() == Refund.RefundType.ON_SALE_REFUND.value() || refundDomain.getRefund().getRefundType() == Refund.RefundType.AFTER_SALE_REFUND.value()) {
			log.info("当前退款单为发货前退款 {}", refundDomain.getRefund().getId());
			if (refundDomain.getRefund().getPaymentId() == 0L) {
				EventSender.sendApplicationEvent(new ReturnStockEvent(refundDomain.getRefund().getId()));
				return;
			}
			// 判断订单的金额是否和支付单一致, 与支付单一致时 判断退款金额与支付单是否一致, 一致则将回退对应库存
			Payment paidPayment = paymentReadService.findByPaySerialNoAndChannel(refundDomain.getRefund().getPaySerialNo(), refundDomain.getRefund().getChannel()).getResult();
			log.info("当前支付金额 {}", paidPayment.getFee());
			List<? extends OrderBase> orderBases = paymentReadService.findOrdersByPaymentId(paidPayment.getId()).getResult();
			boolean paymentFeeFitOrder = orderBases.stream().map(OrderBase::getFee)
					.reduce(Long::sum).filter(Predicate.isEqual(paidPayment.getFee())).isPresent();
			if (!paymentFeeFitOrder) {
				// 查看其对应的订单的全部支付单是否全部完成退款
				long orderRestNotRefundFee = -refundDomain.getRefund().getFee();
				for (OrderBase orderBase : orderBases) {
					switch (OrderStatus.fromInt(orderBase.getStatus())) {
						case BUYER_CANCEL:
						case SELLER_CANCEL:
						case TIMEOUT_CANCEL:
							continue;
						default:
					}
					orderRestNotRefundFee += orderBase.getFee();
					OrderLevel orderLevel = OrderLevel.ofOrderBase(orderBase);
					OrderPayment orderPayment = paymentReadService.findOrderPaymentByOrderIdAndOrderLevel(orderBase.getId(), orderLevel).getResult();
					if (orderPayment.getPaymentId().equals(paidPayment.getId())) {
						continue;
					}
					String refundTradeId = paymentReadService.findById(orderPayment.getPaymentId()).getResult().getOutId();
					orderRestNotRefundFee -= refundReadService.findByTradeNo(refundTradeId).getResult().stream()
							.filter(refunded -> OrderStatus.fromInt(refunded.getStatus()) == OrderStatus.REFUND)
							.map(Refund::getFee).reduce(Long::sum).orElse(0L);
				}
				if (orderRestNotRefundFee == 0) {
					EventSender.sendApplicationEvent(new ReturnStockEvent(refundDomain.getRefund().getId()));
				}
			} else if (paidPayment.getFee().equals(refundDomain.getRefund().getFee())) {
				// 是全额退款 直接回滚库存
				log.info("全额退款，直接回滚库存 退款单号 {}", refundDomain.getRefund().getId());
				EventSender.sendApplicationEvent(new ReturnStockEvent(refundDomain.getRefund().getId()));
			}else {
				log.info("部分退款，部分库存回滚 退款单号 {}", refundDomain.getRefund().getId());
				EventSender.sendApplicationEvent(new ReturnStockEvent(refundDomain.getRefund().getId()));
			}
		}
	}

}
