/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core;

import com.danding.profit.ProfitAutoConfig;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.hazelcast.config.Config;
import com.hazelcast.config.TcpIpConfig;
import com.hazelcast.config.XmlConfigBuilder;
import com.hazelcast.spi.discovery.impl.DefaultDiscoveryServiceProvider;
import com.hazelcast.spi.discovery.integration.DiscoveryServiceSettings;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import io.terminus.common.utils.Splitters;
import io.terminus.msg.api.MessageValidatorChain;
import io.terminus.msg.api.validate.DefaultMessageValidatorChain;
import io.terminus.msg.api.validate.SmsValidator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.App;
import moonstone.common.model.EntityInformationCenter;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.XML;
import moonstone.web.core.component.HazelcastFinderInRedis;
import moonstone.web.core.component.Y800APIInsertFactory;
import moonstone.web.core.component.captcha.CaptchaGenerator;
import moonstone.web.core.component.captcha.DefaultCaptchaGenerator;
import moonstone.web.core.config.MybatisAutoConfiguration;
import moonstone.web.core.events.HazelcastHeartBeatByRedis;
import moonstone.web.core.events.settle.listener.config.SettleListenerConfig;
import moonstone.web.core.events.trade.listener.config.TradeListenerConfig;
import moonstone.web.core.exports.common.ExportTables;
import moonstone.web.core.order.component.CommunityRichOrderMaker;
import moonstone.web.core.order.component.DefaultPaymentParamsMaker;
import moonstone.web.core.order.component.DefaultRefundParamsMaker;
import moonstone.web.core.session.SessionConfig;
import org.eclipse.jetty.server.handler.gzip.GzipHandler;
import org.eclipse.jetty.util.thread.QueuedThreadPool;
import org.eclipse.jetty.util.thread.ThreadPool;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.web.ErrorProperties;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.embedded.jetty.JettyServerCustomizer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.*;
import org.springframework.context.event.ApplicationEventMulticaster;
import org.springframework.context.event.SimpleApplicationEventMulticaster;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.DateFormatter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.GsonHttpMessageConverter;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.util.CollectionUtils;
import org.springframework.web.filter.HiddenHttpMethodFilter;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.servlet.Filter;
import javax.servlet.MultipartConfigElement;
import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.security.Security;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-09
 */
@SuppressWarnings("UnstableApiUsage")
@Slf4j
@Configuration
@EnableWebMvc
@EnableAutoConfiguration
@ComponentScan(excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = {"moonstone.web.core.express.*"})
})
@Import({MybatisAutoConfiguration.class, TradeListenerConfig.class, SettleListenerConfig.class, ProfitAutoConfig.class, SessionConfig.class})
@EnableWebSocket
public class CoreWebConfiguration implements WebMvcConfigurer {
    static {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }

    @Autowired
    GsonHttpMessageConverter gsonHttpMessageConverter;
    @Lazy
    @Autowired
    private HazelcastFinderInRedis hazelcastFinderInRedis;
    @Autowired(required = false)
    private List<DiscoveryServiceSettings> discoveryServiceSettingsList;

    @PreDestroy
    public void relax() throws Exception {
        Thread.sleep(10000);
    }


    @Bean
    App configureOMSApp(@Value("${Y800.partnerCode}") String appId,
                        @Value("${Y800.partnerKey}") String secret){
        App.OMS_V3.appId(appId);
        App.OMS_V3.secret(secret);
        return App.OMS_V3;
    }

    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    @Bean
    public ErrorPropertyConfigure errorPropertyConfigure(ServerProperties serverProperties) {
        return new ErrorPropertyConfigure(serverProperties.getError());
    }

    @Bean
    public MessageValidatorChain defaultMessageValidatorChain() {
        DefaultMessageValidatorChain chain = new DefaultMessageValidatorChain();
        chain.validators.removeIf(i -> i instanceof SmsValidator);
        return chain;
    }

    /**
     * 增加容器启动后自定义器
     * 修改Jetty的线程池名字
     *
     * @return 容器自定义器
     */
    @Bean
    public JettyServerCustomizer embeddedServletContainerCustomizer() {
        return jetty -> {
            ThreadPool threadPool = jetty.getThreadPool();
            if (threadPool instanceof QueuedThreadPool) {
                ((QueuedThreadPool) threadPool).setName("HTTP");
            }
            jetty.setAttribute("org.eclipse.jetty.server.Request.maxFormContentSize", 100000);
            GzipHandler gzip = new GzipHandler();
            gzip.addIncludedPaths("/*");
            gzip.addIncludedMethods("GET", "POST", "HEAD");
            gzip.addIncludedMimeTypes("application/json", "application/xml", "text/xml", "text/html", "text/plain");
            jetty.insertHandler(gzip);
        };
    }

    /**
     * provide a default Executor{@link org.springframework.scheduling.concurrent.ConcurrentTaskExecutor}
     */
    @Bean
    public Executor executor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(10000);
        executor.setMaxPoolSize(300);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix("parana-core-executor-thread-");

        return executor;
    }

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        return new MultipartConfigElement("/tmp");
    }

    @Bean
    public TaskScheduler taskScheduler() {
        //  provide a default thread pool for taskSchedule
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(Runtime.getRuntime().availableProcessors());
        scheduler.setThreadNamePrefix("parana-core-taskScheduler-");
        return scheduler;
    }

    @Bean
    public Config hazelConfigRead(@Value("${hazelcast.config}") String hazelCastConfigResource, @Value("${environment}") String environment) throws IOException {
        XmlConfigBuilder builder = new XmlConfigBuilder(Objects.requireNonNull(getClass().getClassLoader().getResource(hazelCastConfigResource)));
        Config config = builder.build();
        config.setClusterName(config.getClusterName() + "#" + environment);
        config.getNetworkConfig().getJoin().getMulticastConfig().setEnabled(false);
        if ("DEV".equals(environment.toUpperCase(Locale.ROOT))) {
            config.getNetworkConfig().getJoin().getMulticastConfig().setEnabled(true);
        } else {
            hazelcastFinderInRedis.declareSelf();
            List<String> foundAddress = hazelcastFinderInRedis.getAddress();
            TcpIpConfig tcpIpConfig = config.getNetworkConfig().getJoin().getTcpIpConfig();
            tcpIpConfig.setEnabled(true);
            tcpIpConfig.setMembers(foundAddress);
            tcpIpConfig.setConnectionTimeoutSeconds(3);
            EventSender.publish(new HazelcastHeartBeatByRedis(InetAddress.getLocalHost().getHostAddress(), config.getNetworkConfig().getPort()));
            if (!CollectionUtils.isEmpty(discoveryServiceSettingsList)) {
                if (config.getNetworkConfig().getJoin().getDiscoveryConfig().getDiscoveryServiceProvider() == null) {
                    config.getNetworkConfig().getJoin().getDiscoveryConfig().setDiscoveryServiceProvider(new DefaultDiscoveryServiceProvider());
                }
                discoveryServiceSettingsList.forEach(config.getNetworkConfig().getJoin().getDiscoveryConfig().getDiscoveryServiceProvider()::newDiscoveryService);
            }
        }

        return config;
    }

    /**
     * set eventDispatcher run at thread
     *
     * @param executor 线程池执行器
     */
    @Bean
    public ApplicationEventMulticaster applicationEventMulticaster(@Autowired Executor executor) {
        SimpleApplicationEventMulticaster concurrentEventExecutor = new SimpleApplicationEventMulticaster();
        concurrentEventExecutor.setTaskExecutor(executor);
        return concurrentEventExecutor;
    }

    @Bean
    public ExportTables exportTables() throws Exception {
        ExportTables exportTables = null;
        InputStream inputStream = getClass().getResourceAsStream("/export.xml");
        if (inputStream != null) {
            XML xml = XML.parseXML(inputStream);
            exportTables = xml.toJava(ExportTables.class);
        }
        return exportTables;
    }

    @Bean
    public Filter hiddenHttpMethodFilter() {
        return new HiddenHttpMethodFilter();
    }

    /**
     * Y800 API初始化注入器
     *
     * @return 注入器hah
     */
    @Autowired
    @Bean
    public Y800APIInsertFactory y800APIFactory(@Value("${Y800.partnerCode}") String key, @Value("${Y800.partnerKey}") String secret, @Value("${Y800.open.api.gate}") String supportApiUrl, ApplicationContext context) {
        return new Y800APIInsertFactory(key, secret, supportApiUrl, context);
    }

    @SuppressWarnings("UnstableApiUsage")
    @Bean
    public EventBus eventBus() {
        return new AsyncEventBus(new ForkJoinPool());
    }

    @Bean
    @ConditionalOnMissingBean(ObjectMapper.class)
    public ObjectMapper nonNullObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper;
    }

    @Bean
    @ConditionalOnMissingBean(name = "refundParamsMaker")
    public DefaultRefundParamsMaker refundParamsMaker() {
        return new DefaultRefundParamsMaker();
    }

    @Bean
    @ConditionalOnMissingBean(name = "paymentParamsMaker")
    public DefaultPaymentParamsMaker paymentParamsMaker() {
        return new DefaultPaymentParamsMaker();
    }

    @Bean
    @ConditionalOnMissingBean(name = "communityRichOrderMaker")
    public CommunityRichOrderMaker communityRichOrderMaker() {
        return new CommunityRichOrderMaker();
    }


    /**
     * 移除除了String默认与Jackson以外的数据读取?
     */
    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        //force utf-8 encode
        StringHttpMessageConverter stringHttpMessageConverter = new StringHttpMessageConverter(Charsets.UTF_8);
        stringHttpMessageConverter.setSupportedMediaTypes(Lists.newArrayList(MediaType.TEXT_PLAIN, MediaType.ALL));
        converters.add(1, stringHttpMessageConverter);

        GsonBuilder builder = new GsonBuilder();
        builder.registerTypeAdapter(Date.class, new TypeAdapter<Date>() {
            @Override
            public void write(JsonWriter out, Date value) throws IOException {
                if (value != null) {
                    out.value(value.toInstant().toEpochMilli());
                } else {
                    out.nullValue();
                }
            }

            @Override
            public Date read(JsonReader in) throws IOException {
                return switch (in.peek()) {
                    case NUMBER -> Date.from(Instant.ofEpochMilli(in.nextLong()));
                    case STRING -> DateTime.parse(in.nextString()).toDate();
                    default -> null;
                };
            }
        });
        gsonHttpMessageConverter.setGson(builder.create());
        converters.add(0, gsonHttpMessageConverter);
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addFormatter(new DateFormatter("yyyy-MM-dd HH:mm:ss"));
        registry.addFormatter(new DateFormatter("yyyy-MM-dd"));
    }

    @Bean
    @ConditionalOnMissingBean(MessageSource.class)
    public MessageSource messageSource(@Value("${ext.messages.classpath: messages/messages}") String extMessagesClasspath) {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.addBasenames("classpath:messages");
        for (String path : Splitters.COMMA.split(extMessagesClasspath)) {
            messageSource.addBasenames("classpath:" + path);
        }
        messageSource.setCacheSeconds(3600);
        messageSource.setUseCodeAsDefaultMessage(true);
        messageSource.setDefaultEncoding("UTF-8");
        return messageSource;
    }

    @Bean
    @ConditionalOnMissingBean(CaptchaGenerator.class)
    public CaptchaGenerator captchaGenerator() {
        return new DefaultCaptchaGenerator();
    }

    @Bean
    @ConditionalOnMissingBean(EntityInformationCenter.class)
    public EntityInformationCenter init() {
        EntityInformationCenter center = new EntityInformationCenter();
        center.init();
        return center;
    }

    @Bean
    @ConditionalOnClass(HikariDataSource.class)
    @ConditionalOnMissingBean(DataSource.class)
    @ConditionalOnProperty(name = "spring.datasource.type", havingValue = "custom",
            matchIfMissing = true)
    @ConfigurationProperties(prefix = "spring.datasource.hikari")
    HikariDataSource customDataSource(DataSourceProperties dataSourceProperties) {
        HikariConfig hikariConfig = new HikariConfig();
        BeanUtils.copyProperties(dataSourceProperties, hikariConfig);
        // limit the datasource size
        hikariConfig.setMinimumIdle(1);
        hikariConfig.setMaximumPoolSize(150);
        // max life : 30 Minutes
        hikariConfig.setMaxLifetime(30 * 60 * 60 * 1000);
        hikariConfig.setJdbcUrl(dataSourceProperties.getUrl());
        log.debug("{} Hikari [MinIdle -> {}, MaxConnection -> {}]", LogUtil.getClassMethodName(),
                hikariConfig.getMinimumIdle(), hikariConfig.getMaximumPoolSize());
        hikariConfig.setPoolName("Hikari[Parana-DB]");
        return new HikariDataSource(hikariConfig);
    }

    @AllArgsConstructor
    public static class ErrorPropertyConfigure {
        ErrorProperties errorProperties;

        @PostConstruct
        public void configure() {
            errorProperties.setIncludeMessage(ErrorProperties.IncludeAttribute.ALWAYS);
        }
    }
}
