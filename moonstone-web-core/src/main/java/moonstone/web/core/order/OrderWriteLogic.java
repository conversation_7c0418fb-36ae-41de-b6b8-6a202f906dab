package moonstone.web.core.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.event.OrderCancelEvent;
import moonstone.event.OrderCreatedEvent;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.FlowPicker;
import moonstone.order.dto.CommunitySubmittedOrder;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;
import moonstone.order.dto.fsm.Flow;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.enu.OrderCreateChannelEnum;
import moonstone.order.model.*;
import moonstone.order.rule.OrderRuleEngine;
import moonstone.order.service.*;
import moonstone.user.model.UserCertification;
import moonstone.user.service.UserCertificationReadService;
import moonstone.web.core.constants.RedisConstants;
import moonstone.web.core.fileNew.logic.SkuOrderLogic;
import moonstone.web.core.order.component.CommunityRichOrderMaker;
import moonstone.common.enums.OrderSourceEnum;
import moonstone.web.core.user.RealNameCertificationManager;
import moonstone.web.core.user.UserRelationManager;
import moonstone.web.core.util.LockKeyUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Mail: <EMAIL>
 * Data: 16/7/19
 * Author: yangzefeng
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderWriteLogic {

    @Autowired
    private OrderWriteLogic self;
    @Autowired
    SkuOrderReadService skuOrderReadService;
    @Autowired
    ShopOrderReadService shopOrderReadService;
    @Autowired
    private FlowPicker flowPicker;
    @Autowired
    private OrderWriteService orderWriteService;
    @Autowired
    private GatherOrderWriteService gatherOrderWriteService;

    @Autowired
    private PaymentReadService paymentReadService;

    @Autowired
    private PaymentWriteService paymentWriteService;

    @Resource
    private CommunityRichOrderMaker communityRichOrderMaker;

    @Autowired
    private SkuReadService skuReadService;

    @Resource
    UserCertificationReadService userCertificationReadService;

    @Autowired
    private RealNameCertificationManager realNameCertificationManager;

    @Resource
    OrderRuleEngine orderRuleEngine;

    @Autowired
    UserRelationManager userRelationManager;

    @Resource
    private RedissonClient redissonClient;

    @Autowired(required = false)
    JedisPool jedisPool;

    @Resource
    private SkuOrderLogic skuOrderLogic;

    private static final String PAYER_ID_PATTERN = "(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{4}\\*{9}\\d{4}$)";


    /**
     * mark order has refund that can be used as refund reject
     *
     * @param orderBase SkuOrder or ShopOrder
     * @return update success
     * @see OrderWriteService#markHasRefund(OrderBase) delegate method
     */
    public Either<Boolean> markHasRefund(OrderBase orderBase) {
        return orderWriteService.markHasRefund(orderBase);
    }

    public boolean updateOrderStatusByOrderEvent(OrderBase orderBase, OrderLevel orderLevel, OrderEvent orderEvent) {
        Flow flow = flowPicker.pick(orderBase, orderLevel);
        Integer targetStatus = flow.target(orderBase.getStatus(), orderEvent.toOrderOperation());

        if (Objects.equals(orderEvent.getValue(), OrderEvent.BUYER_CANCEL.getValue())
                || Objects.equals(orderEvent.getValue(), OrderEvent.SELLER_CANCEL.getValue())) {
            EventSender.sendApplicationEvent(new OrderCancelEvent(orderBase.getId(), orderLevel.getValue(), orderEvent));
        }

        switch (orderLevel) {
            case SHOP:
                Response<Boolean> updateShopOrderResp = orderWriteService.shopOrderStatusChanged(orderBase.getId(), orderBase.getStatus(), targetStatus);
                if (!updateShopOrderResp.isSuccess()) {
                    log.error("{} fail to update shop order(id={}) from current status:{} to target:{},cause:{}", LogUtil.getClassMethodName(),
                            orderBase.getId(), orderBase.getStatus(), targetStatus, updateShopOrderResp.getError());
                    throw new JsonResponseException(updateShopOrderResp.getError());
                }
                List<SkuOrder> skuOrderList = skuOrderLogic.listByShopOrderId(orderBase.getId());
                for (SkuOrder skuOrder : skuOrderList) {
                    skuOrder.setStatus(targetStatus);
                    skuOrderLogic.update(skuOrder);
                }
                return updateShopOrderResp.getResult();
            case SKU:
                Response<Boolean> updateSkuOrderResp = orderWriteService.skuOrderStatusChanged(orderBase.getId(), orderBase.getStatus(), targetStatus);
                if (!updateSkuOrderResp.isSuccess()) {
                    log.error("{} fail to update sku shop order(id={}) from current status:{} to target:{},cause:{}", LogUtil.getClassMethodName(),
                            orderBase.getId(), orderBase.getStatus(), targetStatus, updateSkuOrderResp.getError());
                    throw new JsonResponseException(updateSkuOrderResp.getError());
                }
                return updateSkuOrderResp.getResult();
            case GATHER:
                return gatherOrderWriteService.updateStatus(orderBase.getId(), orderBase.getStatus(), targetStatus).take();
            default:
                throw new IllegalArgumentException("unknown.order.type");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean sellerAuthOrder(Long shopOrderId, List<SkuOrder> skuOrderList, String sellerNote) {
        Response<Boolean> authResponse = orderWriteService.updateShopOrderAuth(shopOrderId, sellerNote);
        if (!authResponse.isSuccess()) {
            log.error("failed to update shopOrder(id={}) auth by sellerNote={}, error code:{}",
                    shopOrderId, sellerNote, authResponse.getError());
            throw new JsonResponseException(authResponse.getError());
        }
        List<Payment> payments = Optional.ofNullable(paymentReadService.findByOrderIdAndOrderLevel(shopOrderId, OrderLevel.SHOP).getResult()).orElseGet(ArrayList::new)
                .stream().filter(payment -> payment.getStatus() > 0)
                .collect(Collectors.toList());

        List<Long> boundSkuOrders = new LinkedList<>();
        for (SkuOrder skuOrder : skuOrderList) {
            if (1 == skuOrder.getIsBonded()) {
                boundSkuOrders.add(skuOrder.getId());
                if (payments.isEmpty()) {
                    payments = Optional.ofNullable(paymentReadService.findByOrderIdAndOrderLevel(skuOrder.getId(), OrderLevel.SKU).getResult()).orElseGet(ArrayList::new)
                            .stream().filter(payment -> payment.getStatus() > 0)
                            .collect(Collectors.toList());

                }
            }
        }
        Response<Boolean> updatePushStatusResponse = orderWriteService.updateSkuOrderPushStatusByOrderId(
                shopOrderId, SkuOrderPushStatus.WAITING.value(), SkuOrderPushStatus.WAITING_SELLER_AUTH.value());
        if (!updatePushStatusResponse.isSuccess()) {
            log.error("failed to update skuOrders push status by shopOrderId={}, currentPushStatus={}, targetPushStatus={}, error code: {}",
                    shopOrderId, SkuOrderPushStatus.WAITING.value(), SkuOrderPushStatus.WAITING_SELLER_AUTH.value(), updatePushStatusResponse.getError());
            throw new JsonResponseException(updatePushStatusResponse.getError());
        }

        payments.stream().filter(payment -> Objects.equals(payment.getPushStatus(), PaymentPushStatus.NEED_AUTH.getValue()))
                .map(Payment::getId)
                .forEach(paymentId -> {
                    Payment update = new Payment();
                    update.setId(paymentId);
                    if (!boundSkuOrders.isEmpty()) {
                        update.setPushStatus(PaymentPushStatus.WAIT_PUSH.getValue());
                    } else {
                        update.setPushStatus(PaymentPushStatus.NO_NEED_PUSH.getValue());
                    }

                    log.info("OrderWriteLogic.sellerAuthOrder, 审核订单-更新支付单推送状态, shopOrderId={}, updatePayment={}",
                            shopOrderId, JSON.toJSONString(update));
                    paymentWriteService.update(update);
                });

        return Boolean.TRUE;
    }

    public void turnItAsStrangeOrder(Long orderId) {
        ShopOrder shopOrder = shopOrderReadService.findById(orderId).getResult();
        if (shopOrder.getStatus() >= 500) {
            throw new RuntimeException("ALREADY MARKED");
        }
        orderWriteService.updateOrderStatus(orderId, OrderLevel.SHOP, shopOrder.getStatus() + 500);
        for (SkuOrder skuOrder : skuOrderReadService.findByShopOrderId(orderId).getResult()) {
            if (skuOrder.getStatus() >= 500) {
                throw new RuntimeException("ALREADY MARKED");
            }
            orderWriteService.updateOrderStatus(skuOrder.getId(), OrderLevel.SKU, skuOrder.getStatus() + 500);
        }
    }

    public List<Long> createCommunity(CommunitySubmittedOrder submittedOrder, CommonUser user) {
        var lock = redissonClient.getLock(LockKeyUtils.orderCreate(user.getId()));
        if (!lock.tryLock()) {
            log.error("Orders.create error, userId={}, submittedOrder={}, 获取锁失败", user.getId(), JSON.toJSONString(submittedOrder));
            throw new RuntimeException("正在处理下单流程，请稍后重试");
        }
        submittedOrder.setOutFrom(OrderOutFrom.COMMUNITY_OPERATION.getOrderOutFormCode());
        Long buyerId = submittedOrder.getBuyerId();
        try {
            //构造订单（包含所有校验参数）
            RichOrder richOrder = communityRichOrderMaker.full(submittedOrder, user);
//            //检查用户是否具有购买资格 校验提前到新小程序了
//            orderRuleEngine.canBuy(richOrder);
            //含有保税商品，检查订购人实名认证信息
            Predicate<RichSkusByShop> isBondedCheck = richSkusByShop -> richSkusByShop.getRichSkus().stream()
                    .map(RichSku::getSku).map(Sku::getType).anyMatch(Predicate.isEqual(1));
            boolean hasBonded = richOrder.getRichSkusByShops().stream().anyMatch(isBondedCheck);
            checkPayerInfo(submittedOrder.getChannel(), richOrder, buyerId, hasBonded);
            //创建订单&持久化数据
            List<Long> shopOrderIds = self.persistRichOrder(richOrder).getResult();
            log.info("Orders.create, userId={}, submittedOrder={}, result shopOrderIds={}",
                    buyerId, JSON.toJSONString(submittedOrder), JSON.toJSONString(shopOrderIds));
            if (CollUtil.isEmpty(shopOrderIds)) {
                return Collections.emptyList();
            }
            orderCreatedEventSend(shopOrderIds);
            return shopOrderIds;
        } catch (Exception ex) {
            log.error("订单创建失败 用户id {} 提交的订单信息 {} 失败原因 {}", buyerId, JSON.toJSONString(submittedOrder), ex.getMessage(), ex);
            throw ex;
        } finally {
            lock.unlock();
        }
    }

    private void checkPayerInfo(Integer channel, RichOrder richOrder, Long buyerId, boolean hasBonded) {
        var channelEnum = OrderCreateChannelEnum.parse(channel);
        if (channelEnum == null) {
            return;
        }

        switch (channelEnum) {
            case PC_WEB -> {
                if (ObjectUtils.isEmpty(richOrder.getPayerName()) || ObjectUtils.isEmpty(richOrder.getPayerId())) {
                    log.warn("submitOrder has no payer info[{}], when submit order with bonded item", buyerId);
                    // 尝试使用默认的验证信息
                    if (ObjectUtils.isEmpty(richOrder.getReceiverInfo())) {
                        if (hasBonded) {
                            throw new JsonResponseException("请填写收货人的身份证信息, 因为是跨境商品");
                        } else {
                            return;
                        }
                    }
                    richOrder.setPayerId(richOrder.getReceiverInfo().getPaperNo());
                    richOrder.setPayerName(richOrder.getReceiverInfo().getReceiveUserName());
                    if (ObjectUtils.isEmpty(richOrder.getPayerId())) {
                        if (hasBonded) {
                            throw new JsonResponseException("请填写收货人的身份证信息, 因为是跨境商品");
                        } else {
                            return;
                        }
                    }
                }
                judgePaperNo(richOrder.getPayerId());

                //当前只针对pc网页端提交的订单做实名验证并保存
                realNameCertificationManager.saveUserCertification(richOrder.getBuyer().getId(), richOrder.getPayerName(), richOrder.getPayerId());
            }
            case WX_APP -> {
                Response<Optional<UserCertification>> userCertificationResponse = userCertificationReadService.findDefaultByUserId(buyerId);
                if (!userCertificationResponse.isSuccess()) {
                    log.error("failed to find default user certification by userId={}, error code: {}", buyerId, userCertificationResponse.getError());
                    if (hasBonded) {
                        throw new JsonResponseException(userCertificationResponse.getError());
                    } else {
                        return;
                    }
                }
                Optional<UserCertification> userCertificationOptional = userCertificationResponse.getResult();
                if (userCertificationOptional.isPresent()) {
                    UserCertification userCertification = userCertificationOptional.get();
                    richOrder.setPayerName(userCertification.getPaperName());
                    richOrder.setPayerId(userCertification.getPaperNo());
                } else {
                    log.warn("user(id={}) has no default certification, when submit order with bonded item on wxa", buyerId);
                    if (hasBonded) {
                        throw new JsonResponseException("default.user.certification.not.exist");
                    }
                }
            }
        }
    }
    private void judgePaperNo(String paperNo) {
        if (!paperNo.matches(PAYER_ID_PATTERN)) {
            throw new JsonResponseException(400, "user.certification.paperNo.invalid");
        }
    }

    /**
     * 用户下单后的事件发送
     *
     * @param shopOrderIds 订单创建结果（订单主体id)
     */
    private void orderCreatedEventSend(List<Long> shopOrderIds) {
        for (Long shopOrderId : shopOrderIds.stream().filter(Objects::nonNull).toList()) {
            EventSender.sendApplicationEvent(new OrderCreatedEvent(shopOrderId));
        }
    }

    /**
     * 判断库存是否可用，然后持久化订单
     *
     * @param richOrder 订单内容
     * @return 是否持久化成功
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Response<List<Long>> persistRichOrder(RichOrder richOrder) {
        // 如果没有配置redis 那么就直接走吧
        if (jedisPool == null) {
            log.debug("{} redis not enable so may cause over sell", LogUtil.getClassMethodName());
            return orderWriteService.create(richOrder);
        }
        // 配置了redis 那么就从redis里拉缓存
        try (Jedis jedis = jedisPool.getResource()) {
            HashMap<String, Long> skuStockQuantityModifyHistory = new HashMap<>(8);
            //modify redis and record its action
            BiFunction<Long, Long, Long> modifySkuQuantity = (skuId, modQuantity) -> {
                String skuStockQuantityCacheInRedis = RedisConstants.SKU_STOCK_PREFIX + skuId;
                Lock lock = redissonClient.getLock(RedisConstants.SKU_STOCK_LOCK_PREFIX + skuId);
                lock.lock();
                try {
                    String skuRedisSkuStockQuantity = jedis.get(skuStockQuantityCacheInRedis);
                    log.info("第一次查询redis 当前skuId {} 的库存数 {} ", skuId, skuRedisSkuStockQuantity);
                    if (StrUtil.isBlank(skuRedisSkuStockQuantity)) {
                        Integer stockQuantity = skuReadService.findSkuById(skuId).getResult().getStockQuantity();
                        log.info("查询到数据库当中的 skuId {} 的库存 {}", skuId, stockQuantity);
                        jedis.setnx(skuStockQuantityCacheInRedis, stockQuantity.toString());
                        log.info("将skuId {} 的库存数 {} 设置到redis中", skuId, stockQuantity);
                    }
                    skuRedisSkuStockQuantity = jedis.get(skuStockQuantityCacheInRedis);
                    log.info("第二次查询redis 当前skuId {} 的库存数 {} 需要扣减的库存数 {}", skuId, skuRedisSkuStockQuantity, modQuantity);
                    if (StrUtil.isNotBlank(skuRedisSkuStockQuantity) && Integer.parseInt(skuRedisSkuStockQuantity) <= 0) {
                        log.warn("当前skuId {} 在redis中的库存数为 {}", skuId, skuRedisSkuStockQuantity);
                        jedis.del(skuStockQuantityCacheInRedis);
                        return -1L;
                    }
                    skuStockQuantityModifyHistory.put(skuStockQuantityCacheInRedis, modQuantity);
                    jedis.expire(skuStockQuantityCacheInRedis, 2 * 60);
                    return jedis.decrBy(skuStockQuantityCacheInRedis, modQuantity);
                } finally {
                    lock.unlock();
                }
            };
            //revoke all redis action
            Consumer<Map<String, Long>> revokeRedisAction = (map) -> map.forEach((skuStockQuantityCacheInRedis, modQuantity) -> {
                log.debug("{} redis stock[{}] add {} result [{}]", LogUtil.getClassMethodName(), skuStockQuantityCacheInRedis, modQuantity,
                        jedis.decrBy(skuStockQuantityCacheInRedis, -modQuantity));
                jedis.expire(skuStockQuantityCacheInRedis, 2 * 60);
            });
            boolean failed = false;
            // 遍历处理所有的单品去处理库存
            StockLoop:
            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                log.info("需要扣减的sku信息 {}", JSONUtil.toJsonStr(richSkusByShop.getRichSkus()));
                for (RichSku richSkus : richSkusByShop.getRichSkus()) {
                    if (OrderSourceEnum.isHasIndependentStock(richSkusByShop.getOrderSource())) {
                        // 是拥有独立库存的订单类型  新项目中进行校验
                        log.info("订单拥有独立库存 忽略本项目的库存校验 {} {}", richSkus.getSku().getId(), richSkusByShop.getOrderSource());
                        continue;
                    }
                    Long afterQuantity = modifySkuQuantity.apply(richSkus.getSku().getId(), richSkus.getQuantity().longValue());
                    log.debug("{} skuId:{} after-stock-quantity:{} in redis", LogUtil.getClassMethodName(), richSkus.getSku().getId(), afterQuantity);
                    if (afterQuantity < 0) {
                        log.error("{} buyer:{} skuId:{} buy-quantity:{} quantity:{}", LogUtil.getClassMethodName()
                                , richOrder.getBuyer()
                                , richSkus.getSku()
                                , richSkus.getQuantity()
                                , afterQuantity);
                        failed = true;
                        break StockLoop;
                    }
                }
            }
            // 出现库存不足的情况了 回退所有吃掉的库存
            if (failed) {
                revokeRedisAction.accept(skuStockQuantityModifyHistory);
                throw new ApiException("亲，库存暂时不足失败，请稍后重新下单试试哦");
            }
            Response<List<Long>> rShopOrderIds = orderWriteService.create(richOrder);
            if (!rShopOrderIds.isSuccess()) {
                revokeRedisAction.accept(skuStockQuantityModifyHistory);
            }
            return rShopOrderIds;
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} sku-stock-quantity modify error:{}", LogUtil.getClassMethodName(), ex.getMessage());
            throw ex;
        }
    }
}
