package moonstone.web.core.order.enums;

import lombok.Getter;

/**
 * 开团状态：1: 团长待支付 2：进行中 3：开团成功 4：开团失败 5:团员已满")
 */
@Getter
public enum GbGroupStatusEnum {

    LEADER_PENDING_PAYMENT(1, "团长待支付",true),

    IN_PROGRESS(2, "进行中",true),

    GROUP_SUCCESS(3, "开团成功",true),

    GROUP_FAILED(4, "开团失败",true),

    GROUP_FULL(5, "团员已满",true);
    private final Integer code;
    private final String description;
    private final Boolean isView;

    GbGroupStatusEnum(Integer code, String description, Boolean isView) {
        this.code = code;
        this.description = description;
        this.isView = isView;
    }

    public static GbGroupStatusEnum fromCode(Integer code) {
        for (GbGroupStatusEnum status : GbGroupStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }

    public static String findDescByCode(Integer groupStatus) {
        if(groupStatus == null){
            return "";
        }
        return fromCode(groupStatus).getDescription();
    }
}
