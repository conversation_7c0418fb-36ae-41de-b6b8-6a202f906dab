package moonstone.web.core.order.enums;

import lombok.Getter;

/**
 * 团员类型：1：团长 2：团员  ")
 */
@Getter
public enum GbGroupMemberRoleEnum {

    MEMBER_ROLE_LEADER(1, "团长",true),

    MEMBER_ROLE_NORMAL(2, "团员",true);



    private final Integer code;
    private final String description;
    private final Boolean isView;

    GbGroupMemberRoleEnum(Integer code, String description, Boolean isView) {
        this.code = code;
        this.description = description;
        this.isView = isView;
    }
    public static GbGroupMemberRoleEnum fromCode(Integer code) {
        for (GbGroupMemberRoleEnum status : GbGroupMemberRoleEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }

    public static String findDescByCode(Integer memberRole) {
        if(memberRole == null){
            return "";
        }
        return fromCode(memberRole).getDescription();
    }
}
