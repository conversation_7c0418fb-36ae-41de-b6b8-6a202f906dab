package moonstone.web.core.order.enums;

import lombok.Getter;

/**
 * 团员状态：1：待支付 2：已支付  3：退出 4: 开团成功 5：已发货 6：已签收 10：待下单")
 */
@Getter
public enum GbGroupMemberStatusEnum {

    MEMBER_STATUS_BEFORE_PAY(1, "待支付",true),

    MEMBER_STATUS_IN(2, "已支付",true),

    MEMBER_STATUS_OUT(3, "已退团",true),

    MEMBER_STATUS_SUCCESS(4, "开团成功",true),

    MEMBER_STATUS_DELIVERED(5, "已发货",true),

    MEMBER_STATUS_SIGNED(6, "已签收",true),

    MEMBER_STATUS_OCCUPANCY(10, "待下单",true);


    private final Integer code;
    private final String description;
    private final Boolean isView;

    GbGroupMemberStatusEnum(Integer code, String description, Boolean isView) {
        this.code = code;
        this.description = description;
        this.isView = isView;
    }
    public static GbGroupMemberStatusEnum fromCode(Integer code) {
        for (GbGroupMemberStatusEnum status : GbGroupMemberStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }

    public static String findDescByCode(Integer memberRole) {
        if(memberRole == null){
            return "";
        }
        return fromCode(memberRole).getDescription();
    }
}
