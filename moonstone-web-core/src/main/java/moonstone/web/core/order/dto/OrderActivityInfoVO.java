package moonstone.web.core.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class OrderActivityInfoVO implements Serializable {

    /**
     * {@link com.danding.mall.app.order.enums.OrderSourceEnum}
     */
    @ApiModelProperty(value = "订单活动来源")
    private Integer marketingToolId;

    @ApiModelProperty(value = "参团ID 团长开通时可为空")
    private Long groupId;

    @ApiModelProperty(value = "活动ID")
    private Integer activityId;


    @ApiModelProperty(value = "团状态 1：团长待支付 2：进行中 3：开团成功 4：开团失败 5：团员已满")
    private Integer groupStatus;

    @ApiModelProperty(value = "团活动描述")
    private String groupStatusDesc;

    @ApiModelProperty(value = "团角色：1团长 2团员")
    private Integer memberRole;
    private String memberRoleDesc;

    @ApiModelProperty(value = "当前团人数")
    private Long groupMemberNum;
    @ApiModelProperty(value = "成团需要人数")
    private Integer activityNum;

    @ApiModelProperty(value = "活动状态 1：已开始 2：进行中 3：已结束 4：已作废 9：已清结")
    private Integer activityStatus;
    @ApiModelProperty(value = "活动状态描述")
    private String activityStatusDesc;

    @ApiModelProperty(value = "团成员状态")
    private Integer memberStatus;
    private String memberStatusDesc;
}
