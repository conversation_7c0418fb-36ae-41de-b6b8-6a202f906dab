package moonstone.web.core.order.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.danding.mercury.pay.sdk.MercuryPayAccountType;
import com.danding.mercury.pay.sdk.customs.declare.MercuryPayCustomsDeclareModel;
import com.danding.mercury.pay.sdk.customs.declare.MercuryPayCustomsDeclareResult;
import com.danding.mercury.pay.sdk.customs.declare.MercuryPayCustomsGoodsInfo;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.model.Response;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.constants.RocketMQConstant;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.OrderSourceEnum;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.Either;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.*;
import moonstone.event.*;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.emu.ThirdPartyItemType;
import moonstone.item.service.GbGroupInfoReadService;
import moonstone.order.component.PaymentAccountUtil;
import moonstone.order.dto.PayInfoPushCondition;
import moonstone.order.dto.PayInfoPushGoodsInfo;
import moonstone.order.dto.PayInfoPushOrderGoodsInfo;
import moonstone.order.dto.PushOrderItem;
import moonstone.order.dto.fsm.OrderExceptionEnum;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.enu.PaymentExtraIndexEnum;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.enu.SkuOrderShippingWarehouseTypeEnum;
import moonstone.order.model.*;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.model.ShopPayInfo;
import moonstone.stock.impl.service.DepotCustomManager;
import moonstone.thirdParty.enums.ThirdPartyUserShopExtraIndex;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.user.enums.ThirdPartyUserType;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.ThirdPartyUser;
import moonstone.user.service.ThirdPartyUserReadService;
import moonstone.web.core.component.UMFAccountWeb;
import moonstone.web.core.component.api.Y800V3Api;
import moonstone.web.core.component.pay.PayChannelsConstants;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import moonstone.web.core.mirror.model.MirrorSource;
import moonstone.web.core.order.app.CCSClient;
import moonstone.web.core.order.dto.ccs.PaymentDeclareRequest;
import moonstone.web.core.order.service.OrderDeclareService;
import moonstone.web.core.order.service.Y800V3CustomsClearancePushService;
import org.joda.time.DateTime;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.Key;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Lock;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static moonstone.stock.impl.service.DepotCustomManager.CUSTOM_NOT_FOUND;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OrderDeclareServiceImpl implements OrderDeclareService {
    final Function<Long, String> getPaymentLock = paymentId -> String.format("[PaymentPush](%s)", paymentId);

    @Autowired
    PushSystemDep pushSystemDep;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    ZeroPaymentSpareTool zeroPaymentSpareTool;
    @Autowired
    SourceShopQuerySlice sourceShopQuerySlice;
    @Resource
    ThirdPartyUserReadService thirdPartyUserReadService;
    @Autowired
    UMFAccountWeb umfAccountWeb;
    @Resource
    SkuOrderReadService skuOrderReadService;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    OutSystemIdProvider outSystemIdProvider;
    @Autowired
    Y800V3CustomsClearancePushService y800V3CustomsClearancePushService;

    @RemoteAPI
    private Y800V3Api y800V3Api;

    @Resource
    private RocketMQProducer rocketMQProducer;

    @Resource
    private OrderWriteService orderWriteService;

    @Resource
    private GbGroupInfoReadService gbGroupInfoReadService;

    /**
     * 检测支付单推送状态是否同步
     * 不同步则返回一个更新的payment
     *
     * @param payment 被检测的payment
     * @return 需要更新的Payment
     */
    @Override
    public Optional<Payment> syncPaymentPushStatus(Payment payment) {
        Payment check = new Payment();
        check.setId(payment.getId());
        check.setPushStatus(payment.getPushStatus());
        check.setUpdatedAt(payment.getUpdatedAt());
        if (Objects.equals(pushSystemDep.paymentReadService.checkPayment(check).getResult(), true)) {
            return Optional.empty();
        }
        log.warn("{} payment[{}] need update so it may update Fail-Push-Status", LogUtil.getClassMethodName(), payment.getId());
        return Optional.ofNullable(pushSystemDep.paymentReadService.findById(payment.getId()).getResult());
    }

    /**
     * 尝试申报失败时,推送状态不同则不更新状态
     * 获取分布式锁 避免同步重复推单
     *
     * @param payment 待申报支付单
     */
    @Override
    public void paymentDeclare(Payment payment) {
        if (payment == null) {
            log.info("[op: 海关支付单申报] 支付单不存在");
            return;
        }
        if (redissonClient == null) {
            log.error("{} lock system is down for payment[{}] push", LogUtil.getClassMethodName(), payment.getId());
            paymentDeclareWithoutLock(payment);
            return;
        }
        String lockName = getPaymentLock.apply(payment.getId());
        Lock lock = redissonClient.getLock(lockName);

        TaskWithLock.processWithLock(lock, lockName, payment, payment.getId(), this::paymentDeclareWithoutLock);
    }

    /**
     * 尝试申报失败时,推送状态不同则不更新状态 继续重试
     *
     * @param waitDeclare 待申报支付单
     */
    public void paymentDeclareWithoutLock(Payment waitDeclare) {
        Payment payment = syncPaymentPushStatus(waitDeclare).orElse(waitDeclare);
        if (payment.getPushStatus() != PaymentPushStatus.WAIT_PUSH.getValue()
                && payment.getPushStatus() != PaymentPushStatus.DECLARED_FAIL_WAIT_PUSH_RETRY.getValue()
                && payment.getPushStatus() != PaymentPushStatus.DECLARE_FAIL.getValue()
                && payment.getPushStatus() != PaymentPushStatus.WAIT_CALLBACK.getValue()) {
            log.warn("{} payment[{}] not suit declare-status [{}] but at [{}]", LogUtil.getClassMethodName(), payment.getId(), PaymentPushStatus.WAIT_PUSH, payment.getPushStatus());
            return;
        }
        /// > 由于关区获取接口已经启用,开启海关申报
        Boolean requireDelay = needDelay(payment);
        if (requireDelay) {
            log.info("[op: 门店分销支付单申报] 延迟一小时推送海关 paymentId:{}", payment.getId());
            EventSender.sendApplicationEvent(new PaymentDeclaredDelayNotify(payment.getId()));
            return;
        }
        //  如果是模拟支付就不申报了
        if (Optional.of(payment.getChannel()).orElse("").contains("mockpay") || notBoundOrder(payment)) {
            payment.setPushStatus(PaymentPushStatus.DECLARE_SUCCESS.getValue());
            pushSystemDep.paymentWriteService.update(payment);
            log.warn("{} payment[{}] pay by {} no need declare (maybe bound no order)", LogUtil.getClassMethodName(), payment.getId(), payment.getChannel());
            return;
        }


        List<ShopOrder> shopOrderList = findOrderByPaymentId(payment.getId());
        /// 订单数据不存在则直接申报无视
        if (shopOrderList.isEmpty()) {
            log.warn("{} shopOrder is empty,paymentId:{}", LogUtil.getClassMethodName(), payment.getId());
            return;
        }

        log.info("[op: 海关支付单申报] ,支付单ID={}, 订单号={}", payment.getId(), Arrays.toString(shopOrderList.stream().map(OrderBase::getId).toArray()));

        // if some order fee is **zero** so spare the fee from all order
        // 如果支付单内订单价格有0,则拆分价格
        zeroPaymentSpareTool.spareThePriceIfExistsZeroPriceOrder(shopOrderList);
        // 申报工作执行器, 组装入成功回调和失败回调 则开始申报
        List<Function<Consumer<ShopOrder>, Consumer<Consumer<ShopOrder>>>> declareRequestExecutorWorkList = new ArrayList<>();

        // 由订单包裹生成模型处理器
        // 如果模型处理器不存在 则结束申报
        // 组装工作流
        shopOrderList.forEach(shopOrder ->
                genDeclareRequest(payment, shopOrder)
                        .map(construct -> generateRequestExecutorWorkExecutor(payment, shopOrder, construct))
                        .ifPresent(declareRequestExecutorWorkList::add));
        // 暂存处理器, 代替CPS用
        List<ShopOrder> completeOrder = new ArrayList<>();
        List<ShopOrder> failAt = new ArrayList<>();
        if (declareRequestExecutorWorkList.isEmpty()) {
            return;
        }
        // 使用列表增加作为成功或者失败工作流动作
        for (Function<Consumer<ShopOrder>, Consumer<Consumer<ShopOrder>>> successOrFailDeclarePromiseTrigger : declareRequestExecutorWorkList) {
            // 如果有出现申报错误 则暂停申报
            if (failAt.isEmpty()) {
                // 只有注入全部操作后才会开始正常申报
                successOrFailDeclarePromiseTrigger.apply(completeOrder::add).accept(failAt::add);
            } else {
                // 代替CPS, 跳出执行判断最终状态
                break;
            }
        }
        // 判断最终状态
        judgePaymentPushStatusByCons(failAt.isEmpty(), payment, completeOrder, failAt);
    }

    /**
     * 组装申报机, 申报机器接受一个申报成功回调 与失败回调 便开始执行申报
     *
     * @param payment                 支付单
     * @param shopOrder               订单
     * @param requestExecutorConsumer 申报执行器
     * @return 申报机
     */
    private Function<Consumer<ShopOrder>, Consumer<Consumer<ShopOrder>>> generateRequestExecutorWorkExecutor(Payment payment, ShopOrder shopOrder, Consumer<Consumer<PaymentDeclareRequest>> requestExecutorConsumer) {
        log.info("生成申报单相关数据 0 {},{}",JSONUtil.toJsonStr(payment),JSONUtil.toJsonStr(shopOrder));
        return success -> fail -> {
            log.info("生成申报单相关数据 1{} {}",JSONUtil.toJsonStr(payment),JSONUtil.toJsonStr(shopOrder));
            // 查询申报商户号
            String mchCode = pushSystemDep.y800PayMchManager.findPay800MchByShopId(shopOrder.getShopId()).map(Y800PayMch::getPayMch)
                    .ifFail(() -> DefaultMchForShopSet.defaultMchForShop(shopOrder.getShopId()))
                    .orElse(pushSystemDep.mercuryPayMerchantCode);

            log.info("查询到申报商户号 {}", mchCode);
            log.debug("{} paymentId:{} orderId(maybe):{} mchCode:{}", LogUtil.getClassMethodName("mch-from-order"), payment.getId(), shopOrder.getId(), mchCode);
            log.info("查询商户号之后");
            // 组装客户端
            CCSClient client = new CCSClient(pushSystemDep.mercuryPayHost, pushSystemDep.mercuryPayAppCode, MercuryPayAccountType.COMPANY, mchCode);

            // 申报执行器组装
            Consumer<PaymentDeclareRequest> requestDeclareExecutor = generateDeclareExecutor(client, payment, shopOrder, success, fail);
            // 将申报请求塞入 启动申报
            requestExecutorConsumer.accept(requestDeclareExecutor);
        };
    }

    /**
     * 组装执行真实申报的执行机
     *
     * @param client    申报客户端
     * @param payment   支付单
     * @param shopOrder 订单
     * @param success   申报成功回调
     * @param fail      申报失败回调
     * @return 执行机器
     */
    private Consumer<PaymentDeclareRequest> generateDeclareExecutor(CCSClient client, Payment payment, ShopOrder shopOrder, Consumer<ShopOrder> success, Consumer<ShopOrder> fail) {
        return request -> {
            try {
                com.danding.common.net.Response<MercuryPayCustomsDeclareResult> response = client.execute(request, MercuryPayCustomsDeclareResult.class);
                log.info("[op: mercuryPayCustomsDeclare] shopOrderId={}, paymentId={}, request={}, response={}",
                        shopOrder.getId(), payment.getId(), JSON.toJSONString(request), JSON.toJSONString(response));

                if (!response.isSuccess()) {
                    log.error("推送支付单失败 支付单id {} 失败原因 {}", payment.getId(), response.getErrorMessage());
                    JSONObject msg = new JSONObject();
                    msg.set("orderId", shopOrder.getId());
                    msg.set("exceptionType", OrderExceptionEnum.SYSTEM_ERROR.getCode());
                    msg.set("exceptionMessage", "推送支付单失败，失败原因：" + response.getErrorMessage());
                    rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_EXCEPTION_RECORD_TAG, msg.toString());
                    log.info("发送支付单失败，订单异常消息 {}", msg);
                    fail.accept(shopOrder);
                    return;
                }
                JSONObject msg = new JSONObject();
                msg.set("orderId", shopOrder.getId());
                msg.set("exceptionType", OrderExceptionEnum.CLEAR_ERROR.getCode());
                msg.set("exceptionMessage","");
                rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_EXCEPTION_RECORD_TAG, msg.toString());
                MercuryPayCustomsDeclareResult result = response.getResult();
                log.debug("{} result:{}", LogUtil.getClassMethodName(), JSON.toJSONString(result));
                // 如果支付订单推送成功
                // 执行成功申报回调 => 进入成功申报流
                success.accept(shopOrder);
            } catch (Exception ex) {
                log.error("{} failed to push paymentId={}, shopOrderId={}",
                        LogUtil.getClassMethodName(), payment.getId(), shopOrder.getId(), ex);

                if (!syncPaymentPushStatus(payment).isPresent()) {
                    EventSender.sendApplicationEvent(new PaymentDeclareFailNotify(payment.getId()));
                }
                // 进入失败申报流
                fail.accept(shopOrder);
            }
        };
    }

    /**
     * 查找支付单关联的订单
     *
     * @param paymentId 支付单号
     * @return 订单列表
     */
    private List<ShopOrder> findOrderByPaymentId(Long paymentId) {
        List<ShopOrder> shopOrderList = new ArrayList<>();
        Set<Long> orderIdSet = new HashSet<>();

        List<OrderPayment> orderPaymentList = pushSystemDep.paymentReadService.findOrderIdsByPaymentId(paymentId).getResult();
        orderPaymentList.stream().filter(orderPayment -> orderPayment.getOrderType() == 1)
                .map(OrderRelation::getOrderId)
                .filter(orderId -> !orderIdSet.contains(orderId))
                .peek(orderIdSet::add)
                .map(pushSystemDep.shopOrderReadService::findById)
                .map(Response::getResult)
                .filter(Objects::nonNull).filter(order -> order.getShopId() != null).forEach(shopOrderList::add);

        orderPaymentList.stream().filter(orderPayment -> orderPayment.getOrderType() == 2)
                .map(OrderRelation::getOrderId)
                .filter(orderId -> !orderIdSet.contains(orderId))
                .peek(orderIdSet::add)
                .map(pushSystemDep.shopOrderReadService::findById)
                .map(Response::getResult)
                .filter(Objects::nonNull).filter(order -> order.getShopId() != null).forEach(shopOrderList::add);
        return shopOrderList;
    }

    /**
     * 检测支付单最后推送状态
     *
     * @param alive   目前推送是否进入死亡(失败状态)
     * @param payment 支付单
     */
    private void judgePaymentPushStatusByCons(boolean alive, Payment payment, List<ShopOrder> completeOrder, List<ShopOrder> failAt) {
        // 如果 工作流存活 (申报成功)
        if (alive) {
            if (completeOrder.isEmpty()) {
                // 没有发起申报
                return;
            }

            if (!alreadyDeclareSuccess(payment.getId())) {
                payment.setPushStatus(PaymentPushStatus.WAIT_CALLBACK.getValue());
                var paymentUpdate = pushSystemDep.paymentWriteService.pushedWaitCallback(payment.getId());
                log.info("OrderDeclareServiceImpl.judgePaymentPushStatusByCons, payment[{}] complete declare [{}], updateSuccess={}, updateResult={}",
                        payment.getId(), completeOrder.stream().map(OrderBase::getId).toArray(), paymentUpdate.isSuccess(), paymentUpdate.getResult());
            }

            for (ShopOrder shopOrder : completeOrder) {
                EventSender.sendApplicationEvent(new PaymentDeclareSuccessNotify(shopOrder.getId(), new Date()));
            }
            return;
        }
        // 失败
        failAt.stream().findFirst().ifPresent(shopOrder -> EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("订单申报失败",
                String.format("支付单[%s] 在订单[%s, Custom = %s, OuterSkuId = %s, Channel = %s, PaySerialNo = %s, PayRequest = %s, PayResponse = %s, PushStatus = %s, Fee = %s]申报失败", payment.getId(),
                        shopOrder.getId(), shopOrder.getDepotCustomName(),
                        Arrays.toString(Optional.ofNullable(skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult())
                                .orElse(Collections.emptyList()).stream().map(SkuOrder::getOuterSkuId).collect(Collectors.toList()).toArray()),
                        payment.getChannel(), payment.getPaySerialNo(), payment.getPayRequest(), payment.getPayResponse(), payment.getPushStatus(), payment.getFee()), EmailReceiverGroup.DEVELOPER)));
        if (syncPaymentPushStatus(payment).isPresent()) {
            return;
        }
        payment.setPushStatus(PaymentPushStatus.DECLARED_FAIL_WAIT_PUSH_RETRY.getValue());
        pushSystemDep.paymentWriteService.update(payment);
        EventSender.sendApplicationEvent(new PaymentDeclareFailNotify(payment.getId()));
    }

    private boolean alreadyDeclareSuccess(Long paymentId) {
        var payment = pushSystemDep.paymentReadService.findById(paymentId).getResult();
        if (payment == null) {
            throw new RuntimeException(String.format("paymentId=%s 查询结果为空", paymentId));
        }

        var declareSuccess = List.of(PaymentPushStatus.DECLARE_SUCCESS.getValue(), PaymentPushStatus.PUSH_SUCCESS.getValue());
        return declareSuccess.contains(payment.getPushStatus());
    }

    /**
     * 创建申报请求
     *
     * @param payment   支付单
     * @param shopOrder 订单
     * @return 推送处理流
     */
    private Optional<Consumer<Consumer<PaymentDeclareRequest>>> genDeclareRequest(Payment payment, ShopOrder shopOrder) {
        // 不允许申报 返回一个不会处理任何数据的模型处理器 以跳过该订单的申报
        Long orderId = shopOrder.getId();
        if (!pushSystemDep.pushOrderJudge.allow(shopOrder)) {
            log.warn("{} shopOrderId:{} not need push because **PushOrderJudge** rule", LogUtil.getClassMethodName(), orderId);
            if (!pushSystemDep.orderWriteService.updateSkuOrderPushStatusByOrderId(orderId, SkuOrderPushStatus.WAITING.value(), SkuOrderPushStatus.WAIT_PAYMENT_PUSH_PASS.value()).isSuccess()) {
                log.warn("{} rewind the SkuPushState back into NOT_NEED failed,orderId:{}", LogUtil.getClassMethodName(), orderId);
            }
            return Optional.of(taker -> {
            });
        }

        if (shopOrder.getOrderSource().equals(OrderSourceEnum.GROUP_BUYING.getCode()) || shopOrder.getOrderSource().equals(OrderSourceEnum.SHARE_PURCHASE.getCode())) {
            // 属于拼团购 或者 分享购的订单 需要校验是否成团
            log.info("当前订单 {}", JSONUtil.toJsonStr(shopOrder));
            boolean whetherGroupSuccess = gbGroupInfoReadService.groupIsSuccess(orderId);
            if (!whetherGroupSuccess) {
                return Optional.empty();
            }
        }

        List<SkuOrder> skuOrderList = pushSystemDep.skuOrderReadService.findByShopOrderId(orderId).getResult();
        // 订单需要审核 => 则停止申报
        // 订单需要审核, 整个申报流程需要停下, 将不返回模型处理器, 则将停止整个申报流程
        Optional<Long> needAuthOrderId = ifNeedAuth(skuOrderList);
        if (needAuthOrderId.isPresent()) {
            log.warn("{} order[{}] need auth so pause the payment[{}] declare", LogUtil.getClassMethodName(), needAuthOrderId.get(), payment.getId());
            Payment update = new Payment();
            update.setPushStatus(PaymentPushStatus.NEED_AUTH.getValue());
            pushSystemDep.paymentWriteService.update(update);
            return Optional.empty();
        }
        // 订单不需要申报, 其属于特殊仓库
        if (shouldSkipDeclare(skuOrderList)) {
            log.warn("{} order[{}] skip declare, it is on special rule, push it directly", LogUtil.getClassMethodName(), orderId);
            Payment update = new Payment();
            update.setPushStatus(PaymentPushStatus.PUSH_SUCCESS.getValue());
            update.setId(payment.getId());
            pushSystemDep.paymentWriteService.update(update);
            return Optional.empty();
        }

        if ("t".equals(Optional.ofNullable(shopOrder.getExtra()).orElseGet(HashMap::new).getOrDefault(ShopOrderExtra.declareOk.name(), "f"))) {
            log.debug("{} shopOrderId[{}] has declare success with extra[{}], won't declare again", LogUtil.getClassMethodName(), orderId, shopOrder.getExtra());
            return Optional.of(taker -> {
            });
        }
        return Optional.of(baseDeclareRequest(payment, shopOrder, skuOrderList));
    }

    /**
     * 默认申报处理器
     *
     * @param payment      支付单
     * @param shopOrder    订单
     * @param skuOrderList 子订单
     * @return 处理器
     */
    private Consumer<Consumer<PaymentDeclareRequest>> baseDeclareRequest(Payment payment, ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        MercuryPayCustomsDeclareModel model = generateDefaultDeclareModel(payment, shopOrder, skuOrderList);
        // 海关，当前澳新都记为上海关区，由于未分仓这里还不知道货物从哪个关区发
        // 默认采取上海关区
        Stream.of("alipay", "wechat").filter(payment.getChannel()::contains)
                .map(channel -> channel.endsWith("pay") ? channel : channel + "pay")
                .findFirst().ifPresent(model::setChannel);
        if ("umf".equals(payment.getChannel())) {
            model.setChannel("umf");
        }
        if (PaymentChannelEnum.ALLINPAY.getCode().equals(payment.getChannel()) ||
                PaymentChannelEnum.ALLINPAY_YST.getCode().equals(payment.getChannel())) {
            model.setChannel("TONGLIAN");
        }

        // 支付方式需要映射内部系统的值
        Map<String, String> payTypeMap = ImmutableMap.<String, String>builder()
                .put("jsapi", "jsapi").put("app", "app")
                .put("pc", "pc").put("wap", "wap")
                .put("qr", "native")
                .put("umf", "umf")
                .put(PaymentChannelEnum.ALLINPAY.getCode(), "TONGLIAN")
                .put(PaymentChannelEnum.ALLINPAY_YST.getCode(), "TONGLIAN")
                .build();
        payTypeMap.keySet().stream().filter(payment.getChannel()::contains).findFirst()
                .map(payTypeMap::get).ifPresent(model::setPayType);

        PaymentDeclareRequest request = new PaymentDeclareRequest();
        request.setBizModel(model);
        request.setNotifyUrl(pushSystemDep.mercuryPayCustomsNotify);
        log.info("支付单申报前的请求参数 {}", JSONUtil.toJsonStr(request));
        return (executor -> executor.accept(request));
    }

    /**
     * 生产申报基础DTO
     *
     * @param payment      支付单
     * @param shopOrder    订单
     * @param skuOrderList 单品订单
     * @return 申报器
     */
    private MercuryPayCustomsDeclareModel generateDefaultDeclareModel(Payment payment, ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        // 生成模型处理器
        MercuryPayCustomsDeclareModelV2 model = new MercuryPayCustomsDeclareModelV2();
        model.setBankNo(payment.getPaySerialNo());
        model.setEbpCode("该字段目前无用处");
        model.setOrderNo(payment.getOutId());
        model.setOutOrderNo(payment.getOutId());
        model.setReldeclareOrderNoList(findRelatedDeclareIds(payment.getId()));
        model.setPayMerchantOutNo(PaymentExtraIndexEnum.getAllInPayCusId(payment));

        // 如果是联动的 则不更新支付单号
        model.setDeclareOrderNo(pushSystemDep.y800OrderIdGenerator.getDeclareId(shopOrder));
        if (Objects.equals(payment.getChannel(), "umf")) {
            // revoke the seq update
            model.setDeclareOrderNo(revokeSeq(model.getDeclareOrderNo()));
        }
        /// 设置真实关区
        if (pushSystemDep.environmentConfig.isOnline() || !OrderOutFrom.WE_SHOP.Code().equals(shopOrder.getOutFrom())) {
//            model.setCustoms(hasNoCustoms(shopOrder) ? pushSystemDep.depotCustomManager.getCustomCodeByOrder(shopOrder) : shopOrder.getDepotCustomName());
            if (hasNoCustoms(shopOrder)) {
                // 不应该走这个逻辑
                String customs = pushSystemDep.depotCustomManager.getCustomCodeByOrder(shopOrder);
                log.info("ccs推送口岸 {} 无口岸则重新获取", customs);
                model.setCustoms(customs);
            } else {
                String customs = shopOrder.getDepotCustomName();
                log.info("ccs推送口岸 {}", customs);
                model.setCustoms(customs);
            }
        } else {
            model.setCustoms("HANGZHOU");
        }
        // > 重置非跨境关区的字段
        if (DepotCustomManager.originCustomConfig.DOMESTIC.getCode().equals(model.getCustoms())) {
            model.setCustoms(null);
        }
        log.debug("推单时使用的口岸 {} shopOrderId {} Custom {}", LogUtil.getClassMethodName("set-custom"), shopOrder.getId(), model.getCustoms());

        List<MercuryPayCustomsGoodsInfo> goodsInfoList = generateGoodsInfo(skuOrderList);
        model.setGoodsInfoList(goodsInfoList);
        model.setSplitFlag(true);
        // 备案信息在支付系统设置
        model.setAmount(new BigDecimal(shopOrder.getFee()).divide(new BigDecimal(100), 2, RoundingMode.DOWN).toString());
        model.setCommodityFee(new BigDecimal(shopOrder.getFee()).divide(new BigDecimal(100), 2, RoundingMode.DOWN).toString());
        model.setTransportFee("0");
        // 设置支付人信息
        PayerInfo payerInfo = mongoTemplate.findOne(Query.query(Criteria.where("orderId").is(shopOrder.getId())), PayerInfo.class);
        if (payerInfo == null) {
            // 使用老版本信息
            Optional.ofNullable(shopOrder.getExtra()).map(extra -> extra.get(ShopOrderExtra.payerName.name())).ifPresent(model::setBuyerName);
            Optional.ofNullable(shopOrder.getExtra()).map(extra -> extra.get(ShopOrderExtra.payerNo.name())).map(String::toUpperCase).ifPresent(model::setBuyerIdNo);
        } else {
            Key key = EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey);
            PayerInfo.Helper.Info info = PayerInfo.Helper.decodeWithoutCache(key, payerInfo);
            model.setBuyerName(info.getName());
            model.setBuyerIdNo(info.getNo());
        }
        model.setRequestMessage(payment.getPayRequest());
        model.setResponseMessage(payment.getPayResponse());

        model.setTaxFee(calculateTax(shopOrder, skuOrderList));

        log.info("OrderDeclareServiceImpl.generateDefaultDeclareModel, 构造申报基础DTO, shopOrderId={}, result mode={}",
                shopOrder.getId(), JSON.toJSONString(model));
        return model;
    }

    private List<String> findRelatedDeclareIds(Long paymentId) {
        var list = findOrderByPaymentId(paymentId);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.stream().map(ShopOrder::getDeclaredId).collect(Collectors.toList());
    }

    /**
     * 算税费，恶心了
     *
     * @param shopOrder
     * @param skuOrders
     * @return
     */
    private BigDecimal calculateTax(ShopOrder shopOrder, List<SkuOrder> skuOrders) {
        // 京东云交易，不算税
        if (skuOrders.stream().allMatch(skuOrder ->
                Objects.equals(skuOrder.getShippingWarehouseType(), SkuOrderShippingWarehouseTypeEnum.JD_CLOUD_TRANSACTION.getValue()))) {
            return BigDecimal.ZERO;
        }

        // 全家福的直接 *0.091
        if (StrUtil.isNotBlank(shopOrder.getOutFrom()) && shopOrder.getOutFrom().equals(OrderOutFrom.COMMUNITY_OPERATION.getOrderOutFormCode())) {
            return BigDecimal.valueOf(shopOrder.getFee()).multiply(BigDecimal.valueOf(0.091)).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);
        }

        long realShopOriginFee = 0;
        realShopOriginFee += skuOrders.stream().mapToLong(SkuOrder::getOriginFee).sum();

        var shareDiscount = getShareDiscount(shopOrder, skuOrders);

        // 同v2推送订单一样，计算每个商品项的税费
        List<PushOrderItem> itemList = new ArrayList<>();
        for (int i = 0; i < skuOrders.size(); i++) {
            SkuOrder skuOrder = skuOrders.get(i);

            PushOrderItem pushOrderItem = pushSystemDep.orderTaxSplitManager.makePushOrderItem_M(skuOrder,
                    shareDiscount.get(i), shopOrder.getShipFee().longValue(), realShopOriginFee, shopOrder.getOutFrom()).take();

            itemList.add(pushOrderItem);
        }

        // 汇总
        return itemList.stream()
                .map(PushOrderItem::getTax)
                .filter(Objects::nonNull)
                .reduce(new BigDecimal("0.000"), BigDecimal::add);
    }

    private List<BigDecimal> getShareDiscount(ShopOrder shopOrder, List<SkuOrder> skuOrders) {
        Integer shopDiscount = shopOrder.getDiscount() == null ? 0 : shopOrder.getDiscount();
        Integer shipDiscount = shopOrder.getOriginShipFee() - shopOrder.getShipFee();
        BigDecimal needShareDiscount = BigDecimal.valueOf(shopDiscount + shipDiscount).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);

        List<BigDecimal> originPrice = new ArrayList<>();
        for (SkuOrder skuOrder : skuOrders) {
            originPrice.add(BigDecimal.valueOf(skuOrder.getOriginFee()).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN));
        }

        return ShareUtils.share(needShareDiscount, originPrice, 1);
    }

    /**
     *  只要满足 任意一个条件，就返回 true
     *      depotCustomName 是空/空白
     *      depotCustomName 等于 CUSTOM_NOT_FOUND
     * @param shopOrder
     * @return
     */
    private boolean hasNoCustoms(ShopOrder shopOrder) {
        var customs = shopOrder.getDepotCustomName();
        return !StringUtils.hasText(customs) || CUSTOM_NOT_FOUND.equals(shopOrder.getDepotCustomName());
    }

    private String revokeSeq(String declareOrderNo) {
        OutSystemIdProvider.OutSystemIdContainer container = outSystemIdProvider.decode(declareOrderNo);
        if (container.getSeq() == 0) {
            return declareOrderNo;
        }
        int idLen = (container.getId() + "").length();
        int startAt = declareOrderNo.length() - 3 - idLen;
        return declareOrderNo.substring(0, startAt) + "0" + declareOrderNo.substring(startAt + 1);
    }

    /**
     * 生成商品申报列表
     *
     * @param skuOrderList 单品订单列表
     * @return 列表
     */
    private List<MercuryPayCustomsGoodsInfo> generateGoodsInfo(List<SkuOrder> skuOrderList) {
        List<MercuryPayCustomsGoodsInfo> goodsInfoList = new ArrayList<>();
        for (SkuOrder skuOrder : skuOrderList) {
            MercuryPayCustomsGoodsInfo goodsInfo = new MercuryPayCustomsGoodsInfo();
            String link = pushSystemDep.webUrl + "/item/snapshot?skuOrderId=" + skuOrder.getId();
            goodsInfo.setGname(skuOrder.getItemName());
            goodsInfo.setItemLink(link);
            goodsInfoList.add(goodsInfo);
        }
        return goodsInfoList;
    }

    /**
     * 判断是否需要跳过支付单申报
     * 由于订单已经由仓库拆单, 因此不需要重复判断, 只要有一单是 则全是
     *
     * @param skuOrderList 订单列表
     * @return 是否需要
     */
    private boolean shouldSkipDeclare(List<SkuOrder> skuOrderList) {
        if (skuOrderList.isEmpty()) {
            return false;
        }
        SkuOrder skuOrder = skuOrderList.get(0);
        if (!Objects.equals(1, skuOrder.getIsThirdPartyItem())) {
            return false;
        }
        if (Objects.equals(skuOrder.getIsThirdPartyItem(), ThirdPartyItemType.MANUAL_PARTY_ITEM.getType())) {
            return true;
        }
        Optional<Integer> thirdPartySystemIdOpt = Optional.ofNullable(skuOrder.getTags()).map(tags -> tags.getOrDefault(SkuTagIndex.pushSystem.name(), "")
                .split(",")[0]).map(Integer::parseInt);
        if (!thirdPartySystemIdOpt.isPresent()) {
            return false;
        }
        List<ThirdPartySkuStock> skuStockList = pushSystemDep.thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(skuOrder.getShopId(), thirdPartySystemIdOpt.get(), skuOrder.getOuterSkuId()).getResult();
        if (CollectionUtils.isEmpty(skuStockList)) {
            return false;
        }
        Optional<ThirdPartySkuStock> thirdPartySkuStockOpt = skuStockList.stream().filter(stock -> Objects.equals(1, stock.getStatus()) && Objects.nonNull(stock.getDepotCode()))
                .findFirst();
        boolean skipDeclare = thirdPartySkuStockOpt.map(ThirdPartySkuStock::getDepotCode).filter(pushSystemDep.skipDeclareDepotCustomCodeHashSet::contains).isPresent();
        if (skipDeclare) {
            if (!pushSystemDep.orderWriteService.updateSkuOrderPushStatusByOrderId(skuOrder.getOrderId(), SkuOrderPushStatus.WAITING.value(), SkuOrderPushStatus.WAIT_PAYMENT_PUSH_PASS.value()).isSuccess()) {
                log.warn("{} rewind skuPushStatus failed,orderId:{}", LogUtil.getClassMethodName(), skuOrder.getOrderId());
            }
            log.debug("{} skip declare by stock data [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(thirdPartySkuStockOpt.get()));
        }
        return skipDeclare;
    }

    /**
     * 不是跨境订单
     *
     * @param payment 支付单
     * @return 不是跨境
     */
    private boolean notBoundOrder(Payment payment) {
        List<SkuOrder> skuOrderList = new ArrayList<>();
        for (OrderBase orderBase : pushSystemDep.paymentReadService.findOrdersByPaymentId(payment.getId()).getResult()) {
            if (orderBase instanceof ShopOrder) {
                skuOrderList.addAll(pushSystemDep.skuOrderReadService.findByShopOrderId(orderBase.getId()).getResult());
            }
            if (orderBase instanceof SkuOrder) {
                skuOrderList.add((SkuOrder) orderBase);
            }
        }
        return skuOrderList.stream().map(SkuOrder::getIsBonded).filter(Objects::nonNull)
                .map(BondedType::fromInt).map(BondedType::isBonded).noneMatch(Predicate.isEqual(true));
    }

    /**
     * 是否需要延迟推送
     * 判断其支付时间
     *
     * @param payment 订单
     */
    @Override
    public Boolean needDelay(Payment payment) {
        List<OrderPayment> orderPaymentList = pushSystemDep.paymentReadService.findOrderIdsByPaymentId(payment.getId()).getResult();
        if (orderPaymentList == null || orderPaymentList.isEmpty()) {
            return false;
        }
        AtomicBoolean shouldDelay = new AtomicBoolean(false);
        Long shopId = NumberUtil.parseNumber(payment.getPayAccountNo(), Long.TYPE).orElseGet(() -> {
            OrderBase orderBody = pushSystemDep.orderReadLogic.findOrder(orderPaymentList.get(0).getOrderId(), orderPaymentList.get(0).getOrderLevel());
            if (Optional.ofNullable(orderBody).map(OrderBase::getOutFrom).filter(OrderOutFrom.SUB_STORE.Code()::equals).isPresent()) {
                shouldDelay.set(!payment.getPaidAt().before(new DateTime(Date.from(Instant.now())).minusMinutes(60).toDate()));
            }
            return Optional.ofNullable(orderBody).map(OrderBase::getShopId).orElse(null);
        });
        /*
         * 由shopId查询
         */
        if (shopId == null) {
            return shouldDelay.get();
        }
        Shop shop = pushSystemDep.shopCacheHolder.findShopById(shopId);
        if (shop.getExtra() != null && !"".equals(shop.getExtra().getOrDefault(ShopExtra.delayPaymentPush.getCode(), ""))) {
            Integer delayMinutes = NumberUtil.parseNumber(shop.getExtra().get(ShopExtra.delayPaymentPush.getCode()), Integer.TYPE).orElse(0);
            if (!payment.getPaidAt().before(new DateTime(Date.from(Instant.now())).minusMinutes(delayMinutes).toDate())) {
                shouldDelay.set(true);
            }
        }
        return shouldDelay.get();
    }

    /**
     * 第三平台支付信息上传
     *
     * @param payment 支付信息
     */
    @Override
    public void payInfoPushV1(Payment payment) {
        if (payment == null) {
            return;
        }
        if (redissonClient == null) {
            log.error("{} lock system is down for payment [{}]", LogUtil.getClassMethodName(), payment.getId());
            payInfoPushV1WithoutLock(payment);
            return;
        }
        String lockName = getPaymentLock.apply(payment.getId());
        Lock lock = redissonClient.getLock(lockName);
        TaskWithLock.processWithLock(lock, lockName, payment, payment.getId(), this::payInfoPushV1WithoutLock);
    }

    /**
     * 支付单上传
     *
     * @param waitPush 等待推送支付单
     */
    public void payInfoPushV1WithoutLock(Payment waitPush) {
        Payment payment = syncPaymentPushStatus(waitPush).orElse(waitPush);
        if (!ifPaymentContainVerDeptInfo(payment)) {
            log.warn("OrderDeclareServiceImpl.payInfoPushV1WithoutLock, paymentId={}, payment not carry verDepot info[verDept => {}, payTransactionId => {}]",
                    payment.getId(), payment.getVerDept(), payment.getPayTransactionId());
            return;
        }
        // 寻找可以推送的订单
        Optional<List<ShopOrder>> relatedShopOrderList = findAllowPushShopOrderList(payment);
        if (relatedShopOrderList.isEmpty()) {
            log.info("OrderDeclareServiceImpl.payInfoPushV1WithoutLock, paymentId={}, no need payment push", payment.getId());
            return;
        }

        // 打包可以使用的信息实体
        List<ShopOrder> shopOrderList = relatedShopOrderList.get();
        Optional<PayInfoPushCondition> pushConditionOpt = buildPayInfoPushCondition(payment, shopOrderList);
        if (!pushConditionOpt.isPresent()) {
            log.info("OrderDeclareServiceImpl.payInfoPushV1WithoutLock, no payInfo push need for paymentId={}", payment.getId());
            return;
        }

        log.debug("OrderDeclareServiceImpl.payInfoPushV1WithoutLock, paymentId={}, PayInfoPushCondition={}",
                payment.getId(), pushConditionOpt.map(JSON::toJSONString).orElse("null"));
        Either<Boolean> result = null;
        if (isY800V3Order(relatedShopOrderList)) {
            result = pushByV3(shopOrderList.get(0).getShopId(), payment, pushConditionOpt.get());
        } else {
            // 发送请求
            HttpRequest request = HttpRequest.post(pushSystemDep.y800YangSupport);
            pushConditionOpt.map(this::packIntoFormMap).ifPresent(request::form);
            result = parseFromRequest(request, payment);
        }

        // 开始判断请求结果
        if (result == null || !result.isSuccess() || !Boolean.TRUE.equals(result.getResult())) {
            // 请求失败了
            payment.setPushStatus(PaymentPushStatus.PUSH_FAIL.getValue());
            if (syncPaymentPushStatus(payment).isPresent()) {
                return;
            }
            pushSystemDep.paymentWriteService.update(payment);
            EventSender.sendApplicationEvent(new PaymentPushFailedNotify(payment.getId()));
            JSONObject msg = new JSONObject();
            msg.set("paymentId", payment.getId());
            msg.set("exceptionType", OrderExceptionEnum.UPLOAD_PAYMENT_ORIGINAL_INFO_FAILED.getCode());
            msg.set("exceptionMessage", "上传支付单信息失败");
            rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.ORDER_EXCEPTION_RECORD_TAG, msg.toString());
        } else {
            paymentInfoPushSuccess(payment, shopOrderList);
            EventSender.sendApplicationEvent(new PaymentPushSuccessNotify(payment.getId(), new Date()));
        }
    }

    private Either<Boolean> pushByV3(Long shopId, Payment payment, PayInfoPushCondition request) {
        try (Y800V3Api apiV3 = y800V3Api) {
            var thirdPartyUserShop = pushSystemDep.thirdPartyUserShopReadService.findByThirdPartyIdAndShopId(
                    ThirdPartySystem.Y800_V3.Id(), shopId).getResult();
            apiV3.setAppId(thirdPartyUserShop.getThirdPartyCode());
            apiV3.setSecret(thirdPartyUserShop.getThirdPartyKey());

            request.setAccessCode(thirdPartyUserShop.getExtra().get(ThirdPartyUserShopExtraIndex.ACCESS_CODE.getCode()));

            var result = apiV3.pushPayInfo(request);
            log.debug("OrderDeclareServiceImpl.pushByV3, paymentId={}, result={}", payment.getId(), JSON.toJSONString(result));

            return result;
        } catch (Exception ex) {
            log.error("OrderDeclareServiceImpl.pushByV3 error, paymentId={}", payment.getId(), ex);
            return Either.error(ex.getMessage());
        }
    }

    private Either<Boolean> parseFromRequest(HttpRequest request, Payment payment) {
        if (!request.ok()) {
            log.error("OrderDeclareServiceImpl.payInfoPushV1WithoutLock, paymentId={}, payInfoPushV1 request is reject status={}, message={}",
                    payment.getId(), request.code(), JSON.toJSONString(request.message()));
            return Either.error(request.message());
        }

        Map<String, Object> map = JSON.parseObject(request.body(), new TypeReference<>() {
        });
        log.debug("OrderDeclareServiceImpl.payInfoPushV1WithoutLock, paymentId={}, request.body()={}", payment.getId(), JSON.toJSONString(map));

        if ("success".equals(map.get("code").toString()) || map.get("errorMsg").toString().contains("信息已经存在") ||
                map.get("errorMsg").toString().contains("已存在") || map.get("errorMsg").toString().contains("重复")) {
            // 成功
            return Either.ok(true);
        } else {
            // 失败
            log.warn("OrderDeclareServiceImpl.payInfoPushV1WithoutLock, paymentId={}, payInfoPushV1.errorMsg={}, response={}",
                    payment.getId(), JSON.toJSONString(map.get("errorMsg")), JSON.toJSONString(map));
            return Either.error(request.message());
        }
    }

    /**
     * 是否全部为api-v3接口的订单
     *
     * @param shopOrders
     * @return
     */
    private boolean isY800V3Order(Optional<List<ShopOrder>> shopOrders) {
        if (shopOrders.isEmpty() || CollectionUtils.isEmpty(shopOrders.get())) {
            return false;
        }

        return shopOrders.get().stream()
                .allMatch(shopOrder -> y800V3CustomsClearancePushService.isY800V3Order(shopOrder));
    }

    /**
     * 判断支付单是否携带校验机构数据
     *
     * @param payment 订单
     * @return 是否携带
     */
    private boolean ifPaymentContainVerDeptInfo(Payment payment) {
        if (payment.getVerDept() == null || payment.getPayTransactionId() == null) {
            log.warn("{} paymentId:{} push without verDept or payTransactionId", LogUtil.getClassMethodName(), payment.getId());
            if (payment.getPushStatus() != PaymentPushStatus.DECLARE_SUCCESS.getValue()) {
                log.error("{} paymentId:{} push pay info reject because no declared Msg", LogUtil.getClassMethodName(), payment.getId());
                //  只有在目前是最新的pushStatus时才会发送失败提醒
                if (!syncPaymentPushStatus(payment).isPresent()) {
                    EventSender.sendApplicationEvent(new PaymentPushFailedNotify(payment.getId()));
                }
                return false;
            }
        }
        return true;
    }

    /**
     * 支付信息推送成功, 标记订单推送状态为继续推送
     *
     * @param payment       支付单
     * @param shopOrderList 订单
     */
    private void paymentInfoPushSuccess(Payment payment, List<ShopOrder> shopOrderList) {
        // 支付信息还是照常往洋800推，推送状态由推支付系统结果决定
        log.debug("{} rewind skuOrder push Status of paymentId:{}", LogUtil.getClassMethodName("push-success"), payment.getId());
        try {
            for (ShopOrder shopOrder : shopOrderList) {
                if (!pushSystemDep.orderWriteService.updateSkuOrderPushStatusByOrderId(shopOrder.getId(), SkuOrderPushStatus.WAITING.value(), SkuOrderPushStatus.WAIT_PAYMENT_PUSH_PASS.value()).isSuccess()) {
                    log.warn("{} rewind skuPushStatus failed,orderId:{} paymentId:{}", LogUtil.getClassMethodName(), shopOrder.getId(), payment.getId());
                }
                orderWriteService.updateOrderPushException(shopOrder.getId(), OrderExceptionEnum.CLEAR_ERROR.getCode(), OrderExceptionEnum.CLEAR_ERROR.getMessage());
            }
        } catch (Exception ex) {
            log.error("{}", LogUtil.getClassMethodName(), ex);
        }
        Payment update = new Payment();
        update.setId(payment.getId());
        update.setPushStatus(PaymentPushStatus.PUSH_SUCCESS.getValue());
        pushSystemDep.paymentWriteService.update(update);
    }

    /**
     * 打包出Http请求使用的Form Map
     *
     * @param payInfoPushCondition 推送支付单使用的原始DTO
     * @return Form Map
     */
    private Map<String, Object> packIntoFormMap(PayInfoPushCondition payInfoPushCondition) {
        String jsonStr = JSON.toJSONString(payInfoPushCondition);
        String bizData = new String(Base64.getEncoder().encode(jsonStr.getBytes()));
        String partnerId = this.pushSystemDep.y800PartnerCode;
        String token = this.pushSystemDep.y800PartnerKey;

        String v = "2.0";
        String serviceName = "push.pay.info";
        String dataBuffer = "bizData=" + bizData +
                "partnerId=" + partnerId +
                "serviceName=" + serviceName +
                "v=" + v +
                token;
        String sign = MD5Util.MD5(dataBuffer);
        log.debug("{} sign:{}", LogUtil.getClassMethodName(), Optional.ofNullable(sign).

                map(String::toLowerCase));

        Map<String, Object> params = new HashMap<>(8);
        params.put("sign", sign);
        params.put("v", v);
        params.put("partnerId", partnerId);
        params.put("serviceName", serviceName);
        params.put("bizData", bizData);
        log.debug("OrderDeclareServiceImpl.packIntoFormMap, push params={}", JSON.toJSONString(params));
        return params;
    }

    /**
     * 查找到允许推送的订单列表
     *
     * @param payment 主支付单
     * @return 允许推送的订单列表
     */
    private Optional<List<ShopOrder>> findAllowPushShopOrderList(Payment payment) {
        if (payment.getPushStatus() != PaymentPushStatus.DECLARE_SUCCESS.getValue()
                && payment.getPushStatus() != PaymentPushStatus.PUSH_FAIL.getValue()) {
            log.warn("{} payment[{}] not suit push-status [{}] but at [{}]", LogUtil.getClassMethodName(), payment.getId(), PaymentPushStatus.DECLARE_SUCCESS, payment.getPushStatus());
            return Optional.empty();
        }
        log.debug("{} ready to push {}", LogUtil.getClassMethodName(), JSON.toJSONString(payment));
        if (Objects.equals(payment.getChannel(), PayChannelsConstants.INTEGRAL_PAY)) {
            log.warn("{} payment [{}] channel [{}] not suit to PayInfoPush", LogUtil.getClassMethodName(), payment.getId(), payment.getChannel());
            // 如果需要订单推送 则将支付单设置为已推送完成
            return Optional.empty();
        }
        List<OrderPayment> orderPaymentList = pushSystemDep.paymentReadService.findOrderIdsByPaymentId(payment.getId()).getResult();
        List<ShopOrder> shopOrderList = new ArrayList<>();
        for (OrderPayment orderPayment : orderPaymentList) {
            if (orderPayment.getOrderType() == 1) {
                // 店铺级别  2019/01/07：目前支付单主要与店铺级别订单关联,这里以店铺级别订单推送
                shopOrderList.add(pushSystemDep.shopOrderReadService.findById(orderPayment.getOrderId()).getResult());
            } else {
                // SKU级别
                log.warn("{} not support for skuOrder by payment[{}]", LogUtil.getClassMethodName(), payment.getId());
            }
        }
        return shopOrderList.isEmpty() ? Optional.empty() : Optional.of(shopOrderList);
    }

    /**
     * 查询订单是否需要审核
     *
     * @param skuOrderList 子订单
     * @return 需要审核的子订单的主订单号
     */
    private Optional<Long> ifNeedAuth(List<SkuOrder> skuOrderList) {
        for (SkuOrder skuOrder : skuOrderList) {
            if (Objects.equals(SkuOrderPushStatus.WAITING_SELLER_AUTH.value(), skuOrder.getPushStatus())) {
                return Optional.ofNullable(skuOrder.getOrderId());
            }
        }
        return Optional.empty();
    }

    /**
     * 打包出支付单推送信息实体
     *
     * @param payment       支付单信息
     * @param shopOrderList 订单列表
     * @return 推送用的实体
     */
    private Optional<PayInfoPushCondition> buildPayInfoPushCondition(Payment payment, List<ShopOrder> shopOrderList) {
        if (emptyNormalStatusOrder(payment, shopOrderList)) {
            return Optional.empty();
        }
        final Long shopId = shopOrderList.get(0).getShopId();

        PayInfoPushCondition payInfoPushCondition = new PayInfoPushCondition();
        // warn: 平台Key 是否需要变化?
        payInfoPushCondition.setPlatform("WEBSC");

        // todo : 为供销设置特殊值
        // accessCode 便是ThirdPartyCode 不需要Secret
        String accessCode = findAccessCodeByShopId(shopId);
        if (ObjectUtils.isEmpty(accessCode)) {
            log.error("{} REQUIRE ACCESS_CODE for paymentId:{} shopOrderId:{} shopId:{}"
                    , LogUtil.getClassMethodName(), payment.getId()
                    , shopOrderList.get(0).getId(), shopOrderList.get(0).getShopId());
        }

        // 设置访问码
        payInfoPushCondition.setAccessCode(accessCode);

        // 为供销设置特殊值
        if (OrderOutFrom.WE_SHOP.Code().equals(shopOrderList.get(0).getOutFrom())) {
            if (Objects.equals(sourceShopQuerySlice.queryShopIdByOutShopCodeAndSource(null, MirrorSource.GongXiao.name()).orElse(-1L), shopId)) {
                thirdPartyUserReadService.findByTypeAndUserId(ThirdPartyUserType.OMS.getType(), shopOrderList.get(0).getReferenceId())
                        .map(ThirdPartyUser::getThirdPartyId).ifSuccess(userId -> payInfoPushCondition.setAccessCode("webB2C" + userId)).take();
            }
        }

        // 如果未配置支付信息,那么便使用分销商场信息
        // 注意 禁止随意删除支付信息
        setPayWayAndRecpAccountFromPayment(payInfoPushCondition, payment, shopId);
        // 初始化使用代码配置的社会信用代码
        RecpCodeAndNameDefaultSet.initRecpCodeAndNameFromShopId(payInfoPushCondition, shopId);
        // 试图使用支付单的配置
        usePaymentRecpCodeAndName(payInfoPushCondition, payment, shopId);
        // 打包支付单的基础信息
        packPayInfoPushConditionWithNormalPaymentInfo(payInfoPushCondition, payment);
        // 打包订单的货物信息
        Optional<List<PayInfoPushOrderGoodsInfo>> payInfoPushOrderGoodsInfoListOpt = buildPayInfoOrderGoodsInfo(payment, shopOrderList);
        if (!payInfoPushOrderGoodsInfoListOpt.isPresent()) {
            log.warn("{} not suit goods found for Payment Info Push, payment[{}]", LogUtil.getClassMethodName(), payment.getId());
            return Optional.empty();
        }
        List<PayInfoPushOrderGoodsInfo> orderGoodsInfoList = payInfoPushOrderGoodsInfoListOpt.get();
        payInfoPushCondition.setOrderGoodsInfoList(orderGoodsInfoList);
        return Optional.of(payInfoPushCondition);
    }

    /**
     * 将订单内货物信息与外部订单号打包进入支付信息推送实体
     *
     * @param payment       支付单
     * @param shopOrderList 订单列表
     * @return 货物订单信息
     */
    private Optional<List<PayInfoPushOrderGoodsInfo>> buildPayInfoOrderGoodsInfo(Payment payment, List<ShopOrder> shopOrderList) {
        List<PayInfoPushOrderGoodsInfo> orderGoodsInfoList = new ArrayList<>();
        for (ShopOrder shopOrder : shopOrderList) {
            PayInfoPushOrderGoodsInfo orderGoodsInfo = new PayInfoPushOrderGoodsInfo();
            Optional<String> orderSn = generateOrderSnFromShopOrder(shopOrder);
            if (!orderSn.isPresent()) {
                log.error("{} fail to generate OrderSn for shopOrder[{}] at push payment[{}] info", LogUtil.getClassMethodName(), shopOrder.getId(), payment.getId());
                return Optional.empty();
            }
            orderSn.ifPresent(orderGoodsInfo::setOrderSn);
            if (payment.getChannel().equals("umf")) {
                orderGoodsInfo.setOrderSn(revokeSeq(orderGoodsInfo.getOrderSn()));
            }
            List<SkuOrder> skuOrderList = pushSystemDep.skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
            Optional<Long> needAuthOrderId = ifNeedAuth(skuOrderList);
            if (needAuthOrderId.isPresent()) {
                log.warn("{} order[{}] need auth so pause the payment[{}] push", LogUtil.getClassMethodName(), needAuthOrderId.get(), payment.getId());
                Payment update = new Payment();
                update.setPushStatus(PaymentPushStatus.NEED_AUTH.getValue());
                pushSystemDep.paymentWriteService.update(update);
                return Optional.empty();
            }

            List<PayInfoPushGoodsInfo> goodsInfoList = new ArrayList<>();
            for (SkuOrder skuOrder : skuOrderList) {
                PayInfoPushGoodsInfo goodsInfo = new PayInfoPushGoodsInfo();
                String link = pushSystemDep.webUrl + "/item/snapshot?skuOrderId=" + skuOrder.getId();
                goodsInfo.setGname(skuOrder.getItemName());
                goodsInfo.setItemLink(link);
                goodsInfoList.add(goodsInfo);
            }
            orderGoodsInfo.setGoodsInfo(goodsInfoList);
            orderGoodsInfoList.add(orderGoodsInfo);
        }
        return Optional.of(orderGoodsInfoList);
    }

    /**
     * 生成支付单推送使用的外部订单号, 如果成功生成或者已经存在则直接返回, 不然将可能同步一次库存再生成
     *
     * @param shopOrder 订单信息
     * @return 外部订单号
     */
    private Optional<String> generateOrderSnFromShopOrder(ShopOrder shopOrder) {
        try {
            return Optional.of(pushSystemDep.y800OrderIdGenerator.getDeclareId(shopOrder));
        } catch (Exception ex) {
            log.warn("{} try to re-synchronize stock for acquire depot for orderId:{}", LogUtil.getClassMethodName(), shopOrder.getId(), ex);
            Optional<ThirdPartyUserShop> keyPair = Objects.requireNonNull(pushSystemDep.thirdPartyUserShopReadService.findByShopId(shopOrder.getShopId()).getResult()).stream().filter(thirdPartyUserShop -> ThirdPartySystem.Y800_V2.Id().equals(thirdPartyUserShop.getThirdPartyId()))
                    .findFirst();
            if (!keyPair.isPresent()) {
                log.error("{} failed to acquire ThirdPartySystem-Y800 keyPair to resynchronize for acquire shopOrder-depot (orderId:{}) shopId:{}", LogUtil.getClassMethodName(), shopOrder.getId(), shopOrder.getShopId());
                return Optional.empty();
            }
            try {
                pushSystemDep.thirdPartyJobService.synchronize(ThirdPartySystem.Y800_V2, keyPair.get());
            } catch (Exception reason) {
                log.error("{} failed to sync stock for shopId:{} for gain depot to acquire DeclareId for orderId:{}", LogUtil.getClassMethodName(), shopOrder.getShopId(), shopOrder.getId(), reason);
                return Optional.empty();
            }
            return Optional.of(pushSystemDep.y800OrderIdGenerator.getDeclareId(shopOrder));
        }
    }

    /**
     * 将支付单内普通的订单信息打包进入到推送实体中
     *
     * @param payInfoPushCondition 支付单推送实体
     * @param payment              支付单信息
     */
    private void packPayInfoPushConditionWithNormalPaymentInfo(PayInfoPushCondition payInfoPushCondition, Payment payment) {
        payInfoPushCondition.setTotalAmount(new BigDecimal(payment.getFee()).divide(BigDecimal.valueOf(100)));
        payInfoPushCondition.setCurrency("142");
        payInfoPushCondition.setPayType("1");
        payInfoPushCondition.setPayRequest(payment.getPayRequest());
        payInfoPushCondition.setPayResponse(payment.getPayResponse());
        payInfoPushCondition.setPayTransactionId(payment.getPaySerialNo());
        payInfoPushCondition.setTradeTime(Optional.ofNullable(payment.getPaidAt()).map(Date::getTime).map(String::valueOf).orElse(null));
        // 设置外部交易号
        if (!ObjectUtils.isEmpty(payment.getPayTransactionId())) {
            payInfoPushCondition.setPayTransactionId(payment.getPayTransactionId());
        }
        // 校验机构
        if (Objects.isNull(payment.getVerDept())) {
            return;
        }
        String verDeptCode;
        if (payment.getVerDept().contains(":")) {
            verDeptCode = payment.getVerDept().substring(payment.getVerDept().indexOf(":"));
            payInfoPushCondition.setVerDept(verDeptCode);
            return;
        }
        if (payment.getVerDept().length() > 1) {
            verDeptCode = "3";
            if (payment.getVerDept().startsWith("UNIONPAY")) {
                verDeptCode = "1";
            }
            if (payment.getVerDept().startsWith("NETSUNION")) {
                verDeptCode = "2";
            }
        } else {
            verDeptCode = payment.getVerDept();
        }
        payInfoPushCondition.setVerDept(verDeptCode);
    }

    /**
     * 查找可用的accessCode
     *
     * @param shopId 店铺Id
     * @return 可用的AccessCode
     */
    private String findAccessCodeByShopId(Long shopId) {
        return Optional.ofNullable(pushSystemDep.thirdPartyUserShopReadService.findByShopId(shopId).getResult())
                .map(Collection::stream)
                .orElse(Stream.empty())
                .filter(Objects::nonNull)
                .filter(account -> ThirdPartySystem.Y800_V2.Id().equals(account.getThirdPartyId()))
                .map(ThirdPartyUserShop::getThirdPartyCode).findFirst().orElse("");
    }

    /**
     * 主支付单关联的订单列表中是否没有正常的订单
     *
     * @param payment       支付单
     * @param shopOrderList 订单列表
     * @return 没有正常的
     */
    private boolean emptyNormalStatusOrder(Payment payment, List<ShopOrder> shopOrderList) {
        Set<Integer> notValidStatusForOrder = Stream.of(OrderStatus.BUYER_CANCEL, OrderStatus.SELLER_CANCEL, OrderStatus.DELETED
                        , OrderStatus.RETURN, OrderStatus.REFUND
                        , OrderStatus.REFUND_APPLY_AGREED, OrderStatus.RETURN_APPLY_AGREED
                        , OrderStatus.RETURN_CONFIRMED, OrderStatus.REFUND_PROCESSING)
                .map(OrderStatus::getValue).collect(Collectors.toSet());
        // 订单状态不正常 则不推送
        if (shopOrderList.stream().map(OrderBase::getStatus).allMatch(notValidStatusForOrder::contains)) {
            Payment update = new Payment();
            update.setId(payment.getId());
            update.setPushStatus(1);
            log.debug("{} payment(id={}) close push because all order is closed,res:{}", LogUtil.getClassMethodName(), update.getId(), pushSystemDep.paymentWriteService.update(update));
            return true;
        }
        return false;
    }

    /**
     * 为推送实体设置支付方式和支付收款人信息
     *
     * @param payInfoPushCondition 推送实体
     * @param payment              支付单
     * @param shopId               店铺Id
     */
    private void setPayWayAndRecpAccountFromPayment(PayInfoPushCondition payInfoPushCondition, Payment payment, Long shopId) {
        Supplier<Optional<ShopPayInfo>> shopPaySupplier = () -> Optional.ofNullable(pushSystemDep.shopPayInfoReadService.findByShopIdAndPayChannel(shopId, payment.getChannel().split("-")[0]).getResult());
        String paymentChannel = payment.getChannel();
        // 设置支付方式 新老平台不一致
        boolean isNewPlatform = "old".equals(pushSystemDep.methodVersion.getOrDefault("pushPayInfo", "old")) && !pushSystemDep.forceNewVersionSet.contains(shopId);
        if (paymentChannel.contains("alipay") || paymentChannel.contains("mockpay")) {
            payInfoPushCondition.setPayWay(isNewPlatform ? "6" : "alipay");
            payInfoPushCondition.setRecpAccount(PaymentAccountUtil.genRecpAccount(payment.getPayRequest())
                    .orElse(shopPaySupplier.get().map(ShopPayInfo::getAccountNo)
                            .orElse("<EMAIL>")));

        } else if (paymentChannel.contains("wechat")) {
            // 设置支付方式 新老平台不一致
            payInfoPushCondition.setPayWay(isNewPlatform ? "5" : "wechatpay");
            payInfoPushCondition.setRecpAccount(PaymentAccountUtil.genRecpAccount(payment.getPayRequest())
                    .orElse(shopPaySupplier.get().map(ShopPayInfo::getMchId)
                            .orElse("**********")));
        } else if (paymentChannel.contains("umf")) {
            payInfoPushCondition.setPayWay(umfAccountWeb.config().getPayWay());
            payInfoPushCondition.setRecpAccount(umfAccountWeb.config().getRecpAccount());
        } else if (PaymentChannelEnum.ALLINPAY.getCode().equals(paymentChannel)) {
            payInfoPushCondition.setPayWay("tonglian");

            String recpAccount = PaymentExtraIndexEnum.getAllInPayCusId(payment);
            if (!StringUtils.hasText(recpAccount)) {
                var shopPayInfo = shopPaySupplier.get();
                if (shopPayInfo.isPresent()) {
                    recpAccount = shopPayInfo.get().getMchId();
                }
            }
            payInfoPushCondition.setRecpAccount(recpAccount);

        } else if (PaymentChannelEnum.ALLINPAY_YST.getCode().equals(paymentChannel)) {
            payInfoPushCondition.setPayWay("tonglian");
            payInfoPushCondition.setRecpAccount(PaymentExtraIndexEnum.getAllInPayCusId(payment));
        }
    }

    /**
     * 使用支付单携带的社会信用代码
     *
     * @param payInfoPushCondition 支付推送实体
     * @param payment              支付单信息
     */
    private void usePaymentRecpCodeAndName(PayInfoPushCondition payInfoPushCondition, Payment payment, Long shopId) {
        if (!ObjectUtils.isEmpty(payment.getRecpCode())) {
            if (StringUtils.hasText(payInfoPushCondition.getRecpCode())) {
                log.warn("{} shop[{}] can remove the recpCodeAtSystem", LogUtil.getClassMethodName(), shopId);
            }
            payInfoPushCondition.setRecpCode(payment.getRecpCode());
        }
        if (!ObjectUtils.isEmpty(payment.getRecpName())) {
            if (StringUtils.hasText(payInfoPushCondition.getRecpCode())) {
                log.warn("{} shop[{}] can remove the recpNameAtSystem", LogUtil.getClassMethodName(), shopId);
            }
            payInfoPushCondition.setRecpName(payment.getRecpName());
        }
    }

    public static class MercuryPayCustomsDeclareModelV2 extends MercuryPayCustomsDeclareModel {
        @Getter
        @Setter
        String outOrderNo;

        /**
         * 税费
         */
        @Getter
        @Setter
        private BigDecimal taxFee;

        /**
         * 支付流水号关联申报单号
         */
        @Getter
        @Setter
        private List<String> reldeclareOrderNoList;

        /**
         * 申报使用的商户号（非必填，不填时，ccs取其系统内的配置商户号）
         */
        @Getter
        @Setter
        private String payMerchantOutNo;
    }
}
