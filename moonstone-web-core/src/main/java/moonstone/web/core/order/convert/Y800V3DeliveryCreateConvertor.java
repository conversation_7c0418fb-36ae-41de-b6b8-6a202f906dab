package moonstone.web.core.order.convert;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.BondedType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.rpcAPI.y800Storage.Y800ShipmentOrder;
import moonstone.common.model.rpcAPI.y800Storage.Y800ShipmentOrderSku;
import moonstone.common.mongo.SkuComboRelationHistory;
import moonstone.common.mongo.SkuCustomHistory;
import moonstone.common.mongo.SkuHistory;
import moonstone.common.mongo.SkuHistoryData;
import moonstone.common.utils.EncryptHelper;
import moonstone.item.emu.ItemTypeEnum;
import moonstone.item.emu.SkuCustomTaxHolderEnum;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuCustomReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.Y800OrderIdGenerator;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.enu.SkuOrderShippingWarehouseTypeEnum;
import moonstone.order.enu.Y800V3PayCodeEnum;
import moonstone.order.model.*;
import moonstone.order.service.OutIdShopOrderReadService;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.User;
import moonstone.user.service.PayerInfoReadService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.fileNew.logic.SkuHistoryLogic;
import moonstone.web.core.order.service.impl.PushSystemDep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.Key;
import java.util.*;
import java.util.stream.Collectors;

/**
 * api-v3 发货单创建-模型转换
 */
@Slf4j
@Component
public class Y800V3DeliveryCreateConvertor {

    @Autowired
    private Y800OrderIdGenerator y800OrderIdGenerator;

    @Resource
    private ReceiverInfoReadService receiverInfoReadService;

    @Resource
    private OutIdShopOrderReadService outIdShopOrderReadService;

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private PayerInfoReadService payerInfoReadService;

    @Resource
	private UserReadService userReadService;


    @Autowired
    private EnvironmentConfig environmentConfig;

    @Autowired
    private PushSystemDep pushSystemDep;

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private SkuReadService skuReadService;

	@Resource
	private SkuCustomReadService skuCustomReadService;

	private static final BigDecimal HUNDRED = new BigDecimal("100");

    //一般贸易的订单
    private static final String BOND_TYPE_GENERAL = "general";
    //保税订单
    private static final String BOND_TYPE_BOND = "bond";
    //保税和完税商品的订单
    private static final String BOND_TYPE_MIX = "mix";

    public Y800ShipmentOrder convert(String whCode, String accessCode, ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        Y800ShipmentOrder target = new Y800ShipmentOrder();

        //"WEBSC"
        target.setSourcePlatform(pushSystemDep.getY800SourcePlatformV3());
        target.setAccessCode(accessCode);
        target.setWhCode(whCode);
        target.setThirdNo(y800OrderIdGenerator.getDeclareId(shopOrder));

        if (Objects.equals(OrderOutFrom.EXCEL_IMPORT.Code(), shopOrder.getOutFrom())) {
            outIdShopOrderReadService.findByShopOrderIdWithLimit(shopOrder.getId(), 1).orElseGet(ArrayList::new)
                    .stream().findFirst().map(OutIdShopOrder::getOutId).ifPresent(target::setSourceOrderNo);
        }

        //贸易类型
        target.setBondType(getBondType(skuOrderList));

        //收件人信息
        appendReceiverInfo(target, shopOrder);

        //支付相关的信息（跨境和混合模式订单必填）
        if (!target.getBondType().equals(BOND_TYPE_GENERAL)) {
            appendPaymentInfo(target, shopOrder, skuOrderList);
        }

        //商品信息
        target.setSkuList(convert(shopOrder, skuOrderList, target.getBondType()));

        //快递公司代码
        Optional.ofNullable(shopOrder.getExtra())
                .map(extra -> extra.get(ShopOrderExtra.shipCorp.name()))
                .ifPresent(target::setLogisticsCode);

        return target;
    }

    private void appendReceiverInfo(Y800ShipmentOrder target, ShopOrder shopOrder) {
        ReceiverInfo receiverInfo = receiverInfoReadService.findByOrderId(shopOrder.getId(), OrderLevel.SHOP).getResult().get(0);
        if (receiverInfo == null) {
            log.warn("Y800V3DeliveryCreateConvertor.appendPaymentInfo 收件人信息查询结果为空，shopOrderId={}", shopOrder.getId());
            return;
        }

        target.setReceiverMobile(receiverInfo.getMobile());
        target.setReceiverName(receiverInfo.getReceiveUserName());
        target.setReceiverProvince(receiverInfo.getProvince());
        target.setReceiverCity(receiverInfo.getCity());
        target.setReceiverDistrict(receiverInfo.getRegion());
        target.setReceiverDetailAddress(receiverInfo.getDetail());
    }

    /**
     * 填充支付相关的信息
     *
     * @param target
     * @param shopOrder
     * @param skuOrderList
     */
    private void appendPaymentInfo(Y800ShipmentOrder target, ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        //支付单的信息
        var payment = findPayment(shopOrder, skuOrderList);
        if (payment == null) {
            log.warn("Y800V3DeliveryCreateConvertor.appendPaymentInfo 支付单信息查询结果为空，shopOrderId={}", shopOrder.getId());
            return;
        }

        target.setPayCode(getPayCode(payment));
        target.setPayNo(payment.getPaySerialNo());
        target.setPayAmount(new BigDecimal(shopOrder.getFee())
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                .toPlainString());

        //支付人的信息
        var payerInfos = payerInfoReadService.findByOrderIds(Lists.newArrayList(shopOrder.getId())).getResult();
        if (CollectionUtils.isEmpty(payerInfos)) {
            log.warn("Y800V3DeliveryCreateConvertor.appendPaymentInfo 支付人信息查询结果为空，shopOrderId={}", shopOrder.getId());
            return;
        }
        Key key = EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey);
        PayerInfo.Helper.Info info = PayerInfo.Helper.decodeWithoutCache(key, payerInfos.get(0).info());

        User buyerUser = (User) userReadService.findById(shopOrder.getBuyerId()).getResult();

        target.setPayerName(info.getName());
        target.setPayerNo(info.getNo());
        if (buyerUser != null) {
            target.setPayerMobile(buyerUser.getMobile());
        }
    }

    /**
     * 由payment的channel映射到 api-v3 的支付平台编码
     *
     * @param payment
     * @return
     */
    private String getPayCode(Payment payment) {
        if (payment == null) {
            return null;
        }

        if (!environmentConfig.isOnline() && PaymentChannelEnum.MOCK_PAY.getCode().equals(payment.getChannel())) {
            log.info("paymentId={}, 非线上环境的模拟支付，统一传联动={}", payment.getId(), Y800V3PayCodeEnum.LIAN_DONG.getCode());
            return Y800V3PayCodeEnum.LIAN_DONG.getCode();
        }

        Y800V3PayCodeEnum y800V3PayCode = Y800V3PayCodeEnum.from(PaymentChannelEnum.from(payment.getChannel()));
        if (y800V3PayCode == null) {
            return null;
        }

        return y800V3PayCode.getCode();
    }

    /**
     * 查询对应的支付单
     *
     * @param shopOrder
     * @param skuOrderList
     * @return
     */
    public Payment findPayment(ShopOrder shopOrder, List<SkuOrder> skuOrderList) {
        var payments = paymentReadService.findByOrderIdAndOrderLevel(shopOrder.getId(), OrderLevel.SHOP).getResult();
        payments = CollectionUtils.isEmpty(payments) ? payments :
                payments.stream().filter(payment -> OrderStatus.PAID.getValue() == payment.getStatus()).toList();
        if (!CollectionUtils.isEmpty(payments)) {
            return payments.get(0);
        }

        payments = paymentReadService.findByOrderIdAndOrderLevel(skuOrderList.get(0).getId(), OrderLevel.SKU).getResult();
        payments = CollectionUtils.isEmpty(payments) ? payments :
                payments.stream().filter(payment -> OrderStatus.PAID.getValue() == payment.getStatus()).toList();
        if (CollectionUtils.isEmpty(payments)) {
            return null;
        }

        return payments.get(0);
    }

    /**
     * bond-商品均为保税订单,<br/>
     * general-商品均为一般贸易的订单, <br/>
     * mix-商品包含保税和完税商品的订单,<br/>
     * 但当前系统是会把 保税商品和完税商品进行拆单的，即一个shopOrder要么是保税，要么是完税，不存在mix
     *
     * @param skuOrderList
     * @return
     */
    private String getBondType(List<SkuOrder> skuOrderList) {
        if (CollectionUtils.isEmpty(skuOrderList)) {
            return BOND_TYPE_GENERAL;
        }

        return skuOrderList.stream().allMatch(skuOrder -> BondedType.fromInt(skuOrder.getIsBonded()).isBonded()) ?
                BOND_TYPE_BOND : BOND_TYPE_GENERAL;
    }
	private static final BigDecimal TAX_RATE = new BigDecimal("0.091");
	@Resource
	private SkuHistoryLogic skuHistoryLogic;

    /**
     * 老旧的订单打包 仅仅保证可用
     *
     * @param skuOrderList 单品订单列表
     */
    private Y800ShipmentOrderSku[] convert(ShopOrder shopOrder, List<SkuOrder> skuOrderList, String bondType) {
		BigDecimal shipFee = new BigDecimal(shopOrder.getShipFee()).divide(new BigDecimal(100), 4, RoundingMode.DOWN);
		List<Y800ShipmentOrderSku> y800ShipmentOrderSkuList = new ArrayList<>(skuOrderList.size());
        for (int i = 0; i < skuOrderList.size(); i++) {
			// 是否为首位
			boolean isFirst = i == 0;
            SkuOrder skuOrder = skuOrderList.get(i);
            Long itemId = skuOrder.getItemId();
            Item item = itemReadService.findById(itemId).getResult();
			Long skuId = skuOrder.getSkuId();
			Integer skuVersion = skuOrder.getSkuVersion();
			SkuHistory skuHistory = skuHistoryLogic.getSkuHistory(skuId, skuVersion);
			if (skuHistory == null) {
				skuHistory = new SkuHistory();
				Sku sku = skuReadService.findSkuById(skuId).getResult();
				SkuHistoryData skuHistoryData = BeanUtil.copyProperties(sku, SkuHistoryData.class);
				SkuCustom skuCustom = skuCustomReadService.findBySkuId(skuId);
				skuHistoryData.setSkuCustomHistory(BeanUtil.copyProperties(skuCustom, SkuCustomHistory.class));
				skuHistory.setCurrentData(skuHistoryData);
			}
            if (!item.getType().equals(ItemTypeEnum.COMBINATION.getCode())) {
				// 非组合品推单
				fillNonCombinationProductInfo(skuOrder, skuHistory, shipFee, isFirst, y800ShipmentOrderSkuList);
            }else{
				// 组合品推单
				fillCombinationProductInfo(skuOrder, skuHistory, shipFee, isFirst, y800ShipmentOrderSkuList);
			}
		}
		return y800ShipmentOrderSkuList.toArray(new Y800ShipmentOrderSku[]{});
	}

	private void fillCombinationProductInfo(SkuOrder skuOrder, SkuHistory skuHistory, BigDecimal shipFee, boolean isFirst, List<Y800ShipmentOrderSku> y800ShipmentOrderSkuList) {
		SkuHistoryData skuHistoryData = skuHistory.getCurrentData();
		SkuCustomHistory skuCustomHistory = skuHistoryData.getSkuCustomHistory();
		// 组合品的关联关系
		List<SkuComboRelationHistory> skuComboRelationHistories = skuHistoryData.getSkuComboRelationHistories();

		if (skuComboRelationHistories == null || skuComboRelationHistories.isEmpty()) {
			return;
		}

		List<Long> comboSkuIdList = skuComboRelationHistories.stream()
				.map(SkuComboRelationHistory::getComboSkuId)
				.toList();

		Response<List<Sku>> result = skuReadService.findSkusByIds(comboSkuIdList);
		if (result == null || result.getResult() == null || result.getResult().isEmpty()) {
			return;
		}

		List<Sku> skuList = result.getResult();
		Map<Long, Sku> comboSkuIdMap = skuList.stream()
				.collect(Collectors.toMap(Sku::getId, sku -> sku));

		// 组合品下的子品 实际需要发的数量
		Map<Long, Integer> quantityByComboSkuIdMap = skuComboRelationHistories.stream()
				.collect(Collectors.toMap(
						SkuComboRelationHistory::getComboSkuId,
						relation -> relation.getComboSkuQuantity() * skuOrder.getQuantity()
				));
		// 组合品下的子品 单独买的总价
		BigDecimal totalComboSkuPrice = skuList.stream()
				.map(sku -> {
					Integer quantity = quantityByComboSkuIdMap.getOrDefault(sku.getId(), 1);
					return new BigDecimal(sku.getPrice()).multiply(new BigDecimal(quantity));
				})
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		// 组合品下的子品 分别占比
		Map<Long, BigDecimal> rateByComboSkuIdMap = skuList.stream()
				.collect(Collectors.toMap(
						Sku::getId,
						sku -> {
							Integer quantity = quantityByComboSkuIdMap.getOrDefault(sku.getId(), 0);
							BigDecimal comboSkuPrice = new BigDecimal(sku.getPrice()).multiply(new BigDecimal(quantity));
							return comboSkuPrice.divide(totalComboSkuPrice, 4, RoundingMode.DOWN);
						}
				));
		long skuOrderDiscount = skuOrder.getDiscount() == null ? 0L : skuOrder.getDiscount();
		long skuOrderTax = skuOrder.getTax() == null ? 0L : skuOrder.getTax();
		BigDecimal remainingPrice = new BigDecimal(skuOrder.getOriginFee()).divide(HUNDRED, 4, RoundingMode.DOWN);
		BigDecimal remainingDiscount = new BigDecimal(skuOrderDiscount).divide(HUNDRED, 4, RoundingMode.DOWN);
		// 消费者所承担的税费
		BigDecimal remainingTaxOfConsumer = new BigDecimal(skuOrderTax).divide(HUNDRED, 4, RoundingMode.DOWN);

		for (int i = 0; i < skuComboRelationHistories.size(); i++) {
			Y800ShipmentOrderSku y800ShipmentOrderSku = new Y800ShipmentOrderSku();
			if (isFirst && i == 0) {
				y800ShipmentOrderSku.setShipFee(shipFee.toPlainString());
			} else {
				y800ShipmentOrderSku.setShipFee("0");
			}
			y800ShipmentOrderSku.setTax("0");
			y800ShipmentOrderSku.setDiscount("0");

			// 获取组合品下的子品关系
			SkuComboRelationHistory skuComboRelationHistory = skuComboRelationHistories.get(i);

			Long comboSkuId = skuComboRelationHistory.getComboSkuId();
			Sku sku = comboSkuIdMap.get(comboSkuId);

			Integer quantity = quantityByComboSkuIdMap.getOrDefault(comboSkuId, 0);
			y800ShipmentOrderSku.setSkuNo(sku.getOuterSkuId());
			y800ShipmentOrderSku.setNum(String.valueOf(quantity));

			BigDecimal price;
			BigDecimal discount;
			BigDecimal taxOfConsumer;

			if (i == skuComboRelationHistories.size() - 1) {
				price = remainingPrice;
				discount = remainingDiscount;
                if (!skuOrder.getIsBonded().equals(BondedType.GENERAL_TRADE.getCode())) {
                    // 保税情况下
                    if (skuCustomHistory.getCustomTaxHolder().equals(SkuCustomTaxHolderEnum.BUYER.getCode())) {
                        y800ShipmentOrderSku.setTax(remainingTaxOfConsumer.toPlainString());
                    }
                }
			} else {
				BigDecimal rate = rateByComboSkuIdMap.getOrDefault(comboSkuId, BigDecimal.ZERO);
				price = new BigDecimal(skuOrder.getOriginFee())
						.multiply(rate)
						.divide(HUNDRED, 4, RoundingMode.DOWN);
				remainingPrice = remainingPrice.subtract(price);

				remainingPrice = remainingPrice.max(BigDecimal.ZERO); // 避免负值

				discount = new BigDecimal(skuOrderDiscount)
						.multiply(rate)
						.divide(HUNDRED, 4, RoundingMode.DOWN);
				remainingDiscount = remainingDiscount.subtract(discount);

				remainingDiscount = remainingDiscount.max(BigDecimal.ZERO);// 避免负值

				if (!skuOrder.getIsBonded().equals(BondedType.GENERAL_TRADE.getCode())) {
					// 保税情况下
					if (skuOrder.getShippingWarehouseType().equals(SkuOrderShippingWarehouseTypeEnum.DAITA_SELF.getValue())) {
						if (skuCustomHistory.getCustomTaxHolder().equals(SkuCustomTaxHolderEnum.SELLER.getCode())) {
							// 商家承担税费
							BigDecimal taxOfMerchant = price.multiply(TAX_RATE);
							price = price.subtract(taxOfMerchant);
							y800ShipmentOrderSku.setTax(taxOfMerchant.toPlainString());
						} else {
							// 买家承担税费
							taxOfConsumer = new BigDecimal(skuOrderTax)
									.multiply(rate)
									.divide(HUNDRED, 4, RoundingMode.DOWN);
							remainingTaxOfConsumer = remainingTaxOfConsumer.subtract(taxOfConsumer);
							remainingTaxOfConsumer = remainingTaxOfConsumer.max(BigDecimal.ZERO);
							y800ShipmentOrderSku.setTax(taxOfConsumer.toPlainString());
						}
					}
				}
			}

			y800ShipmentOrderSku.setGoodsAmount(price.toPlainString());
			y800ShipmentOrderSku.setDiscount(discount.toPlainString());

			y800ShipmentOrderSkuList.add(y800ShipmentOrderSku);
		}
	}

	private void fillNonCombinationProductInfo(SkuOrder skuOrder, SkuHistory skuHistory, BigDecimal shipFee, boolean isFirst, List<Y800ShipmentOrderSku> y800ShipmentOrderSkuList) {
		Y800ShipmentOrderSku y800ShipmentOrderSku = new Y800ShipmentOrderSku();
		if (isFirst) {
			// 运费
			y800ShipmentOrderSku.setShipFee(shipFee.toPlainString());
		} else {
			y800ShipmentOrderSku.setShipFee("0");
		}
		// 默认税费
		y800ShipmentOrderSku.setTax("0");
		y800ShipmentOrderSku.setDiscount("0");
		SkuHistoryData skuHistoryData = skuHistory.getCurrentData();
		y800ShipmentOrderSku.setSkuNo(skuHistoryData.getOuterSkuId());
		y800ShipmentOrderSku.setNum(String.valueOf(skuOrder.getQuantity()));
		// 此商品项的总价
		BigDecimal price = new BigDecimal(skuOrder.getOriginFee()).divide(HUNDRED, 4, RoundingMode.DOWN);
		if (!skuOrder.getIsBonded().equals(BondedType.GENERAL_TRADE.getCode())) {
			// 保税情况下
			if (skuOrder.getShippingWarehouseType().equals(SkuOrderShippingWarehouseTypeEnum.DAITA_SELF.getValue())) {
				// 并且是代塔仓下 才算税费
				// 此商品项总税费
				BigDecimal tax = price.multiply(TAX_RATE);
				y800ShipmentOrderSku.setTax(tax.toPlainString());
				SkuCustomHistory skuCustomHistory = skuHistoryData.getSkuCustomHistory();
				if (skuCustomHistory.getCustomTaxHolder().equals(SkuCustomTaxHolderEnum.SELLER.getCode())) {
					// 只有在商家承担税费的时候，需要在此商品项的总价上减去税费
					price = price.subtract(tax);
				}
			}
        }
		y800ShipmentOrderSku.setGoodsAmount(price.toPlainString());
		BigDecimal discount = new BigDecimal(skuOrder.getDiscount() == null ? 0L : skuOrder.getDiscount()).divide(HUNDRED, 4, RoundingMode.DOWN);
		// 此商品项的总优惠
		y800ShipmentOrderSku.setDiscount(discount.toPlainString());
		y800ShipmentOrderSkuList.add(y800ShipmentOrderSku);
    }

}
