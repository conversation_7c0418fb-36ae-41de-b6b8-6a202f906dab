package moonstone.web.core.order.component;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.Json;
import moonstone.common.utils.Translate;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.emu.SkuTagIndex;
import moonstone.item.model.Item;
import moonstone.item.model.ItemDetail;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.DistributionRateProvider;
import moonstone.order.dto.*;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.rule.SkuItemLimitChecker;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import moonstone.user.service.UserCertificationReadService;
import moonstone.weShop.model.WeShop;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;


@Slf4j
public class CommunityRichOrderMaker {
    @Resource
    private ReceiverInfoReadService receiverInfoReadService;
    @Resource
    private ShopReadService shopReadService;
    @Resource
    private UserCertificationReadService userCertificationReadService;
    @Resource
    private ItemReadService itemReadService;
    @Resource
    private SkuReadService skuReadService;
    @Resource
    private ThirdPartySkuStockReadService thirdPartySkuStockReadService;
    @Resource
    private DistributionRateProvider distributionRateProvider;
//    @Autowired
//    private SkuItemLimitChecker skuItemLimitChecker;
//    @Autowired
//    private CommissionRuleReadService commissionRuleReadService;
    @Resource
    private ItemCacheHolder itemCacheHolder;
    @Resource
    private SkuCacheHolder skuCacheHolder;

    @Value("${beanConvertRate:0.1}")
    private BigDecimal beanConvertRate;

    static private final Integer maxSize = 360;

    private ReceiverInfo findReceiverInfoById(Long receiverInfoId) {
        Response<ReceiverInfo> rReceiverInfo = receiverInfoReadService.findById(receiverInfoId);
        if (!rReceiverInfo.isSuccess()) {
            log.error("receiverInfo(id={}) find fail, error code:{}", receiverInfoId, rReceiverInfo.getError());
            throw new ServiceException(rReceiverInfo.getError());
        }
        return rReceiverInfo.getResult();
    }
    private Shop findShopById(Long shopId) {
        Response<Shop> rShop = shopReadService.findById(shopId);
        if (!rShop.isSuccess()) {
            log.error("failed to find shop(id={}), error code:{}", shopId, rShop.getError());
            throw new ServiceException(rShop.getError());
        }
        return rShop.getResult();
    }
    private long queryPackageSize(Sku sku, Item item) {
        String key = SkuExtraIndex.skuOrderSplitLine.name();
        if (!CollUtil.isEmpty(sku.getExtraMap()) && sku.getExtraMap().containsKey(key) && StringUtils.hasText(sku.getExtraMap().get(key))) {
            try {
                BigDecimal skuOrderSplitLine = new BigDecimal(sku.getExtraMap().get(key));
                if (skuOrderSplitLine.compareTo(new BigDecimal("0.00")) <= 0) {
                    return 1;
                }

                BigDecimal volume = BigDecimal.valueOf(maxSize).divide(skuOrderSplitLine, RoundingMode.DOWN);
                return volume.longValue();
            } catch (Exception e) {
                log.error("FAIL TO COVERT THE SIZE OF ITEM[{}]", item, e);
            }
        }
        return 1;
    }
    private ThirdPartySkuStock readSkuStock(Sku sku) {
        try {
            var pushSystem = sku.getTags().get(SkuTagIndex.pushSystem.name());
            var system = Integer.parseInt(pushSystem.split(SkuTagIndex.pushSystem.getSplitter())[0]);
            return thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(
                    sku.getShopId(), system, sku.getOuterSkuId()).getResult().get(0);
        } catch (Exception e) {
            log.warn("当前的sku的商家编码不在第三方商品库存信息中 skuId {} 报错原因 {}",sku.getId(), e.getMessage());
            return new ThirdPartySkuStock();
        }
    }
    private ThirdPartySkuStock readSkuStock(SkuForOrderConstruct construct) {
        try {
            var pushSystem = construct.getSku().getTags().get(SkuTagIndex.pushSystem.name());
            var system = Integer.parseInt(pushSystem.split(SkuTagIndex.pushSystem.getSplitter())[0]);
            return thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(
                    construct.getShop().getId(), system, construct.getSku().getOuterSkuId()
            ).getResult().get(0);
        } catch (Exception e) {
            log.warn("未找到对应的第三方商品库存信息 当前的skuId {} 报错信息 {}", construct.getSku().getId(), e.getMessage());
            return new ThirdPartySkuStock();
        }
    }
    private Map<String, String> shapeMap(Map<String, String> map) {
        // remove the formId
        Map<String, String> newMap = map == null ? new TreeMap<>() : map;
        newMap.remove("formId");
        return newMap;
    }
    /// ## makeRichSku 由制定信息构造购买订单
    /// - channel 购买渠道
    /// - submittedSku 单品订单
    /// - sku 单品实体
    /// - shop 商店实体,便是这次提供货的商店
    /// - quantity 这次生成的订单中商品的数量
    /// - weShopId 微分销商铺id
    private RichSku makeRichSku(Integer channel, ThirdPartySkuStock thirdPartySkuStock, SubmittedSku submittedSku, Sku sku, Shop shop, int quantity, @Nullable Long weShopId) {
        RichSku richSku = new RichSku();
        if (thirdPartySkuStock != null) {
            BeanUtils.copyProperties(thirdPartySkuStock, richSku);
        }

        richSku.setPromotionId(submittedSku.getPromotionId());
        richSku.setShipmentType(submittedSku.getShipmentType());
        //richSku.setQuantity(submittedSku.getQuantity());
        richSku.setQuantity(quantity);
        richSku.setInvoiceId(submittedSku.getInvoiceId());
        richSku.setChannel(channel);
        richSku.setOrderStatus(OrderStatus.NOT_PAID.getValue());
        if (submittedSku.getReceiverInfoId() != null) {
            submittedSku.setReceiverInfoId(submittedSku.getReceiverInfoId());
        }

        richSku.setSku(sku);
        richSku.setDiffFee(0);

        Item item = itemReadService.findById(sku.getItemId()).getResult();
        richSku.setItem(item);
        // 分销抽佣费率 默认都为0
        Integer distributionRateForSkuOrder = distributionRateProvider.retrieveRateForSkuOrder(shop, richSku);
        richSku.setDistributionRate(distributionRateForSkuOrder);
        richSku.setTags(sku.getTags());
        richSku.setSourceType(submittedSku.getSourceType());
        ShopSkuDiscountDetailDTO discountDetail = submittedSku.getDiscountDetail();
        if (Objects.isNull(discountDetail.getGiftBeanNum()) && Objects.nonNull(discountDetail.getGiftPrice())) {
            // 初始化福豆数量
            discountDetail.setGiftBeanNum(discountDetail.getGiftPrice().divide(beanConvertRate, 2, RoundingMode.DOWN));
        }
        richSku.setDiscountDetail(discountDetail);

        richSku.setFee(discountDetail.getFee().multiply(BigDecimal.valueOf(100)).longValue());
        richSku.setOriginFee(discountDetail.getOriginFee().multiply(BigDecimal.valueOf(100)).longValue());
        richSku.setTax(discountDetail.getTax().multiply(BigDecimal.valueOf(100)).longValue());
//        richSku.setShipFee(discountDetail.getShipFee().multiply(BigDecimal.valueOf(100)).longValue());
        BigDecimal discount = discountDetail.getCashPrice()
                .add(discountDetail.getGiftPrice())
                .add(discountDetail.getItemCouponPrice())
                .add(discountDetail.getShopCouponPrice());
        richSku.setDiscount(discount.multiply(BigDecimal.valueOf(100)).longValue());
        BigDecimal afterDiscountFee = discountDetail.getOriginFee().subtract(discountDetail.getDiscount());
        richSku.setAfterDiscountFee(afterDiscountFee.multiply(BigDecimal.valueOf(100)).longValue());
        //  复制
        richSku.setExtra(Optional.ofNullable(richSku.getExtra()).orElseGet(HashMap::new));
        richSku.getExtra().putAll(shapeMap(sku.getExtraMap()));
        richSku.getExtra().putAll(shapeMap(submittedSku.getExtra()));
        return richSku;
    }

    /**
     * 拆单并且重新组装
     *
     * @param submittedSkus  订单中的sku聚集
     * @param shop           购买的店铺
     * @param richSkusByShop 组合订单模型
     * @param channel        购买渠道
     * @return 组合完毕的预组合订单列表
     */
    private List<RichSkusByShop> splitSkuSubmittedOrderAndConstructIt(List<SubmittedSku> submittedSkus, Shop shop, RichSkusByShop richSkusByShop, Integer channel) {
        List<RichSkusByShop> richSkusByShops = new ArrayList<>();
        List<SkuSplitOrderList> sealedSkuForOrderConstructList = new ArrayList<>();
        // 未装满的包裹
        List<SkuSplitOrderList> unfilledPackageList = new ArrayList<>();
        for (int i = 0; i < submittedSkus.size(); i++) {
            // 是否为最后一个商品
            SubmittedSku submittedSku = submittedSkus.get(i);

            /// 填充数据以供商品组构使用，并进行粗略分单
            final Long skuId = submittedSku.getSkuId();
            Sku sku = skuReadService.findSkuById(skuId).getResult();
            Item item = Optional.ofNullable(itemReadService.findById(sku.getItemId()).getResult())
                    .orElseThrow(() -> new JsonResponseException(Translate.of("商品找不到")));

            // 根据活动处理商品图片信息
            itemReadService.processItemForActivity(item, new ItemDetail());
            // 根据活动处理sku的价格
            skuReadService.processSkuForActivity(List.of(sku));

            long packageSize = queryPackageSize(sku, item);

            List<SkuSplitOrderList> prepareList = new ArrayList<>();
            if (CollUtil.isNotEmpty(unfilledPackageList)) {
                prepareList.addAll(unfilledPackageList);
                unfilledPackageList.clear();
            } else {
                // 默认的一个预装包裹
                prepareList.add(new SkuSplitOrderList(500000L, (long) maxSize, packageSize));
            }

            SkuForOrderConstruct construct = new SkuForOrderConstruct();
            construct.setItem(item);
            construct.setShop(shop);
            construct.setSku(sku);
            construct.setPackageSize(packageSize);
            construct.setSubmittedSku(submittedSku);
            construct.setQuantity(submittedSku.getQuantity());

            while (construct.getQuantity() > 0) {
                prepareList.forEach(list -> list.insert(construct));
                if (construct.getQuantity() > 0) {
                    if (!prepareList.isEmpty() && packageSize != 0L && prepareList.get(prepareList.size() - 1).getPackVolumeSumUp() == 0L) {
                        throw new JsonResponseException(new Translate("商品包裹已超限").toString());
                    } else {
                        prepareList.add(new SkuSplitOrderList(500000L, (long) maxSize, packageSize));
                    }
                }
            }

            for (SkuSplitOrderList dealPackage : prepareList) {
                if (dealPackage.full()) {
                    sealedSkuForOrderConstructList.add(dealPackage);
                    unfilledPackageList.remove(dealPackage);
                } else {
                    unfilledPackageList.add(dealPackage);
                }
            }
        }

        if (CollUtil.isNotEmpty(unfilledPackageList)) {
            sealedSkuForOrderConstructList.addAll(unfilledPackageList);
        }

        log.info("实际拆单后的包裹 {}", JSONUtil.toJsonStr(sealedSkuForOrderConstructList));

        for (SkuSplitOrderList skuSplitOrderList : sealedSkuForOrderConstructList) {
            RichSkusByShop subRichSkusByShop = new RichSkusByShop();
            BeanUtils.copyProperties(richSkusByShop, subRichSkusByShop);
            subRichSkusByShop.setRichSkus(new ArrayList<>());
            for (SkuForOrderConstruct construct : skuSplitOrderList.getSkuForOrderConstructList()) {
                ThirdPartySkuStock thirdPartySkuStock = new ThirdPartySkuStock();
                if (construct.getSku().getOuterSkuId() != null) {
                    thirdPartySkuStock = readSkuStock(construct);
                }
                /// 由购买渠道,订单信息,单品信息,数量构造单品订单 移动该部分代码
                RichSku richSku = makeRichSku(channel, thirdPartySkuStock
                        , construct.getSubmittedSku(), construct.getSku(), construct.getShop(), construct.getQuantity()
                        , Optional.ofNullable(construct.getSubmittedSku().getWeShopId()).orElseGet(() -> Optional.ofNullable(richSkusByShop.getWeShop()).map(WeShop::getId).orElse(null)));
                /// 由map和list混合的多级订单结构构造出多个shop订单
                subRichSkusByShop.getRichSkus().add(richSku);
            }
            Integer subDistributeRateForShopOrder = distributionRateProvider.retrieveRateForShopOrder(shop, subRichSkusByShop.getRichSkus());
            subRichSkusByShop.setDistributionRate(subDistributeRateForShopOrder);
            richSkusByShops.add(subRichSkusByShop);
        }
        return richSkusByShops;
    }

    /**
     * 提交创建订单请求, 此时应包括创建订单所需要的全部信息
     *
     * @param submittedOrder 包括skuId和购买数量信息, 以及收货地址, 发票, 各级别优惠等信息
     * @param user
     * @return
     */
    public RichOrder full(CommunitySubmittedOrder submittedOrder, CommonUser user) {

        RichOrder richOrder = new RichOrder();
        richOrder.setBuyer(user);
        Long globalReceiverInfoId = submittedOrder.getReceiverInfoId();
        if (globalReceiverInfoId != null) {
            final ReceiverInfo result = findReceiverInfoById(globalReceiverInfoId);
            richOrder.setReceiverInfo(result);
        }

//        richOrder.setBuyer(buyer);
        richOrder.setChannel(submittedOrder.getChannel());
        richOrder.setPayType(submittedOrder.getPayType());
        richOrder.setExtra(shapeMap(submittedOrder.getExtra()));

        final List<SubmittedSkusByShop> submittedSkusByShops = submittedOrder.getSubmittedSkusByShops();
        if (CollectionUtils.isEmpty(submittedSkusByShops)) {
            log.error("该订单没有提交的商品");
            throw new ServiceException("店铺订单没有需要提交的商品");
        }

        List<RichSkusByShop> richSkusByShops = new ArrayList<>(submittedSkusByShops.size());
        for (SubmittedSkusByShop submittedSkusByShop : submittedSkusByShops) {
            Map<Long, List<SubmittedSku>> submittedSkuListByShopIdMap = new HashMap<>();
            log.info("传入进来的店铺以及对应的商品信息 {}", JSONUtil.toJsonStr(submittedSkusByShop.getSubmittedSkus()));
            for (SubmittedSku submittedSku : submittedSkusByShop.getSubmittedSkus()) {
                Sku sku = skuReadService.findSkuById(submittedSku.getSkuId()).getResult();
                if (!submittedSkuListByShopIdMap.containsKey(sku.getShopId())) {
                    submittedSkuListByShopIdMap.put(sku.getShopId(), new ArrayList<>());
                }
                submittedSkuListByShopIdMap.get(sku.getShopId()).add(submittedSku);
            }
            log.info("根据店铺id为key，商品信息为value的map {}", JSONUtil.toJsonStr(submittedSkuListByShopIdMap));

            for (Long shopId : submittedSkuListByShopIdMap.keySet()) {
                List<SubmittedSku> submittedSkus = submittedSkuListByShopIdMap.get(shopId);
                Shop shop = findShopById(shopId);

                RichSkusByShop richSkusByShop = new RichSkusByShop();
                richSkusByShop.setOutFrom(submittedOrder.getOutFrom());
                richSkusByShop.setShop(shop);
                richSkusByShop.setBuyerNote(submittedSkusByShop.getBuyerNote());
                richSkusByShop.setInvoiceId(submittedSkusByShop.getInvoiceId());
                richSkusByShop.setShipmentType(submittedSkusByShop.getShipmentType());
                richSkusByShop.setPromotionId(submittedSkusByShop.getPromotionId());
                richSkusByShop.setShipmentPromotionId(submittedSkusByShop.getShipmentPromotionId());
                richSkusByShop.setChannel(submittedOrder.getChannel());
                richSkusByShop.setOrderStatus(OrderStatus.NOT_PAID.getValue());
                richSkusByShop.setOrderType(1);
                richSkusByShop.setOutFrom(OrderOutFrom.COMMUNITY_OPERATION.getOrderOutFormCode());
                richSkusByShop.setExtra(shapeMap(submittedSkusByShop.getExtra()));
                ShopOrderFeeDetailDTO shopOrderFeeDetailDTO = submittedSkusByShop.getShopOrderFeeDetailDTO();
                Assert.notNull(shopOrderFeeDetailDTO, "shopOrderFeeDetailDTO is null");
                if (shopOrderFeeDetailDTO.getGiftBeanNum() == null && shopOrderFeeDetailDTO.getGiftPrice() != null) {
                    // 初始化福豆数量
                    shopOrderFeeDetailDTO.setGiftBeanNum(shopOrderFeeDetailDTO.getGiftPrice().divide(beanConvertRate, 2, RoundingMode.DOWN));
                }
                richSkusByShop.setShopOrderFeeDetailDTO(shopOrderFeeDetailDTO);
                richSkusByShop.setFee(shopOrderFeeDetailDTO.getActualPrice().multiply(BigDecimal.valueOf(100)).longValue());
                richSkusByShop.setOriginFee(shopOrderFeeDetailDTO.getTotalPrice().multiply(BigDecimal.valueOf(100)).longValue());
                richSkusByShop.setDiscount(shopOrderFeeDetailDTO.getDiscountPrice().multiply(BigDecimal.valueOf(100)).intValue());
                richSkusByShop.setShipFee(shopOrderFeeDetailDTO.getShippingPrice().multiply(BigDecimal.valueOf(100)).intValue());
                richSkusByShop.setOriginShipFee(richSkusByShop.getShipFee());
                richSkusByShop.setOrderSource(submittedOrder.getOrderSource());
                richSkusByShop.setInventoryId(submittedOrder.getInventoryId());
                richSkusByShop.setGroupId(submittedOrder.getGroupId());
                richSkusByShop.setActivityId(submittedOrder.getActivityId());

                Optional.ofNullable(submittedOrder.getIdentityId())
                        .map(userCertificationReadService::findById)
                        .map(Response::getResult)
                        .ifPresent(userCertification -> {
                            submittedOrder.setPayerName(userCertification.getPaperName());
                            submittedOrder.setPayerNo(userCertification.getPaperNo());
                        });
                // 拷贝支付人信息
                if (ObjectUtils.isEmpty(richOrder.getPayerName())) {
                    Optional.ofNullable(submittedOrder.getPayerName())
                            .filter(StringUtils::hasText)
                            .ifPresent(richOrder::setPayerName);
                    Optional.ofNullable(submittedOrder.getExtra())
                            .map(map -> map.get(ShopOrderExtra.payerName.name()))
                            .filter(StringUtils::hasText)
                            .ifPresent(richOrder::setPayerName);
                }
                if (ObjectUtils.isEmpty(richOrder.getPayerId())) {
                    Optional.ofNullable(submittedOrder.getPayerNo()).filter(StringUtils::hasText)
                            .ifPresent(richOrder::setPayerId);
                    Optional.ofNullable(submittedOrder.getExtra())
                            .map(map -> map.get(ShopOrderExtra.payerNo.name()))
                            .filter(StringUtils::hasText)
                            .ifPresent(richOrder::setPayerId);
                }
                final Long receiverInfoId = submittedSkusByShop.getReceiverInfoId();
                if (receiverInfoId != null) {
                    richSkusByShop.setReceiverInfo(findReceiverInfoById(receiverInfoId));
                }
                // 拆单
                richSkusByShops.addAll(splitSkuSubmittedOrderAndConstructIt(submittedSkus, shop, richSkusByShop, submittedOrder.getChannel()));
                log.info("before check buy limit richSkusByShops={}", JSON.toJSONString(richSkusByShops));
                for (RichSkusByShop shouldCheckRichSkusByShop : richSkusByShops) {
                    try {
                        SkuItemLimitChecker skuItemLimitChecker = SkuItemLimitChecker.build();
                        shouldCheckRichSkusByShop.getRichSkus().forEach(skuItemLimitChecker::checkBuyLimit);
                    } catch (Exception e) {
                        log.error("FAIL TO BUILD ORDER {}", Json.toJson(richOrder), e);
                        throw e;
                    }
                }
            }
        }
        richOrder.setRichSkusByShops(richSkusByShops);
        return richOrder;
    }
}
