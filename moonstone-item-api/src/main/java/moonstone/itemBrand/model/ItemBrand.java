package moonstone.itemBrand.model;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * @Author: yousx
 * @Date: 2025/02/21
 * @Description:
 */
@Data
public class ItemBrand implements Serializable {


    @Serial
    private static final long serialVersionUID = 502393694407954304L;

    private Long id;

    private String name;

    private Long shopId;

    private String createdBy;

    private Date createdAt;

    private Date updatedAt;

}
