package moonstone.itemBrand.dto;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.BaseQuery;
import moonstone.common.model.dto.PageDto;

/**
 * @Author: yousx
 * @Date: 2025/02/21
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ItemBrandPageDto extends BaseQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 5171431353320007062L;

    private Integer size;

    private String name;

    private Long shopId;

    private Integer current;
}
