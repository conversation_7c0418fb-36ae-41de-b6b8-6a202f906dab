package moonstone.itemBrand.dto;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * @Author: yousx
 * @Date: 2025/02/21
 * @Description:
 */
@Data
public class ItemBrandDto implements Serializable {

    @Serial
    private static final long serialVersionUID = -8067863957907406762L;

    private Long id;

    private String name;

    private String createTime;

    private String createBy;

    private Date createdAt;

    private Date updatedAt;
}
