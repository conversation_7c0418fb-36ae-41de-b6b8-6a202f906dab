/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.vertx.core.impl.ConcurrentHashSet;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.OtherAttribute;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.common.VertxInstance;
import moonstone.common.api.HashCache;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.utils.B3Util;
import moonstone.common.utils.Json;
import moonstone.common.utils.R;
import moonstone.delivery.model.DeliveryFeeTemplate;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.delivery.service.DeliveryFeeReadService;
import moonstone.item.dto.ImageInfo;
import moonstone.item.dto.ItemWithAttribute;
import moonstone.item.dto.ViewedItem;
import moonstone.item.dto.ViewedItemDetailInfo;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.model.Item;
import moonstone.item.model.ItemAttribute;
import moonstone.item.model.ItemDetail;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuCustomReadService;
import moonstone.item.service.SkuReadService;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.service.ThirdPartySkuStockReadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 商品相关的缓存
 */
@Slf4j
@Component
public class ItemCacheHolder extends HashCache.JedisCache {
    @Autowired
    DeliveryFeeReadService deliveryFeeReadService;
    @Autowired
    ShopCacheHolder shopCache;
    @Autowired
    SkuCustomReadService skuCustomReadService;
    Pool<Jedis> pool;
    @Autowired
    private ItemReadService itemReadService;
    @Autowired
    private SkuReadService skuReadService;
    @Autowired
    private ThirdPartySkuStockReadService thirdPartySkuStockReadService;
    private LoadingCache<Long, Item> itemCache;
    private LoadingCache<Long, List<Long>> itemByShopCache;

    public ItemCacheHolder(Pool<Jedis> jedisPool) {
        super(jedisPool);
        pool = jedisPool;
    }

    @Override
    public long ttl() {
        // 2 hour
        return 2 * 60 * 60 * 1000;
    }

    public R appItemView(Long itemId) {
        var view = getCache(Prefix.ViewItemForApp.toString(), itemId,
                this::cacheItemViewI,
                id -> B3Util.b3sumStr(Objects.requireNonNull(Json.toJson(itemReadService.findById(id).getResult())).getBytes(StandardCharsets.UTF_8)),
                ResultItemDetail.class,
                3);
        Item quantity = itemReadService.queryQuantity(itemId);
        var mapped = JsonObject.mapFrom(view);
        mapped.put("saleQuantity", quantity.getSaleQuantity());
        mapped.put("stockQuantity", quantity.getStockQuantity());
        return R.ok().add("data", mapped.mapTo(ResultItemDetail.class));
    }

    //@EventListener(ContextRefreshedEvent.class)
    public void preloadItem() {
        Set<Long> itemDuplicate = new ConcurrentHashSet<>();
        new Thread(() -> findItemListByShopId(274L)
                .stream()
                .map(Item::getId)
                .filter(id -> !itemDuplicate.contains(id))
                .peek(itemDuplicate::add)
                .forEach(this::appItemView)).start();
        new Thread(() -> {
            // load the item first and load it
            long warmUpStartAt = System.currentTimeMillis();
            itemReadService.queryHotSellItem(3000).getResult()
                    .stream().filter(id -> !itemDuplicate.contains(id))
                    .peek(itemDuplicate::add)
                    .forEach(this::appItemView);
            log.info("Warm Up -> {}", System.currentTimeMillis() - warmUpStartAt);
        }).start();
    }

    @PostConstruct
    public void init() {
        itemCache = Caffeine.newBuilder()
                .expireAfterWrite(15, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(itemId -> {
                    final Response<Item> rItem = itemReadService.findById(itemId);
                    if (!rItem.isSuccess()) {
                        log.error("failed to find item(id={}), error code:{}", itemId, rItem.getError());
                        throw new ServiceException(rItem.getError());
                    }
                    return rItem.getResult();
                });

        itemByShopCache = Caffeine.newBuilder().expireAfterWrite(10, TimeUnit.MINUTES)
                .maximumSize(1000).build(shopId -> itemReadService.findByShopIds(Collections.singletonList(shopId)).getResult()
                        .stream().map(Item::getId).collect(Collectors.toList()));
    }

    /**
     * 根据商品id查找对应的商品
     *
     * @param itemId 商品id
     * @return 对应的商品信息
     */
    public Item findItemById(Long itemId) {
        return itemCache.get(itemId);
    }

    /**
     * 根据商品id查找商品详情页上半部分信息
     *
     * @param itemId 商品id
     * @return 商品详情页上半部分信息
     */
    public ViewedItem findViewedItemById(Long itemId) {
        long startAt = System.currentTimeMillis();
        ViewedItem viewedItem;
        try {
            viewedItem = getCache(Prefix.ViewItem.toString(), itemId, id -> itemReadService.findForView(itemId).getResult(),
                    id -> B3Util.b3sumStr(Objects.requireNonNull(Json.toJson(itemReadService.findById(id).getResult())).getBytes(StandardCharsets.UTF_8)),
                    ViewedItem.class, 3);
        } finally {
            if (System.currentTimeMillis() - startAt > 100) {
                log.warn("Item [{}] Cost -> {}", itemId, System.currentTimeMillis() - startAt);
            }
        }

        //校验：若使用了无效的活动数据，则将缓存失效一次，重新再查一遍
        //校验：活动数据生效时，但没有使用活动数据，则将缓存失效一次，重新再查一遍
        if (isUseInvalidActivityData(viewedItem) || notUseValidActivityData(viewedItem)) {
            invalidAllCache(itemId);
            viewedItem = itemReadService.findForView(itemId).getResult();
        }

        Item quantity = itemReadService.queryQuantity(itemId);
        viewedItem.getItem().setSaleQuantity(quantity.getSaleQuantity());
        viewedItem.getItem().setStockQuantity(quantity.getStockQuantity());

        return viewedItem;
    }

    /**
     * 使商品缓存失活
     */
    public void invalid(Long itemId) {
        invalidAllCache(itemId);

        VertxInstance.Main.getVertx().createSharedWorkerExecutor("ViewItemCache")
                .executeBlocking(p -> p.complete(findViewedItemById(itemId)));
        VertxInstance.Main.getVertx().createSharedWorkerExecutor("ViewItemCache")
                .executeBlocking(p -> p.complete(findViewDetail(itemId)));
        VertxInstance.Main.getVertx().createSharedWorkerExecutor("ViewItemCache")
                .executeBlocking(p -> p.complete(R.ok().add("data", appItemView(itemId))));
    }

    /**
     * 使商品缓存失活
     */
    public void invalidAllCache(Long itemId) {
        itemCache.invalidate(itemId);
        Long shopId = itemReadService.findById(itemId).getResult().getShopId();
        itemByShopCache.refresh(shopId);
        log.trace("Item Cache invalid from Item[{}] -> [{} {} {}]", itemId
                , invalid(Prefix.ViewItem.toString() + itemId)
                , invalid(Prefix.ViewItemDetail.toString() + itemId)
                , invalid(Prefix.ViewItemForApp.toString() + itemId));
    }

    /**
     * 获取商品详情
     *
     * @param itemId 商品Id
     * @return 详情
     */
    public ViewedItemDetailInfo findViewDetail(Long itemId) {
        long start = System.currentTimeMillis();
        try {
            var result = getCache(Prefix.ViewItemDetail.toString(), itemId
                    , id -> itemReadService.findItemDetailInfoByItemId(id).getResult()
                    , id -> B3Util.b3sumStr(Objects.requireNonNull(Json.toJson(itemReadService.findById(id).getResult())).getBytes(StandardCharsets.UTF_8))
                    , ViewedItemDetailInfo.class, 3);

            //校验：若使用了无效的活动数据，则将缓存失效一次，重新再查一遍
            //校验：活动数据生效时，但没有使用活动数据，则将缓存失效一次，重新再查一遍
            if (result.getItem() != null &&
                    (result.getItem().isUseInvalidActivityData() || result.getItem().notUseValidActivityData())) {
                invalidAllCache(itemId);
                result = itemReadService.findItemDetailInfoByItemId(itemId).getResult();
            }

            if (result != null && result.getGroupedOtherAttributes() == null) {
                result.setGroupedOtherAttributes(new ArrayList<>());
            }
            return result;
        } finally {
            long cost = System.currentTimeMillis() - start;
            if (cost > 100) {
                System.out.println("ItemDetail Generate -> " + cost);
            }
        }
    }

    public List<Item> findItemListByShopId(Long shopId) {
        if (shopId == null) {
            return Collections.emptyList();
        }
        List<Item> items = new ArrayList<>();
        for (Long itemId : Optional.ofNullable(itemByShopCache.get(shopId)).orElse(Collections.emptyList())) {
            if (itemId == null) {
                continue;
            }
            var item = itemCache.get(itemId);
            if (item != null) {
                var quantity = itemReadService.queryQuantity(itemId);
                item.setStockQuantity(quantity.getStockQuantity());
                item.setSaleQuantity(quantity.getSaleQuantity());
            }
            items.add(item);
        }
        return items;
    }

    private ResultItemDetail cacheItemViewI(Long itemId) {
        //商品详情上半部
        var viewedItem = findViewedItemById(itemId);

        var detailInfo = findViewDetail(itemId);
        if (detailInfo != null && detailInfo.getItemDetail() != null && viewedItem != null) {
            detailInfo.getItemDetail().setImages(viewedItem.getImageInfos());
        }

        //自建商品不需要仓库
        List<String> stockName = new ArrayList<>();
        if (Objects.equals(viewedItem.getItem().getIsThirdPartyItem(), 1)) {
            //获取仓库来源
            stockName.addAll(checkCost(() -> queryStockName(itemId)));
        }
        return checkCost(() -> buildItemDetail(viewedItem, detailInfo, stockName));
    }

    private <T> T checkCost(Supplier<T> s) {
        long start = System.currentTimeMillis();
        try {
            return s.get();
        } finally {
            long cost = System.currentTimeMillis() - start;
            if (cost > 200) {
                // get a supper cost
                StackTraceElement element = new RuntimeException().getStackTrace()[1];
                log.warn("{}@{} Cost -> {}", element.getMethodName(), element.getLineNumber(), cost);
            }
        }
    }

    private ResultItemDetail buildItemDetail(ViewedItem itemView, ViewedItemDetailInfo detailInfo, List<String> stockNameList) {
        if (itemView == null) {
            throw new RuntimeException("未查询到数据！！");
        }

        Item item = itemView.getItem();
        List<KvBody> params = extractItemBrandAndSpecificationAttribute(itemView.getItem());
        String paramsName = params.subList(0, Math.min(4, params.size())).stream().map(KvBody::key).collect(Collectors.joining(" "));
        //设置是否包邮
        Response<List<ItemDeliveryFee>> itemDeliveryFee = deliveryFeeReadService.findByItemIds(Collections.singletonList(item.getId()));
        boolean isFreeExp = false;
        if (itemDeliveryFee.isSuccess() && !ObjectUtils.isEmpty(itemDeliveryFee.getResult())) {
            Long deliveryFeeTemplateId = itemDeliveryFee.getResult().get(0).getDeliveryFeeTemplateId();
            Response<DeliveryFeeTemplate> deliveryFeeTemplateResponse = deliveryFeeReadService.findDeliveryFeeTemplateById(deliveryFeeTemplateId);
            if (deliveryFeeTemplateResponse.isSuccess() && deliveryFeeTemplateResponse.getResult() != null &&
                    deliveryFeeTemplateResponse.getResult().getIsFree()) {
                isFreeExp = true;
            }
        }
        List<String> subPics = Collections.emptyList();
        // is GongXiao Item
        if (1 == item.getIsThirdPartyItem() && itemView.getSkus().stream().anyMatch(this::isGongXiaoSource)) {
            subPics = buildSubPics(detailInfo);
        }
        //设置是否包税
        boolean isFreeTax = false;
        if (1 == item.getIsBonded()) {
            isFreeTax = itemView.getSkus().stream().map(Sku::getId).map(skuCustomReadService::findBySkuId)
                    .anyMatch(skuCustom -> 2 == skuCustom.getCustomTaxHolder());
        }
        List<SkusBody> skusBodies = new ArrayList<>();
        PriceSet priceSet = checkCost(() -> decorateViewItemDetail(itemView.getSkus(), skusBodies));
        // decorate the item production date from pc end set
        return new ResultItemDetail(
                item.getId()
                , item.getShopId()
                , item.getIsBonded()
                , item.getAdvertise()
                , item.getMainImage_()
                , item.getStatus()
                , item.getType()
                , subPics
                , item.getName()
                , item.getStockQuantity()
                , item.getSaleQuantity()
                , itemView.getRate()
                , CollectionUtils.isEmpty(stockNameList) ? StringUtils.EMPTY : stockNameList.stream().filter(StringUtils::isNotBlank).findFirst().orElse(StringUtils.EMPTY)
                , Optional.ofNullable(detailInfo).map(ViewedItemDetailInfo::getItemDetail).map(ItemDetail::getDetail).orElse("")
                , shopCache.findShopById(item.getShopId()).getName()
                , shopCache.findShopById(item.getShopId()).getImageUrl()
                , buildOrigin(itemView)
                , skusBodies
                , paramsName
                , params
                , isFreeExp
                , isFreeTax
                , CollectionUtils.isEmpty(stockNameList) ? StringUtils.EMPTY : stockNameList.stream().filter(StringUtils::isNotBlank).findFirst().orElse(StringUtils.EMPTY)
                , itemView.getTax()
                , Optional.ofNullable(item.getExtra()).map(e -> e.get("productionDateStart")).orElse(null)
                , Optional.ofNullable(item.getExtra()).map(e -> e.get("productionDateEnd")).orElse(null)
                , CollectionUtils.isEmpty(skusBodies) ? null : skusBodies.get(0).suggestPrice()
                , priceSet
                , skusBodies.stream().map(SkusBody::suggestPrice).collect(() -> new PriceSet(Integer.MAX_VALUE, Integer.MIN_VALUE), (set, p) -> new PriceSet(Math.min(set.min(), p), Math.max(set.max(), p)), (a, b) -> new PriceSet(Math.min(a.min(), b.min()), Math.max(a.max(), b.max()))));
    }

    public boolean isGongXiaoSource(Sku sku) {
        return Optional.ofNullable(sku.getTags()).orElseGet(TreeMap::new).getOrDefault("pushSystem", "").contains("4");
    }

    /**
     * extract the only need sku view from sku
     * copy name, price, status, quantity, if tax hold
     *
     * @param skus origin sku list
     * @return the sku view list
     */
    private PriceSet decorateViewItemDetail(List<Sku> skus, List<SkusBody> skuViewList) {
        //设置sku属性
        if (ObjectUtils.isEmpty(skus)) {
            return null;
        }
        PriceSet priceSet = new PriceSet(Integer.MAX_VALUE, Integer.MIN_VALUE);
        for (Sku sku : skus) {
            SkusBody skuView = new SkusBody(
                    sku.getId(),
                    buildKeyValueList(sku.getAttrs()),
                    sku.getStockQuantity(),
                    sku.getStatus(),
                    sku.getPrice(),
                    Optional.ofNullable(sku.getExtraPrice()).map(p -> p.get(SkuExtraIndex.suggest.getCode())).orElse(sku.getPrice())
                    , sku.getSpecification()
                    , Optional.ofNullable(sku.getExtraPrice()).map(p -> p.get(SkuExtraIndex.origin.getCode())).orElse(sku.getPrice())
            );
            // decorate price view from skuView
            priceSet = new PriceSet(Math.min(priceSet.min(), skuView.suggestPrice())
                    , Math.max(priceSet.max(), skuView.suggestPrice()));
            skuViewList.add(skuView);
        }
        return priceSet;
    }

    private List<KvBody> extractItemBrandAndSpecificationAttribute(Item item) {
        List<KvBody> kvBodies = new ArrayList<>();
        if (!ObjectUtils.isEmpty(item.getBrandId())) {
            KvBody kvBody = new KvBody("品牌", item.getBrandName());
            kvBodies.add(kvBody);
        }
        if (!ObjectUtils.isEmpty(item.getSpecification())) {
            KvBody kvBody = new KvBody("规格", item.getSpecification());
            kvBodies.add(kvBody);
        }

        Response<ItemWithAttribute> itemWithAttributeResponse = itemReadService.findItemWithAttributeById(item.getId());
        if (itemWithAttributeResponse.isSuccess() && itemWithAttributeResponse.getResult() != null
                && itemWithAttributeResponse.getResult().getItemAttribute() != null) {
            ItemAttribute itemAttribute = itemWithAttributeResponse.getResult().getItemAttribute();
            if (!ObjectUtils.isEmpty(itemAttribute.getOtherAttrs()) && !itemAttribute.getOtherAttrs().isEmpty()) {
                List<GroupedOtherAttribute> groupedOtherAttributes = itemAttribute.getOtherAttrs();
                if (ObjectUtils.isEmpty(groupedOtherAttributes) || groupedOtherAttributes.isEmpty()) {
                    return kvBodies;
                }
                List<OtherAttribute> otherAttributes = groupedOtherAttributes.get(0).getOtherAttributes();
                if (!ObjectUtils.isEmpty(otherAttributes) && !otherAttributes.isEmpty()) {
                    if (!ObjectUtils.isEmpty(otherAttributes.stream().filter(entity -> Objects.equals(entity.getAttrKey(), "origin"))
                            .map(OtherAttribute::getAttrVal).findFirst().orElse(""))) {
                        KvBody origin = new KvBody("产地", otherAttributes.stream().filter(entity -> Objects.equals(entity.getAttrKey(), "origin"))
                                .map(OtherAttribute::getAttrVal).findFirst().orElse(""));
                        kvBodies.add(origin);
                    }
                    if (!ObjectUtils.isEmpty(otherAttributes.stream().filter(entity -> Objects.equals(entity.getAttrKey(), "weight"))
                            .map(OtherAttribute::getAttrVal).findFirst().orElse(""))) {
                        KvBody weight = new KvBody("重量", otherAttributes.stream().filter(entity -> Objects.equals(entity.getAttrKey(), "weight"))
                                .map(OtherAttribute::getAttrVal).findFirst().orElse(""));
                        kvBodies.add(weight);
                    }
                }
            }
        }
        return kvBodies;
    }

    private List<KvBody> buildKeyValueList(List<SkuAttribute> list) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        List<KvBody> kvBodies = new ArrayList<>();
        for (SkuAttribute skuAttribute : list) {
            KvBody kvBody = new KvBody(skuAttribute.getAttrKey(), skuAttribute.getAttrVal());
            kvBodies.add(kvBody);
        }
        return kvBodies;
    }

    private List<String> buildSubPics(ViewedItemDetailInfo detailInfo) {
        if (detailInfo == null || detailInfo.getItemDetail() == null || detailInfo.getItemDetail().getImages() == null
                || detailInfo.getItemDetail().getImages().isEmpty()) {
            return new ArrayList<>();
        }
        List<String> subPics = new ArrayList<>();

        for (ImageInfo image : detailInfo.getItemDetail().getImages()) {
            subPics.add(image.getUrl());
        }
        return subPics;
    }

    private Map<String, Object> buildOrigin(ViewedItem result) {
        return new JsonObject()
                .put("id", result.getDefaultOriginId())
                .put("name", result.getDefaultOrigin())
                .put("icon", result.getDefaultOriginUrl())
                .getMap();
    }

    private List<String> getDeportNameFromSku(Sku sku) {
        Map<String, String> tags = sku.getTags() == null ? new HashMap<>(8) : sku.getTags();
        String pushSystemStr = tags.getOrDefault("pushSystem", "");
        if (pushSystemStr.isEmpty()) {
            log.error("this item didn't belong any ThirdPartyStock (itemId:{}),(pushSystem:{})", sku.getItemId(), pushSystemStr);
            return new ArrayList<>();
        }
        Integer pushSystemId = null;
        try {
            pushSystemId = Integer.parseInt(pushSystemStr.split(",")[0]);
        } catch (Exception ex) {
            log.error("parse pushSystemStr fail, skuId={}, pushSystemStr={}", sku.getId(), pushSystemStr);
        }
        if (pushSystemId == null) {
            return new ArrayList<>();
        }
        List<String> resultList = new ArrayList<>();
        if (ThirdPartySystem.fromInt(pushSystemId) == ThirdPartySystem.Y800_V2 ||
                ThirdPartySystem.fromInt(pushSystemId) == ThirdPartySystem.Y800_V3) {
            Response<List<ThirdPartySkuStock>> thirdPartySkuStockResponse = thirdPartySkuStockReadService.findByThirdPartyIdAndOuterSkuId(
                    sku.getShopId(), pushSystemId, sku.getOuterSkuId());
            if (thirdPartySkuStockResponse.isSuccess() && !CollectionUtils.isEmpty(thirdPartySkuStockResponse.getResult())) {
                resultList = thirdPartySkuStockResponse.getResult().stream().map(ThirdPartySkuStock::getDepotName).collect(Collectors.toList());
            }
            if (sku.getShopId() == 28) {
                for (int i = 0; i < resultList.size(); i++) {
                    if (resultList.get(i).startsWith("但丁")) {
                        resultList.set(i, resultList.get(i).substring(2));
                    }
                    if (resultList.get(i).contains("金义仓")) {
                        resultList.set(i, "金义保税区");
                    }
                }
            }
        } else {
            log.error("ItemCacheHolder.getDeportNameFromSku, find pushSystem failed, skuId={}, pushSystem={}, outerSkuId={}",
                    sku.getId(), pushSystemId, sku.getOuterSkuId());
        }
        return resultList;
    }

    private List<String> queryStockName(Long itemId) {
        var skuList = skuReadService.findSkusByItemId(itemId).getResult();
        List<List<String>> outerSkuIds = skuList.stream().map(this::getDeportNameFromSku).toList();
        Set<String> resultSet = new LinkedHashSet<>();
        for (List<String> deportName : outerSkuIds) {
            resultSet.addAll(deportName);
        }
        return new ArrayList<>(resultSet);
    }

    /**
     * 校验 viewedItem 是否使用了无效的活动数据
     */
    private boolean isUseInvalidActivityData(ViewedItem viewedItem) {
        if (viewedItem == null) {
            return false;
        }

        // item 是否使用了无效的活动数据
        if (viewedItem.getItem() != null && viewedItem.getItem().isUseInvalidActivityData()) {
            return true;
        }

        // sku 是否使用了无效的活动数据
        if (!CollectionUtils.isEmpty(viewedItem.getSkus())) {
            for (Sku sku : viewedItem.getSkus()) {
                if (sku == null || CollectionUtils.isEmpty(sku.getExtraMap())) {
                    continue;
                }
                if (Boolean.TRUE.toString().equals(sku.getExtraMap().get(SkuExtraIndex.isUseExtraActivity.name())) &&
                        !sku.isExtraActivityValid()) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 是否没有使用生效的活动数据
     *
     * @param viewedItem
     * @return
     */
    private boolean notUseValidActivityData(ViewedItem viewedItem) {
        if (viewedItem == null) {
            return false;
        }

        // item 是否没有使用生效的活动数据
        if (viewedItem.getItem() != null && viewedItem.getItem().notUseValidActivityData()) {
            return true;
        }

        // sku 是否没有使用生效的活动数据
        if (!CollectionUtils.isEmpty(viewedItem.getSkus())) {
            for (Sku sku : viewedItem.getSkus()) {
                if (sku == null || CollectionUtils.isEmpty(sku.getExtraMap())) {
                    continue;
                }
                if (!Boolean.TRUE.toString().equals(sku.getExtraMap().get(SkuExtraIndex.isUseExtraActivity.name())) &&
                        sku.isExtraActivityValid()) {
                    return true;
                }
            }
        }

        return false;
    }

    enum Prefix {
        ViewItemForApp,
        ViewItem,
        ViewItemDetail;

        @Override
        public String toString() {
            return super.toString() + "#";
        }
    }

    record ResultItemDetail(
            Long id,
            Long shopId,
            Integer isBonded,//是否保税(1:保税，0:完税，2：跨境直邮（保税）)
            String advertise,
            String mainPic,
            Integer status,
            Integer type,
            List<String> subPics,
            String title,
            Integer stockQuantity,//库存
            Integer saleQuantity,//销量
            Double rate,//税率
            String description,
            String detail,
            String shopName,
            String shopNamePic,
            Map<String, Object> origin,//使用json
            List<SkusBody> skus,
            String paramsName,
            List<KvBody> params,
            Boolean isFreeExp,
            Boolean isFreeTax,
            String depotName,
            Long tax,
            String productionDateStart,
            String productionDateEnd,
            /*
             * 建议零售价
             */
            Integer suggestPrice,
            /*
             * 通常价格范围字段
             */
            PriceSet priceSet,
            /*
             * 建议价格范围字段
             */
            PriceSet suggestPriceSet) {


    }

    public record PriceSet(Integer min, Integer max) {

    }


    public record SkusBody(
            Long id,
            List<KvBody> keyValueList,
            Integer quantity,
            Integer status,
            Integer price,
            /*
             * 建议零售价
             */
            Integer suggestPrice,
            String specification,
            Integer originPrice) {

    }

    public record KvBody(String key, String value) {
    }
}
