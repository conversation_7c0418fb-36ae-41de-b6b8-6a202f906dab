package moonstone.item.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.EntityBase;
import moonstone.common.utils.Translate;
import moonstone.item.api.VersionWith;
import moonstone.item.emu.CashGiftUnitEnum;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/22 18:02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IntermediateInfo extends EntityBase implements VersionWith {

    /**
     * 矩阵下标用于储存额外的佣金
     *
     * @see IntermediateInfo#setExtraFeeCalMatrix(String)
     * @see IntermediateInfo#getExtraFeeCalMatrix()
     */
    private static String MATRIX_INDEX = "MATRIX_INDEX";

    Long thirdId;//第三方id

    Long firstRate;//一级佣金 在美素门店中，属于门店佣金

    Long firstFee;//一级固定费用

    Long secondRate;//二及佣金 在美素门店中，属于导购佣金

    Long secondFee;//二级固定费用

    Long commission;//拉新佣金

    Long firstCommission;//首购佣金佣金

    Long serviceProviderFee; // 服务商佣金

    Long serviceProviderRate; // 服务商佣金比例

    /**
     * 是否开启单品佣金
     *
     * @see moonstone.common.enums.SingleProductCommissionEnum
     */
    Integer isCommission;//是否开启单品佣金 0不开启 1开启

    Integer isCashGift; //是否开启礼金 0不开启 1开启

    BigDecimal maxDeduction;

    /**
     * @see CashGiftUnitEnum
     */
    Integer unit;

    Long cashGiftUseTimeStart;

    Long cashGiftUseTimeEnd;

    /**
     * 类型
     *
     * @see moonstone.common.enums.ThirdIntermediateType
     */
    Integer type;

    /**
     * @see CalculateType
     */
    Integer flag;//是否开启比率和固定金额 0 固定 1比率  不存数据库

    final int nowVersion = 3;// 目前的佣金版本为3 ---万分制  否则百分制

    /**
     * 适用类型 1-通常配置, 2-活动配置)
     *
     * @see moonstone.common.enums.IntermediateInfoMatchingTypeEnum
     */
    private Integer matchingType;

    /**
     * (非通常配置的)适用开始时间
     */
    private Date matchingStartTime;

    /**
     * (非通常配置的)适用结束时间
     */
    private Date matchingEndTime;

    /**
     * 为更新数据时处理一下非null值进行防止数据重叠现象
     */
    public void trimUpdateFee() {
        if (firstRate == null) {
            firstRate = 0L;
        }
        if (firstRate != 0L) {
            firstFee = 0L;
        }
        if (secondRate == null) {
            secondRate = 0L;
        }
        if (secondRate != 0L) {
            secondFee = 0L;
        }
        long totalRate = Stream.of(getFirstRate(), getSecondRate(), getServiceProviderRate())
                .filter(Objects::nonNull)
                .reduce(Long::sum).orElse(0L);
        if (totalRate >= 5000) {
            throw Translate.exceptionOf("分销不允许反佣金超过50%%");
        }
    }

    public void fillNullWithZero() {
        if (this.getSecondRate() == null) this.setSecondRate(0L);
        if (this.getSecondFee() == null) this.setSecondFee(0L);
        if (this.getFirstRate() == null) this.setFirstRate(0L);
        if (this.getFirstFee() == null) this.setFirstFee(0L);
        if (this.getCommission() == null) this.setCommission(0L);
        if (this.getFirstCommission() == null) this.setFirstCommission(0L);
        if (getServiceProviderFee() == null) setServiceProviderFee(0L);
        if (getServiceProviderRate() == null) setServiceProviderRate(0L);
        long totalRate = Stream.of(getFirstRate(), getSecondRate(), getServiceProviderRate())
                .filter(Objects::nonNull)
                .reduce(Long::sum).orElse(0L);
        long LIMIT_RATE = 5000;
        if (getVersion() < 3) {
            LIMIT_RATE = 50;
        }
        if (totalRate >= LIMIT_RATE) {
            throw Translate.exceptionOf("分销不允许反佣金超过50%% [%s]", totalRate);
        }
    }

    public void checkFlag() {
        if (this.getSecondRate() == null) this.setSecondRate(0L);
        if (this.getSecondFee() == null) this.setSecondFee(0L);
        if (this.getFirstRate() == null) this.setFirstRate(0L);
        if (this.getFirstFee() == null) this.setFirstFee(0L);
        if (this.getServiceProviderRate() == null) this.setServiceProviderRate(0L);
        if (this.getServiceProviderFee() == null) this.setServiceProviderFee(0L);

        if (this.getSecondFee() == 0L && this.getFirstFee() == 0L) {
            this.setFlag(CalculateType.BY_RATIO.getCode());
        } else {
            this.setFlag(CalculateType.FIXED_AMOUNT.getCode());
        }
    }

    @Override
    public int getVersion() {
        if (getExtra() != null) {
            return Integer.parseInt(getExtra().getOrDefault(__VERSION, "0"));
        }
        return 0;
    }

    @Override
    public void setVersion(int version) {
        getExtra().put(__VERSION, version + "");
    }


    public void setExtraFeeCalMatrix(String calMatrix) {
        getExtra().put(MATRIX_INDEX, calMatrix);
    }

    public String getExtraFeeCalMatrix() {
        return getExtra().get(MATRIX_INDEX);
    }

    /**
     * 佣金的计算方式
     */
    public enum CalculateType {
        FIXED_AMOUNT(0, "固定金额"),
        BY_RATIO(1, "按比例");
        private Integer code;
        private String description;

        CalculateType(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static CalculateType parse(Integer code) {
            for (var current : CalculateType.values()) {
                if (current.getCode().equals(code)) {
                    return current;
                }
            }

            return null;
        }
    }
}
