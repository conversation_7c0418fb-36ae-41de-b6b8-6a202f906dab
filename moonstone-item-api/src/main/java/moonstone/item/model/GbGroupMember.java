package moonstone.item.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
* 开团的团员信息
* </p>
*
* <AUTHOR>
* @since 2025-03-31
*/
@Data
@Accessors(chain = true)
@ApiModel(value="GbGroupMember对象", description="开团的团员信息")
public class GbGroupMember implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键id",required = false,hidden = true)
    protected Long id;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "团id")
    private Long groupId;

    @ApiModelProperty(value = "用户id")
    private Long groupUserId;

    @ApiModelProperty(value = "团员角色: 1: 团长  2：团员")
    private Integer memberRole;

    @ApiModelProperty(value = "进团时间")
    private Long joinTime;

    @ApiModelProperty(value = "团员状态：1：待支付 2：在团  3：退出 4: 开团成功 5：已发货 6：已签收")
    private Integer memberStatus;

    @ApiModelProperty(value = "退团时间")
    private Long outTime;

    @ApiModelProperty(value = "退团原因")
    private String outReason;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "版本")
    protected Integer version;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Long createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "最后修改时间")
    private Long updatedTime;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "是否已删除",required = false,hidden = true)
    protected Integer deleted;
}
