package moonstone.item.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
* 开团的团信息
* </p>
*
* <AUTHOR>
* @since 2025-03-31
*/
@Data
@Accessors(chain = true)
@ApiModel(value="GbGroupInfo对象", description="开团的团信息")
public class GbGroupInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键id",required = false,hidden = true)
    protected Long id;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "营销工具id")
    private Integer marketingToolId;

    @ApiModelProperty(value = "开团时间")
    private Long groupTime;

    @ApiModelProperty(value = "开团状态：1: 团长待支付 2：进行中 3：开团成功 4：开团失败")
    private Integer groupStatus;

    @ApiModelProperty(value = "失败原因：1：团长退团 2：到期未成功 3：店铺取消")
    private Integer failReason;

    @ApiModelProperty(value = "开团描述")
    private String groupDes;

    @ApiModelProperty(value = "成团时间")
    private Long successTime;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "版本")
    protected Integer version;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Long createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "最后修改时间")
    private Long updatedTime;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "是否已删除",required = false,hidden = true)
    protected Integer deleted;

}
