package moonstone.item.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SkuComboRelation {

	/**
	 * id
	 */
	private Long id;

	/**
	 * 店铺ID
	 */
	private Long shopId;

	/**
	 * skuId
	 */
	private Long skuId;

	/**
	 * 组合里的商品id
	 */
	private Long comboItemId;

	/**
	 * 组合里的商品名称
	 */
	private String comboItemName;

	/**
	 * 组合里的skuId
	 */
	private Long comboSkuId;

	/**
	 * 组合skuId数量
	 */
	private Integer comboSkuQuantity;

	/**
	 * 乐观锁
	 */
	private Integer version;

	/**
	 * 创建人（用户id）
	 */
	private String createdBy;

	/**
	 * 创建时间
	 */
	private Long createdTime;

	/**
	 * 修改人（用户id）
	 */
	private String updatedBy;

	/**
	 * 修改时间
	 */
	private Long updatedTime;

}
