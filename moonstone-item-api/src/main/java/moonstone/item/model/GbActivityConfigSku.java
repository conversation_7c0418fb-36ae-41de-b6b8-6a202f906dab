package moonstone.item.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
* 平团活动商品关联表
* </p>
*
* <AUTHOR>
* @since 2025-03-31
*/
@Data
@Accessors(chain = true)
@ApiModel(value="GbActivityConfigSpu对象", description="平团活动商品关联表")
public class GbActivityConfigSku implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "item_id")
    private Long itemId;

    @ApiModelProperty(value = "sku_id")
    private Long skuId;

    @ApiModelProperty(value = "拼团价")
    private Integer gbPrice;

    @ApiModelProperty(value = "限购数量")
    private Integer skuLimit;

    @ApiModelProperty(value = "可用福豆")
    private Integer isFudou;

    @ApiModelProperty(value = "可用礼金")
    private Integer isLijing;

    @ApiModelProperty(value = "可用优惠")
    private Integer isYouhui;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "版本")
    protected Integer version;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Long createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "最后修改时间")
    private Long updatedTime;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "是否已删除",required = false,hidden = true)
    protected Integer deleted;

    @ApiModelProperty(value = "是否参与拼团")
    private Integer joinFlag;

    @ApiModelProperty(value = "活动库存")
    private Integer skuStock;

    @ApiModelProperty(value = "营销工具ID（活动类型）")
    private Integer marketingToolId;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "")
    private Long id;

}
