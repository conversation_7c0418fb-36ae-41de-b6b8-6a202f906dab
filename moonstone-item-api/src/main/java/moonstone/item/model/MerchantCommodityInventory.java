package moonstone.item.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import moonstone.common.model.BaseEntity;

/**
* <p>
* 独立库存表	库存隔离表结构，用于后续通用库存功能
* </p>
*
* <AUTHOR>
* @since 2025-03-28
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="MerchantCommodityInventory对象", description="独立库存表	库存隔离表结构，用于后续通用库存功能")
public class MerchantCommodityInventory extends BaseEntity {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键id",required = false,hidden = true)
    protected Long id;

    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "item_id")
    private Long itemId;

    @ApiModelProperty(value = "sku_id")
    private Long skuId;

    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    @ApiModelProperty(value = "库存类型：1-拼团库存 2-分享购库存  3-砍一刀库存 9-其他库存")
    private Integer stockType;

    @ApiModelProperty(value = "初始库存")
    private Integer initQty;

    @ApiModelProperty(value = "可用库存数量")
    private Integer availableQty;

    @ApiModelProperty(value = "已分配库存(下单未支付-未使用)")
    private Integer allocatedQty;

    @ApiModelProperty(value = "锁定库存(支付未发货)")
    private Integer lockedQty;

    @ApiModelProperty(value = "在途数量")
    private Integer inTransitQty;

    @ApiModelProperty(value = "安全库存")
    private Integer safetyStock;

    @ApiModelProperty(value = "补货点")
    private Integer reorderPoint;

    @ApiModelProperty(value = "库存状态：0-初始化 1-启用  2-失效")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remarks;


    public void setId(Long id) {
        this.id = id;
    }
}
