package moonstone.item.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
* <p>
* 拼团活动表，gp全程为Group buying
* </p>
*
* <AUTHOR>
* @since 2025-03-31
*/
@Data
@Accessors(chain = true)
@ApiModel(value="GbActivityInfo对象", description="拼团活动表，gp全程为Group buying")
public class GbActivityInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键id",required = false,hidden = true)
    protected Long id;

    @ApiModelProperty(value = "营销工具id")
    private Integer marketingToolId;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动说明")
    private String activityDes;

    @ApiModelProperty(value = "活动图片")
    private String activityPics;

    @ApiModelProperty(value = "活动规则")
    private String activityRules;

    @ApiModelProperty(value = "成团人数")
    private Integer activityNum;

    @ApiModelProperty(value = "活动开始时间")
    private Long startTime;

    @ApiModelProperty(value = "活动结束时间")
    private Long endTime;

    @ApiModelProperty(value = "配置状态: 1-新建完成 2-进行中 3- 已到期  4-已失效 9-已清结")
    private Integer activityStatus;

    @ApiModelProperty(value = "是否有奖励")
    private Integer rewardFlag;

    @ApiModelProperty(value = "奖励对象")
    private Integer rewardObject;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "版本")
    protected Integer version;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Long createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "最后修改时间")
    private Long updatedTime;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "支付时限-分享购使用")
    private Double paymentDeadline;

    @ApiModelProperty(value = "是否已删除",required = false,hidden = true)
    protected Integer deleted;

    @ApiModelProperty(value = "是否自动成团： 1：是 2：否")
    private Integer autoSuccess;

    public void setId(Long id) {
        this.id = id;
    }
}
