package moonstone.item.service;

import moonstone.item.model.GbActivityInfo;
import moonstone.item.model.GbGroupInfo;

public interface GbGroupInfoReadService {

    /**
     * 根据shopOrderId查询团状态
     * @param shopOrderId
     * @return true:成功 false:失败
     */
    boolean groupIsSuccess(Long shopOrderId);

    GbActivityInfo findActivityInfoById(Integer activityId);

    GbGroupInfo findById(Long gbGroupMember);
}
