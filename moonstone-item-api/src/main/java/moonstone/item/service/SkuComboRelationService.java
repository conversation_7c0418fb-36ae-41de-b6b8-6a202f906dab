package moonstone.item.service;

import moonstone.item.model.SkuComboRelation;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SkuComboRelationService {


	/**
	 * 根据skuIdList查询组合关联关系
	 * @param skuIdList skuId列表
	 * @return 组合关联关系
	 */
	List<SkuComboRelation> getSkuComboRelationListBySkuIdList(List<Long> skuIdList);

	/**
	 * 根据skuId查询组合关联关系
	 * @param skuId skuId
	 * @return 组合关联关系
	 */
	List<SkuComboRelation> getSkuComboRelationListBySkuId(Long skuId);

	/**
	 * 根据comboItemId查询组合关联关系
	 * @param comboItemId comboItemId
	 * @return 组合关联关系
	 */
	List<SkuComboRelation> getSkuComboRelationListByComboItemId(Long comboItemId);
}
