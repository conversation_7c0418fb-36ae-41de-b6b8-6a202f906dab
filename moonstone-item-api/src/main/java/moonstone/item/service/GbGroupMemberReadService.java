package moonstone.item.service;

import moonstone.item.model.GbGroupInfo;
import moonstone.item.model.GbGroupMember;

import java.util.List;

public interface GbGroupMemberReadService {

    /**
     * 查找该活动中 该用户是否存在已经支付成功的团长记录
     * @param activityId
     * @param userId
     * @return
     */
    List<GbGroupMember> findLeaderByActivityId(Integer activityId, Long userId);

    /**
     * 根据userId和订单ID查询团记录
     * @param shopOrderId
     * @param userId
     * @return
     */
    GbGroupMember findByShopOrderId(Long shopOrderId, Long userId);


    Long countGroupMemberNum(Integer activityId, Long groupId);

    GbGroupInfo findGroupInfoByShopOrderId(Long shopOrderId, Long userId);

    GbGroupInfo findGroupInfo(GbGroupMember gbGroupMember);
}
