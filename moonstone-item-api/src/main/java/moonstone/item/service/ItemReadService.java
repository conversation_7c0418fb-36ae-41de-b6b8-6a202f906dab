/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.item.dto.*;
import moonstone.item.dto.paging.ItemCriteria;
import moonstone.item.model.Item;
import moonstone.item.model.ItemDetail;
import moonstone.item.model.Sku;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 商品相关读服务
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-15
 */
public interface ItemReadService {

    /**
     * find the item for the hot up
     *
     * @param num num limit
     * @return item id list
     */
    Either<List<Long>> queryHotSellItem(long num);

    /**
     * 根据id查找商品
     *
     * @param itemId 商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    Response<Item> findById(Long itemId);

    Response<List<Item>> findByShopIdAndExtraJsonLike(Long shopId, String extraJson);

    /**
     * 根据商铺id分页查询商品
     *
     * @param shopIds 商户id列表
     * @param offset  第几页
     * @param limit   每页展示数量
     * @return 返回商品数据
     */
    Response<List<Item>> pageByShopIds(List<Long> shopIds, int offset, int limit);

    Response<List<Item>> pageByShopIdsInWeShop(List<Long> shopIds, int offset, int limit);

    Response<List<Item>> findByShopIds(List<Long> shopIds);

    /**
     * 根据商铺id与状态分页查询商品
     *
     * @param shopIds 商户id列表
     * @param offset  第几页
     * @param limit   每页展示数量
     * @param status  目标是否上架
     * @return 返回商品数据
     */
    Response<List<Item>> pageByShopIdsAndStatus(List<Long> shopIds, int offset, int limit, int status);

    Response<List<Item>> pageByShopIdsAndStatusInWeShop(List<Long> shopIds, int offset, int limit, int status);

    Response<List<Item>> findByShopIdsAndStatus(List<Long> shopIds, int status);

    /**
     * 根据shopId列表和对name右匹配进行分页查找
     *
     * @param shopIds shopId列表 不可为空
     * @param name    名字 不可为空
     * @param offset  第几页
     * @param limit   每页数据数量
     * @return 商品数据
     */
    Response<List<Item>> pageByShopIdsAndLikeName(List<Long> shopIds, String name, int offset, int limit);

    Response<List<Item>> pageByShopIdsAndLikeNameInWeShop(List<Long> shopIds, String name, int offset, int limit);

    Response<List<Item>> pageByShopIdsAndLikeNameAndStatus(List<Long> shopIds, String name, int offset, int limit, int status);

    Response<List<Item>> pageByShopIdsAndLikeNameAndStatusInWeShop(List<Long> shopIds, String name, int offset, int limit, int status);

    Response<List<Item>> findByShopIdsAndLikeName(List<Long> shopIds, String name);

    Response<List<Item>> findByShopIdsAndLikeNameAndStatus(List<Long> shopIds, String name, int status);

    /**
     * 根据shopId列表和对name右匹配进行分页查找
     *
     * @param itemIds itemIds列表 不可为空
     * @param name    名字 不可为空
     * @param offset  第几页
     * @param limit   每页数据数量
     * @return 商品数据
     */
    Response<List<Item>> pageByItemIdsAndLikeName(List<Long> itemIds, String name, int offset, int limit);

    Response<List<Item>> pageByItemIdsAndLikeNameInWeShop(List<Long> itemIds, String name, int offset, int limit);

    Response<List<Item>> pageByItemIdsAndLikeNameAndStatus(List<Long> itemIds, String name, int offset, int limit, int status);

    Response<List<Item>> pageByItemIdsAndLikeNameAndStatusInWeShop(List<Long> itemIds, String name, int offset, int limit, int status);

    Response<List<Item>> findByItemIdsAndLikeName(List<Long> itemIds, String name);

    Response<List<Item>> findByItemIdsAndLikeNameAndStatus(List<Long> itemIds, String name, int status);


    /**
     * 根据ids查找商品列表
     *
     * @param itemIds 商品id列表
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    Response<List<Item>> findByIds(List<Long> itemIds);

    Response<List<Item>> findByIdsInWeShop(List<Long> itemIds);

    Response<List<Item>> findByIdsAndStatus(List<Long> itemIds, int status);

    Response<List<Item>> findByIdsAndStatusInWeShop(List<Long> itemIds, int status);

    Response<Long> countByIdsAndStatus(List<Long> itemIds, int status);

    Response<Long> countByIds(List<Long> itemIds);


    /**
     * 根据商品编码查询商品
     *
     * @param itemCode 商品编码
     * @return 对应的商品列表
     */
    Response<List<Item>> findByItemCode(String itemCode);


    /**
     * 根据店铺id及商品编码来查找对应的商品
     *
     * @param itemCode 商品编码
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    Response<List<Item>> findByShopIdAndCode(Long shopId, String itemCode);

    /**
     * 根据店铺ID及商品后台类目ID查询商品列表
     * 且根据销量排序，取前limitNum个
     *
     * @param shopId     店铺id
     * @param categoryId 后台类目ID
     * @param limitNum   limit数量，即取前面多少个
     */
    Response<List<Item>> findByShopIdAndCategoryIdLimit(Long shopId, Long categoryId, Integer limitNum);

    Response<Paging<Item>> paging(ItemCriteria criteria);

    Response<List<Item>> list(ItemCriteria criteria);

    /**
     * 商家后台商品列表,支持按状态查询,用于管理商品内商品，这个接口是被商家后台用的
     *
     * @param user     当前用户
     * @param itemCode 商品编码
     * @param itemId   商品id
     * @param itemName 商品名称
     * @param statuses 逗号分隔状态
     * @param status   状态
     * @param type     类型
     * @param pageNo   起始页码, 从1开始
     * @param pageSize 分页大小
     * @return 商品分页列表
     */
    Response<Paging<Item>> findBy(CommonUser user,
                                  List<Long> exShopIds,
                                  String itemCode,
                                  Long itemId,
                                  String itemName,
                                  String statuses,
                                  Integer status,
                                  Integer type,
                                  String typees,
                                  Integer sellOutStatus,
                                  Long shopCategoryId,
                                  Long restrictedSalesAreaTemplateId,
                                  Integer pageNo,
                                  Integer pageSize);

    /**
     * 根据条件获取商品列表
     *
     * @param criteria 分页条件
     * @return 商品分页
     */
    Response<Paging<ItemForList>> pagingItemForList(ItemCriteria criteria);

    Item queryQuantity(Long itemId);

    /**
     * 根据商品id查找对应的商品及属性
     *
     * @param itemId 商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    Response<ItemWithAttribute> findItemWithAttributeById(Long itemId);


    /**
     * 根据商品id查找对应的商品信息及sku, 这个是给渲染商品详情页用的, 负责商品详情页的上半部分
     *
     * @param itemId 商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    Response<ViewedItem> findForView(Long itemId);


    /**
     * 根据商品id查找商品详细信息, 包括富文本, 包装参数, 服务信息, 分组属性信息等, 负责商品详情页的下半部分
     *
     * @param itemId 商品id
     * @return 商品详细信息
     */
    Response<ViewedItemDetailInfo> findItemDetailInfoByItemId(Long itemId);


    /**
     * 根据商品id查找对应的商品的所有信息
     *
     * @param itemId 商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    Response<FullItem> findFullInfoByItemId(Long itemId);


    /**
     * 根据商品id查找对应的商品详情富文本
     *
     * @param itemId 商品id
     * @return 如果找到对应的商品, success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    Response<String> findRichTextById(Long itemId);

    /**
     * 在店铺范围内根据商品编码或者商品名称查询商品及对应的sku
     *
     * @param shopId   店铺id
     * @param itemCode 商品编码,可选
     * @param itemName 商品名称,可选
     * @param pageNo   页码
     * @param pageSize 每页返回数量
     * @return 商品及对应的sku
     */
    Response<Paging<ItemWithSkus>> findItemWithSkus(Long shopId, String itemCode, String itemName, Integer pageNo, Integer pageSize);

    /**
     * 在店铺范围内根据商品编码或者商品名称查询商品及对应的sku
     *
     * @param shopId   店铺id
     * @param itemCode 商品编码,可选
     * @param itemName 商品名称,可选
     * @param typeStr  商品状态
     * @param pageNo   页码
     * @param pageSize 每页返回数量
     * @return 商品及对应的sku
     */
    Response<Paging<ItemWithSkus>> findItemWithSkus(Long shopId, String itemCode, String itemName, Long shopCategoryId,
                                                    String typeStr, Integer pageNo, Integer pageSize);


    /**
     * 查询id小于lastId且更新时间大于since的limit个商品
     *
     * @param lastId 最大的商品id,若为空,则取商品表的最大id值+1
     * @param since  更新起止时间
     * @param limit  商品个数
     * @return id小于lastId且更新时间大于since的limit个商品
     */
    Response<List<Item>> listSince(Long lastId, Date since, Integer limit);

    /**
     * 若在活动生效期间，替换item的商品主图、明细图
     *
     * @param item
     * @param itemDetail
     */
    void processItemForActivity(Item item, ItemDetail itemDetail);

    /**
     * 若在活动生效期间，替换 sku 的售价
     *
     * @param skus
     */
    void processSkuForActivity(List<Sku> skus);

    Response<List<Item>> findAllByShopId(Long shopId);

    Response<Long> countByRestrictedSalesAreaTemplate(Long shopId, Long templateId);

    /**
     * 查询所有的v2商品
     * @param shopId
     * @return
     */
    Response<List<Item>> findAllByThirdParty(Long shopId, ThirdPartySystem thirdPartySystem);

    /**
     * 根据请求条件获取命中数
     * @param query 查询参数
     * @return 命中数
     */
    Long countByCondition(Map<String, Object> query);

    /**
     * 根据请求条件获取商品列表信息
     * @param query 查询参数
     * @return 商品列表信息
     */
    List<Item> pagesByCondition(Map<String, Object> query);

    Long countByBrand(Long shopId, Long brandId);

    List<Item> findByBrandId(Long id);
}
