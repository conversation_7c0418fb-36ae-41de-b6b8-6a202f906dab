package moonstone.item.emu;

import moonstone.common.enums.Y800GoodsAndRecordTypeEnum;
import moonstone.common.model.rpcAPI.enums.Y800StorageSkuTradeTypeEnum;

public enum SkuTypeEnum {
    DUTY_PAID(0, "大贸（完税）"),
    BONDED(1, "全球购（保税）"),
    ;

    private final Integer code;
    private final String description;

    SkuTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String getDesc(Integer type) {
        for (SkuTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(type)) {
                return typeEnum.getDescription();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static SkuTypeEnum from(Y800StorageSkuTradeTypeEnum source) {
        if (source == null) {
            return DUTY_PAID;
        }

        if (Y800StorageSkuTradeTypeEnum.BONDED.equals(source) || Y800StorageSkuTradeTypeEnum.CROSS_BORDER_DIRECT.equals(source)) {
            return SkuTypeEnum.BONDED;
        } else {
            return SkuTypeEnum.DUTY_PAID;
        }
    }

    public static SkuTypeEnum fromY800GoodsAndRecordType(Y800GoodsAndRecordTypeEnum source) {
        if (source == null) {
            return DUTY_PAID;
        }

        if (Y800GoodsAndRecordTypeEnum.BONDED.equals(source)) {
            return SkuTypeEnum.BONDED;
        } else {
            return SkuTypeEnum.DUTY_PAID;
        }
    }
}
