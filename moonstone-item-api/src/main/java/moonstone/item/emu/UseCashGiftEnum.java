package moonstone.item.emu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: yousx
 * @Date: 2025/03/05
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum UseCashGiftEnum {

    NO(0, "否"),
    YES(1, "是");

    private final Integer code;
    private final String desc;

    public static UseCashGiftEnum getByCode(Integer code) {
        for (UseCashGiftEnum useCashGiftEnum : UseCashGiftEnum.values()) {
            if (useCashGiftEnum.getCode().equals(code)) {
                return useCashGiftEnum;
            }
        }
        return null;
    }
}
