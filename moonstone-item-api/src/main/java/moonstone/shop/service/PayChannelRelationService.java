package moonstone.shop.service;

import io.terminus.common.model.Response;
import moonstone.shop.dto.shoppayinfo.PayChannelRelationDTO;
import moonstone.shop.model.PayChannelRelation;

import java.util.List;

public interface PayChannelRelationService {

    Response<List<PayChannelRelation>> findAll();

    Response<Long> saveRelation(PayChannelRelation relation);

    Response<Boolean> modifyRelation(PayChannelRelation relation);

    Response<Boolean> deleteRelation(Long id);

    Response<PayChannelRelation> findById(Long shopId);

    Response<List<PayChannelRelation>> findByShopId(Long shopId);

    Response<List<PayChannelRelation>> findByProFileId(Long shopId, Long proFileId);

    Response<List<PayChannelRelation>> findByType(Long shopId, Integer tradeType, Integer relationType);

    /**
     * 支付渠道配置发布
     *
     * @param relationDTO
     * @return
     */
    Response<Boolean> shopPayChannelRelease(PayChannelRelationDTO relationDTO);

    /**
     * 取消支付渠道配置
     *
     * @param relation
     * @return
     */
    Response<Boolean> shopPayChannelCancel(PayChannelRelation relation);

}
