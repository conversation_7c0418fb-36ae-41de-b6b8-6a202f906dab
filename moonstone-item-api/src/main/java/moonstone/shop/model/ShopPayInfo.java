package moonstone.shop.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.Data;
import moonstone.common.constants.JacksonType;
import moonstone.shop.enums.ShopPayInfoExtraIndexEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;

@Data
public class ShopPayInfo implements Serializable {

    private static final long serialVersionUID = 3527549794203495428L;
    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    /**
     * 主键id
     */
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 支付渠道
     *
     * @see moonstone.shop.enums.ShopPayInfoPayChannelEnum
     */
    private String payChannel;

    /**
     * 微信：商户号
     */
    private String mchId;

    /**
     * 公众账号
     * |-支付宝：合作者身份ID-partner
     * |--微信：公众账号ID-appid
     */
    private String accountNo;

    /**
     * 秘钥
     */
    private String secretKey;

    /**
     * 微信支付API证书状态：0 未上传，1 已上传
     *
     * @see moonstone.shop.enums.WxCertFileStatusEnum
     */
    private Integer wxCertFileStatus;

    /**
     * 状态
     *
     * @see moonstone.shop.enums.ShopPayInfoStatusEnum
     */
    private Integer status;

    /**
     * 时间状态(唯一索引使用)
     */
    private Long statusTime;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 该支付信息下的支付海关信息
     */
    private List<ShopPayCustoms> shopPayCustoms;

    /**
     * 是否有海关信息
     */
    private Boolean hasChildren;

    /**
     * 收款公司名
     */
    private String recpName;
    /**
     * 收款公司社会信用代码
     */
    private String recpCode;

    /**
     * 使用渠道
     *
     * @see moonstone.shop.enums.ShopPayInfoUsageChannelEnum
     */
    private Integer usageChannel;

    /**
     * 扩展字段
     *
     * @see moonstone.shop.enums.ShopPayInfoExtraIndexEnum
     */
    @JsonIgnore
    private String extraJson;
    private Map<String, String> extra;

    /**
     * 公钥
     */
    private String publicKey;


    //==========临时使用==============
    /**
     * 类型: 1 灰度, 2 全量
     */
    private Integer relationType;
    /**
     * 0 大贸,1 保税
     */
    private Integer tradeType;
    //===============================

    public void setExtraJson(String extraJson) throws Exception {
        this.extraJson = extraJson;
        if (Strings.isNullOrEmpty(extraJson)) {
            this.extra = Collections.emptyMap();
        } else {
            this.extra = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if (extra == null || extra.isEmpty()) {
            this.extraJson = null;
        } else {
            try {
                this.extraJson = objectMapper.writeValueAsString(extra);
            } catch (Exception e) {
                //ignore
            }
        }
    }

    /**
     * 打码秘钥
     */
    public void encryptionSecretKey() {
        if (this.secretKey != null && this.secretKey.length() > 3) {
            this.secretKey = simpleStarEncryption(this.secretKey);
        }
        if (StringUtils.isNotBlank(this.publicKey) && this.publicKey.length() > 3) {
            this.publicKey = simpleStarEncryption(this.publicKey);
        }

        var extra = this.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }

        String certPassword = extra.get(ShopPayInfoExtraIndexEnum.CERT_PASSWORD.getCode());
        if (StringUtils.isNotBlank(certPassword) && certPassword.length() > 3) {
            extra.put(ShopPayInfoExtraIndexEnum.CERT_PASSWORD.getCode(), simpleStarEncryption(certPassword));
        }
    }

    private String simpleStarEncryption(String source) {
        return source.substring(0, 3) + "******" + source.substring(source.length() - 3);
    }

}
