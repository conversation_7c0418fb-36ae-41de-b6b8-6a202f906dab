package moonstone.shop.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.common.utils.JsonMapper;
import lombok.Data;

import java.io.Serializable;

@Data
public class PayChannelRelation implements Serializable {

    private static final long serialVersionUID = 3527549794203495429L;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    /**
     * 主键id
     */
    private Long id;

    /**
     * 支付配置id
     */
    private Long proFileId;

    /**
     * 类型: 1 启用, 2 禁用
     */
    private Integer status;

    /**
     * 类型: 1 灰度, 2 全量
     */
    private Integer relationType;

    /**
     * 0 大贸, 1 保税
     */
    private Integer tradeType;

    private String payChannel;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 乐观锁版本号
     */
    private Long version;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Long createdTime;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Long updatedTime;

    /**
     * 逻辑删除 1 删除 0 正常
     */
    private Integer deleted = 0;

}
