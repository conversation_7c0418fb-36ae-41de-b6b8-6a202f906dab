package moonstone.shop.enums;

public enum ShopPayInfoExtraIndexEnum {
    GREY_RELEASE_MEMBERS("greyReleaseMembers", "灰度成员"),
    SIGN_TYPE("signType", "通联支付的签名方式"),

    // 通联支付 - 云商通 - 开始
    ACCOUNT_SET_NO("accountSetNo", "商家在通联上面的账户集编号"),
    BIZ_USER_PERCENT("bizUserPercent",
            "托管代付时，减去分佣对象(如：门店)所得后，剩下的订单金小B账户分得的百分比，采用万分制，即为 5000 时， 小B账户 分得余下的 50% 订单金额"),
    CERT_PASSWORD("certPassword", "私钥证书文件对应的密码"),
    CERT_PATH("certPath", "私钥证书文件路径"),
    TL_CERT_PATH("tlCertPath", "公钥证书文件路径"),

    VSP_APP_ID("vspAppId", "集团模式下的集团商户号appid"),
    VSP_CUSID("vspCusid", "集团模式下的收银宝子商户号"),
    VSP_MERCHANTID("vspMerchantid", "集团模式下的集团商户收银宝商户号"),
    VSP_TERMID("vspTermid", "要绑定的终端信息(会员收银宝渠道商户信息及终端信息绑定接口用)"),

    ENTERPRISE_WITHDRAW_ACCOUNT("enterpriseWithdrawAccount", "付汇提现使用的账户"),

    VSP_REDIRECT("vspRedirect","支付方式：通联支付下 跳转到通联收银宝小程序开关")

    // 通联支付 - 云商通 - 结束

    ;

    private final String code;
    private final String description;

    ShopPayInfoExtraIndexEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
