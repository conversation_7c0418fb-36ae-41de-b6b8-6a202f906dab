package moonstone.shop.enums;

import java.util.List;

public enum ShopPayInfoStatusEnum {
    ACTIVE(1, "使用中"),
    @Deprecated
    GRAY_RELEASE(2, "灰度中"),
    INACTIVE(-1, "已停用"),
    FROZEN(-2, "冻结"),
    DELETED(-3, "已删除"),
    ;

    private final Integer code;
    private final String description;

    ShopPayInfoStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String parseDescription(Integer status) {
        for (var current : values()) {
            if (current.getCode().equals(status)) {
                return current.getDescription();
            }
        }

        return null;
    }

    public static List<Integer> validStatus() {
        return List.of(ACTIVE.getCode(), GRAY_RELEASE.getCode());
    }
}
