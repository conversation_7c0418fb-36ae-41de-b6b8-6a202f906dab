package moonstone.shop.enums;

public enum ShopPayInfoPayChannelEnum {
    ALIPAY("alipay", "支付宝支付"),
    WECHATPAY("wechatpay", "微信支付"),
    UMF("umf", "联动支付"),
    ALLINPAY("allinpay", "通联支付"),
    ALLINPAY_YST("allinpay-yst", "通联支付云商通"),

    // 什么鬼。。。
    WECHATPAY_JSAPI("wechatpay-jsapi", "微信支付JSAPI"),
    HBECARD("hbecard", "EasyPay"),
    ;

    private final String code;
    private final String description;

    ShopPayInfoPayChannelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static ShopPayInfoPayChannelEnum parse(String code) {
        for (var current : values()) {
            if (current.getCode().equals(code)) {
                return current;
            }
        }

        return null;
    }

    public static String parseDescription(String payChannel) {
        for (var current : values()) {
            if (current.getCode().equals(payChannel)) {
                return current.getDescription();
            }
        }

        return null;
    }
}
