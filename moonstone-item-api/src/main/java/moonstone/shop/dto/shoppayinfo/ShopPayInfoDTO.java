package moonstone.shop.dto.shoppayinfo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

@Data
public class ShopPayInfoDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1702732059326685024L;

    private Long id;

    /**
     * 商家id
     */
    private Long shopId;

    /**
     * 商家名称
     */
    private String shopName;

    /**
     * 使用渠道
     */
    private String usageChannelName;

    /**
     * 支付方式
     */
    private String payChannel;
    private String payChannelName;

    /**
     * 状态
     */
    private Integer status;
    private String statusName;

    /**
     * 1 启用, 2 禁用 (页面使用)
     */
    private Integer disable;

    /**
     * 支付配置明细
     */
    private ShopPayInfoDetailDTO detail;

}
