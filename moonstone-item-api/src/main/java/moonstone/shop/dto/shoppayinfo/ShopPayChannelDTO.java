package moonstone.shop.dto.shoppayinfo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ShopPayChannelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 0 大贸,1 保税, 2混合
     */
    private Integer tradeType;

    /**
     * 完税
     */
    private ShopPayChannelTypeDTO channelDutyPaid;

    /**
     * 保税
     */
    private ShopPayChannelTypeDTO channelBonded;

    @Data
    public static class ShopPayChannelTypeDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 灰度
         */
        private ShopPayChannelRelationDTO grayType;
        /**
         * 全量
         */
        private ShopPayChannelRelationDTO allType;

    }

    @Data
    public static class ShopPayChannelRelationDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        private Long id;

        private Long proFileId;

        private Long shopId;

        /**
         * 支付渠道
         */
        private String payChannel;
        private String payChannelName;

        /**
         * 灰度成员
         */
        private List<String> memberList;

    }

}
