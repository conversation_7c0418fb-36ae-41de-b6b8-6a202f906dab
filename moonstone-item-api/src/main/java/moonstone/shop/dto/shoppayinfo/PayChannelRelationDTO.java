package moonstone.shop.dto.shoppayinfo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PayChannelRelationDTO implements Serializable {

    private static final long serialVersionUID = 3527549794203495429L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 支付配置id
     */
    private Long proFileId;

    /**
     * 类型: 1 启用, 2 禁用
     */
    private Integer status;

    /**
     * 类型: 1 灰度, 2 全量
     */
    private Integer relationType;

    /**
     * 0 大贸, 1 保税
     */
    private Integer tradeType;

    /**
     * 支付渠道
     */
    private String payChannel;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改人
     */
    private String updatedBy;

    private List<String> memberList;

}
