/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.storage.service;

import io.terminus.common.model.Response;

/**
 * 库存服务
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-24
 */
public interface StorageService {

    /**
     * 根据产品id(可以是skuId, 也可以是itemId, 甚至也可以是spuId等), 以及对应的仓库id, 查找对应的库存
     *
     * @param productId  产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等), 由productType决定
     * @param productType 产品类型, 决定productId指sku, item还是spu
     * @param warehouseCode   仓库编码
     * @return   产品在对应仓库的库存
     */
    Response<Integer> findBy(Long productId, Integer productType, String warehouseCode);


    /**
     * 减少对应产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)及仓库id的库存
     *
     * @param productId     产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)
     * @param productType   产品类型, 决定productId指sku, item还是spu
     * @param warehouseCode 仓库编码
     * @param delta         库存变更数量
     * @param itemType      商品类型
     * @return 是否变更成功
     */
    Response<Boolean> decreaseBy(Long productId, Integer productType, String warehouseCode, Integer delta, Integer itemType);


    /**
     * 增加对应产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)及仓库id的库存
     *
     * @param productId     产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)
     * @param productType   产品类型, 决定productId指sku, item还是spu
     * @param warehouseCode 仓库编码
     * @param delta         库存变更数量
     * @param itemType      商品类型
     * @return 是否变更成功
     */
    Response<Boolean> increaseBy(Long productId, Integer productType, String warehouseCode, Integer delta, Integer itemType);


    /**
     * 设置对应产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)及仓库id的库存
     *
     * @param productId  产品id (可以是skuId, 也可以是itemId, 甚至也可以是spuId等)
     * @param productType 产品类型, 决定productId指sku, item还是spu
     * @param warehouseCode   仓库编码
     * @param quantity   库存目标数量
     * @return   是否设置成功
     */
    Response<Boolean> set(Long productId, Integer productType, String warehouseCode, Integer quantity);
}
