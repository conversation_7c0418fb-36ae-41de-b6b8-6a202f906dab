/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.component.item.component;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Objects;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.category.model.BackCategory;
import moonstone.category.service.BackCategoryReadService;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.component.dto.item.EditItem;
import moonstone.item.dto.FullItem;
import moonstone.item.dto.SkuWithCustom;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuCustomReadService;
import moonstone.rule.RuleEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 平台强管控商品及类目属性, 这里要对商品的属性做一些规则校验
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-24
 */
@Component
@Slf4j
public class ItemReader {

    @Autowired
    private ItemReadService itemReadService;

    @Autowired
    private SkuCustomReadService skuCustomReadService;

    @Autowired
    private RuleEngine ruleEngine;

    @Autowired
    private BackCategoryReadService backCategoryReadService;

    /**
     * 对要编辑的商品信息, 用最新的属性规则进行校验
     * @param seller 当前登录商家
     * @param itemId 商品id
     * @return  经过严格校验的商品信息
     */
    public Response<EditItem> findForEdit(CommonUser seller, Long itemId) {
        Response<FullItem> rFullItem = itemReadService.findFullInfoByItemId(itemId);
        if (!rFullItem.isSuccess()) {
            log.error("failed to find item(id={}), error code:{}", itemId, rFullItem.getError());
            return Response.fail(rFullItem.getError());
        }
        final FullItem fullItem = rFullItem.getResult();

        if (!Objects.equal(fullItem.getItem().getShopId(), seller.getShopId())) {
            log.error("the item(id={}) is not belong to seller(shop id={})", itemId, seller.getShopId());
            return Response.fail("item.not.belong.to.seller");
        }

        EditItem editItem = new EditItem();
        editItem.setItem(fullItem.getItem());
        editItem.setItemDeliveryFee(fullItem.getItemDeliveryFee());
        editItem.setItemDetail(fullItem.getItemDetail());
        ruleEngine.handleOutboundData(fullItem, editItem);
        List<SkuWithCustom> skuWithCustoms = editItem.getSkuWithCustoms();
        skuCustomReadService.fillSkuWithCustoms(skuWithCustoms);
        editItem.setSkuWithCustoms(skuWithCustoms);
        List<BackCategory> categories = backCategoryReadService.findAncestorsOf(fullItem.getCategoryId()).getResult();
        if (CollUtil.isEmpty(categories)) {
            throw new ApiException("当前商品对应的分类为空");
        }
        List<DropDownBoxVo> backCategoryList = categories.stream().map(category -> {
            DropDownBoxVo entity = new DropDownBoxVo();
            entity.setId(String.valueOf(category.getId()));
            entity.setName(category.getName());
            return entity;
        }).toList();
        editItem.setBackCategoryList(backCategoryList);
        return Response.ok(editItem);
    }


    public EditItem getItemAttr(Long itemId) {
        FullItem fullItem = itemReadService.findFullInfoByItemId(itemId).getResult();
        EditItem editItem = new EditItem();
        ruleEngine.handleOutboundData(fullItem, editItem);
        return editItem;
    }
}
